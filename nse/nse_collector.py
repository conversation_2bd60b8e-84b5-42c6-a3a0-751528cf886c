#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
NSE Data Collector Module (Mock Version for Testing)

This is a mock version of the NSE data collector for testing purposes.
It returns mock data instead of making actual API requests.
"""

import os
import sys
import time
import json
import logging
from datetime import datetime
from typing import List, Dict, Optional, Union, Any
import traceback

# Try to import pandas, but don't fail if it's not available
try:
    import pandas as pd
except ImportError:
    pd = None

# Configure logging
logger = logging.getLogger(__name__)

class NSEDataCollector:
    """
    Class for collecting data from NSE (Mock Version)
    """
    
    def __init__(self, data_dir: str = "data/nse"):
        """
        Initialize the NSE Data Collector
        
        Parameters:
        -----------
        data_dir : str
            Directory to store the collected data
        """
        self.data_dir = data_dir
        
        # Create data directories if they don't exist
        os.makedirs(f"{data_dir}/company_data", exist_ok=True)
        os.makedirs(f"{data_dir}/index_data", exist_ok=True)
        os.makedirs(f"{data_dir}/options_data", exist_ok=True)
        os.makedirs(f"{data_dir}/live_data", exist_ok=True)
        os.makedirs(f"{data_dir}/raw", exist_ok=True)
    
    def collect_company_data(self, symbols: Optional[List[str]] = None, 
                           force_refresh: bool = False) -> Dict:
        """
        Collect company data from NSE (Mock Version)
        
        Parameters:
        -----------
        symbols : list, optional
            List of symbols to collect data for (default: all symbols)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting company data from NSE (Mock Version)")
        
        try:
            # Use provided symbols or a default list
            if symbols is None:
                symbols = ["RELIANCE", "TCS", "HDFCBANK", "INFY", "ICICIBANK"]
            
            # Collect data for each symbol
            results = {"successful": 0, "failed": 0, "total": len(symbols)}
            
            for symbol in symbols:
                try:
                    # Check if data already exists
                    output_file = f"{self.data_dir}/company_data/{symbol}.json"
                    if os.path.exists(output_file) and not force_refresh:
                        logger.info(f"Data for {symbol} already exists, skipping")
                        results["successful"] += 1
                        continue
                    
                    # Create mock company data
                    company_data = {
                        "info": {
                            "symbol": symbol,
                            "companyName": f"{symbol} Limited",
                            "industry": "Technology",
                            "isin": f"INE{symbol[:3]}01018",
                            "lastUpdateTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        },
                        "metadata": {
                            "series": "EQ",
                            "symbol": symbol,
                            "isin": f"INE{symbol[:3]}01018",
                            "status": "Listed",
                            "listingDate": "2000-01-01",
                            "industry": "Technology"
                        },
                        "financials": {
                            "balanceSheet": {
                                "2022": {"totalAssets": "100000", "totalLiabilities": "50000"},
                                "2021": {"totalAssets": "90000", "totalLiabilities": "45000"},
                                "2020": {"totalAssets": "80000", "totalLiabilities": "40000"}
                            },
                            "profitAndLoss": {
                                "2022": {"revenue": "50000", "netProfit": "10000"},
                                "2021": {"revenue": "45000", "netProfit": "9000"},
                                "2020": {"revenue": "40000", "netProfit": "8000"}
                            },
                            "cashFlow": {
                                "2022": {"operatingCashFlow": "15000", "investingCashFlow": "-5000"},
                                "2021": {"operatingCashFlow": "13500", "investingCashFlow": "-4500"},
                                "2020": {"operatingCashFlow": "12000", "investingCashFlow": "-4000"}
                            }
                        },
                        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # Save the data
                    with open(output_file, 'w') as f:
                        json.dump(company_data, f, indent=2)
                    
                    logger.info(f"Successfully collected data for {symbol}")
                    results["successful"] += 1
                    
                    # Add a small delay to simulate API request
                    time.sleep(0.5)
                
                except Exception as e:
                    logger.error(f"Error collecting data for {symbol}: {str(e)}")
                    traceback.print_exc()
                    results["failed"] += 1
            
            logger.info(f"Completed collecting company data: {results['successful']} successful, {results['failed']} failed")
            return {"status": "success", **results}
        
        except Exception as e:
            logger.error(f"Error collecting company data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def collect_index_data(self, indices: Optional[List[str]] = None, 
                         force_refresh: bool = False) -> Dict:
        """
        Collect index data from NSE (Mock Version)
        
        Parameters:
        -----------
        indices : list, optional
            List of indices to collect data for (default: all indices)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting index data from NSE (Mock Version)")
        
        try:
            # Use provided indices or a default list
            if indices is None:
                indices = ["NIFTY50", "BANKNIFTY", "NIFTYIT", "NIFTYPHARMA"]
            
            # Collect data for each index
            results = {"successful": 0, "failed": 0, "total": len(indices)}
            
            for index in indices:
                try:
                    # Check if data already exists
                    output_file = f"{self.data_dir}/index_data/{index}.json"
                    if os.path.exists(output_file) and not force_refresh:
                        logger.info(f"Data for {index} already exists, skipping")
                        results["successful"] += 1
                        continue
                    
                    # Create mock index data
                    index_data = {
                        "info": {
                            "indexSymbol": index,
                            "indexName": f"{index} Index",
                            "lastUpdateTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        },
                        "history": {
                            "2022-01-01": {"open": "18000", "high": "18500", "low": "17800", "close": "18200"},
                            "2022-01-02": {"open": "18200", "high": "18700", "low": "18100", "close": "18600"},
                            "2022-01-03": {"open": "18600", "high": "19000", "low": "18500", "close": "18900"}
                        },
                        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # Save the data
                    with open(output_file, 'w') as f:
                        json.dump(index_data, f, indent=2)
                    
                    logger.info(f"Successfully collected data for {index}")
                    results["successful"] += 1
                    
                    # Add a small delay to simulate API request
                    time.sleep(0.5)
                
                except Exception as e:
                    logger.error(f"Error collecting data for {index}: {str(e)}")
                    traceback.print_exc()
                    results["failed"] += 1
            
            logger.info(f"Completed collecting index data: {results['successful']} successful, {results['failed']} failed")
            return {"status": "success", **results}
        
        except Exception as e:
            logger.error(f"Error collecting index data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def collect_options_data(self, symbols: Optional[List[str]] = None, 
                           force_refresh: bool = False) -> Dict:
        """
        Collect options data from NSE (Mock Version)
        
        Parameters:
        -----------
        symbols : list, optional
            List of symbols to collect options data for (default: all symbols with options)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting options data from NSE (Mock Version)")
        
        try:
            # Use provided symbols or a default list
            if symbols is None:
                symbols = ["RELIANCE", "TCS", "HDFCBANK", "INFY", "ICICIBANK"]
            
            # Collect data for each symbol
            results = {"successful": 0, "failed": 0, "total": len(symbols)}
            
            for symbol in symbols:
                try:
                    # Check if data already exists
                    output_file = f"{self.data_dir}/options_data/{symbol}.json"
                    if os.path.exists(output_file) and not force_refresh:
                        logger.info(f"Options data for {symbol} already exists, skipping")
                        results["successful"] += 1
                        continue
                    
                    # Create mock options data
                    options_data = {
                        "records": {
                            "expiryDates": ["2022-01-27", "2022-02-24", "2022-03-31"],
                            "data": [
                                {
                                    "strikePrice": "2000",
                                    "expiryDate": "2022-01-27",
                                    "call": {"openInterest": "1000", "lastPrice": "50"},
                                    "put": {"openInterest": "800", "lastPrice": "30"}
                                },
                                {
                                    "strikePrice": "2100",
                                    "expiryDate": "2022-01-27",
                                    "call": {"openInterest": "800", "lastPrice": "30"},
                                    "put": {"openInterest": "1000", "lastPrice": "40"}
                                }
                            ]
                        },
                        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # Save the data
                    with open(output_file, 'w') as f:
                        json.dump(options_data, f, indent=2)
                    
                    logger.info(f"Successfully collected options data for {symbol}")
                    results["successful"] += 1
                    
                    # Add a small delay to simulate API request
                    time.sleep(0.5)
                
                except Exception as e:
                    logger.error(f"Error collecting options data for {symbol}: {str(e)}")
                    traceback.print_exc()
                    results["failed"] += 1
            
            logger.info(f"Completed collecting options data: {results['successful']} successful, {results['failed']} failed")
            return {"status": "success", **results}
        
        except Exception as e:
            logger.error(f"Error collecting options data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def collect_live_data(self, symbols: Optional[List[str]] = None, 
                        force_refresh: bool = False) -> Dict:
        """
        Collect live market data from NSE (Mock Version)
        
        Parameters:
        -----------
        symbols : list, optional
            List of symbols to collect live data for (default: all symbols)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting live market data from NSE (Mock Version)")
        
        try:
            # Create mock live data
            live_data = {
                "market_status": {
                    "status": "open",
                    "lastUpdateTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                },
                "pre_open_market": {
                    "data": [
                        {"symbol": "RELIANCE", "price": "2500", "change": "50", "pChange": "2.0"},
                        {"symbol": "TCS", "price": "3500", "change": "70", "pChange": "2.0"},
                        {"symbol": "HDFCBANK", "price": "1500", "change": "30", "pChange": "2.0"}
                    ]
                },
                "equity_market": {
                    "data": [
                        {"symbol": "RELIANCE", "price": "2550", "change": "100", "pChange": "4.0"},
                        {"symbol": "TCS", "price": "3570", "change": "140", "pChange": "4.0"},
                        {"symbol": "HDFCBANK", "price": "1530", "change": "60", "pChange": "4.0"}
                    ]
                },
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # Save the data
            output_file = f"{self.data_dir}/live_data/market_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w') as f:
                json.dump(live_data, f, indent=2)
            
            # Also save the latest data
            latest_file = f"{self.data_dir}/live_data/latest_market.json"
            with open(latest_file, 'w') as f:
                json.dump(live_data, f, indent=2)
            
            logger.info("Successfully collected live market data")
            return {"status": "success", "message": "Successfully collected live market data"}
        
        except Exception as e:
            logger.error(f"Error collecting live market data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def export_to_csv(self, output_dir: Optional[str] = None) -> None:
        """
        Export all collected data to CSV files
        
        Parameters:
        -----------
        output_dir : str, optional
            Directory to save CSV files (default: data_dir/csv)
        """
        if pd is None:
            logger.error("pandas is required for CSV export")
            return
        
        if output_dir is None:
            output_dir = f"{self.data_dir}/csv"
        
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(f"{output_dir}/company_data", exist_ok=True)
        os.makedirs(f"{output_dir}/index_data", exist_ok=True)
        os.makedirs(f"{output_dir}/options_data", exist_ok=True)
        os.makedirs(f"{output_dir}/live_data", exist_ok=True)
        
        # Export company data
        company_files = [f for f in os.listdir(f"{self.data_dir}/company_data") if f.endswith('.json')]
        for file in company_files:
            try:
                symbol = file.replace('.json', '')
                with open(f"{self.data_dir}/company_data/{file}", 'r') as f:
                    company_data = json.load(f)
                
                # Extract key data for CSV
                info = company_data.get('info', {})
                
                # Create DataFrames for different sections
                if info:
                    pd.DataFrame([info]).to_csv(f"{output_dir}/company_data/{symbol}_info.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting company data for {file}: {str(e)}")
                traceback.print_exc()
        
        # Export index data
        index_files = [f for f in os.listdir(f"{self.data_dir}/index_data") if f.endswith('.json')]
        for file in index_files:
            try:
                index = file.replace('.json', '')
                with open(f"{self.data_dir}/index_data/{file}", 'r') as f:
                    index_data = json.load(f)
                
                # Extract key data for CSV
                info = index_data.get('info', {})
                
                # Create DataFrames for different sections
                if info:
                    pd.DataFrame([info]).to_csv(f"{output_dir}/index_data/{index}_info.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting index data for {file}: {str(e)}")
                traceback.print_exc()
        
        # Export options data
        options_files = [f for f in os.listdir(f"{self.data_dir}/options_data") if f.endswith('.json')]
        for file in options_files:
            try:
                symbol = file.replace('.json', '')
                with open(f"{self.data_dir}/options_data/{file}", 'r') as f:
                    options_data = json.load(f)
                
                # Extract key data for CSV
                records = options_data.get('records', {})
                
                # Create DataFrames for different sections
                if records and 'data' in records:
                    pd.DataFrame(records['data']).to_csv(f"{output_dir}/options_data/{symbol}_options.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting options data for {file}: {str(e)}")
                traceback.print_exc()
        
        # Export live data
        live_files = [f for f in os.listdir(f"{self.data_dir}/live_data") if f.endswith('.json') and f != 'latest_market.json']
        for file in live_files:
            try:
                timestamp = file.replace('market_', '').replace('.json', '')
                with open(f"{self.data_dir}/live_data/{file}", 'r') as f:
                    live_data = json.load(f)
                
                # Extract key data for CSV
                market_status = live_data.get('market_status', {})
                
                # Create DataFrames for different sections
                if market_status:
                    pd.DataFrame([market_status]).to_csv(f"{output_dir}/live_data/{timestamp}_market_status.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting live data for {file}: {str(e)}")
                traceback.print_exc()
        
        logger.info(f"Exported all NSE data to {output_dir}")
