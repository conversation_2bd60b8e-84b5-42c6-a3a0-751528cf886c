import requests
from bs4 import BeautifulSoup
import random
import time
import re
import json
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import socket
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Install required packages: pip install requests beautifulsoup4 pysocks

# Configuration
NSE_URLS = {
    "gainers": "https://www.nseindia.com/api/live-analysis-variations?index=gainers",
    "losers": "https://www.nseindia.com/api/live-analysis-variations?index=losers", 
    "most_active": "https://www.nseindia.com/api/live-analysis-variations?index=volume",
    "equity_info": "https://www.nseindia.com/api/equity-stockIndices?index=NIFTY%2050"
}

TEST_URL = "https://httpbin.org/ip"

# Enhanced headers to mimic real browser
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "en-US,en;q=0.9",
    "Accept-Encoding": "gzip, deflate, br",
    "Referer": "https://www.nseindia.com/market-data/live-equity-market",
    "Origin": "https://www.nseindia.com",
    "Connection": "keep-alive",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "Cache-Control": "no-cache",
    "Pragma": "no-cache"
}

def create_session_with_retry(proxy_type=None, proxy_host=None, proxy_port=None):
    """Create a robust requests session with retry strategy and proxy support"""
    session = requests.Session()
    
    # Configure retry strategy
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    # Configure proxy if provided
    if proxy_type and proxy_host and proxy_port:
        if proxy_type.lower() in ['http', 'https']:
            session.proxies = {
                "http": f"http://{proxy_host}:{proxy_port}",
                "https": f"http://{proxy_host}:{proxy_port}"
            }
        elif proxy_type.lower() in ['socks4', 'socks5']:
            session.proxies = {
                "http": f"socks5://{proxy_host}:{proxy_port}",
                "https": f"socks5://{proxy_host}:{proxy_port}"
            }
    
    # Set headers
    session.headers.update(HEADERS)
    
    return session

def create_session(proxy_type, proxy_host, proxy_port):
    """Create a requests session with proper proxy configuration"""
    return create_session_with_retry(proxy_type, proxy_host, proxy_port)

def test_proxy(proxy):
    """Test proxy with enhanced validation"""
    proxy_type, proxy_host, proxy_port = proxy
    
    try:
        start_time = time.time()
        session = create_session(proxy_type, proxy_host, proxy_port)
        
        # Test with a simple HTTP request first
        response = session.get("http://httpbin.org/ip", timeout=8, verify=False)
        
        if response.status_code == 200:
            latency = int((time.time() - start_time) * 1000)
            
            # Verify the proxy is actually working by checking IP
            try:
                response_data = response.json()
                proxy_ip = response_data.get('origin', '')
                if proxy_ip and proxy_ip != proxy_host:
                    print(f"✅ {proxy_type.upper()} {proxy_host}:{proxy_port} | {latency}ms | IP: {proxy_ip}")
                    return True, latency
                else:
                    print(f"⚠️ {proxy_type.upper()} {proxy_host}:{proxy_port} | IP not changed")
                    return False, 9999
            except:
                print(f"✅ {proxy_type.upper()} {proxy_host}:{proxy_port} | {latency}ms")
                return True, latency
                
    except Exception as e:
        pass
    
    print(f"❌ {proxy_type.upper()} {proxy_host}:{proxy_port} failed")
    return False, 9999

def get_free_proxy_list():
    """Get proxies from free-proxy-list.net"""
    try:
        response = requests.get(
            "https://free-proxy-list.net/",
            headers=HEADERS,
            timeout=15
        )
        soup = BeautifulSoup(response.text, 'html.parser')
        proxies = []
        
        table = soup.find('table', {'id': 'proxylisttable'})
        if table:
            for row in table.find('tbody').find_all('tr'):
                cells = row.find_all('td')
                if len(cells) >= 7:
                    ip = cells[0].text.strip()
                    port = cells[1].text.strip()
                    https = cells[6].text.strip().lower()
                    
                    if re.match(r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}', ip) and port.isdigit():
                        protocol = 'https' if https == 'yes' else 'http'
                        proxies.append((protocol, ip, int(port)))
        
        return proxies[:50]  # Limit to first 50
    except Exception as e:
        print(f"Free-proxy-list error: {str(e)[:100]}")
        return []

def get_proxy_list_download():
    """Get proxies from proxy-list.download"""
    try:
        response = requests.get(
            "https://www.proxy-list.download/api/v1/get?type=http",
            headers=HEADERS,
            timeout=15
        )
        
        proxies = []
        for line in response.text.strip().split('\n'):
            if ':' in line:
                try:
                    ip, port = line.strip().split(':')
                    if re.match(r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}', ip) and port.isdigit():
                        proxies.append(('http', ip, int(port)))
                except:
                    continue
        
        return proxies[:30]  # Limit to first 30
    except Exception as e:
        print(f"Proxy-list-download error: {str(e)[:100]}")
        return []

def get_all_proxies():
    """Combine proxies from all sources"""
    sources = [
        get_free_proxy_list,
        get_proxy_list_download
    ]
    
    all_proxies = []
    print("Scraping proxies from multiple sources...")
    
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(source) for source in sources]
        for future in as_completed(futures):
            try:
                proxies = future.result()
                if proxies:
                    print(f"Added {len(proxies)} proxies")
                    all_proxies.extend(proxies)
            except Exception as e:
                print(f"Source failed: {e}")
    
    # Add some manual high-quality proxies (you can update these)
    manual_proxies = [
        ('http', '***********', 80),
        ('http', '************', 8888),
        ('http', '***************', 80),
        ('http', '**************', 8080),
    ]
    all_proxies.extend(manual_proxies)
    
    # Remove duplicates
    unique_proxies = list(set(all_proxies))
    print(f"Total unique proxies: {len(unique_proxies)}")
    return unique_proxies

def get_nse_data(proxy, data_type="gainers"):
    """Fetch NSE data using proxy with enhanced session management"""
    proxy_type, proxy_host, proxy_port = proxy
    
    try:
        session = create_session(proxy_type, proxy_host, proxy_port)
        
        # Step 1: Visit homepage to get cookies and establish session
        print(f"  → Establishing session...")
        home_response = session.get(
            "https://www.nseindia.com/", 
            timeout=15,
            verify=False
        )
        
        if home_response.status_code != 200:
            print(f"  → Homepage failed: {home_response.status_code}")
            return None
            
        # Step 2: Visit market data page to get more cookies
        market_response = session.get(
            "https://www.nseindia.com/market-data/live-equity-market",
            timeout=15,
            verify=False
        )
        
        # Step 3: Make API request
        print(f"  → Fetching {data_type} data...")
        api_url = NSE_URLS.get(data_type, NSE_URLS["gainers"])
        
        response = session.get(
            api_url,
            timeout=20,
            verify=False
        )
        
        if response.status_code == 200:
            try:
                data = response.json()
                return data
            except json.JSONDecodeError:
                print(f"  → Invalid JSON response")
                return None
        else:
            print(f"  → API request failed: {response.status_code}")
            if response.status_code == 403:
                print(f"  → Access forbidden - proxy may be blocked")
            return None
            
    except requests.exceptions.Timeout:
        print(f"  → Timeout error")
    except requests.exceptions.ConnectionError:
        print(f"  → Connection error")
    except Exception as e:
        print(f"  → Request failed: {str(e)[:50]}...")
    
    return None

def try_without_proxy():
    """Try to access NSE directly without proxy"""
    print("Attempting direct access (no proxy)...")
    try:
        session = create_session_with_retry()
        
        # Visit homepage first
        session.get("https://www.nseindia.com/", timeout=15, verify=False)
        
        # Visit market data page
        session.get("https://www.nseindia.com/market-data/live-equity-market", timeout=15, verify=False)
        
        # Try API request
        response = session.get(NSE_URLS["gainers"], timeout=20, verify=False)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Direct access failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"Direct access error: {str(e)[:80]}")
        return None

def display_nse_data(data, data_type="gainers"):
    """Display NSE data in a formatted way"""
    if not data:
        print("No data to display")
        return
    
    print("\n" + "="*80)
    print(f"✅ Successfully fetched NSE {data_type.upper()} data!")
    print("="*80)
    
    # Handle different data structures
    if 'data' in data and isinstance(data['data'], list):
        items = data['data'][:10]  # Show top 10
        print(f"Timestamp: {data.get('time', 'N/A')}")
        print(f"\nTop 10 {data_type}:")
        print("-" * 60)
        
        for i, item in enumerate(items, 1):
            symbol = item.get('symbol', 'N/A')
            ltp = item.get('lastPrice', item.get('ltp', 'N/A'))
            change = item.get('netChange', item.get('change', 'N/A'))
            pct_change = item.get('pChange', item.get('pctChange', 'N/A'))
            
            print(f"{i:2d}. {symbol:15} | LTP: {ltp:>8} | Change: {change:>8} ({pct_change:>6}%)")
            
    elif isinstance(data, dict) and 'data' in data:
        print(f"Data structure: {list(data.keys())}")
        print(f"Records found: {len(data.get('data', []))}")
        
    else:
        print(f"Raw data preview: {str(data)[:200]}...")
    
    print("="*80)

def main():
    print("NSE Data Collector with Proxy Support")
    print("="*50)
    
    # Step 1: Try direct access first
    print("\n1. Trying direct access (no proxy)...")
    direct_data = try_without_proxy()
    
    if direct_data:
        display_nse_data(direct_data, "gainers")
        return
    
    print("Direct access failed. Proceeding with proxy method...\n")
    
    # Step 2: Get proxies from all sources
    print("2. Collecting proxies...")
    all_proxies = get_all_proxies()
    
    if not all_proxies:
        print("No proxies found from any source!")
        return
    
    # Step 3: Test proxies
    print(f"\n3. Testing {len(all_proxies)} proxies (this may take 2-3 minutes)...")
    valid_proxies = []
    
    with ThreadPoolExecutor(max_workers=15) as executor:
        futures = {executor.submit(test_proxy, proxy): proxy for proxy in all_proxies[:50]}
        for future in as_completed(futures):
            proxy = futures[future]
            try:
                success, latency = future.result()
                if success:
                    valid_proxies.append((latency, proxy))
            except Exception as e:
                pass
    
    # Sort by latency (fastest first)
    valid_proxies.sort(key=lambda x: x[0])
    fast_proxies = [proxy for _, proxy in valid_proxies]
    
    print(f"\nFound {len(fast_proxies)} working proxies")
    
    if not fast_proxies:
        print("No working proxies available!")
        print("\nTips to improve success rate:")
        print("1. Try running the script at different times")
        print("2. Consider using paid proxy services")
        print("3. Check if you have VPN access")
        return
    
    # Step 4: Try to fetch NSE data with fastest proxies first
    print(f"\n4. Attempting to fetch NSE data with {min(10, len(fast_proxies))} fastest proxies...")
    
    for i, proxy in enumerate(fast_proxies[:10]):
        print(f"\nAttempt {i+1}/10: Using {proxy[0].upper()} {proxy[1]}:{proxy[2]}")
        
        data = get_nse_data(proxy, "gainers")
        if data:
            display_nse_data(data, "gainers")
            
            # Try to get other data types as well
            print(f"\nTrying to fetch additional data...")
            for data_type in ["losers", "most_active"]:
                print(f"Fetching {data_type}...")
                additional_data = get_nse_data(proxy, data_type)
                if additional_data:
                    display_nse_data(additional_data, data_type)
                time.sleep(1)
            
            return
        
        # Wait before next attempt
        time.sleep(3)
    
    print("\n❌ Failed to fetch data after multiple attempts.")
    print("\nTroubleshooting suggestions:")
    print("1. NSE may have enhanced their blocking mechanisms")
    print("2. Try running the script at different times of day")
    print("3. Consider using residential proxies or VPN services")
    print("4. Check if NSE has updated their API endpoints")

if __name__ == "__main__":
    main()