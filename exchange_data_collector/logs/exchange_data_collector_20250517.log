2025-05-17 22:34:41,231 - __main__ - INFO - Collecting company data from NSE
2025-05-17 22:34:41,231 - nse.nse_collector - INFO - Collecting company data from NSE
2025-05-17 22:34:41,231 - nse.nse_collector - INFO - Data for RELIANCE already exists, skipping
2025-05-17 22:34:41,232 - nse.nse_collector - INFO - Completed collecting company data: 1 successful, 0 failed
2025-05-17 22:34:44,215 - __main__ - INFO - Collecting company data from BSE
2025-05-17 22:34:47,230 - __main__ - INFO - Collecting index data from NSE
2025-05-17 22:34:47,231 - nse.nse_collector - INFO - Collecting index data from NSE
2025-05-17 22:34:47,231 - nse.nse_collector - INFO - Data for NIFTY50 already exists, skipping
2025-05-17 22:34:47,231 - nse.nse_collector - INFO - Completed collecting index data: 1 successful, 0 failed
2025-05-17 22:34:50,228 - __main__ - INFO - Collecting index data from BSE
2025-05-17 22:34:53,216 - __main__ - INFO - Collecting options data from NSE
2025-05-17 22:34:53,216 - nse.nse_collector - INFO - Collecting options data from NSE
2025-05-17 22:34:53,216 - nse.nse_collector - INFO - Options data for RELIANCE already exists, skipping
2025-05-17 22:34:53,217 - nse.nse_collector - INFO - Completed collecting options data: 1 successful, 0 failed
2025-05-17 22:34:56,432 - __main__ - INFO - Collecting options data from BSE
2025-05-17 22:34:59,396 - __main__ - INFO - Collecting live data from NSE
2025-05-17 22:34:59,396 - nse.nse_collector - INFO - Collecting live market data from NSE
2025-05-17 22:34:59,587 - nse.nse_collector - ERROR - Error getting cookies from NSE: 403 Client Error: Forbidden for url: https://www.nseindia.com/
2025-05-17 22:34:59,785 - nse.nse_collector - ERROR - Error making API request to https://www.nseindia.com/api/marketStatus: 403 Client Error: Forbidden for url: https://www.nseindia.com/api/marketStatus
2025-05-17 22:35:03,194 - __main__ - INFO - Collecting live data from BSE
2025-05-17 22:35:06,623 - nse.nse_collector - INFO - Exported all NSE data to data/csv/nse
2025-05-17 22:35:06,623 - __main__ - INFO - Exported all data to data/csv
