2025-05-17 21:14:37,140 - __main__ - INFO - Running all tests
2025-05-17 21:14:37,141 - __main__ - INFO - Testing NSE company data collection
2025-05-17 21:14:37,143 - main - INFO - Collecting company data from NSE
2025-05-17 21:14:37,144 - nse.nse_collector - INFO - Collecting company data from NSE
2025-05-17 21:14:37,144 - nse.nse_collector - INFO - Data for RELIANCE already exists, skipping
2025-05-17 21:14:37,144 - nse.nse_collector - INFO - Completed collecting company data: 1 successful, 0 failed
2025-05-17 21:14:37,145 - __main__ - INFO - Testing BSE company data collection
2025-05-17 21:14:37,147 - main - INFO - Collecting company data from BSE
2025-05-17 21:14:37,148 - __main__ - INFO - Testing NSE index data collection
2025-05-17 21:14:37,150 - __main__ - ERROR - Error in NSE Index Data test: ExchangeDataCollector.collect_nse_data() got an unexpected keyword argument 'indices'
2025-05-17 21:14:37,150 - __main__ - INFO - Testing BSE index data collection
2025-05-17 21:14:37,152 - __main__ - ERROR - Error in BSE Index Data test: ExchangeDataCollector.collect_bse_data() got an unexpected keyword argument 'indices'
2025-05-17 21:14:37,152 - __main__ - INFO - Testing CSV export
2025-05-17 21:14:37,181 - nse.nse_collector - INFO - Exported all NSE data to data/csv/nse
2025-05-17 21:14:37,181 - main - INFO - Exported all data to data/csv
2025-05-17 21:18:23,615 - __main__ - INFO - Running all tests
2025-05-17 21:18:23,616 - __main__ - INFO - Testing NSE company data collection
2025-05-17 21:18:23,620 - main - INFO - Collecting company data from NSE
2025-05-17 21:18:23,620 - nse.nse_collector - INFO - Collecting company data from NSE
2025-05-17 21:18:23,620 - nse.nse_collector - INFO - Data for RELIANCE already exists, skipping
2025-05-17 21:18:23,621 - nse.nse_collector - INFO - Completed collecting company data: 1 successful, 0 failed
2025-05-17 21:18:23,622 - __main__ - INFO - Testing BSE company data collection
2025-05-17 21:18:23,624 - main - INFO - Collecting company data from BSE
2025-05-17 21:18:23,625 - __main__ - INFO - Testing NSE index data collection
2025-05-17 21:18:23,628 - __main__ - ERROR - Error in NSE Index Data test: ExchangeDataCollector.collect_nse_data() got an unexpected keyword argument 'indices'
2025-05-17 21:18:23,628 - __main__ - INFO - Testing BSE index data collection
2025-05-17 21:18:23,630 - __main__ - ERROR - Error in BSE Index Data test: ExchangeDataCollector.collect_bse_data() got an unexpected keyword argument 'indices'
2025-05-17 21:18:23,631 - __main__ - INFO - Testing CSV export
2025-05-17 21:18:23,664 - nse.nse_collector - INFO - Exported all NSE data to data/csv/nse
2025-05-17 21:18:23,664 - main - INFO - Exported all data to data/csv
2025-05-17 21:20:30,998 - __main__ - INFO - Running all tests
2025-05-17 21:20:30,999 - __main__ - INFO - Testing NSE company data collection
2025-05-17 21:20:31,001 - main - INFO - Collecting company data from NSE
2025-05-17 21:20:31,002 - nse.nse_collector - INFO - Collecting company data from NSE
2025-05-17 21:20:31,003 - nse.nse_collector - INFO - Data for RELIANCE already exists, skipping
2025-05-17 21:20:31,003 - nse.nse_collector - INFO - Completed collecting company data: 1 successful, 0 failed
2025-05-17 21:20:31,005 - __main__ - INFO - Testing BSE company data collection
2025-05-17 21:20:31,006 - main - INFO - Collecting company data from BSE
2025-05-17 21:20:31,008 - __main__ - INFO - Testing NSE index data collection
2025-05-17 21:20:31,009 - __main__ - ERROR - Error in NSE Index Data test: ExchangeDataCollector.collect_nse_data() got an unexpected keyword argument 'indices'
2025-05-17 21:20:31,010 - __main__ - INFO - Testing BSE index data collection
2025-05-17 21:20:31,012 - __main__ - ERROR - Error in BSE Index Data test: ExchangeDataCollector.collect_bse_data() got an unexpected keyword argument 'indices'
2025-05-17 21:20:31,014 - __main__ - INFO - Testing CSV export
2025-05-17 21:20:31,042 - nse.nse_collector - INFO - Exported all NSE data to data/csv/nse
2025-05-17 21:20:31,043 - main - INFO - Exported all data to data/csv
2025-05-17 22:32:55,860 - __main__ - INFO - Running all tests
2025-05-17 22:32:55,861 - __main__ - INFO - Testing NSE company data collection
2025-05-17 22:32:55,870 - main - INFO - Collecting company data from NSE
2025-05-17 22:32:55,870 - nse.nse_collector - INFO - Collecting company data from NSE
2025-05-17 22:32:55,871 - nse.nse_collector - INFO - Data for RELIANCE already exists, skipping
2025-05-17 22:32:55,871 - nse.nse_collector - INFO - Completed collecting company data: 1 successful, 0 failed
2025-05-17 22:32:55,872 - __main__ - INFO - Testing BSE company data collection
2025-05-17 22:32:55,874 - main - INFO - Collecting company data from BSE
2025-05-17 22:32:55,875 - __main__ - INFO - Testing NSE index data collection
2025-05-17 22:32:55,877 - __main__ - ERROR - Error in NSE Index Data test: ExchangeDataCollector.collect_nse_data() got an unexpected keyword argument 'indices'
2025-05-17 22:32:55,878 - __main__ - INFO - Testing BSE index data collection
2025-05-17 22:32:55,880 - __main__ - ERROR - Error in BSE Index Data test: ExchangeDataCollector.collect_bse_data() got an unexpected keyword argument 'indices'
2025-05-17 22:32:55,880 - __main__ - INFO - Testing CSV export
2025-05-17 22:32:55,914 - nse.nse_collector - INFO - Exported all NSE data to data/csv/nse
2025-05-17 22:32:55,915 - main - INFO - Exported all data to data/csv
2025-05-17 22:33:37,952 - __main__ - INFO - Running all tests
2025-05-17 22:33:37,953 - __main__ - INFO - Testing NSE company data collection
2025-05-17 22:33:37,961 - main - INFO - Collecting company data from NSE
2025-05-17 22:33:37,962 - nse.nse_collector - INFO - Collecting company data from NSE
2025-05-17 22:33:37,962 - nse.nse_collector - INFO - Data for RELIANCE already exists, skipping
2025-05-17 22:33:37,962 - nse.nse_collector - INFO - Completed collecting company data: 1 successful, 0 failed
2025-05-17 22:33:37,963 - __main__ - INFO - Testing BSE company data collection
2025-05-17 22:33:37,965 - main - INFO - Collecting company data from BSE
2025-05-17 22:33:37,966 - __main__ - INFO - Testing NSE index data collection
2025-05-17 22:33:37,968 - __main__ - ERROR - Error in NSE Index Data test: ExchangeDataCollector.collect_nse_data() got an unexpected keyword argument 'indices'
2025-05-17 22:33:37,969 - __main__ - INFO - Testing BSE index data collection
2025-05-17 22:33:37,970 - __main__ - ERROR - Error in BSE Index Data test: ExchangeDataCollector.collect_bse_data() got an unexpected keyword argument 'indices'
2025-05-17 22:33:37,971 - __main__ - INFO - Testing CSV export
2025-05-17 22:33:38,008 - nse.nse_collector - INFO - Exported all NSE data to data/csv/nse
2025-05-17 22:33:38,008 - main - INFO - Exported all data to data/csv
2025-05-17 22:34:26,395 - __main__ - INFO - Running all tests
2025-05-17 22:34:26,395 - __main__ - INFO - Testing NSE company data collection
2025-05-17 22:34:26,404 - main - INFO - Collecting company data from NSE
2025-05-17 22:34:26,404 - nse.nse_collector - INFO - Collecting company data from NSE
2025-05-17 22:34:26,405 - nse.nse_collector - INFO - Data for RELIANCE already exists, skipping
2025-05-17 22:34:26,405 - nse.nse_collector - INFO - Completed collecting company data: 1 successful, 0 failed
2025-05-17 22:34:26,406 - __main__ - INFO - Testing BSE company data collection
2025-05-17 22:34:26,408 - main - INFO - Collecting company data from BSE
2025-05-17 22:34:26,409 - __main__ - INFO - Testing NSE index data collection
2025-05-17 22:34:26,411 - main - INFO - Collecting index data from NSE
2025-05-17 22:34:26,411 - nse.nse_collector - INFO - Collecting index data from NSE
2025-05-17 22:34:26,411 - nse.nse_collector - INFO - Data for NIFTY50 already exists, skipping
2025-05-17 22:34:26,412 - nse.nse_collector - INFO - Completed collecting index data: 1 successful, 0 failed
2025-05-17 22:34:26,412 - __main__ - INFO - Testing BSE index data collection
2025-05-17 22:34:26,414 - main - INFO - Collecting index data from BSE
2025-05-17 22:34:26,415 - __main__ - INFO - Testing CSV export
2025-05-17 22:34:26,449 - nse.nse_collector - INFO - Exported all NSE data to data/csv/nse
2025-05-17 22:34:26,449 - main - INFO - Exported all data to data/csv
