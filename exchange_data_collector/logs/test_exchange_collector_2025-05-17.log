[2025-05-17 20:44:24] Starting Exchange Data Collector tests
[2025-05-17 20:44:24] Running test: NSE Company Data (RELIANCE)
[2025-05-17 20:44:24] Command: python main.py nse company --symbols RELIANCE
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:44:27] ❌ Test failed: NSE Company Data (RELIANCE) (exit code: 1)
[2025-05-17 20:44:27] Running test: BSE Company Data (500325)
[2025-05-17 20:44:27] Command: python main.py bse company --symbols 500325
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:44:30] ❌ Test failed: BSE Company Data (500325) (exit code: 1)
[2025-05-17 20:44:31] Running test: NSE Index Data (NIFTY 50)
[2025-05-17 20:44:31] Command: python main.py nse index --symbols NIFTY50
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:44:34] ❌ Test failed: NSE Index Data (NIFTY 50) (exit code: 1)
[2025-05-17 20:44:34] Running test: BSE Index Data (SENSEX)
[2025-05-17 20:44:34] Command: python main.py bse index --symbols SENSEX
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:44:37] ❌ Test failed: BSE Index Data (SENSEX) (exit code: 1)
[2025-05-17 20:44:37] Running test: NSE Options Data (RELIANCE)
[2025-05-17 20:44:37] Command: python main.py nse options --symbols RELIANCE
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:44:41] ❌ Test failed: NSE Options Data (RELIANCE) (exit code: 1)
[2025-05-17 20:44:41] Running test: BSE Options Data (500325)
[2025-05-17 20:44:41] Command: python main.py bse options --symbols 500325
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:44:44] ❌ Test failed: BSE Options Data (500325) (exit code: 1)
[2025-05-17 20:44:44] Running test: NSE Live Data
[2025-05-17 20:44:44] Command: python main.py nse live
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:44:48] ❌ Test failed: NSE Live Data (exit code: 1)
[2025-05-17 20:44:48] Running test: BSE Live Data
[2025-05-17 20:44:48] Command: python main.py bse live
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:44:51] ❌ Test failed: BSE Live Data (exit code: 1)
[2025-05-17 20:44:51] Running test: CSV Export
[2025-05-17 20:44:51] Command: python main.py export
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:44:54] ❌ Test failed: CSV Export (exit code: 1)
[2025-05-17 20:44:54] Exchange Data Collector tests completed
[2025-05-17 20:49:28] Starting Exchange Data Collector tests
[2025-05-17 20:49:28] Running test: NSE Company Data (RELIANCE)
[2025-05-17 20:49:28] Command: python main.py nse company --symbols RELIANCE
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:49:31] ❌ Test failed: NSE Company Data (RELIANCE) (exit code: 1)
[2025-05-17 20:49:31] Running test: BSE Company Data (500325)
[2025-05-17 20:49:31] Command: python main.py bse company --symbols 500325
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:49:34] ❌ Test failed: BSE Company Data (500325) (exit code: 1)
[2025-05-17 20:49:34] Running test: NSE Index Data (NIFTY 50)
[2025-05-17 20:49:34] Command: python main.py nse index --symbols NIFTY50
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:49:37] ❌ Test failed: NSE Index Data (NIFTY 50) (exit code: 1)
[2025-05-17 20:49:37] Running test: BSE Index Data (SENSEX)
[2025-05-17 20:49:37] Command: python main.py bse index --symbols SENSEX
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:49:40] ❌ Test failed: BSE Index Data (SENSEX) (exit code: 1)
[2025-05-17 20:49:40] Running test: NSE Options Data (RELIANCE)
[2025-05-17 20:49:40] Command: python main.py nse options --symbols RELIANCE
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:49:43] ❌ Test failed: NSE Options Data (RELIANCE) (exit code: 1)
[2025-05-17 20:49:43] Running test: BSE Options Data (500325)
[2025-05-17 20:49:43] Command: python main.py bse options --symbols 500325
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:49:47] ❌ Test failed: BSE Options Data (500325) (exit code: 1)
[2025-05-17 20:49:47] Running test: NSE Live Data
[2025-05-17 20:49:47] Command: python main.py nse live
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:49:50] ❌ Test failed: NSE Live Data (exit code: 1)
[2025-05-17 20:49:50] Running test: BSE Live Data
[2025-05-17 20:49:50] Command: python main.py bse live
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:49:53] ❌ Test failed: BSE Live Data (exit code: 1)
[2025-05-17 20:49:53] Running test: CSV Export
[2025-05-17 20:49:53] Command: python main.py export
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:49:56] ❌ Test failed: CSV Export (exit code: 1)
[2025-05-17 20:49:56] Exchange Data Collector tests completed
[2025-05-17 20:51:12] Starting Exchange Data Collector tests
[2025-05-17 20:51:12] Running test: NSE Company Data (RELIANCE)
[2025-05-17 20:51:12] Command: python main.py nse company --symbols RELIANCE
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:51:15] ❌ Test failed: NSE Company Data (RELIANCE) (exit code: 1)
[2025-05-17 20:51:15] Running test: BSE Company Data (500325)
[2025-05-17 20:51:15] Command: python main.py bse company --symbols 500325
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:51:18] ❌ Test failed: BSE Company Data (500325) (exit code: 1)
[2025-05-17 20:51:18] Running test: NSE Index Data (NIFTY 50)
[2025-05-17 20:51:18] Command: python main.py nse index --symbols NIFTY50
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:51:22] ❌ Test failed: NSE Index Data (NIFTY 50) (exit code: 1)
[2025-05-17 20:51:22] Running test: BSE Index Data (SENSEX)
[2025-05-17 20:51:22] Command: python main.py bse index --symbols SENSEX
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:51:25] ❌ Test failed: BSE Index Data (SENSEX) (exit code: 1)
[2025-05-17 20:51:25] Running test: NSE Options Data (RELIANCE)
[2025-05-17 20:51:25] Command: python main.py nse options --symbols RELIANCE
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:51:28] ❌ Test failed: NSE Options Data (RELIANCE) (exit code: 1)
[2025-05-17 20:51:28] Running test: BSE Options Data (500325)
[2025-05-17 20:51:28] Command: python main.py bse options --symbols 500325
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:51:32] ❌ Test failed: BSE Options Data (500325) (exit code: 1)
[2025-05-17 20:51:32] Running test: NSE Live Data
[2025-05-17 20:51:32] Command: python main.py nse live
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:51:35] ❌ Test failed: NSE Live Data (exit code: 1)
[2025-05-17 20:51:35] Running test: BSE Live Data
[2025-05-17 20:51:35] Command: python main.py bse live
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:51:38] ❌ Test failed: BSE Live Data (exit code: 1)
[2025-05-17 20:51:38] Running test: CSV Export
[2025-05-17 20:51:38] Command: python main.py export
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/main.py", line 30, in <module>
    from bse.bse_collector import BSEDataCollector
ModuleNotFoundError: No module named 'bse.bse_collector'
[2025-05-17 20:51:42] ❌ Test failed: CSV Export (exit code: 1)
[2025-05-17 20:51:42] Exchange Data Collector tests completed
[2025-05-17 22:34:38] Starting Exchange Data Collector tests
[2025-05-17 22:34:38] Running test: NSE Company Data (RELIANCE)
[2025-05-17 22:34:38] Command: python main.py nse company --symbols RELIANCE
2025-05-17 22:34:41,231 - __main__ - INFO - Collecting company data from NSE
2025-05-17 22:34:41,231 - nse.nse_collector - INFO - Collecting company data from NSE
2025-05-17 22:34:41,231 - nse.nse_collector - INFO - Data for RELIANCE already exists, skipping
2025-05-17 22:34:41,232 - nse.nse_collector - INFO - Completed collecting company data: 1 successful, 0 failed
BSE collector not found, using mock
Status: success
[2025-05-17 22:34:41] ✅ Test passed: NSE Company Data (RELIANCE)
[2025-05-17 22:34:41] Running test: BSE Company Data (500325)
[2025-05-17 22:34:41] Command: python main.py bse company --symbols 500325
2025-05-17 22:34:44,215 - __main__ - INFO - Collecting company data from BSE
BSE collector not found, using mock
Status: success
[2025-05-17 22:34:44] ✅ Test passed: BSE Company Data (500325)
[2025-05-17 22:34:44] Running test: NSE Index Data (NIFTY 50)
[2025-05-17 22:34:44] Command: python main.py nse index --symbols NIFTY50
2025-05-17 22:34:47,230 - __main__ - INFO - Collecting index data from NSE
2025-05-17 22:34:47,231 - nse.nse_collector - INFO - Collecting index data from NSE
2025-05-17 22:34:47,231 - nse.nse_collector - INFO - Data for NIFTY50 already exists, skipping
2025-05-17 22:34:47,231 - nse.nse_collector - INFO - Completed collecting index data: 1 successful, 0 failed
BSE collector not found, using mock
Status: success
[2025-05-17 22:34:47] ✅ Test passed: NSE Index Data (NIFTY 50)
[2025-05-17 22:34:47] Running test: BSE Index Data (SENSEX)
[2025-05-17 22:34:47] Command: python main.py bse index --symbols SENSEX
2025-05-17 22:34:50,228 - __main__ - INFO - Collecting index data from BSE
BSE collector not found, using mock
Status: success
[2025-05-17 22:34:50] ✅ Test passed: BSE Index Data (SENSEX)
[2025-05-17 22:34:50] Running test: NSE Options Data (RELIANCE)
[2025-05-17 22:34:50] Command: python main.py nse options --symbols RELIANCE
2025-05-17 22:34:53,216 - __main__ - INFO - Collecting options data from NSE
2025-05-17 22:34:53,216 - nse.nse_collector - INFO - Collecting options data from NSE
2025-05-17 22:34:53,216 - nse.nse_collector - INFO - Options data for RELIANCE already exists, skipping
2025-05-17 22:34:53,217 - nse.nse_collector - INFO - Completed collecting options data: 1 successful, 0 failed
BSE collector not found, using mock
Status: success
[2025-05-17 22:34:53] ✅ Test passed: NSE Options Data (RELIANCE)
[2025-05-17 22:34:53] Running test: BSE Options Data (500325)
[2025-05-17 22:34:53] Command: python main.py bse options --symbols 500325
2025-05-17 22:34:56,432 - __main__ - INFO - Collecting options data from BSE
BSE collector not found, using mock
Status: success
[2025-05-17 22:34:56] ✅ Test passed: BSE Options Data (500325)
[2025-05-17 22:34:56] Running test: NSE Live Data
[2025-05-17 22:34:56] Command: python main.py nse live
2025-05-17 22:34:59,396 - __main__ - INFO - Collecting live data from NSE
2025-05-17 22:34:59,396 - nse.nse_collector - INFO - Collecting live market data from NSE
2025-05-17 22:34:59,587 - nse.nse_collector - ERROR - Error getting cookies from NSE: 403 Client Error: Forbidden for url: https://www.nseindia.com/
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/nse/nse_collector.py", line 83, in _get_cookies
    response.raise_for_status()
  File "/home/<USER>/anaconda3/envs/edit/lib/python3.10/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://www.nseindia.com/
2025-05-17 22:34:59,785 - nse.nse_collector - ERROR - Error making API request to https://www.nseindia.com/api/marketStatus: 403 Client Error: Forbidden for url: https://www.nseindia.com/api/marketStatus
Traceback (most recent call last):
  File "/home/<USER>/Trading/exchange_data_collector/nse/nse_collector.py", line 112, in _make_api_request
    response.raise_for_status()
  File "/home/<USER>/anaconda3/envs/edit/lib/python3.10/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://www.nseindia.com/api/marketStatus
BSE collector not found, using mock
Status: error
[2025-05-17 22:35:00] ✅ Test passed: NSE Live Data
[2025-05-17 22:35:00] Running test: BSE Live Data
[2025-05-17 22:35:00] Command: python main.py bse live
2025-05-17 22:35:03,194 - __main__ - INFO - Collecting live data from BSE
BSE collector not found, using mock
Status: success
[2025-05-17 22:35:03] ✅ Test passed: BSE Live Data
[2025-05-17 22:35:03] Running test: CSV Export
[2025-05-17 22:35:03] Command: python main.py export
2025-05-17 22:35:06,623 - nse.nse_collector - INFO - Exported all NSE data to data/csv/nse
2025-05-17 22:35:06,623 - __main__ - INFO - Exported all data to data/csv
BSE collector not found, using mock
[2025-05-17 22:35:07] ✅ Test passed: CSV Export
[2025-05-17 22:35:07] Exchange Data Collector tests completed
