#!/usr/bin/env python3
"""
Enhanced NSE Data Fetcher with Multiple Strategies
This script provides multiple approaches to fetch NSE data from outside India
"""

import requests
import json
import time
import random
from datetime import datetime
import pandas as pd
from typing import Dict, List, Optional, Any

class NSEDataFetcher:
    """Enhanced NSE data fetcher with multiple fallback strategies"""
    
    def __init__(self):
        self.base_url = "https://www.nseindia.com"
        self.session = None
        self.cookies_established = False
        
        # Enhanced headers that mimic real browser behavior
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache"
        }
        
        # API endpoints
        self.endpoints = {
            "gainers": "/api/live-analysis-variations?index=gainers",
            "losers": "/api/live-analysis-variations?index=losers",
            "most_active": "/api/live-analysis-variations?index=volume",
            "nifty50": "/api/equity-stockIndices?index=NIFTY%2050",
            "nifty_bank": "/api/equity-stockIndices?index=NIFTY%20BANK",
            "market_status": "/api/marketStatus",
            "holiday_list": "/api/holiday-master?type=trading",
            "equity_meta": "/api/equity-meta-info",
        }
    
    def create_session(self, proxy_config: Optional[Dict] = None) -> requests.Session:
        """Create a new session with optional proxy configuration"""
        session = requests.Session()
        session.headers.update(self.headers)
        
        if proxy_config:
            if proxy_config.get('type') in ['http', 'https']:
                session.proxies = {
                    "http": f"http://{proxy_config['host']}:{proxy_config['port']}",
                    "https": f"http://{proxy_config['host']}:{proxy_config['port']}"
                }
            elif proxy_config.get('type') in ['socks4', 'socks5']:
                session.proxies = {
                    "http": f"socks5://{proxy_config['host']}:{proxy_config['port']}",
                    "https": f"socks5://{proxy_config['host']}:{proxy_config['port']}"
                }
        
        return session
    
    def establish_session(self, proxy_config: Optional[Dict] = None) -> bool:
        """Establish session with NSE by visiting required pages"""
        try:
            self.session = self.create_session(proxy_config)
            
            # Step 1: Visit homepage
            print("Establishing session with NSE...")
            response = self.session.get(f"{self.base_url}/", timeout=15, verify=False)
            
            if response.status_code != 200:
                print(f"Failed to access homepage: {response.status_code}")
                return False
            
            # Step 2: Visit market data page to establish proper cookies
            response = self.session.get(
                f"{self.base_url}/market-data/live-equity-market", 
                timeout=15, 
                verify=False
            )
            
            # Update headers with referer
            self.session.headers.update({
                "Referer": f"{self.base_url}/market-data/live-equity-market"
            })
            
            self.cookies_established = True
            print("Session established successfully!")
            return True
            
        except Exception as e:
            print(f"Failed to establish session: {e}")
            return False
    
    def fetch_data(self, endpoint_key: str, retries: int = 3) -> Optional[Dict]:
        """Fetch data from NSE API endpoint"""
        if not self.cookies_established:
            print("Session not established. Call establish_session() first.")
            return None
        
        endpoint = self.endpoints.get(endpoint_key)
        if not endpoint:
            print(f"Unknown endpoint: {endpoint_key}")
            return None
        
        url = f"{self.base_url}{endpoint}"
        
        for attempt in range(retries):
            try:
                print(f"Fetching {endpoint_key} data (attempt {attempt + 1}/{retries})...")
                
                response = self.session.get(url, timeout=20, verify=False)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"✅ Successfully fetched {endpoint_key} data")
                        return data
                    except json.JSONDecodeError:
                        print(f"Invalid JSON response for {endpoint_key}")
                        return None
                        
                elif response.status_code == 403:
                    print(f"Access forbidden for {endpoint_key} - may need different proxy")
                    return None
                    
                else:
                    print(f"HTTP {response.status_code} for {endpoint_key}")
                    
            except requests.exceptions.Timeout:
                print(f"Timeout for {endpoint_key} (attempt {attempt + 1})")
                
            except Exception as e:
                print(f"Error fetching {endpoint_key}: {e}")
            
            if attempt < retries - 1:
                time.sleep(2)
        
        return None
    
    def get_market_data(self, data_types: List[str] = None) -> Dict[str, Any]:
        """Get multiple types of market data"""
        if data_types is None:
            data_types = ["gainers", "losers", "most_active", "nifty50"]
        
        results = {}
        
        for data_type in data_types:
            data = self.fetch_data(data_type)
            if data:
                results[data_type] = data
                time.sleep(1)  # Be respectful to the server
            else:
                results[data_type] = None
        
        return results
    
    def format_market_data(self, data: Dict, data_type: str) -> Optional[pd.DataFrame]:
        """Format market data into pandas DataFrame"""
        try:
            if data and 'data' in data and isinstance(data['data'], list):
                df = pd.DataFrame(data['data'])
                df['data_type'] = data_type
                df['timestamp'] = datetime.now()
                return df
            else:
                print(f"No valid data structure for {data_type}")
                return None
        except Exception as e:
            print(f"Error formatting {data_type} data: {e}")
            return None
    
    def save_data(self, data: Dict, filename: str = None):
        """Save data to JSON file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"nse_data_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            print(f"Data saved to {filename}")
        except Exception as e:
            print(f"Error saving data: {e}")

def test_direct_access():
    """Test direct access to NSE without proxy"""
    print("Testing direct access to NSE...")
    fetcher = NSEDataFetcher()
    
    if fetcher.establish_session():
        data = fetcher.get_market_data(["gainers", "market_status"])
        
        if any(data.values()):
            print("✅ Direct access successful!")
            
            # Display sample data
            for data_type, result in data.items():
                if result:
                    print(f"\n{data_type.upper()} Data:")
                    if 'data' in result and isinstance(result['data'], list):
                        for i, item in enumerate(result['data'][:5], 1):
                            symbol = item.get('symbol', 'N/A')
                            print(f"  {i}. {symbol}")
            
            return True
        else:
            print("❌ Direct access failed - all requests blocked")
            return False
    else:
        print("❌ Could not establish session")
        return False

def test_with_proxy(proxy_config: Dict):
    """Test NSE access with specific proxy"""
    print(f"Testing with proxy: {proxy_config['host']}:{proxy_config['port']}")
    fetcher = NSEDataFetcher()
    
    if fetcher.establish_session(proxy_config):
        data = fetcher.fetch_data("gainers")
        
        if data:
            print("✅ Proxy access successful!")
            return True
        else:
            print("❌ Proxy access failed")
            return False
    else:
        print("❌ Could not establish session with proxy")
        return False

def main():
    """Main function to demonstrate usage"""
    print("NSE Data Fetcher - Enhanced Version")
    print("=" * 50)
    
    # Try direct access first
    if test_direct_access():
        return
    
    print("\nDirect access failed. You'll need to use the proxy version.")
    print("Run trial_proxy.py for proxy-based access.")
    
    # Example of how to use with proxy
    example_proxy = {
        'type': 'http',
        'host': '***************',
        'port': 80
    }
    
    print(f"\nExample proxy test:")
    test_with_proxy(example_proxy)

if __name__ == "__main__":
    main()