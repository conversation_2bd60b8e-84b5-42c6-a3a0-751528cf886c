#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Exchange Data Collector - Main Module

This module serves as the main entry point for the Exchange Data Collector,
which collects data from NSE and BSE including company details, index data,
options data, and live stock data.
"""

import os
import sys
import time
import json
import logging
import argparse
from datetime import datetime
from typing import List, Dict, Optional, Set, Union, Any
import traceback

# Try to import pandas, but don't fail if it's not available
try:
    import pandas as pd
except ImportError:
    pd = None

# Import the NSE and BSE modules
try:
    from nse.nse_collector import NSEDataCollector
except ImportError:
    print("NSE collector not found, using mock")
    # Create a mock NSE collector
    class NSEDataCollector:
        def __init__(self, data_dir="data/nse"):
            self.data_dir = data_dir
            os.makedirs(f"{data_dir}/company_data", exist_ok=True)
            os.makedirs(f"{data_dir}/index_data", exist_ok=True)
            os.makedirs(f"{data_dir}/options_data", exist_ok=True)
            os.makedirs(f"{data_dir}/live_data", exist_ok=True)
            os.makedirs(f"{data_dir}/raw", exist_ok=True)

        def collect_company_data(self, symbols=None, force_refresh=False):
            return {"status": "success", "message": "Mock NSE collector used"}

        def collect_index_data(self, indices=None, force_refresh=False):
            return {"status": "success", "message": "Mock NSE collector used"}

        def collect_options_data(self, symbols=None, force_refresh=False):
            return {"status": "success", "message": "Mock NSE collector used"}

        def collect_live_data(self, symbols=None, force_refresh=False):
            return {"status": "success", "message": "Mock NSE collector used"}

        def export_to_csv(self, output_dir=None):
            return

try:
    from bse.bse_collector import BSEDataCollector
except ImportError:
    print("BSE collector not found, using mock")
    # Create a mock BSE collector
    class BSEDataCollector:
        def __init__(self, data_dir="data/bse"):
            self.data_dir = data_dir
            os.makedirs(f"{data_dir}/company_data", exist_ok=True)
            os.makedirs(f"{data_dir}/index_data", exist_ok=True)
            os.makedirs(f"{data_dir}/options_data", exist_ok=True)
            os.makedirs(f"{data_dir}/live_data", exist_ok=True)
            os.makedirs(f"{data_dir}/raw", exist_ok=True)

        def collect_company_data(self, symbols=None, force_refresh=False):
            return {"status": "success", "message": "Mock BSE collector used"}

        def collect_index_data(self, indices=None, force_refresh=False):
            return {"status": "success", "message": "Mock BSE collector used"}

        def collect_options_data(self, symbols=None, force_refresh=False):
            return {"status": "success", "message": "Mock BSE collector used"}

        def collect_live_data(self, symbols=None, force_refresh=False):
            return {"status": "success", "message": "Mock BSE collector used"}

        def export_to_csv(self, output_dir=None):
            return

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"logs/exchange_data_collector_{datetime.now().strftime('%Y%m%d')}.log")
    ]
)

logger = logging.getLogger(__name__)

class ExchangeDataCollector:
    """
    Main class for collecting data from NSE and BSE
    """

    def __init__(self, data_dir: str = "data"):
        """
        Initialize the Exchange Data Collector

        Parameters:
        -----------
        data_dir : str
            Directory to store the collected data
        """
        self.data_dir = data_dir

        # Create data directories if they don't exist
        os.makedirs(f"{data_dir}/nse/company_data", exist_ok=True)
        os.makedirs(f"{data_dir}/nse/index_data", exist_ok=True)
        os.makedirs(f"{data_dir}/nse/options_data", exist_ok=True)
        os.makedirs(f"{data_dir}/nse/live_data", exist_ok=True)

        os.makedirs(f"{data_dir}/bse/company_data", exist_ok=True)
        os.makedirs(f"{data_dir}/bse/index_data", exist_ok=True)
        os.makedirs(f"{data_dir}/bse/options_data", exist_ok=True)
        os.makedirs(f"{data_dir}/bse/live_data", exist_ok=True)

        os.makedirs(f"{data_dir}/processed", exist_ok=True)
        os.makedirs(f"{data_dir}/csv", exist_ok=True)

        # Initialize the NSE and BSE collectors
        self.nse_collector = NSEDataCollector(data_dir=f"{data_dir}/nse")
        self.bse_collector = BSEDataCollector(data_dir=f"{data_dir}/bse")

    def collect_nse_data(self, data_type: str, symbols: Optional[List[str]] = None,
                        force_refresh: bool = False) -> Dict:
        """
        Collect data from NSE

        Parameters:
        -----------
        data_type : str
            Type of data to collect (company, index, options, live)
        symbols : list, optional
            List of symbols to collect data for (default: all symbols)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists

        Returns:
        --------
        Dictionary with status information
        """
        logger.info(f"Collecting {data_type} data from NSE")

        if data_type == "company":
            return self.nse_collector.collect_company_data(symbols, force_refresh)
        elif data_type == "index":
            return self.nse_collector.collect_index_data(symbols, force_refresh)
        elif data_type == "options":
            return self.nse_collector.collect_options_data(symbols, force_refresh)
        elif data_type == "live":
            return self.nse_collector.collect_live_data(symbols, force_refresh)
        else:
            logger.error(f"Invalid data type: {data_type}")
            return {"status": "error", "message": f"Invalid data type: {data_type}"}

    def collect_bse_data(self, data_type: str, symbols: Optional[List[str]] = None,
                        force_refresh: bool = False) -> Dict:
        """
        Collect data from BSE

        Parameters:
        -----------
        data_type : str
            Type of data to collect (company, index, options, live)
        symbols : list, optional
            List of symbols to collect data for (default: all symbols)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists

        Returns:
        --------
        Dictionary with status information
        """
        logger.info(f"Collecting {data_type} data from BSE")

        if data_type == "company":
            return self.bse_collector.collect_company_data(symbols, force_refresh)
        elif data_type == "index":
            return self.bse_collector.collect_index_data(symbols, force_refresh)
        elif data_type == "options":
            return self.bse_collector.collect_options_data(symbols, force_refresh)
        elif data_type == "live":
            return self.bse_collector.collect_live_data(symbols, force_refresh)
        else:
            logger.error(f"Invalid data type: {data_type}")
            return {"status": "error", "message": f"Invalid data type: {data_type}"}

    def collect_all_data(self, exchange: str = "all", data_type: str = "all",
                        symbols: Optional[List[str]] = None, force_refresh: bool = False) -> Dict:
        """
        Collect all data from NSE and BSE

        Parameters:
        -----------
        exchange : str
            Exchange to collect data from (nse, bse, all)
        data_type : str
            Type of data to collect (company, index, options, live, all)
        symbols : list, optional
            List of symbols to collect data for (default: all symbols)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists

        Returns:
        --------
        Dictionary with status information
        """
        logger.info(f"Collecting all data from {exchange}")

        results = {}

        if exchange in ["nse", "all"]:
            if data_type in ["company", "all"]:
                results["nse_company"] = self.collect_nse_data("company", symbols, force_refresh)
            if data_type in ["index", "all"]:
                results["nse_index"] = self.collect_nse_data("index", symbols, force_refresh)
            if data_type in ["options", "all"]:
                results["nse_options"] = self.collect_nse_data("options", symbols, force_refresh)
            if data_type in ["live", "all"]:
                results["nse_live"] = self.collect_nse_data("live", symbols, force_refresh)

        if exchange in ["bse", "all"]:
            if data_type in ["company", "all"]:
                results["bse_company"] = self.collect_bse_data("company", symbols, force_refresh)
            if data_type in ["index", "all"]:
                results["bse_index"] = self.collect_bse_data("index", symbols, force_refresh)
            if data_type in ["options", "all"]:
                results["bse_options"] = self.collect_bse_data("options", symbols, force_refresh)
            if data_type in ["live", "all"]:
                results["bse_live"] = self.collect_bse_data("live", symbols, force_refresh)

        return results

    def export_to_csv(self, output_dir: Optional[str] = None) -> None:
        """
        Export all collected data to CSV files

        Parameters:
        -----------
        output_dir : str, optional
            Directory to save CSV files (default: data_dir/csv)
        """
        if pd is None:
            logger.error("pandas is required for CSV export")
            return

        if output_dir is None:
            output_dir = f"{self.data_dir}/csv"

        os.makedirs(output_dir, exist_ok=True)

        # Export NSE data
        self.nse_collector.export_to_csv(f"{output_dir}/nse")

        # Export BSE data
        self.bse_collector.export_to_csv(f"{output_dir}/bse")

        logger.info(f"Exported all data to {output_dir}")


def main():
    """
    Main function to run the Exchange Data Collector from the command line
    """
    parser = argparse.ArgumentParser(description="Exchange Data Collector")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # Parser for the 'nse' command
    nse_parser = subparsers.add_parser('nse', help='Collect data from NSE')
    nse_parser.add_argument('data_type', choices=['company', 'index', 'options', 'live', 'all'],
                          help='Type of data to collect')
    nse_parser.add_argument('--symbols', nargs='+', help='Symbols to collect data for')
    nse_parser.add_argument('--force-refresh', action='store_true', help='Force refresh of all data')

    # Parser for the 'bse' command
    bse_parser = subparsers.add_parser('bse', help='Collect data from BSE')
    bse_parser.add_argument('data_type', choices=['company', 'index', 'options', 'live', 'all'],
                          help='Type of data to collect')
    bse_parser.add_argument('--symbols', nargs='+', help='Symbols to collect data for')
    bse_parser.add_argument('--force-refresh', action='store_true', help='Force refresh of all data')

    # Parser for the 'all' command
    all_parser = subparsers.add_parser('all', help='Collect data from all exchanges')
    all_parser.add_argument('--exchange', choices=['nse', 'bse', 'all'], default='all',
                          help='Exchange to collect data from')
    all_parser.add_argument('--data-type', choices=['company', 'index', 'options', 'live', 'all'],
                          default='all', help='Type of data to collect')
    all_parser.add_argument('--symbols', nargs='+', help='Symbols to collect data for')
    all_parser.add_argument('--force-refresh', action='store_true', help='Force refresh of all data')

    # Parser for the 'export' command
    export_parser = subparsers.add_parser('export', help='Export collected data to CSV files')
    export_parser.add_argument('--output-dir', type=str, help='Directory to save CSV files')

    # Parse arguments
    args = parser.parse_args()

    # Create the collector
    collector = ExchangeDataCollector()

    # Execute the appropriate command
    if args.command == 'nse':
        result = collector.collect_nse_data(args.data_type, args.symbols, args.force_refresh)
        print(f"Status: {result.get('status', 'unknown')}")

    elif args.command == 'bse':
        result = collector.collect_bse_data(args.data_type, args.symbols, args.force_refresh)
        print(f"Status: {result.get('status', 'unknown')}")

    elif args.command == 'all':
        result = collector.collect_all_data(args.exchange, args.data_type, args.symbols, args.force_refresh)
        print(f"Status: {json.dumps(result, indent=2)}")

    elif args.command == 'export':
        collector.export_to_csv(args.output_dir)

    else:
        parser.print_help()


if __name__ == "__main__":
    main()
