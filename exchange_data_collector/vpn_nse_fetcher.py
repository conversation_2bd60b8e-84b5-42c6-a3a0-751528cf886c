#!/usr/bin/env python3
"""
NSE Data Fetcher with VPN Detection and Alternative Methods
"""

import requests
import json
import time
from datetime import datetime
import subprocess
import platform
import socket
from typing import Dict, Optional, List

class VPNNSEFetcher:
    """NSE data fetcher with VPN detection and alternative methods"""
    
    def __init__(self):
        self.session = None
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Referer": "https://www.nseindia.com/market-data/live-equity-market",
            "Origin": "https://www.nseindia.com",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin"
        }
        
        self.nse_endpoints = {
            "gainers": "https://www.nseindia.com/api/live-analysis-variations?index=gainers",
            "losers": "https://www.nseindia.com/api/live-analysis-variations?index=losers",
            "most_active": "https://www.nseindia.com/api/live-analysis-variations?index=volume",
            "nifty50": "https://www.nseindia.com/api/equity-stockIndices?index=NIFTY%2050",
            "market_status": "https://www.nseindia.com/api/marketStatus"
        }
    
    def check_current_ip(self) -> Dict:
        """Check current IP address and location"""
        try:
            print("Checking current IP address and location...")
            
            # Try multiple IP checking services
            ip_services = [
                "http://ipinfo.io/json",
                "http://ip-api.com/json",
                "https://httpbin.org/ip"
            ]
            
            for service in ip_services:
                try:
                    response = requests.get(service, timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        
                        # Normalize response format
                        ip_info = {
                            "ip": data.get("ip", data.get("origin", "unknown")),
                            "country": data.get("country", data.get("countryCode", "unknown")),
                            "region": data.get("region", data.get("regionName", "unknown")),
                            "city": data.get("city", "unknown"),
                            "org": data.get("org", data.get("isp", "unknown"))
                        }
                        
                        print(f"Current IP: {ip_info['ip']}")
                        print(f"Location: {ip_info['city']}, {ip_info['region']}, {ip_info['country']}")
                        print(f"ISP/Org: {ip_info['org']}")
                        
                        return ip_info
                        
                except Exception as e:
                    continue
            
            return {"ip": "unknown", "country": "unknown"}
            
        except Exception as e:
            print(f"Error checking IP: {e}")
            return {"ip": "unknown", "country": "unknown"}
    
    def is_indian_ip(self, ip_info: Dict) -> bool:
        """Check if current IP appears to be from India"""
        country = ip_info.get("country", "").upper()
        return country in ["IN", "INDIA"]
    
    def create_session(self) -> requests.Session:
        """Create a new session with proper headers"""
        session = requests.Session()
        session.headers.update(self.headers)
        
        # Add some additional headers for better compatibility
        session.headers.update({
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "DNT": "1",
            "Sec-GPC": "1"
        })
        
        return session
    
    def establish_nse_session(self) -> bool:
        """Establish session with NSE"""
        try:
            self.session = self.create_session()
            
            print("Establishing session with NSE...")
            
            # Step 1: Visit NSE homepage
            print("  → Visiting NSE homepage...")
            home_response = self.session.get(
                "https://www.nseindia.com/",
                timeout=15,
                verify=False
            )
            
            if home_response.status_code != 200:
                print(f"  ❌ Homepage access failed: {home_response.status_code}")
                return False
            
            print("  ✅ Homepage accessed successfully")
            
            # Step 2: Visit market data page
            print("  → Visiting market data page...")
            market_response = self.session.get(
                "https://www.nseindia.com/market-data/live-equity-market",
                timeout=15,
                verify=False
            )
            
            if market_response.status_code == 200:
                print("  ✅ Market data page accessed successfully")
            else:
                print(f"  ⚠️ Market data page returned: {market_response.status_code}")
            
            # Step 3: Test a simple API call
            print("  → Testing API access...")
            test_response = self.session.get(
                self.nse_endpoints["market_status"],
                timeout=15,
                verify=False
            )
            
            if test_response.status_code == 200:
                print("  ✅ API access successful!")
                return True
            elif test_response.status_code == 403:
                print("  ❌ API access forbidden (403)")
                return False
            else:
                print(f"  ⚠️ API returned: {test_response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ Session establishment failed: {e}")
            return False
    
    def fetch_nse_data(self, data_type: str = "gainers") -> Optional[Dict]:
        """Fetch NSE data"""
        if not self.session:
            print("Session not established. Call establish_nse_session() first.")
            return None
        
        endpoint = self.nse_endpoints.get(data_type)
        if not endpoint:
            print(f"Unknown data type: {data_type}")
            return None
        
        try:
            print(f"Fetching {data_type} data...")
            
            response = self.session.get(endpoint, timeout=20, verify=False)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ Successfully fetched {data_type} data")
                    return data
                except json.JSONDecodeError:
                    print(f"❌ Invalid JSON response for {data_type}")
                    return None
            elif response.status_code == 403:
                print(f"❌ Access forbidden for {data_type}")
                return None
            else:
                print(f"❌ HTTP {response.status_code} for {data_type}")
                return None
                
        except Exception as e:
            print(f"❌ Error fetching {data_type}: {e}")
            return None
    
    def display_data(self, data: Dict, data_type: str):
        """Display NSE data in a formatted way"""
        if not data:
            print("No data to display")
            return
        
        print(f"\n{'='*60}")
        print(f"NSE {data_type.upper()} DATA")
        print(f"{'='*60}")
        
        if 'data' in data and isinstance(data['data'], list):
            items = data['data'][:10]
            print(f"Timestamp: {data.get('time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))}")
            print(f"Total records: {len(data['data'])}")
            print(f"\nTop 10 {data_type}:")
            print("-" * 60)
            
            for i, item in enumerate(items, 1):
                symbol = item.get('symbol', 'N/A')
                ltp = item.get('lastPrice', item.get('ltp', 'N/A'))
                change = item.get('netChange', item.get('change', 'N/A'))
                pct_change = item.get('pChange', item.get('pctChange', 'N/A'))
                
                print(f"{i:2d}. {symbol:15} | Price: {ltp:>10} | Change: {change:>8} ({pct_change:>6}%)")
        
        elif data_type == "market_status":
            print("Market Status Information:")
            for key, value in data.items():
                if isinstance(value, list):
                    print(f"{key}: {len(value)} items")
                else:
                    print(f"{key}: {value}")
        
        else:
            print(f"Data structure: {list(data.keys()) if isinstance(data, dict) else type(data)}")
        
        print("=" * 60)
    
    def save_data(self, data: Dict, data_type: str):
        """Save data to JSON file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"nse_{data_type}_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            print(f"💾 Data saved to {filename}")
        except Exception as e:
            print(f"❌ Error saving data: {e}")

def check_vpn_suggestions():
    """Provide VPN suggestions for accessing NSE"""
    print("\n" + "="*60)
    print("VPN RECOMMENDATIONS FOR NSE ACCESS")
    print("="*60)
    
    vpn_services = [
        {
            "name": "ExpressVPN",
            "indian_servers": "Mumbai, Chennai, Bangalore",
            "reliability": "High",
            "notes": "Fast speeds, reliable for financial sites"
        },
        {
            "name": "NordVPN", 
            "indian_servers": "Mumbai, Delhi",
            "reliability": "High",
            "notes": "Good for streaming and financial data"
        },
        {
            "name": "Surfshark",
            "indian_servers": "Mumbai, Delhi, Bangalore",
            "reliability": "Medium-High",
            "notes": "Cost-effective option"
        },
        {
            "name": "CyberGhost",
            "indian_servers": "Mumbai",
            "reliability": "Medium",
            "notes": "User-friendly interface"
        }
    ]
    
    for vpn in vpn_services:
        print(f"\n{vpn['name']}:")
        print(f"  Indian Servers: {vpn['indian_servers']}")
        print(f"  Reliability: {vpn['reliability']}")
        print(f"  Notes: {vpn['notes']}")
    
    print(f"\n{'='*60}")
    print("SETUP INSTRUCTIONS:")
    print("1. Subscribe to a VPN service with Indian servers")
    print("2. Connect to an Indian server (Mumbai recommended)")
    print("3. Verify your IP shows as Indian location")
    print("4. Run this script again")
    print("="*60)

def main():
    """Main function"""
    print("NSE Data Fetcher with VPN Detection")
    print("="*50)
    
    fetcher = VPNNSEFetcher()
    
    # Check current IP and location
    ip_info = fetcher.check_current_ip()
    
    # Check if we appear to be in India
    if fetcher.is_indian_ip(ip_info):
        print("✅ Your IP appears to be from India - proceeding with NSE access...")
    else:
        print("⚠️ Your IP does not appear to be from India")
        print("NSE blocks international access - you'll need a VPN or proxy")
        
        # Show VPN recommendations
        check_vpn_suggestions()
        
        # Ask if user wants to try anyway
        try_anyway = input("\nDo you want to try accessing NSE anyway? (y/n): ").lower().strip()
        if try_anyway != 'y':
            print("Exiting. Please set up VPN with Indian server and try again.")
            return
    
    # Try to establish NSE session
    print(f"\n{'='*50}")
    if fetcher.establish_nse_session():
        print("🎉 NSE session established successfully!")
        
        # Fetch different types of data
        data_types = ["market_status", "gainers", "losers", "most_active"]
        
        for data_type in data_types:
            print(f"\n{'-'*30}")
            data = fetcher.fetch_nse_data(data_type)
            
            if data:
                fetcher.display_data(data, data_type)
                
                # Ask if user wants to save data
                save_data = input(f"\nSave {data_type} data to file? (y/n): ").lower().strip()
                if save_data == 'y':
                    fetcher.save_data(data, data_type)
            
            time.sleep(1)  # Be respectful to NSE servers
        
        print(f"\n{'='*50}")
        print("✅ NSE data collection completed successfully!")
        
    else:
        print("❌ Could not establish NSE session")
        print("\nPossible solutions:")
        print("1. Use VPN with Indian server")
        print("2. Try the enhanced proxy fetcher (enhanced_proxy_fetcher.py)")
        print("3. Check if NSE is currently accessible")
        print("4. Verify your internet connection")

if __name__ == "__main__":
    main()