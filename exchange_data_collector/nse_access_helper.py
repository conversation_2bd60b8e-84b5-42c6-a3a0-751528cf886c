#!/usr/bin/env python3
"""
NSE Access Helper - Choose the best method for your situation
"""

import requests
import json
import time
from datetime import datetime

def check_direct_access():
    """Check if direct access to NSE works"""
    print("Testing direct access to NSE...")
    
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        }
        
        # Test NSE homepage
        response = requests.get("https://www.nseindia.com/", headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ NSE homepage accessible")
            
            # Test API endpoint
            session = requests.Session()
            session.headers.update(headers)
            
            # Get homepage first for cookies
            session.get("https://www.nseindia.com/", timeout=10)
            
            # Try API
            api_response = session.get(
                "https://www.nseindia.com/api/marketStatus",
                timeout=15
            )
            
            if api_response.status_code == 200:
                print("✅ NSE API accessible - You can use direct access!")
                return True
            elif api_response.status_code == 403:
                print("❌ NSE API blocked (403) - You need VPN or proxy")
                return False
            else:
                print(f"⚠️ NSE API returned {api_response.status_code}")
                return False
        else:
            print(f"❌ NSE homepage blocked ({response.status_code})")
            return False
            
    except Exception as e:
        print(f"❌ Direct access failed: {e}")
        return False

def check_ip_location():
    """Check current IP location"""
    print("\nChecking your current IP location...")
    
    try:
        response = requests.get("http://ipinfo.io/json", timeout=10)
        if response.status_code == 200:
            data = response.json()
            
            ip = data.get("ip", "unknown")
            country = data.get("country", "unknown")
            region = data.get("region", "unknown")
            city = data.get("city", "unknown")
            org = data.get("org", "unknown")
            
            print(f"IP: {ip}")
            print(f"Location: {city}, {region}, {country}")
            print(f"ISP: {org}")
            
            if country.upper() == "IN":
                print("✅ You appear to be in India")
                return True
            else:
                print("❌ You appear to be outside India")
                return False
        else:
            print("❌ Could not determine IP location")
            return False
            
    except Exception as e:
        print(f"❌ IP check failed: {e}")
        return False

def show_recommendations(direct_works, in_india):
    """Show recommendations based on test results"""
    print("\n" + "="*60)
    print("RECOMMENDATIONS")
    print("="*60)
    
    if direct_works:
        print("🎉 GREAT NEWS! Direct access works for you!")
        print("\nRecommended approach:")
        print("1. Use vpn_nse_fetcher.py for reliable data collection")
        print("2. No need for proxies or VPN")
        print("3. You can collect NSE data directly")
        
    elif in_india and not direct_works:
        print("🤔 You're in India but NSE API is blocked")
        print("\nPossible causes:")
        print("- Your ISP might be blocking NSE")
        print("- NSE might have temporary restrictions")
        print("- Network configuration issues")
        print("\nRecommended approach:")
        print("1. Try enhanced_proxy_fetcher.py")
        print("2. Check with your ISP")
        print("3. Try from a different network")
        
    else:
        print("🌍 You're outside India - NSE blocks international access")
        print("\nRecommended approaches (in order of preference):")
        print("\n1. VPN Solution (BEST):")
        print("   - Subscribe to ExpressVPN, NordVPN, or Surfshark")
        print("   - Connect to Indian server (Mumbai recommended)")
        print("   - Use vpn_nse_fetcher.py")
        print("   - Most reliable method")
        
        print("\n2. Enhanced Proxy Method:")
        print("   - Run: python enhanced_proxy_fetcher.py")
        print("   - Uses multiple proxy sources")
        print("   - Free but less reliable")
        
        print("\n3. Paid Proxy Services:")
        print("   - BrightData, Oxylabs, SmartProxy")
        print("   - More reliable than free proxies")
        print("   - Requires subscription")
        
        print("\n4. Cloud Server Method:")
        print("   - Deploy script on Indian cloud server")
        print("   - AWS, Google Cloud, or Azure")
        print("   - Run scheduled data collection")

def show_next_steps(direct_works):
    """Show specific next steps"""
    print("\n" + "="*60)
    print("NEXT STEPS")
    print("="*60)
    
    if direct_works:
        print("Run this command:")
        print("python vpn_nse_fetcher.py")
        
    else:
        print("Choose one of these options:")
        print("\nOption 1 - VPN (Recommended):")
        print("1. Subscribe to a VPN service with Indian servers")
        print("2. Connect to Mumbai or Delhi server")
        print("3. Run: python vpn_nse_fetcher.py")
        
        print("\nOption 2 - Enhanced Proxies:")
        print("1. Run: python enhanced_proxy_fetcher.py")
        print("2. Wait for proxy testing (3-5 minutes)")
        print("3. Script will automatically test NSE access")
        
        print("\nOption 3 - Manual Proxy Setup:")
        print("1. Get working proxies from paid services")
        print("2. Update proxy_config.py with your proxies")
        print("3. Run: python trial_proxy.py")

def main():
    """Main function"""
    print("NSE Access Helper")
    print("="*40)
    print("This tool will help you determine the best method to access NSE data")
    print()
    
    # Check IP location
    in_india = check_ip_location()
    
    # Check direct access
    direct_works = check_direct_access()
    
    # Show recommendations
    show_recommendations(direct_works, in_india)
    
    # Show next steps
    show_next_steps(direct_works)
    
    print("\n" + "="*60)
    print("ADDITIONAL RESOURCES")
    print("="*60)
    print("📁 Files in this package:")
    print("- vpn_nse_fetcher.py: For VPN/direct access")
    print("- enhanced_proxy_fetcher.py: Advanced proxy method")
    print("- trial_proxy.py: Original proxy method (improved)")
    print("- proxy_config.py: Proxy testing utilities")
    print("- nse_data_fetcher.py: Clean NSE data fetcher class")
    
    print("\n💡 Tips for success:")
    print("- VPN is the most reliable method")
    print("- Free proxies have low success rates")
    print("- Try different times of day")
    print("- Be respectful to NSE servers (don't spam requests)")
    
    print("\n🆘 If nothing works:")
    print("- NSE may have enhanced their blocking")
    print("- Consider official NSE data subscriptions")
    print("- Use alternative data sources (Yahoo Finance, etc.)")

if __name__ == "__main__":
    main()