#!/bin/bash

# Test script for Exchange Data Collector
# This script tests the collector with a limited set of data to verify functionality

# Get current date for logging
DATE=$(date +"%Y-%m-%d")
LOG_FILE="logs/test_exchange_collector_${DATE}.log"

# Create necessary directories
mkdir -p logs
mkdir -p data/nse
mkdir -p data/bse
mkdir -p data/csv

# Function to log messages
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" | tee -a "$LOG_FILE"
}

# Function to run a test and check the result
run_test() {
    local test_name=$1
    local command=$2
    
    log "Running test: $test_name"
    log "Command: $command"
    
    # Run the command
    eval $command >> "$LOG_FILE" 2>&1
    local result=$?
    
    if [ $result -eq 0 ]; then
        log "✅ Test passed: $test_name"
        echo "✅ Test passed: $test_name"
    else
        log "❌ Test failed: $test_name (exit code: $result)"
        echo "❌ Test failed: $test_name (exit code: $result)"
    fi
    
    echo ""
    return $result
}

# Main test execution
log "Starting Exchange Data Collector tests"
echo "Starting Exchange Data Collector tests"
echo "======================================"
echo ""

# Test 1: Test NSE company data collection for a single company (RELIANCE)
run_test "NSE Company Data (RELIANCE)" "python main.py nse company --symbols RELIANCE"

# Test 2: Test BSE company data collection for a single company (500325 - RELIANCE)
run_test "BSE Company Data (500325)" "python main.py bse company --symbols 500325"

# Test 3: Test NSE index data collection for a single index (NIFTY 50)
run_test "NSE Index Data (NIFTY 50)" "python main.py nse index --symbols NIFTY50"

# Test 4: Test BSE index data collection for a single index (SENSEX)
run_test "BSE Index Data (SENSEX)" "python main.py bse index --symbols SENSEX"

# Test 5: Test NSE options data collection for a single company
run_test "NSE Options Data (RELIANCE)" "python main.py nse options --symbols RELIANCE"

# Test 6: Test BSE options data collection for a single company
run_test "BSE Options Data (500325)" "python main.py bse options --symbols 500325"

# Test 7: Test NSE live data collection
run_test "NSE Live Data" "python main.py nse live"

# Test 8: Test BSE live data collection
run_test "BSE Live Data" "python main.py bse live"

# Test 9: Test CSV export
run_test "CSV Export" "python main.py export"

# Print summary
log "Exchange Data Collector tests completed"
echo "Exchange Data Collector tests completed"
echo "Check $LOG_FILE for detailed logs"
