#!/usr/bin/env python3
"""
Enhanced Proxy Fetcher with Multiple Sources and Better Success Rate
"""

import requests
import json
import time
import random
from bs4 import BeautifulSoup
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Tuple, Optional
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class EnhancedProxyFetcher:
    def __init__(self):
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }
        
    def get_proxyscrape_proxies(self) -> List[Tuple]:
        """Get proxies from proxyscrape.com API"""
        try:
            print("Fetching from ProxyScrape...")
            urls = [
                "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all",
                "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=5000&country=IN&ssl=all&anonymity=all",
                "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=US,GB,CA&ssl=all&anonymity=all"
            ]
            
            proxies = []
            for url in urls:
                try:
                    response = requests.get(url, timeout=15, headers=self.headers)
                    if response.status_code == 200:
                        for line in response.text.strip().split('\n'):
                            if ':' in line and line.strip():
                                try:
                                    ip, port = line.strip().split(':')
                                    if self._is_valid_ip(ip) and port.isdigit():
                                        proxies.append(('http', ip, int(port)))
                                except:
                                    continue
                except Exception as e:
                    print(f"ProxyScrape URL failed: {e}")
                    continue
                    
            return list(set(proxies))[:50]  # Remove duplicates and limit
            
        except Exception as e:
            print(f"ProxyScrape error: {e}")
            return []
    
    def get_github_proxy_lists(self) -> List[Tuple]:
        """Get proxies from GitHub repositories"""
        try:
            print("Fetching from GitHub proxy lists...")
            github_urls = [
                "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
                "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt",
                "https://raw.githubusercontent.com/proxy4parsing/proxy-list/main/http.txt",
                "https://raw.githubusercontent.com/ALIILAPRO/Proxy/main/http.txt"
            ]
            
            proxies = []
            for url in github_urls:
                try:
                    response = requests.get(url, timeout=10, headers=self.headers)
                    if response.status_code == 200:
                        for line in response.text.strip().split('\n'):
                            if ':' in line and line.strip():
                                try:
                                    ip, port = line.strip().split(':')
                                    if self._is_valid_ip(ip) and port.isdigit():
                                        proxies.append(('http', ip, int(port)))
                                except:
                                    continue
                except Exception as e:
                    print(f"GitHub URL failed: {url} - {e}")
                    continue
                    
            return list(set(proxies))[:40]  # Remove duplicates and limit
            
        except Exception as e:
            print(f"GitHub proxy lists error: {e}")
            return []
    
    def get_geonode_proxies(self) -> List[Tuple]:
        """Get proxies from geonode.com"""
        try:
            print("Fetching from GeoNode...")
            url = "https://proxylist.geonode.com/api/proxy-list?limit=50&page=1&sort_by=lastChecked&sort_type=desc&protocols=http%2Chttps"
            
            response = requests.get(url, timeout=15, headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                proxies = []
                
                for proxy in data.get('data', []):
                    ip = proxy.get('ip')
                    port = proxy.get('port')
                    protocols = proxy.get('protocols', [])
                    
                    if ip and port and ('http' in protocols or 'https' in protocols):
                        if self._is_valid_ip(ip):
                            proxies.append(('http', ip, int(port)))
                
                return proxies[:30]
            
        except Exception as e:
            print(f"GeoNode error: {e}")
            
        return []
    
    def get_proxy_list_plus(self) -> List[Tuple]:
        """Get proxies from proxy-list.org"""
        try:
            print("Fetching from Proxy-List.org...")
            url = "https://proxy-list.org/english/index.php"
            
            response = requests.get(url, timeout=15, headers=self.headers)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            proxies = []
            proxy_items = soup.find_all('div', class_='table-wrap')
            
            for item in proxy_items:
                proxy_text = item.get_text()
                # Look for IP:PORT patterns
                matches = re.findall(r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d+)', proxy_text)
                for ip, port in matches:
                    if self._is_valid_ip(ip):
                        proxies.append(('http', ip, int(port)))
            
            return list(set(proxies))[:20]
            
        except Exception as e:
            print(f"Proxy-List.org error: {e}")
            return []
    
    def _is_valid_ip(self, ip: str) -> bool:
        """Validate IP address format"""
        try:
            parts = ip.split('.')
            return len(parts) == 4 and all(0 <= int(part) <= 255 for part in parts)
        except:
            return False
    
    def test_proxy_advanced(self, proxy: Tuple, timeout: int = 8) -> Dict:
        """Advanced proxy testing with multiple checks"""
        proxy_type, host, port = proxy
        
        test_urls = [
            "http://httpbin.org/ip",
            "http://icanhazip.com",
            "http://ipinfo.io/ip"
        ]
        
        proxies_config = {
            "http": f"http://{host}:{port}",
            "https": f"http://{host}:{port}"
        }
        
        for test_url in test_urls:
            try:
                start_time = time.time()
                response = requests.get(
                    test_url,
                    proxies=proxies_config,
                    timeout=timeout,
                    verify=False,
                    headers={"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}
                )
                
                latency = int((time.time() - start_time) * 1000)
                
                if response.status_code == 200:
                    # Try to extract IP from response
                    response_text = response.text.strip()
                    
                    # For JSON responses
                    try:
                        json_data = response.json()
                        proxy_ip = json_data.get('ip', json_data.get('origin', response_text))
                    except:
                        proxy_ip = response_text
                    
                    return {
                        "status": "success",
                        "proxy": proxy,
                        "latency": latency,
                        "ip": proxy_ip,
                        "test_url": test_url
                    }
                    
            except requests.exceptions.Timeout:
                continue
            except requests.exceptions.ConnectionError:
                continue
            except Exception:
                continue
        
        return {
            "status": "failed",
            "proxy": proxy,
            "error": "all_tests_failed"
        }
    
    def get_all_proxies(self) -> List[Tuple]:
        """Get proxies from all sources"""
        all_sources = [
            self.get_proxyscrape_proxies,
            self.get_github_proxy_lists,
            self.get_geonode_proxies,
            self.get_proxy_list_plus
        ]
        
        all_proxies = []
        
        print("Collecting proxies from multiple enhanced sources...")
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(source) for source in all_sources]
            
            for future in as_completed(futures):
                try:
                    proxies = future.result()
                    if proxies:
                        print(f"✅ Source added {len(proxies)} proxies")
                        all_proxies.extend(proxies)
                    else:
                        print("❌ Source returned no proxies")
                except Exception as e:
                    print(f"❌ Source failed: {e}")
        
        # Remove duplicates
        unique_proxies = list(set(all_proxies))
        print(f"\nTotal unique proxies collected: {len(unique_proxies)}")
        
        return unique_proxies
    
    def find_working_proxies(self, max_test: int = 100, max_workers: int = 20) -> List[Dict]:
        """Find working proxies with enhanced testing"""
        all_proxies = self.get_all_proxies()
        
        if not all_proxies:
            print("No proxies to test!")
            return []
        
        # Shuffle and limit
        random.shuffle(all_proxies)
        test_proxies = all_proxies[:max_test]
        
        print(f"\nTesting {len(test_proxies)} proxies with enhanced validation...")
        print("This may take 3-5 minutes...")
        
        working_proxies = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(self.test_proxy_advanced, proxy): proxy for proxy in test_proxies}
            
            completed = 0
            for future in as_completed(futures):
                completed += 1
                proxy = futures[future]
                
                try:
                    result = future.result()
                    if result["status"] == "success":
                        print(f"✅ {proxy[1]}:{proxy[2]} | {result['latency']}ms | IP: {result['ip']}")
                        working_proxies.append(result)
                    else:
                        print(f"❌ {proxy[1]}:{proxy[2]} failed")
                except Exception as e:
                    print(f"❌ {proxy[1]}:{proxy[2]} error: {e}")
                
                # Progress indicator
                if completed % 10 == 0:
                    print(f"Progress: {completed}/{len(test_proxies)} tested...")
        
        # Sort by latency
        working_proxies.sort(key=lambda x: x['latency'])
        
        return working_proxies

def test_nse_with_working_proxies(working_proxies: List[Dict]) -> bool:
    """Test NSE access with working proxies"""
    if not working_proxies:
        print("No working proxies to test with NSE")
        return False
    
    print(f"\nTesting NSE access with {len(working_proxies)} working proxies...")
    
    nse_headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "en-US,en;q=0.9",
        "Referer": "https://www.nseindia.com/market-data/live-equity-market",
        "Origin": "https://www.nseindia.com"
    }
    
    for i, proxy_info in enumerate(working_proxies[:10], 1):
        proxy = proxy_info['proxy']
        proxy_type, host, port = proxy
        
        print(f"\nAttempt {i}: Testing NSE with {host}:{port}")
        
        try:
            proxies_config = {
                "http": f"http://{host}:{port}",
                "https": f"http://{host}:{port}"
            }
            
            session = requests.Session()
            session.proxies = proxies_config
            session.headers.update(nse_headers)
            
            # Step 1: Get NSE homepage
            print("  → Accessing NSE homepage...")
            home_response = session.get("https://www.nseindia.com/", timeout=15, verify=False)
            
            if home_response.status_code != 200:
                print(f"  ❌ Homepage failed: {home_response.status_code}")
                continue
            
            # Step 2: Access market data page
            print("  → Accessing market data page...")
            market_response = session.get(
                "https://www.nseindia.com/market-data/live-equity-market",
                timeout=15,
                verify=False
            )
            
            # Step 3: Try API call
            print("  → Attempting API call...")
            api_response = session.get(
                "https://www.nseindia.com/api/live-analysis-variations?index=gainers",
                timeout=20,
                verify=False
            )
            
            if api_response.status_code == 200:
                try:
                    data = api_response.json()
                    if 'data' in data and len(data['data']) > 0:
                        print(f"  ✅ SUCCESS! Got {len(data['data'])} records")
                        print(f"  📊 Sample data: {data['data'][0].get('symbol', 'N/A')}")
                        
                        # Save successful proxy info
                        with open('successful_proxy.json', 'w') as f:
                            json.dump({
                                'proxy': proxy,
                                'timestamp': time.time(),
                                'sample_data': data['data'][:3]
                            }, f, indent=2)
                        
                        return True
                    else:
                        print("  ⚠️ Empty data response")
                except json.JSONDecodeError:
                    print("  ❌ Invalid JSON response")
            else:
                print(f"  ❌ API failed: {api_response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Error: {str(e)[:50]}...")
        
        time.sleep(2)  # Be respectful
    
    return False

def main():
    """Main function"""
    print("Enhanced NSE Proxy Fetcher")
    print("=" * 50)
    
    fetcher = EnhancedProxyFetcher()
    
    # Find working proxies
    working_proxies = fetcher.find_working_proxies(max_test=150, max_workers=25)
    
    print(f"\n🎯 Found {len(working_proxies)} working proxies!")
    
    if working_proxies:
        # Save working proxies
        with open('working_proxies.json', 'w') as f:
            json.dump(working_proxies, f, indent=2)
        print("💾 Working proxies saved to working_proxies.json")
        
        # Test with NSE
        success = test_nse_with_working_proxies(working_proxies)
        
        if success:
            print("\n🎉 SUCCESS! NSE data collection is working!")
        else:
            print("\n😞 Could not access NSE with current proxies")
            print("\nNext steps:")
            print("1. Try running again later (proxy availability changes)")
            print("2. Consider paid proxy services")
            print("3. Use VPN with Indian servers")
    else:
        print("\n😞 No working proxies found")
        print("\nTroubleshooting:")
        print("1. Check your internet connection")
        print("2. Try running at different times")
        print("3. Free proxies have low success rates")

if __name__ == "__main__":
    main()