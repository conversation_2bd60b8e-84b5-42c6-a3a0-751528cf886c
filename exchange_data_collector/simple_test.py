#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple test script for the exchange data collector
"""

import os
import json
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"logs/simple_test_{datetime.now().strftime('%Y%m%d')}.log")
    ]
)

logger = logging.getLogger(__name__)

def create_mock_data():
    """Create mock data for testing"""
    logger.info("Creating mock data for testing")
    
    # Create necessary directories
    os.makedirs("data/nse/company_data", exist_ok=True)
    os.makedirs("data/nse/index_data", exist_ok=True)
    os.makedirs("data/nse/options_data", exist_ok=True)
    os.makedirs("data/nse/live_data", exist_ok=True)
    
    os.makedirs("data/bse/company_data", exist_ok=True)
    os.makedirs("data/bse/index_data", exist_ok=True)
    os.makedirs("data/bse/options_data", exist_ok=True)
    os.makedirs("data/bse/live_data", exist_ok=True)
    
    # Create mock NSE company data
    nse_company_data = {
        "info": {
            "symbol": "RELIANCE",
            "companyName": "Reliance Industries Limited",
            "industry": "Refineries",
            "isin": "INE002A01018",
            "lastUpdateTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        "metadata": {
            "series": "EQ",
            "symbol": "RELIANCE",
            "isin": "INE002A01018",
            "status": "Listed",
            "listingDate": "2000-01-01",
            "industry": "Refineries"
        },
        "financials": {
            "balanceSheet": {
                "2022": {"totalAssets": "100000", "totalLiabilities": "50000"},
                "2021": {"totalAssets": "90000", "totalLiabilities": "45000"},
                "2020": {"totalAssets": "80000", "totalLiabilities": "40000"}
            },
            "profitAndLoss": {
                "2022": {"revenue": "50000", "netProfit": "10000"},
                "2021": {"revenue": "45000", "netProfit": "9000"},
                "2020": {"revenue": "40000", "netProfit": "8000"}
            },
            "cashFlow": {
                "2022": {"operatingCashFlow": "15000", "investingCashFlow": "-5000"},
                "2021": {"operatingCashFlow": "13500", "investingCashFlow": "-4500"},
                "2020": {"operatingCashFlow": "12000", "investingCashFlow": "-4000"}
            }
        },
        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Save NSE company data
    with open("data/nse/company_data/RELIANCE.json", 'w') as f:
        json.dump(nse_company_data, f, indent=2)
    
    # Create mock BSE company data
    bse_company_data = {
        "info": {
            "scripCode": "500325",
            "scripName": "Reliance Industries Ltd.",
            "status": "Active",
            "group": "A",
            "faceValue": 10,
            "isin": "INE002A01018",
            "industry": "Refineries",
            "sectorCode": "5300",
            "sectorName": "Oil & Gas"
        },
        "financials": {
            "balanceSheet": {
                "2022": {"totalAssets": "100000", "totalLiabilities": "50000"},
                "2021": {"totalAssets": "90000", "totalLiabilities": "45000"},
                "2020": {"totalAssets": "80000", "totalLiabilities": "40000"}
            },
            "profitAndLoss": {
                "2022": {"revenue": "50000", "netProfit": "10000"},
                "2021": {"revenue": "45000", "netProfit": "9000"},
                "2020": {"revenue": "40000", "netProfit": "8000"}
            },
            "cashFlow": {
                "2022": {"operatingCashFlow": "15000", "investingCashFlow": "-5000"},
                "2021": {"operatingCashFlow": "13500", "investingCashFlow": "-4500"},
                "2020": {"operatingCashFlow": "12000", "investingCashFlow": "-4000"}
            }
        },
        "announcements": [
            {
                "date": "2022-01-01",
                "subject": "Board Meeting",
                "description": "Board Meeting to consider financial results"
            },
            {
                "date": "2022-01-15",
                "subject": "Financial Results",
                "description": "Announcement of financial results"
            }
        ],
        "actions": [
            {
                "date": "2022-01-20",
                "action": "Dividend",
                "value": "5.00"
            },
            {
                "date": "2022-02-15",
                "action": "Bonus",
                "value": "1:1"
            }
        ],
        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Save BSE company data
    with open("data/bse/company_data/500325.json", 'w') as f:
        json.dump(bse_company_data, f, indent=2)
    
    # Create mock NSE index data
    nse_index_data = {
        "info": {
            "indexSymbol": "NIFTY50",
            "indexName": "NIFTY 50 Index",
            "lastUpdateTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        "history": {
            "2022-01-01": {"open": "18000", "high": "18500", "low": "17800", "close": "18200"},
            "2022-01-02": {"open": "18200", "high": "18700", "low": "18100", "close": "18600"},
            "2022-01-03": {"open": "18600", "high": "19000", "low": "18500", "close": "18900"}
        },
        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Save NSE index data
    with open("data/nse/index_data/NIFTY50.json", 'w') as f:
        json.dump(nse_index_data, f, indent=2)
    
    # Create mock BSE index data
    bse_index_data = {
        "info": {
            "indexCode": "SENSEX",
            "indexName": "SENSEX Index",
            "lastUpdateTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        "constituents": [
            {"scripCode": "500325", "scripName": "Reliance Industries Ltd.", "weight": "10.5"},
            {"scripCode": "532540", "scripName": "TCS Ltd.", "weight": "8.2"},
            {"scripCode": "500180", "scripName": "HDFC Bank Ltd.", "weight": "7.8"}
        ],
        "history": {
            "2022-01-01": {"open": "58000", "high": "58500", "low": "57800", "close": "58200"},
            "2022-01-02": {"open": "58200", "high": "58700", "low": "58100", "close": "58600"},
            "2022-01-03": {"open": "58600", "high": "59000", "low": "58500", "close": "58900"}
        },
        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Save BSE index data
    with open("data/bse/index_data/SENSEX.json", 'w') as f:
        json.dump(bse_index_data, f, indent=2)
    
    # Create mock NSE options data
    nse_options_data = {
        "records": {
            "expiryDates": ["2022-01-27", "2022-02-24", "2022-03-31"],
            "data": [
                {
                    "strikePrice": "2000",
                    "expiryDate": "2022-01-27",
                    "call": {"openInterest": "1000", "lastPrice": "50"},
                    "put": {"openInterest": "800", "lastPrice": "30"}
                },
                {
                    "strikePrice": "2100",
                    "expiryDate": "2022-01-27",
                    "call": {"openInterest": "800", "lastPrice": "30"},
                    "put": {"openInterest": "1000", "lastPrice": "40"}
                }
            ]
        },
        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Save NSE options data
    with open("data/nse/options_data/RELIANCE.json", 'w') as f:
        json.dump(nse_options_data, f, indent=2)
    
    # Create mock BSE options data
    bse_options_data = {
        "data": [
            {
                "strikePrice": "2000",
                "expiryDate": "2022-01-27",
                "call": {"openInterest": "1000", "lastPrice": "50"},
                "put": {"openInterest": "800", "lastPrice": "30"}
            },
            {
                "strikePrice": "2100",
                "expiryDate": "2022-01-27",
                "call": {"openInterest": "800", "lastPrice": "30"},
                "put": {"openInterest": "1000", "lastPrice": "40"}
            }
        ],
        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Save BSE options data
    with open("data/bse/options_data/500325.json", 'w') as f:
        json.dump(bse_options_data, f, indent=2)
    
    # Create mock NSE live data
    nse_live_data = {
        "market_status": {
            "status": "open",
            "lastUpdateTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        "pre_open_market": {
            "data": [
                {"symbol": "RELIANCE", "price": "2500", "change": "50", "pChange": "2.0"},
                {"symbol": "TCS", "price": "3500", "change": "70", "pChange": "2.0"},
                {"symbol": "HDFCBANK", "price": "1500", "change": "30", "pChange": "2.0"}
            ]
        },
        "equity_market": {
            "data": [
                {"symbol": "RELIANCE", "price": "2550", "change": "100", "pChange": "4.0"},
                {"symbol": "TCS", "price": "3570", "change": "140", "pChange": "4.0"},
                {"symbol": "HDFCBANK", "price": "1530", "change": "60", "pChange": "4.0"}
            ]
        },
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Save NSE live data
    with open("data/nse/live_data/latest_market.json", 'w') as f:
        json.dump(nse_live_data, f, indent=2)
    
    # Create mock BSE live data
    bse_live_data = {
        "market_status": {
            "status": "open",
            "lastUpdateTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        "market_data": {
            "sensex": "58000",
            "nifty": "17000",
            "advance": "1500",
            "decline": "1000",
            "unchanged": "500"
        },
        "gainers_losers": {
            "gainers": [
                {"scripCode": "500325", "scripName": "Reliance Industries Ltd.", "lastPrice": "2500", "change": "50", "pChange": "2.0"},
                {"scripCode": "532540", "scripName": "TCS Ltd.", "lastPrice": "3500", "change": "70", "pChange": "2.0"},
                {"scripCode": "500180", "scripName": "HDFC Bank Ltd.", "lastPrice": "1500", "change": "30", "pChange": "2.0"}
            ],
            "losers": [
                {"scripCode": "500209", "scripName": "Infosys Ltd.", "lastPrice": "1600", "change": "-30", "pChange": "-1.8"},
                {"scripCode": "532174", "scripName": "ICICI Bank Ltd.", "lastPrice": "800", "change": "-15", "pChange": "-1.8"},
                {"scripCode": "500112", "scripName": "State Bank of India", "lastPrice": "500", "change": "-10", "pChange": "-2.0"}
            ]
        },
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Save BSE live data
    with open("data/bse/live_data/latest_market.json", 'w') as f:
        json.dump(bse_live_data, f, indent=2)
    
    logger.info("Mock data created successfully")
    return True

def verify_data():
    """Verify that the mock data was created correctly"""
    logger.info("Verifying mock data")
    
    # Check if the files exist
    files_to_check = [
        "data/nse/company_data/RELIANCE.json",
        "data/bse/company_data/500325.json",
        "data/nse/index_data/NIFTY50.json",
        "data/bse/index_data/SENSEX.json",
        "data/nse/options_data/RELIANCE.json",
        "data/bse/options_data/500325.json",
        "data/nse/live_data/latest_market.json",
        "data/bse/live_data/latest_market.json"
    ]
    
    all_files_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            logger.info(f"File exists: {file_path}")
        else:
            logger.error(f"File does not exist: {file_path}")
            all_files_exist = False
    
    return all_files_exist

def run_test():
    """Run the test"""
    logger.info("Running test")
    
    # Create necessary directories
    os.makedirs("logs", exist_ok=True)
    
    # Create mock data
    create_result = create_mock_data()
    
    # Verify data
    verify_result = verify_data()
    
    # Print summary
    print("\n=== Test Summary ===\n")
    print(f"Create mock data: {'✅ PASSED' if create_result else '❌ FAILED'}")
    print(f"Verify data: {'✅ PASSED' if verify_result else '❌ FAILED'}")
    
    return create_result and verify_result

if __name__ == "__main__":
    run_test()
