#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for the main.py file
"""

import os
import sys
import json
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"logs/test_main_{datetime.now().strftime('%Y%m%d')}.log")
    ]
)

logger = logging.getLogger(__name__)

# Import the main module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from main import ExchangeDataCollector

def test_nse_company_data():
    """Test collecting company data from NSE"""
    logger.info("Testing NSE company data collection")
    
    # Create the collector
    collector = ExchangeDataCollector()
    
    # Test with a single company
    result = collector.collect_nse_data("company", symbols=["RELIANCE"])
    
    # Print the result
    print(f"Status: {result.get('status', 'unknown')}")
    print(f"Message: {result.get('message', '')}")
    
    # Check if the file was created
    file_path = "data/nse/company_data/RELIANCE.json"
    if os.path.exists(file_path):
        print(f"File created: {file_path}")
        
        # Read the file
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Print some data
        print(f"Company name: {data.get('info', {}).get('companyName', '')}")
    else:
        print(f"File not created: {file_path}")
    
    return result.get('status') == 'success'

def test_bse_company_data():
    """Test collecting company data from BSE"""
    logger.info("Testing BSE company data collection")
    
    # Create the collector
    collector = ExchangeDataCollector()
    
    # Test with a single company
    result = collector.collect_bse_data("company", symbols=["500325"])
    
    # Print the result
    print(f"Status: {result.get('status', 'unknown')}")
    print(f"Message: {result.get('message', '')}")
    
    # Check if the file was created
    file_path = "data/bse/company_data/500325.json"
    if os.path.exists(file_path):
        print(f"File created: {file_path}")
        
        # Read the file
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Print some data
        print(f"Company name: {data.get('info', {}).get('scripName', '')}")
    else:
        print(f"File not created: {file_path}")
    
    return result.get('status') == 'success'

def test_nse_index_data():
    """Test collecting index data from NSE"""
    logger.info("Testing NSE index data collection")
    
    # Create the collector
    collector = ExchangeDataCollector()
    
    # Test with a single index
    result = collector.collect_nse_data("index", symbols=["NIFTY50"])
    
    # Print the result
    print(f"Status: {result.get('status', 'unknown')}")
    print(f"Message: {result.get('message', '')}")
    
    # Check if the file was created
    file_path = "data/nse/index_data/NIFTY50.json"
    if os.path.exists(file_path):
        print(f"File created: {file_path}")
        
        # Read the file
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Print some data
        print(f"Index name: {data.get('info', {}).get('indexName', '')}")
    else:
        print(f"File not created: {file_path}")
    
    return result.get('status') == 'success'

def test_bse_index_data():
    """Test collecting index data from BSE"""
    logger.info("Testing BSE index data collection")
    
    # Create the collector
    collector = ExchangeDataCollector()
    
    # Test with a single index
    result = collector.collect_bse_data("index", symbols=["SENSEX"])
    
    # Print the result
    print(f"Status: {result.get('status', 'unknown')}")
    print(f"Message: {result.get('message', '')}")
    
    # Check if the file was created
    file_path = "data/bse/index_data/SENSEX.json"
    if os.path.exists(file_path):
        print(f"File created: {file_path}")
        
        # Read the file
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Print some data
        print(f"Index name: {data.get('info', {}).get('indexName', '')}")
    else:
        print(f"File not created: {file_path}")
    
    return result.get('status') == 'success'

def test_export_to_csv():
    """Test exporting data to CSV"""
    logger.info("Testing CSV export")
    
    # Create the collector
    collector = ExchangeDataCollector()
    
    # Test CSV export
    collector.export_to_csv()
    
    # Check if CSV files were created
    csv_dir = "data/csv"
    if os.path.exists(csv_dir):
        print(f"CSV directory created: {csv_dir}")
        
        # List CSV files
        csv_files = []
        for root, dirs, files in os.walk(csv_dir):
            for file in files:
                if file.endswith('.csv'):
                    csv_files.append(os.path.join(root, file))
        
        print(f"CSV files: {csv_files}")
        
        return len(csv_files) > 0
    else:
        print(f"CSV directory not created: {csv_dir}")
        return False

def run_all_tests():
    """Run all tests"""
    logger.info("Running all tests")
    
    # Create necessary directories
    os.makedirs("logs", exist_ok=True)
    
    # Run tests
    tests = [
        ("NSE Company Data", test_nse_company_data),
        ("BSE Company Data", test_bse_company_data),
        ("NSE Index Data", test_nse_index_data),
        ("BSE Index Data", test_bse_index_data),
        ("CSV Export", test_export_to_csv)
    ]
    
    results = []
    
    for name, test_func in tests:
        print(f"\n=== Testing {name} ===\n")
        try:
            result = test_func()
            status = "✅ PASSED" if result else "❌ FAILED"
            results.append((name, status))
        except Exception as e:
            logger.error(f"Error in {name} test: {str(e)}")
            results.append((name, "❌ ERROR"))
    
    # Print summary
    print("\n=== Test Summary ===\n")
    for name, status in results:
        print(f"{status}: {name}")

if __name__ == "__main__":
    run_all_tests()
