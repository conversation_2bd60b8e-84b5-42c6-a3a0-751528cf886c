# Exchange Data Collector

A robust data collection system for financial data from the National Stock Exchange (NSE) and Bombay Stock Exchange (BSE) of India, designed to create a comprehensive financial database for all listed Indian companies.

## Features

- **Comprehensive Data Collection**: Collects data for all listed companies on NSE and BSE
- **Multiple Data Types**: Gathers company details, index data, options data, and live stock data
- **Structured Database**: Creates a well-organized database in both JSON and CSV formats
- **Robust Error Handling**: Implements rate limiting and error recovery mechanisms
- **Batch Processing**: Processes companies in batches to avoid overwhelming the servers
- **Regular Updates**: Supports incremental updates to keep the database current

## Installation

### Prerequisites

- Python 3.8+
- Required packages:
  - requests
  - beautifulsoup4
  - pandas (optional, but recommended for CSV export)
  - numpy
  - lxml
  - tqdm (optional, for progress bars)

### Setup

```bash
# Clone the repository
git clone <repository-url>
cd exchange_data_collector

# Create and activate a virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements_exchange.txt
```

## Usage

### Creating a Complete Database

We provide a convenient shell script to collect data from both exchanges:

```bash
# Make the script executable
chmod +x collect_exchange_data.sh

# Run the script to collect all data (initial run)
./collect_exchange_data.sh
```

This script will:
1. Collect company data from NSE and BSE
2. Collect index data from NSE and BSE
3. Collect options data from NSE and BSE
4. Collect live market data from NSE and BSE
5. Export all data to CSV format

### Collecting Data from Specific Exchanges

To collect data from only one exchange:

```bash
# Collect only NSE data
./collect_exchange_data.sh nse

# Collect only BSE data
./collect_exchange_data.sh bse
```

### Updating the Database

To update the database with the latest data:

```bash
# Update all data
./collect_exchange_data.sh update

# Force refresh all data
./collect_exchange_data.sh force
```

### Manual Data Collection

You can also collect data manually using the Python command:

```bash
# Collect company data from NSE
python main.py nse company

# Collect index data from BSE
python main.py bse index

# Collect options data from NSE
python main.py nse options

# Collect live data from BSE
python main.py bse live

# Collect all data from both exchanges
python main.py all

# Export data to CSV
python main.py export
```

## Data Structure

### Directory Structure

```
data/
├── nse/                  # NSE data
│   ├── company_data/     # Company details
│   ├── index_data/       # Index data
│   ├── options_data/     # Options data
│   ├── live_data/        # Live market data
│   └── raw/              # Raw API responses
├── bse/                  # BSE data
│   ├── company_data/     # Company details
│   ├── index_data/       # Index data
│   ├── options_data/     # Options data
│   ├── live_data/        # Live market data
│   └── raw/              # Raw API responses
└── csv/                  # CSV exports
    ├── nse/              # NSE CSV exports
    └── bse/              # BSE CSV exports
```

### Data Models

#### NSE Company Data

```json
{
  "info": {
    "symbol": "RELIANCE",
    "companyName": "Reliance Industries Limited",
    "industry": "Refineries",
    "activeSeries": ["EQ"],
    "debtSeries": [],
    "tempSuspendedSeries": [],
    "isFNOSec": true,
    "isCASec": false,
    "isSLBSec": true,
    "isDebtSec": false,
    "isSuspended": false,
    "isETFSec": false,
    "isDelisted": false,
    "isin": "INE002A01018",
    "isTop10": true,
    "identifier": "RELIANCE"
  },
  "metadata": {
    "series": "EQ",
    "symbol": "RELIANCE",
    "isin": "INE002A01018",
    "status": "Listed",
    "listingDate": "1995-01-01",
    "industry": "Refineries",
    "lastUpdateTime": "2023-01-01 15:30:00",
    "pdSectorInd": "NIFTY 100",
    "pdSectorPe": "25.5"
  },
  "financials": {
    "balanceSheet": [...],
    "profitAndLoss": [...],
    "cashFlow": [...],
    "ratios": [...]
  },
  "actions": [...],
  "last_updated": "2023-01-01 15:30:00"
}
```

#### BSE Company Data

```json
{
  "info": {
    "scripCode": "500325",
    "scripName": "RELIANCE INDUSTRIES LTD.",
    "status": "Active",
    "group": "A",
    "faceValue": 10,
    "isin": "INE002A01018",
    "industry": "Refineries",
    "sectorCode": "5300",
    "sectorName": "Oil & Gas"
  },
  "financials": {
    "balanceSheet": [...],
    "profitAndLoss": [...],
    "cashFlow": [...],
    "ratios": [...]
  },
  "announcements": [...],
  "actions": [...],
  "last_updated": "2023-01-01 15:30:00"
}
```

## Integration with Other Systems

### DCF Analysis

The database can be integrated with DCF analysis systems:

```python
# Load company data from NSE
import json
with open('data/nse/company_data/RELIANCE.json', 'r') as f:
    nse_data = json.load(f)

# Load company data from BSE
with open('data/bse/company_data/500325.json', 'r') as f:
    bse_data = json.load(f)

# Access financial data
nse_financials = nse_data['financials']
bse_financials = bse_data['financials']

# Use the data for DCF analysis
balance_sheet = nse_financials['balanceSheet']
profit_loss = nse_financials['profitAndLoss']
cash_flow = nse_financials['cashFlow']
```

### Portfolio Optimization

For portfolio optimization:

```python
# Load the CSV data
import pandas as pd
nse_companies = pd.read_csv('data/csv/nse/company_data/all_companies.csv')
bse_companies = pd.read_csv('data/csv/bse/company_data/all_companies.csv')

# Filter companies by sector, market cap, etc.
large_cap_nse = nse_companies[nse_companies['marketCap'] > 20000]
```

## Troubleshooting

### Access Denied Issues

If you encounter "Access Denied" errors when accessing NSE or BSE websites, try the following:

1. **Use a VPN**: Some exchanges block access from certain regions or IP addresses
2. **Update User-Agent**: Modify the User-Agent header in the code to mimic a different browser
3. **Add Delays**: Increase the delay between requests to avoid being rate-limited
4. **Use Proxies**: Implement a proxy rotation system to distribute requests

### Rate Limiting

Both NSE and BSE implement rate limiting to prevent scraping. To avoid being blocked:

1. Add sufficient delays between requests (at least 1 second)
2. Process data in small batches
3. Implement exponential backoff for failed requests
4. Run the collection during off-peak hours

## License

[MIT License](LICENSE)

## Acknowledgements

- [NSE India](https://www.nseindia.com/) - For providing the financial data
- [BSE India](https://www.bseindia.com/) - For providing the financial data
