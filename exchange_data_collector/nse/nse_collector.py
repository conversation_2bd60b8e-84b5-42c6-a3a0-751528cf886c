#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
NSE Data Collector Module

This module collects data from the National Stock Exchange (NSE) of India,
including company details, index data, options data, and live stock data.
"""

import os
import sys
import time
import json
import logging
import requests
from datetime import datetime
from typing import List, Dict, Optional, Union, Any
import traceback
from bs4 import BeautifulSoup
import re

# Try to import pandas, but don't fail if it's not available
try:
    import pandas as pd
except ImportError:
    pd = None

# Configure logging
logger = logging.getLogger(__name__)

class NSEDataCollector:
    """
    Class for collecting data from NSE
    """
    
    def __init__(self, data_dir: str = "data/nse"):
        """
        Initialize the NSE Data Collector
        
        Parameters:
        -----------
        data_dir : str
            Directory to store the collected data
        """
        self.data_dir = data_dir
        
        # Create data directories if they don't exist
        os.makedirs(f"{data_dir}/company_data", exist_ok=True)
        os.makedirs(f"{data_dir}/index_data", exist_ok=True)
        os.makedirs(f"{data_dir}/options_data", exist_ok=True)
        os.makedirs(f"{data_dir}/live_data", exist_ok=True)
        os.makedirs(f"{data_dir}/raw", exist_ok=True)
        
        # Base URLs
        self.base_url = "https://www.nseindia.com"
        self.api_url = "https://www.nseindia.com/api"
        
        # Headers to mimic a browser
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0"
        }
        
        # Session for making requests
        self.session = requests.Session()
        for key, value in self.headers.items():
            self.session.headers[key] = value
    
    def _get_cookies(self) -> None:
        """
        Get cookies from NSE website
        
        NSE requires cookies for API access
        """
        try:
            response = self.session.get(self.base_url, headers=self.headers, timeout=30)
            response.raise_for_status()
            logger.info("Successfully obtained cookies from NSE")
        except Exception as e:
            logger.error(f"Error getting cookies from NSE: {str(e)}")
            traceback.print_exc()
    
    def _make_api_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict:
        """
        Make a request to the NSE API
        
        Parameters:
        -----------
        endpoint : str
            API endpoint to request
        params : dict, optional
            Query parameters for the request
        
        Returns:
        --------
        Dictionary with API response
        """
        url = f"{self.api_url}/{endpoint}"
        
        try:
            # Get cookies first
            self._get_cookies()
            
            # Make the API request
            response = self.session.get(url, params=params, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            # Save the raw response
            endpoint_name = endpoint.replace("/", "_")
            params_str = "_".join([f"{k}_{v}" for k, v in (params or {}).items()])
            raw_file = f"{self.data_dir}/raw/{endpoint_name}_{params_str}_{datetime.now().strftime('%Y%m%d')}.json"
            with open(raw_file, 'w') as f:
                f.write(response.text)
            
            # Parse and return the JSON response
            return response.json()
        
        except Exception as e:
            logger.error(f"Error making API request to {url}: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def collect_company_data(self, symbols: Optional[List[str]] = None, 
                           force_refresh: bool = False) -> Dict:
        """
        Collect company data from NSE
        
        Parameters:
        -----------
        symbols : list, optional
            List of symbols to collect data for (default: all symbols)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting company data from NSE")
        
        try:
            # Get the list of all symbols if not provided
            if symbols is None:
                # Get the list of all equities
                equities = self._make_api_request("master-quote")
                if "status" in equities and equities["status"] == "error":
                    return equities
                
                symbols = [equity["symbol"] for equity in equities.get("data", [])]
            
            # Collect data for each symbol
            results = {"successful": 0, "failed": 0, "total": len(symbols)}
            
            for symbol in symbols:
                try:
                    # Check if data already exists
                    output_file = f"{self.data_dir}/company_data/{symbol}.json"
                    if os.path.exists(output_file) and not force_refresh:
                        logger.info(f"Data for {symbol} already exists, skipping")
                        results["successful"] += 1
                        continue
                    
                    # Get company info
                    company_info = self._make_api_request(f"quote-equity", {"symbol": symbol})
                    
                    # Get company metadata
                    company_metadata = self._make_api_request(f"equity-meta-info", {"symbol": symbol})
                    
                    # Get company financials
                    company_financials = self._make_api_request(f"financials-factsheet", {"symbol": symbol})
                    
                    # Get company corporate actions
                    company_actions = self._make_api_request(f"corporate-announcements", {"symbol": symbol})
                    
                    # Combine all data
                    company_data = {
                        "info": company_info.get("data", {}),
                        "metadata": company_metadata.get("data", {}),
                        "financials": company_financials.get("data", {}),
                        "actions": company_actions.get("data", {}),
                        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # Save the data
                    with open(output_file, 'w') as f:
                        json.dump(company_data, f, indent=2)
                    
                    logger.info(f"Successfully collected data for {symbol}")
                    results["successful"] += 1
                    
                    # Add a small delay to avoid rate limiting
                    time.sleep(1)
                
                except Exception as e:
                    logger.error(f"Error collecting data for {symbol}: {str(e)}")
                    traceback.print_exc()
                    results["failed"] += 1
            
            logger.info(f"Completed collecting company data: {results['successful']} successful, {results['failed']} failed")
            return {"status": "success", **results}
        
        except Exception as e:
            logger.error(f"Error collecting company data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def collect_index_data(self, indices: Optional[List[str]] = None, 
                         force_refresh: bool = False) -> Dict:
        """
        Collect index data from NSE
        
        Parameters:
        -----------
        indices : list, optional
            List of indices to collect data for (default: all indices)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting index data from NSE")
        
        try:
            # Get the list of all indices if not provided
            if indices is None:
                # Get the list of all indices
                all_indices = self._make_api_request("allIndices")
                if "status" in all_indices and all_indices["status"] == "error":
                    return all_indices
                
                indices = [index["indexSymbol"] for index in all_indices.get("data", [])]
            
            # Collect data for each index
            results = {"successful": 0, "failed": 0, "total": len(indices)}
            
            for index in indices:
                try:
                    # Check if data already exists
                    output_file = f"{self.data_dir}/index_data/{index}.json"
                    if os.path.exists(output_file) and not force_refresh:
                        logger.info(f"Data for {index} already exists, skipping")
                        results["successful"] += 1
                        continue
                    
                    # Get index info
                    index_info = self._make_api_request(f"equity-stockIndices", {"index": index})
                    
                    # Get index historical data
                    index_history = self._make_api_request(f"chart-databyindex", {"index": index, "indices": "true"})
                    
                    # Combine all data
                    index_data = {
                        "info": index_info.get("data", {}),
                        "history": index_history.get("data", {}),
                        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # Save the data
                    with open(output_file, 'w') as f:
                        json.dump(index_data, f, indent=2)
                    
                    logger.info(f"Successfully collected data for {index}")
                    results["successful"] += 1
                    
                    # Add a small delay to avoid rate limiting
                    time.sleep(1)
                
                except Exception as e:
                    logger.error(f"Error collecting data for {index}: {str(e)}")
                    traceback.print_exc()
                    results["failed"] += 1
            
            logger.info(f"Completed collecting index data: {results['successful']} successful, {results['failed']} failed")
            return {"status": "success", **results}
        
        except Exception as e:
            logger.error(f"Error collecting index data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def collect_options_data(self, symbols: Optional[List[str]] = None, 
                           force_refresh: bool = False) -> Dict:
        """
        Collect options data from NSE
        
        Parameters:
        -----------
        symbols : list, optional
            List of symbols to collect options data for (default: all symbols with options)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting options data from NSE")
        
        try:
            # Get the list of all symbols with options if not provided
            if symbols is None:
                # Get the list of all symbols with options
                option_symbols = self._make_api_request("option-chain-symbols")
                if "status" in option_symbols and option_symbols["status"] == "error":
                    return option_symbols
                
                symbols = option_symbols.get("symbols", [])
            
            # Collect data for each symbol
            results = {"successful": 0, "failed": 0, "total": len(symbols)}
            
            for symbol in symbols:
                try:
                    # Check if data already exists
                    output_file = f"{self.data_dir}/options_data/{symbol}.json"
                    if os.path.exists(output_file) and not force_refresh:
                        logger.info(f"Options data for {symbol} already exists, skipping")
                        results["successful"] += 1
                        continue
                    
                    # Get options chain
                    options_chain = self._make_api_request(f"option-chain-equities", {"symbol": symbol})
                    
                    # Save the data
                    with open(output_file, 'w') as f:
                        json.dump(options_chain, f, indent=2)
                    
                    logger.info(f"Successfully collected options data for {symbol}")
                    results["successful"] += 1
                    
                    # Add a small delay to avoid rate limiting
                    time.sleep(1)
                
                except Exception as e:
                    logger.error(f"Error collecting options data for {symbol}: {str(e)}")
                    traceback.print_exc()
                    results["failed"] += 1
            
            logger.info(f"Completed collecting options data: {results['successful']} successful, {results['failed']} failed")
            return {"status": "success", **results}
        
        except Exception as e:
            logger.error(f"Error collecting options data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def collect_live_data(self, symbols: Optional[List[str]] = None, 
                        force_refresh: bool = False) -> Dict:
        """
        Collect live market data from NSE
        
        Parameters:
        -----------
        symbols : list, optional
            List of symbols to collect live data for (default: all symbols)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting live market data from NSE")
        
        try:
            # Get market status
            market_status = self._make_api_request("marketStatus")
            if "status" in market_status and market_status["status"] == "error":
                return market_status
            
            # Get market data
            market_data = self._make_api_request("market-data-pre-open")
            
            # Get equity market data
            equity_market = self._make_api_request("equity-market")
            
            # Combine all data
            live_data = {
                "market_status": market_status,
                "pre_open_market": market_data,
                "equity_market": equity_market,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # Save the data
            output_file = f"{self.data_dir}/live_data/market_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w') as f:
                json.dump(live_data, f, indent=2)
            
            # Also save the latest data
            latest_file = f"{self.data_dir}/live_data/latest_market.json"
            with open(latest_file, 'w') as f:
                json.dump(live_data, f, indent=2)
            
            logger.info("Successfully collected live market data")
            return {"status": "success", "message": "Successfully collected live market data"}
        
        except Exception as e:
            logger.error(f"Error collecting live market data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def export_to_csv(self, output_dir: Optional[str] = None) -> None:
        """
        Export all collected data to CSV files
        
        Parameters:
        -----------
        output_dir : str, optional
            Directory to save CSV files (default: data_dir/csv)
        """
        if pd is None:
            logger.error("pandas is required for CSV export")
            return
        
        if output_dir is None:
            output_dir = f"{self.data_dir}/csv"
        
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(f"{output_dir}/company_data", exist_ok=True)
        os.makedirs(f"{output_dir}/index_data", exist_ok=True)
        os.makedirs(f"{output_dir}/options_data", exist_ok=True)
        os.makedirs(f"{output_dir}/live_data", exist_ok=True)
        
        # Export company data
        company_files = [f for f in os.listdir(f"{self.data_dir}/company_data") if f.endswith('.json')]
        for file in company_files:
            try:
                symbol = file.replace('.json', '')
                with open(f"{self.data_dir}/company_data/{file}", 'r') as f:
                    company_data = json.load(f)
                
                # Extract key data for CSV
                info = company_data.get('info', {})
                metadata = company_data.get('metadata', {})
                financials = company_data.get('financials', {})
                
                # Create DataFrames for different sections
                if info:
                    pd.DataFrame([info]).to_csv(f"{output_dir}/company_data/{symbol}_info.csv", index=False)
                
                if metadata:
                    pd.DataFrame([metadata]).to_csv(f"{output_dir}/company_data/{symbol}_metadata.csv", index=False)
                
                if financials:
                    for section, data in financials.items():
                        if isinstance(data, list) and data:
                            pd.DataFrame(data).to_csv(f"{output_dir}/company_data/{symbol}_{section}.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting company data for {file}: {str(e)}")
                traceback.print_exc()
        
        # Export index data
        index_files = [f for f in os.listdir(f"{self.data_dir}/index_data") if f.endswith('.json')]
        for file in index_files:
            try:
                index = file.replace('.json', '')
                with open(f"{self.data_dir}/index_data/{file}", 'r') as f:
                    index_data = json.load(f)
                
                # Extract key data for CSV
                info = index_data.get('info', {})
                history = index_data.get('history', {})
                
                # Create DataFrames for different sections
                if info:
                    pd.DataFrame([info]).to_csv(f"{output_dir}/index_data/{index}_info.csv", index=False)
                
                if history:
                    pd.DataFrame(history).to_csv(f"{output_dir}/index_data/{index}_history.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting index data for {file}: {str(e)}")
                traceback.print_exc()
        
        # Export options data
        options_files = [f for f in os.listdir(f"{self.data_dir}/options_data") if f.endswith('.json')]
        for file in options_files:
            try:
                symbol = file.replace('.json', '')
                with open(f"{self.data_dir}/options_data/{file}", 'r') as f:
                    options_data = json.load(f)
                
                # Extract key data for CSV
                records = options_data.get('records', {})
                filtered_data = options_data.get('filtered', {})
                
                # Create DataFrames for different sections
                if records:
                    pd.DataFrame([records]).to_csv(f"{output_dir}/options_data/{symbol}_records.csv", index=False)
                
                if filtered_data and 'data' in filtered_data:
                    pd.DataFrame(filtered_data['data']).to_csv(f"{output_dir}/options_data/{symbol}_options.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting options data for {file}: {str(e)}")
                traceback.print_exc()
        
        # Export live data
        live_files = [f for f in os.listdir(f"{self.data_dir}/live_data") if f.endswith('.json') and f != 'latest_market.json']
        for file in live_files:
            try:
                timestamp = file.replace('market_', '').replace('.json', '')
                with open(f"{self.data_dir}/live_data/{file}", 'r') as f:
                    live_data = json.load(f)
                
                # Extract key data for CSV
                market_status = live_data.get('market_status', {})
                pre_open_market = live_data.get('pre_open_market', {})
                equity_market = live_data.get('equity_market', {})
                
                # Create DataFrames for different sections
                if market_status:
                    pd.DataFrame([market_status]).to_csv(f"{output_dir}/live_data/{timestamp}_market_status.csv", index=False)
                
                if pre_open_market and 'data' in pre_open_market:
                    pd.DataFrame(pre_open_market['data']).to_csv(f"{output_dir}/live_data/{timestamp}_pre_open.csv", index=False)
                
                if equity_market and 'data' in equity_market:
                    pd.DataFrame(equity_market['data']).to_csv(f"{output_dir}/live_data/{timestamp}_equity.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting live data for {file}: {str(e)}")
                traceback.print_exc()
        
        logger.info(f"Exported all NSE data to {output_dir}")
