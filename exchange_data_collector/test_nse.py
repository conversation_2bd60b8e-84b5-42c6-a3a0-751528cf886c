#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for NSE data collector
"""

import os
import json
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"logs/test_nse_{datetime.now().strftime('%Y%m%d')}.log")
    ]
)

logger = logging.getLogger(__name__)

# Import the NSE collector
from nse.nse_collector import NSEDataCollector

def test_company_data():
    """Test collecting company data"""
    logger.info("Testing company data collection")
    
    # Create the collector
    collector = NSEDataCollector()
    
    # Test with a single company
    result = collector.collect_company_data(symbols=["RELIANCE"])
    
    # Print the result
    print(f"Status: {result.get('status', 'unknown')}")
    print(f"Message: {result.get('message', '')}")
    print(f"Successful: {result.get('successful', 0)}")
    print(f"Failed: {result.get('failed', 0)}")
    
    # Check if the file was created
    file_path = "data/nse/company_data/RELIANCE.json"
    if os.path.exists(file_path):
        print(f"File created: {file_path}")
        
        # Read the file
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Print some data
        print(f"Company name: {data.get('info', {}).get('companyName', '')}")
    else:
        print(f"File not created: {file_path}")
    
    return result.get('status') == 'success'

def test_index_data():
    """Test collecting index data"""
    logger.info("Testing index data collection")
    
    # Create the collector
    collector = NSEDataCollector()
    
    # Test with a single index
    result = collector.collect_index_data(indices=["NIFTY50"])
    
    # Print the result
    print(f"Status: {result.get('status', 'unknown')}")
    print(f"Message: {result.get('message', '')}")
    print(f"Successful: {result.get('successful', 0)}")
    print(f"Failed: {result.get('failed', 0)}")
    
    # Check if the file was created
    file_path = "data/nse/index_data/NIFTY50.json"
    if os.path.exists(file_path):
        print(f"File created: {file_path}")
        
        # Read the file
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Print some data
        print(f"Index name: {data.get('info', {}).get('indexName', '')}")
    else:
        print(f"File not created: {file_path}")
    
    return result.get('status') == 'success'

def test_options_data():
    """Test collecting options data"""
    logger.info("Testing options data collection")
    
    # Create the collector
    collector = NSEDataCollector()
    
    # Test with a single company
    result = collector.collect_options_data(symbols=["RELIANCE"])
    
    # Print the result
    print(f"Status: {result.get('status', 'unknown')}")
    print(f"Message: {result.get('message', '')}")
    print(f"Successful: {result.get('successful', 0)}")
    print(f"Failed: {result.get('failed', 0)}")
    
    # Check if the file was created
    file_path = "data/nse/options_data/RELIANCE.json"
    if os.path.exists(file_path):
        print(f"File created: {file_path}")
        
        # Read the file
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Print some data
        print(f"Options data: {data.get('records', {}).get('expiryDates', [])}")
    else:
        print(f"File not created: {file_path}")
    
    return result.get('status') == 'success'

def test_live_data():
    """Test collecting live data"""
    logger.info("Testing live data collection")
    
    # Create the collector
    collector = NSEDataCollector()
    
    # Test live data collection
    result = collector.collect_live_data()
    
    # Print the result
    print(f"Status: {result.get('status', 'unknown')}")
    print(f"Message: {result.get('message', '')}")
    
    # Check if the file was created
    latest_file = "data/nse/live_data/latest_market.json"
    if os.path.exists(latest_file):
        print(f"File created: {latest_file}")
        
        # Read the file
        with open(latest_file, 'r') as f:
            data = json.load(f)
        
        # Print some data
        print(f"Market status: {data.get('market_status', {}).get('status', '')}")
    else:
        print(f"File not created: {latest_file}")
    
    return result.get('status') == 'success'

def test_csv_export():
    """Test exporting data to CSV"""
    logger.info("Testing CSV export")
    
    # Create the collector
    collector = NSEDataCollector()
    
    # Test CSV export
    collector.export_to_csv()
    
    # Check if CSV files were created
    csv_dir = "data/nse/csv"
    if os.path.exists(csv_dir):
        print(f"CSV directory created: {csv_dir}")
        
        # List CSV files
        csv_files = [f for f in os.listdir(csv_dir) if f.endswith('.csv')]
        print(f"CSV files: {csv_files}")
        
        return len(csv_files) > 0
    else:
        print(f"CSV directory not created: {csv_dir}")
        return False

def run_all_tests():
    """Run all tests"""
    logger.info("Running all tests")
    
    # Create necessary directories
    os.makedirs("logs", exist_ok=True)
    
    # Run tests
    tests = [
        ("Company Data", test_company_data),
        ("Index Data", test_index_data),
        ("Options Data", test_options_data),
        ("Live Data", test_live_data),
        ("CSV Export", test_csv_export)
    ]
    
    results = []
    
    for name, test_func in tests:
        print(f"\n=== Testing {name} ===\n")
        try:
            result = test_func()
            status = "✅ PASSED" if result else "❌ FAILED"
            results.append((name, status))
        except Exception as e:
            logger.error(f"Error in {name} test: {str(e)}")
            results.append((name, "❌ ERROR"))
    
    # Print summary
    print("\n=== Test Summary ===\n")
    for name, status in results:
        print(f"{status}: {name}")

if __name__ == "__main__":
    run_all_tests()
