#!/bin/bash

# Script to collect and update financial data from NSE and BSE
# Usage: 
#   ./collect_exchange_data.sh           # Collect all data (initial run)
#   ./collect_exchange_data.sh update    # Update existing data only
#   ./collect_exchange_data.sh force     # Force refresh all data
#   ./collect_exchange_data.sh nse       # Collect only NSE data
#   ./collect_exchange_data.sh bse       # Collect only BSE data

# Configuration
BATCH_SIZE=20         # Number of companies to process in each batch
MAX_WORKERS=3         # Number of parallel workers
DELAY_BETWEEN_CHUNKS=30  # Seconds to wait between chunks to avoid rate limiting

# Create necessary directories
mkdir -p logs
mkdir -p data/nse
mkdir -p data/bse
mkdir -p data/csv

# Get current date for logging
DATE=$(date +"%Y-%m-%d")
LOG_FILE="logs/exchange_data_collection_${DATE}.log"

# Function to log messages
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" | tee -a "$LOG_FILE"
}

# Function to collect NSE data
collect_nse_data() {
    local data_type=$1
    local force_refresh=$2
    
    log "Collecting $data_type data from NSE..."
    
    if [ "$force_refresh" = "true" ]; then
        python main.py nse $data_type --force-refresh >> "$LOG_FILE" 2>&1
    else
        python main.py nse $data_type >> "$LOG_FILE" 2>&1
    fi
    
    if [ $? -ne 0 ]; then
        log "ERROR: Failed to collect $data_type data from NSE"
        return 1
    fi
    
    log "NSE $data_type data collection completed"
    return 0
}

# Function to collect BSE data
collect_bse_data() {
    local data_type=$1
    local force_refresh=$2
    
    log "Collecting $data_type data from BSE..."
    
    if [ "$force_refresh" = "true" ]; then
        python main.py bse $data_type --force-refresh >> "$LOG_FILE" 2>&1
    else
        python main.py bse $data_type >> "$LOG_FILE" 2>&1
    fi
    
    if [ $? -ne 0 ]; then
        log "ERROR: Failed to collect $data_type data from BSE"
        return 1
    fi
    
    log "BSE $data_type data collection completed"
    return 0
}

# Function to collect all data from an exchange
collect_exchange_data() {
    local exchange=$1
    local force_refresh=$2
    
    log "Collecting all data from $exchange..."
    
    # Collect company data
    collect_${exchange}_data "company" "$force_refresh"
    
    # Collect index data
    collect_${exchange}_data "index" "$force_refresh"
    
    # Collect options data
    collect_${exchange}_data "options" "$force_refresh"
    
    # Collect live data
    collect_${exchange}_data "live" "$force_refresh"
    
    log "All $exchange data collection completed"
}

# Function to export data to CSV
export_to_csv() {
    log "Exporting data to CSV..."
    
    python main.py export >> "$LOG_FILE" 2>&1
    
    if [ $? -ne 0 ]; then
        log "ERROR: Failed to export data to CSV"
        return 1
    fi
    
    log "Data export completed"
    return 0
}

# Main execution
log "Starting exchange data collection process"

# Process command line arguments
FORCE_REFRESH="false"
EXCHANGE="all"

if [ "$1" = "force" ]; then
    FORCE_REFRESH="true"
    log "Force refresh mode enabled"
elif [ "$1" = "update" ]; then
    log "Update mode enabled"
elif [ "$1" = "nse" ]; then
    EXCHANGE="nse"
    log "NSE only mode enabled"
elif [ "$1" = "bse" ]; then
    EXCHANGE="bse"
    log "BSE only mode enabled"
else
    log "Full collection mode enabled"
fi

# Collect data based on the specified exchange
if [ "$EXCHANGE" = "all" ] || [ "$EXCHANGE" = "nse" ]; then
    collect_exchange_data "nse" "$FORCE_REFRESH"
fi

if [ "$EXCHANGE" = "all" ] || [ "$EXCHANGE" = "bse" ]; then
    collect_exchange_data "bse" "$FORCE_REFRESH"
fi

# Export data to CSV
export_to_csv

# Print summary
log "Exchange data collection process completed"
log "Data is available in:"
log "- NSE data: data/nse/"
log "- BSE data: data/bse/"
log "- CSV exports: data/csv/"

echo ""
echo "Exchange data collection completed successfully!"
echo "Check $LOG_FILE for details"
