#!/usr/bin/env python3
"""
Proxy Configuration and Management for NSE Data Collection
"""

import requests
import json
from typing import List, Dict, Tuple
import time

# High-quality proxy sources (you can add more)
PROXY_SOURCES = {
    "free_proxy_list": "https://free-proxy-list.net/",
    "proxy_list_download": "https://www.proxy-list.download/api/v1/get?type=http",
    "spys_one": "http://spys.one/en/free-proxy-list/",
}

# Manual high-quality proxies (update these regularly)
MANUAL_PROXIES = [
    # Format: (type, host, port, country, reliability_score)
    ('http', '***********', 80, 'CN', 8),
    ('http', '************', 8888, 'CN', 7),
    ('http', '***************', 80, 'IN', 9),
    ('http', '**************', 8080, 'IN', 8),
    ('http', '***********', 80, 'IN', 7),
    ('http', '*************', 55443, 'IN', 6),
    ('http', '************', 55443, 'IN', 6),
    ('http', '************', 55443, 'IN', 6),
]

# Paid proxy services (you can configure these if you have subscriptions)
PAID_PROXY_SERVICES = {
    "brightdata": {
        "endpoint": "http://brd-customer-{customer_id}-zone-{zone}:{password}@zproxy.lum-superproxy.io:22225",
        "requires_auth": True
    },
    "smartproxy": {
        "endpoint": "http://{username}:{password}@gate.smartproxy.com:10000",
        "requires_auth": True
    },
    "oxylabs": {
        "endpoint": "http://{username}:{password}@pr.oxylabs.io:7777",
        "requires_auth": True
    }
}

class ProxyManager:
    """Manage and test proxy connections"""
    
    def __init__(self):
        self.working_proxies = []
        self.failed_proxies = []
        
    def test_proxy_connection(self, proxy: Tuple, timeout: int = 10) -> Dict:
        """Test a single proxy connection"""
        proxy_type, host, port = proxy[:3]
        
        try:
            proxies = {
                "http": f"http://{host}:{port}",
                "https": f"http://{host}:{port}"
            }
            
            start_time = time.time()
            response = requests.get(
                "http://httpbin.org/ip",
                proxies=proxies,
                timeout=timeout,
                verify=False
            )
            
            latency = int((time.time() - start_time) * 1000)
            
            if response.status_code == 200:
                try:
                    ip_data = response.json()
                    return {
                        "status": "success",
                        "proxy": proxy,
                        "latency": latency,
                        "ip": ip_data.get("origin", "unknown")
                    }
                except:
                    return {
                        "status": "success",
                        "proxy": proxy,
                        "latency": latency,
                        "ip": "unknown"
                    }
            else:
                return {"status": "failed", "proxy": proxy, "error": f"HTTP {response.status_code}"}
                
        except requests.exceptions.Timeout:
            return {"status": "failed", "proxy": proxy, "error": "timeout"}
        except requests.exceptions.ConnectionError:
            return {"status": "failed", "proxy": proxy, "error": "connection_error"}
        except Exception as e:
            return {"status": "failed", "proxy": proxy, "error": str(e)}
    
    def get_working_proxies(self, max_proxies: int = 20) -> List[Tuple]:
        """Get a list of working proxies"""
        print("Testing manual proxy list...")
        
        working = []
        for proxy in MANUAL_PROXIES:
            if len(working) >= max_proxies:
                break
                
            result = self.test_proxy_connection(proxy)
            if result["status"] == "success":
                print(f"✅ {proxy[1]}:{proxy[2]} - {result['latency']}ms")
                working.append(proxy)
                self.working_proxies.append(result)
            else:
                print(f"❌ {proxy[1]}:{proxy[2]} - {result['error']}")
                self.failed_proxies.append(result)
            
            time.sleep(0.5)  # Be respectful
        
        return working
    
    def save_proxy_results(self, filename: str = "proxy_test_results.json"):
        """Save proxy test results to file"""
        results = {
            "timestamp": time.time(),
            "working_proxies": self.working_proxies,
            "failed_proxies": self.failed_proxies
        }
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"Proxy test results saved to {filename}")

def get_indian_proxies() -> List[Tuple]:
    """Get proxies specifically from India"""
    indian_proxies = [proxy for proxy in MANUAL_PROXIES if len(proxy) > 3 and proxy[3] == 'IN']
    return indian_proxies

def get_fast_proxies() -> List[Tuple]:
    """Get proxies with high reliability scores"""
    fast_proxies = [proxy for proxy in MANUAL_PROXIES if len(proxy) > 4 and proxy[4] >= 7]
    return sorted(fast_proxies, key=lambda x: x[4], reverse=True)

def test_nse_access_with_proxy(proxy: Tuple) -> bool:
    """Test NSE access specifically with a proxy"""
    proxy_type, host, port = proxy[:3]
    
    try:
        proxies = {
            "http": f"http://{host}:{port}",
            "https": f"http://{host}:{port}"
        }
        
        session = requests.Session()
        session.proxies = proxies
        
        # Test NSE homepage
        response = session.get(
            "https://www.nseindia.com/",
            timeout=15,
            verify=False,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
        )
        
        if response.status_code == 200:
            print(f"✅ NSE access successful with {host}:{port}")
            return True
        else:
            print(f"❌ NSE access failed with {host}:{port} - HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ NSE access failed with {host}:{port} - {str(e)[:50]}")
        return False

def main():
    """Test proxy configuration"""
    print("Proxy Configuration Manager")
    print("=" * 40)
    
    manager = ProxyManager()
    
    # Test manual proxies
    working_proxies = manager.get_working_proxies()
    
    print(f"\nFound {len(working_proxies)} working proxies")
    
    if working_proxies:
        print("\nTesting NSE access with working proxies...")
        for proxy in working_proxies[:5]:  # Test top 5
            test_nse_access_with_proxy(proxy)
            time.sleep(2)
    
    # Save results
    manager.save_proxy_results()
    
    # Show recommendations
    print("\n" + "=" * 40)
    print("RECOMMENDATIONS:")
    print("=" * 40)
    
    indian_proxies = get_indian_proxies()
    if indian_proxies:
        print(f"Indian proxies available: {len(indian_proxies)}")
        for proxy in indian_proxies:
            print(f"  - {proxy[1]}:{proxy[2]}")
    
    fast_proxies = get_fast_proxies()
    if fast_proxies:
        print(f"\nHigh-reliability proxies: {len(fast_proxies)}")
        for proxy in fast_proxies[:3]:
            print(f"  - {proxy[1]}:{proxy[2]} (score: {proxy[4]}/10)")
    
    print("\nFor better results, consider:")
    print("1. Using residential proxy services")
    print("2. VPN services with Indian servers")
    print("3. Paid proxy services like BrightData or Oxylabs")

if __name__ == "__main__":
    main()