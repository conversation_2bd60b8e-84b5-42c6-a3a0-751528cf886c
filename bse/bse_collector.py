#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
BSE Data Collector Module

This module collects data from the Bombay Stock Exchange (BSE) of India,
including company details, index data, options data, and live stock data.
"""

import os
import sys
import time
import json
import logging
import requests
from datetime import datetime
from typing import List, Dict, Optional, Union, Any
import traceback
from bs4 import BeautifulSoup
import re

# Try to import pandas, but don't fail if it's not available
try:
    import pandas as pd
except ImportError:
    pd = None

# Configure logging
logger = logging.getLogger(__name__)

class BSEDataCollector:
    """
    Class for collecting data from BSE
    """
    
    def __init__(self, data_dir: str = "data/bse"):
        """
        Initialize the BSE Data Collector
        
        Parameters:
        -----------
        data_dir : str
            Directory to store the collected data
        """
        self.data_dir = data_dir
        
        # Create data directories if they don't exist
        os.makedirs(f"{data_dir}/company_data", exist_ok=True)
        os.makedirs(f"{data_dir}/index_data", exist_ok=True)
        os.makedirs(f"{data_dir}/options_data", exist_ok=True)
        os.makedirs(f"{data_dir}/live_data", exist_ok=True)
        os.makedirs(f"{data_dir}/raw", exist_ok=True)
        
        # Base URLs
        self.base_url = "https://www.bseindia.com"
        self.api_url = "https://api.bseindia.com/BseIndiaAPI/api"
        
        # Headers to mimic a browser
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0"
        }
        
        # Session for making requests
        self.session = requests.Session()
        for key, value in self.headers.items():
            self.session.headers[key] = value
    
    def _make_api_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict:
        """
        Make a request to the BSE API
        
        Parameters:
        -----------
        endpoint : str
            API endpoint to request
        params : dict, optional
            Query parameters for the request
        
        Returns:
        --------
        Dictionary with API response
        """
        url = f"{self.api_url}/{endpoint}"
        
        try:
            # Make the API request
            response = self.session.get(url, params=params, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            # Save the raw response
            endpoint_name = endpoint.replace("/", "_")
            params_str = "_".join([f"{k}_{v}" for k, v in (params or {}).items()])
            raw_file = f"{self.data_dir}/raw/{endpoint_name}_{params_str}_{datetime.now().strftime('%Y%m%d')}.json"
            with open(raw_file, 'w') as f:
                f.write(response.text)
            
            # Parse and return the JSON response
            return response.json()
        
        except Exception as e:
            logger.error(f"Error making API request to {url}: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def _make_web_request(self, path: str, params: Optional[Dict] = None) -> str:
        """
        Make a request to the BSE website
        
        Parameters:
        -----------
        path : str
            Path to request
        params : dict, optional
            Query parameters for the request
        
        Returns:
        --------
        HTML response as string
        """
        url = f"{self.base_url}/{path}"
        
        try:
            # Make the web request
            response = self.session.get(url, params=params, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            # Save the raw response
            path_name = path.replace("/", "_")
            params_str = "_".join([f"{k}_{v}" for k, v in (params or {}).items()])
            raw_file = f"{self.data_dir}/raw/{path_name}_{params_str}_{datetime.now().strftime('%Y%m%d')}.html"
            with open(raw_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            return response.text
        
        except Exception as e:
            logger.error(f"Error making web request to {url}: {str(e)}")
            traceback.print_exc()
            return ""
    
    def collect_company_data(self, symbols: Optional[List[str]] = None, 
                           force_refresh: bool = False) -> Dict:
        """
        Collect company data from BSE
        
        Parameters:
        -----------
        symbols : list, optional
            List of symbols to collect data for (default: all symbols)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting company data from BSE")
        
        try:
            # Get the list of all symbols if not provided
            if symbols is None:
                # Get the list of all equities
                # BSE provides a CSV file with all listed securities
                response = self.session.get(f"{self.base_url}/markets/equity/EQReports/EquityMarketReports.aspx", headers=self.headers)
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Find the link to the CSV file
                csv_link = None
                for a in soup.find_all('a'):
                    if a.text and 'Download' in a.text and 'Equity' in a.text:
                        csv_link = a.get('href')
                        break
                
                if csv_link:
                    # Download the CSV file
                    csv_response = self.session.get(f"{self.base_url}/{csv_link}", headers=self.headers)
                    csv_file = f"{self.data_dir}/raw/equity_list_{datetime.now().strftime('%Y%m%d')}.csv"
                    with open(csv_file, 'wb') as f:
                        f.write(csv_response.content)
                    
                    # Parse the CSV file
                    if pd is not None:
                        df = pd.read_csv(csv_file)
                        symbols = df['Security Id'].tolist()
                    else:
                        # Fallback to a simple parsing if pandas is not available
                        with open(csv_file, 'r') as f:
                            lines = f.readlines()
                        # Skip header
                        symbols = [line.split(',')[0].strip() for line in lines[1:]]
                else:
                    logger.error("Could not find the CSV file link")
                    return {"status": "error", "message": "Could not find the CSV file link"}
            
            # Collect data for each symbol
            results = {"successful": 0, "failed": 0, "total": len(symbols)}
            
            for symbol in symbols:
                try:
                    # Check if data already exists
                    output_file = f"{self.data_dir}/company_data/{symbol}.json"
                    if os.path.exists(output_file) and not force_refresh:
                        logger.info(f"Data for {symbol} already exists, skipping")
                        results["successful"] += 1
                        continue
                    
                    # Get company info
                    company_info = self._make_api_request(f"CompanyInfo/GetCompanyInfo", {"scripcode": symbol})
                    
                    # Get company financials
                    company_financials = self._make_api_request(f"CompanyInfo/GetCompanyFinancials", {"scripcode": symbol})
                    
                    # Get company announcements
                    company_announcements = self._make_api_request(f"CompanyInfo/GetCompanyAnnouncements", {"scripcode": symbol})
                    
                    # Get company corporate actions
                    company_actions = self._make_api_request(f"CompanyInfo/GetCorporateActions", {"scripcode": symbol})
                    
                    # Combine all data
                    company_data = {
                        "info": company_info.get("data", {}),
                        "financials": company_financials.get("data", {}),
                        "announcements": company_announcements.get("data", {}),
                        "actions": company_actions.get("data", {}),
                        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # Save the data
                    with open(output_file, 'w') as f:
                        json.dump(company_data, f, indent=2)
                    
                    logger.info(f"Successfully collected data for {symbol}")
                    results["successful"] += 1
                    
                    # Add a small delay to avoid rate limiting
                    time.sleep(1)
                
                except Exception as e:
                    logger.error(f"Error collecting data for {symbol}: {str(e)}")
                    traceback.print_exc()
                    results["failed"] += 1
            
            logger.info(f"Completed collecting company data: {results['successful']} successful, {results['failed']} failed")
            return {"status": "success", **results}
        
        except Exception as e:
            logger.error(f"Error collecting company data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def collect_index_data(self, indices: Optional[List[str]] = None, 
                         force_refresh: bool = False) -> Dict:
        """
        Collect index data from BSE
        
        Parameters:
        -----------
        indices : list, optional
            List of indices to collect data for (default: all indices)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting index data from BSE")
        
        try:
            # Get the list of all indices if not provided
            if indices is None:
                # Get the list of all indices
                indices_html = self._make_web_request("sensex/indexinfo.aspx")
                soup = BeautifulSoup(indices_html, 'html.parser')
                
                # Find all indices
                indices = []
                for option in soup.select('select#ddlIndex option'):
                    index_value = option.get('value')
                    if index_value and index_value != '0':
                        indices.append(index_value)
            
            # Collect data for each index
            results = {"successful": 0, "failed": 0, "total": len(indices)}
            
            for index in indices:
                try:
                    # Check if data already exists
                    output_file = f"{self.data_dir}/index_data/{index}.json"
                    if os.path.exists(output_file) and not force_refresh:
                        logger.info(f"Data for {index} already exists, skipping")
                        results["successful"] += 1
                        continue
                    
                    # Get index info
                    index_info = self._make_api_request(f"IndexInfo/GetIndexInfo", {"index": index})
                    
                    # Get index constituents
                    index_constituents = self._make_api_request(f"IndexInfo/GetIndexConstituents", {"index": index})
                    
                    # Get index historical data
                    index_history = self._make_api_request(f"IndexInfo/GetIndexChartData", {"index": index})
                    
                    # Combine all data
                    index_data = {
                        "info": index_info.get("data", {}),
                        "constituents": index_constituents.get("data", {}),
                        "history": index_history.get("data", {}),
                        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # Save the data
                    with open(output_file, 'w') as f:
                        json.dump(index_data, f, indent=2)
                    
                    logger.info(f"Successfully collected data for {index}")
                    results["successful"] += 1
                    
                    # Add a small delay to avoid rate limiting
                    time.sleep(1)
                
                except Exception as e:
                    logger.error(f"Error collecting data for {index}: {str(e)}")
                    traceback.print_exc()
                    results["failed"] += 1
            
            logger.info(f"Completed collecting index data: {results['successful']} successful, {results['failed']} failed")
            return {"status": "success", **results}
        
        except Exception as e:
            logger.error(f"Error collecting index data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def collect_options_data(self, symbols: Optional[List[str]] = None, 
                           force_refresh: bool = False) -> Dict:
        """
        Collect options data from BSE
        
        Parameters:
        -----------
        symbols : list, optional
            List of symbols to collect options data for (default: all symbols with options)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting options data from BSE")
        
        try:
            # Get the list of all symbols with options if not provided
            if symbols is None:
                # Get the list of all symbols with options
                options_html = self._make_web_request("markets/equity/derivatives/derivatives.aspx")
                soup = BeautifulSoup(options_html, 'html.parser')
                
                # Find all symbols with options
                symbols = []
                for option in soup.select('select#ddlunderlyingval option'):
                    symbol_value = option.get('value')
                    if symbol_value and symbol_value != '0':
                        symbols.append(symbol_value)
            
            # Collect data for each symbol
            results = {"successful": 0, "failed": 0, "total": len(symbols)}
            
            for symbol in symbols:
                try:
                    # Check if data already exists
                    output_file = f"{self.data_dir}/options_data/{symbol}.json"
                    if os.path.exists(output_file) and not force_refresh:
                        logger.info(f"Options data for {symbol} already exists, skipping")
                        results["successful"] += 1
                        continue
                    
                    # Get options chain
                    options_chain = self._make_api_request(f"DerivativesInfo/GetOptionsChain", {"underlying": symbol})
                    
                    # Save the data
                    with open(output_file, 'w') as f:
                        json.dump(options_chain, f, indent=2)
                    
                    logger.info(f"Successfully collected options data for {symbol}")
                    results["successful"] += 1
                    
                    # Add a small delay to avoid rate limiting
                    time.sleep(1)
                
                except Exception as e:
                    logger.error(f"Error collecting options data for {symbol}: {str(e)}")
                    traceback.print_exc()
                    results["failed"] += 1
            
            logger.info(f"Completed collecting options data: {results['successful']} successful, {results['failed']} failed")
            return {"status": "success", **results}
        
        except Exception as e:
            logger.error(f"Error collecting options data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def collect_live_data(self, symbols: Optional[List[str]] = None, 
                        force_refresh: bool = False) -> Dict:
        """
        Collect live market data from BSE
        
        Parameters:
        -----------
        symbols : list, optional
            List of symbols to collect live data for (default: all symbols)
        force_refresh : bool
            If True, forces a refresh of all data even if it exists
        
        Returns:
        --------
        Dictionary with status information
        """
        logger.info("Collecting live market data from BSE")
        
        try:
            # Get market status
            market_status = self._make_api_request(f"MarketInfo/GetMarketStatus")
            
            # Get market data
            market_data = self._make_api_request(f"MarketInfo/GetMarketData")
            
            # Get top gainers and losers
            gainers_losers = self._make_api_request(f"MarketInfo/GetGainersLosers")
            
            # Combine all data
            live_data = {
                "market_status": market_status.get("data", {}),
                "market_data": market_data.get("data", {}),
                "gainers_losers": gainers_losers.get("data", {}),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # Save the data
            output_file = f"{self.data_dir}/live_data/market_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w') as f:
                json.dump(live_data, f, indent=2)
            
            # Also save the latest data
            latest_file = f"{self.data_dir}/live_data/latest_market.json"
            with open(latest_file, 'w') as f:
                json.dump(live_data, f, indent=2)
            
            logger.info("Successfully collected live market data")
            return {"status": "success", "message": "Successfully collected live market data"}
        
        except Exception as e:
            logger.error(f"Error collecting live market data: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}
    
    def export_to_csv(self, output_dir: Optional[str] = None) -> None:
        """
        Export all collected data to CSV files
        
        Parameters:
        -----------
        output_dir : str, optional
            Directory to save CSV files (default: data_dir/csv)
        """
        if pd is None:
            logger.error("pandas is required for CSV export")
            return
        
        if output_dir is None:
            output_dir = f"{self.data_dir}/csv"
        
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(f"{output_dir}/company_data", exist_ok=True)
        os.makedirs(f"{output_dir}/index_data", exist_ok=True)
        os.makedirs(f"{output_dir}/options_data", exist_ok=True)
        os.makedirs(f"{output_dir}/live_data", exist_ok=True)
        
        # Export company data
        company_files = [f for f in os.listdir(f"{self.data_dir}/company_data") if f.endswith('.json')]
        for file in company_files:
            try:
                symbol = file.replace('.json', '')
                with open(f"{self.data_dir}/company_data/{file}", 'r') as f:
                    company_data = json.load(f)
                
                # Extract key data for CSV
                info = company_data.get('info', {})
                financials = company_data.get('financials', {})
                announcements = company_data.get('announcements', {})
                
                # Create DataFrames for different sections
                if info:
                    pd.DataFrame([info]).to_csv(f"{output_dir}/company_data/{symbol}_info.csv", index=False)
                
                if financials:
                    for section, data in financials.items():
                        if isinstance(data, list) and data:
                            pd.DataFrame(data).to_csv(f"{output_dir}/company_data/{symbol}_{section}.csv", index=False)
                
                if announcements:
                    pd.DataFrame(announcements).to_csv(f"{output_dir}/company_data/{symbol}_announcements.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting company data for {file}: {str(e)}")
                traceback.print_exc()
        
        # Export index data
        index_files = [f for f in os.listdir(f"{self.data_dir}/index_data") if f.endswith('.json')]
        for file in index_files:
            try:
                index = file.replace('.json', '')
                with open(f"{self.data_dir}/index_data/{file}", 'r') as f:
                    index_data = json.load(f)
                
                # Extract key data for CSV
                info = index_data.get('info', {})
                constituents = index_data.get('constituents', {})
                history = index_data.get('history', {})
                
                # Create DataFrames for different sections
                if info:
                    pd.DataFrame([info]).to_csv(f"{output_dir}/index_data/{index}_info.csv", index=False)
                
                if constituents:
                    pd.DataFrame(constituents).to_csv(f"{output_dir}/index_data/{index}_constituents.csv", index=False)
                
                if history:
                    pd.DataFrame(history).to_csv(f"{output_dir}/index_data/{index}_history.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting index data for {file}: {str(e)}")
                traceback.print_exc()
        
        # Export options data
        options_files = [f for f in os.listdir(f"{self.data_dir}/options_data") if f.endswith('.json')]
        for file in options_files:
            try:
                symbol = file.replace('.json', '')
                with open(f"{self.data_dir}/options_data/{file}", 'r') as f:
                    options_data = json.load(f)
                
                # Extract key data for CSV
                data = options_data.get('data', {})
                
                # Create DataFrames for different sections
                if data:
                    pd.DataFrame(data).to_csv(f"{output_dir}/options_data/{symbol}_options.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting options data for {file}: {str(e)}")
                traceback.print_exc()
        
        # Export live data
        live_files = [f for f in os.listdir(f"{self.data_dir}/live_data") if f.endswith('.json') and f != 'latest_market.json']
        for file in live_files:
            try:
                timestamp = file.replace('market_', '').replace('.json', '')
                with open(f"{self.data_dir}/live_data/{file}", 'r') as f:
                    live_data = json.load(f)
                
                # Extract key data for CSV
                market_status = live_data.get('market_status', {})
                market_data = live_data.get('market_data', {})
                gainers_losers = live_data.get('gainers_losers', {})
                
                # Create DataFrames for different sections
                if market_status:
                    pd.DataFrame([market_status]).to_csv(f"{output_dir}/live_data/{timestamp}_market_status.csv", index=False)
                
                if market_data:
                    pd.DataFrame(market_data).to_csv(f"{output_dir}/live_data/{timestamp}_market_data.csv", index=False)
                
                if gainers_losers:
                    gainers = gainers_losers.get('gainers', [])
                    losers = gainers_losers.get('losers', [])
                    
                    if gainers:
                        pd.DataFrame(gainers).to_csv(f"{output_dir}/live_data/{timestamp}_gainers.csv", index=False)
                    
                    if losers:
                        pd.DataFrame(losers).to_csv(f"{output_dir}/live_data/{timestamp}_losers.csv", index=False)
            
            except Exception as e:
                logger.error(f"Error exporting live data for {file}: {str(e)}")
                traceback.print_exc()
        
        logger.info(f"Exported all BSE data to {output_dir}")
