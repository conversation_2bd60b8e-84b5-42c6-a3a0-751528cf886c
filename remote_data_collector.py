#!/usr/bin/env python3
"""
Remote Data Collector for Indian Stock Market Data
This script is designed to run on a system in India with direct NSE access
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import time
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('remote_data_collector')

class RemoteDataCollector:
    """
    Data collector designed to run on Indian systems with direct NSE access
    """
    
    def __init__(self, output_dir='collected_data'):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        }
        
        # Check available sources
        self.available_sources = self._check_sources()
        
    def _check_sources(self):
        """Check which data sources are available"""
        sources = []
        
        # Check NSEPy
        try:
            import nsepy
            sources.append('nsepy')
            logger.info("NSEPy available")
        except ImportError:
            logger.warning("NSEPy not available")
            
        # Check Jugaad-data
        try:
            import jugaad_data
            sources.append('jugaad_data')
            logger.info("Jugaad-data available")
        except ImportError:
            logger.warning("Jugaad-data not available")
            
        # Check Yahoo Finance
        try:
            import yfinance
            sources.append('yfinance')
            logger.info("Yahoo Finance available")
        except ImportError:
            logger.warning("Yahoo Finance not available")
            
        return sources
    
    def collect_with_nsepy(self, symbols, start_date, end_date):
        """Collect data using NSEPy"""
        try:
            from nsepy import get_history
            data = {}
            
            for symbol in symbols:
                try:
                    logger.info(f"Fetching {symbol} with NSEPy...")
                    df = get_history(symbol=symbol, start=start_date, end=end_date)
                    
                    if not df.empty:
                        # Standardize column names
                        df = df.rename(columns={
                            'Open': 'Open',
                            'High': 'High', 
                            'Low': 'Low',
                            'Close': 'Close',
                            'Volume': 'Volume'
                        })
                        data[symbol] = df
                        logger.info(f"Successfully fetched {len(df)} days for {symbol}")
                    else:
                        logger.warning(f"No data for {symbol}")
                        
                    time.sleep(0.5)  # Be respectful
                    
                except Exception as e:
                    logger.error(f"Error fetching {symbol} with NSEPy: {e}")
                    
            return data
            
        except Exception as e:
            logger.error(f"NSEPy collection failed: {e}")
            return {}
    
    def collect_with_jugaad(self, symbols, start_date, end_date):
        """Collect data using Jugaad-data"""
        try:
            from jugaad_data.nse import stock_df
            data = {}
            
            for symbol in symbols:
                try:
                    logger.info(f"Fetching {symbol} with Jugaad-data...")
                    df = stock_df(symbol=symbol, from_date=start_date, to_date=end_date, series="EQ")
                    
                    if not df.empty:
                        # Set date as index
                        df['DATE'] = pd.to_datetime(df['DATE'])
                        df.set_index('DATE', inplace=True)
                        
                        # Standardize column names
                        df = df.rename(columns={
                            'OPEN': 'Open',
                            'HIGH': 'High',
                            'LOW': 'Low', 
                            'CLOSE': 'Close',
                            'VOLUME': 'Volume'
                        })
                        
                        data[symbol] = df[['Open', 'High', 'Low', 'Close', 'Volume']]
                        logger.info(f"Successfully fetched {len(df)} days for {symbol}")
                    else:
                        logger.warning(f"No data for {symbol}")
                        
                    time.sleep(0.5)  # Be respectful
                    
                except Exception as e:
                    logger.error(f"Error fetching {symbol} with Jugaad-data: {e}")
                    
            return data
            
        except Exception as e:
            logger.error(f"Jugaad-data collection failed: {e}")
            return {}
    
    def collect_with_yfinance(self, symbols, start_date, end_date):
        """Collect data using Yahoo Finance"""
        try:
            import yfinance as yf
            data = {}
            
            for symbol in symbols:
                try:
                    # Try both .NS and .BO suffixes
                    for suffix in ['.NS', '.BO']:
                        ticker_symbol = f"{symbol}{suffix}"
                        logger.info(f"Fetching {ticker_symbol} with Yahoo Finance...")
                        
                        ticker = yf.Ticker(ticker_symbol)
                        df = ticker.history(start=start_date, end=end_date)
                        
                        if not df.empty:
                            data[symbol] = df[['Open', 'High', 'Low', 'Close', 'Volume']]
                            logger.info(f"Successfully fetched {len(df)} days for {symbol}")
                            break
                    else:
                        logger.warning(f"No data for {symbol}")
                        
                    time.sleep(0.5)  # Be respectful
                    
                except Exception as e:
                    logger.error(f"Error fetching {symbol} with Yahoo Finance: {e}")
                    
            return data
            
        except Exception as e:
            logger.error(f"Yahoo Finance collection failed: {e}")
            return {}
    
    def collect_data(self, symbols=None, days_back=365*3):
        """
        Main data collection function
        
        Parameters:
        -----------
        symbols : list, optional
            List of symbols to collect. If None, uses default list
        days_back : int, optional
            Number of days back to collect data
        """
        
        if symbols is None:
            symbols = [
                'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'HINDUNILVR',
                'ICICIBANK', 'SBIN', 'BHARTIARTL', 'BAJFINANCE', 'KOTAKBANK',
                'HCLTECH', 'ASIANPAINT', 'AXISBANK', 'MARUTI', 'SUNPHARMA',
                'TATAMOTORS', 'TITAN', 'BAJAJFINSV', 'WIPRO', 'ADANIPORTS'
            ]
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        logger.info(f"Collecting data for {len(symbols)} symbols from {start_date.date()} to {end_date.date()}")
        
        all_data = {}
        
        # Try each source in order
        for source in self.available_sources:
            if source == 'nsepy':
                data = self.collect_with_nsepy(symbols, start_date, end_date)
            elif source == 'jugaad_data':
                data = self.collect_with_jugaad(symbols, start_date, end_date)
            elif source == 'yfinance':
                data = self.collect_with_yfinance(symbols, start_date, end_date)
            else:
                continue
                
            # Add successful data to all_data
            for symbol, df in data.items():
                if symbol not in all_data and len(df) >= 252:  # At least 1 year of data
                    all_data[symbol] = df
                    
            # Remove successfully collected symbols
            symbols = [s for s in symbols if s not in all_data]
            
            if not symbols:
                break
                
        # Save collected data
        self.save_data(all_data)
        
        return all_data
    
    def save_data(self, data):
        """Save collected data to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save individual stock files
        for symbol, df in data.items():
            filename = f"{self.output_dir}/{symbol}_{timestamp}.csv"
            df.to_csv(filename)
            logger.info(f"Saved {symbol} data to {filename}")
        
        # Save combined data
        if data:
            combined_filename = f"{self.output_dir}/combined_data_{timestamp}.json"
            
            # Convert to JSON-serializable format
            json_data = {}
            for symbol, df in data.items():
                json_data[symbol] = {
                    'data': df.to_dict('records'),
                    'index': df.index.strftime('%Y-%m-%d').tolist(),
                    'shape': df.shape,
                    'start_date': df.index.min().strftime('%Y-%m-%d'),
                    'end_date': df.index.max().strftime('%Y-%m-%d')
                }
            
            with open(combined_filename, 'w') as f:
                json.dump(json_data, f, indent=2, default=str)
            
            logger.info(f"Saved combined data to {combined_filename}")
        
        # Save metadata
        metadata = {
            'collection_time': datetime.now().isoformat(),
            'symbols_collected': list(data.keys()),
            'total_symbols': len(data),
            'available_sources': self.available_sources,
            'data_summary': {
                symbol: {
                    'rows': len(df),
                    'start_date': df.index.min().strftime('%Y-%m-%d'),
                    'end_date': df.index.max().strftime('%Y-%m-%d')
                }
                for symbol, df in data.items()
            }
        }
        
        metadata_filename = f"{self.output_dir}/metadata_{timestamp}.json"
        with open(metadata_filename, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Saved metadata to {metadata_filename}")

def main():
    """Main function"""
    collector = RemoteDataCollector()
    
    # Collect data for default symbols
    data = collector.collect_data()
    
    print(f"\nCollection Summary:")
    print(f"Successfully collected data for {len(data)} symbols")
    
    for symbol, df in data.items():
        print(f"{symbol}: {len(df)} days ({df.index.min().date()} to {df.index.max().date()})")

if __name__ == "__main__":
    main()
