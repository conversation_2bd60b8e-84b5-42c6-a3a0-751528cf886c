# DCF Analysis Tool

A comprehensive Discounted Cash Flow (DCF) analysis tool for valuing companies based on their financial data.

## Overview

This tool performs a complete DCF analysis by:
1. Collecting financial data from sources like screener.in
2. Analyzing historical financial performance
3. Projecting future cash flows
4. Calculating the present value of these cash flows
5. Determining the intrinsic value of the company

## Components

- **Data Collection**: Scrapes financial data from screener.in
- **Financial Analysis**: Analyzes historical financial performance
- **DCF Model**: Projects future cash flows and calculates present value
- **Sensitivity Analysis**: Tests how changes in key assumptions affect valuation
- **Visualization**: Presents results in an easy-to-understand format
- **Report Generation**: Creates comprehensive PDF reports
- **Batch Analysis**: Analyzes multiple companies at once
- **Web Interface**: Provides a user-friendly web-based interface
- **Machine Learning**: Uses ML to improve forecasting accuracy

## Installation

```bash
# Clone the repository
git clone <repository-url>

# Navigate to the project directory
cd dcf

# Install required packages
pip install -r requirements.txt
```

## Usage Examples

### 1. Collect Financial Data

To collect financial data for a company (e.g., TATAMOTORS):

```bash
python fetch_tatamotors.py
```

This will scrape financial data from screener.in and save it locally.

### 2. Run Financial Analysis and DCF Valuation

To analyze the financial data and perform a DCF valuation:

```bash
python analyze_tatamotors.py
```

This will:
- Load the collected financial data
- Analyze historical performance
- Project future cash flows
- Calculate the present value
- Determine the intrinsic value of the company

### 3. Run Sensitivity Analysis

To perform sensitivity analysis on key assumptions:

```bash
python sensitivity_analysis.py --symbol TATAMOTORS
```

This will generate sensitivity tables and charts showing how changes in key assumptions affect the valuation.

### 4. Generate Comprehensive Report

To generate a comprehensive report with all analysis results:

```bash
python generate_report.py --symbol TATAMOTORS
```

This will create a detailed PDF report with all analysis results, charts, and investment recommendations.

## Example Output

### Financial Analysis Summary

```
Financial Analysis Summary for Tata Motors Ltd:
Latest Revenue: ₹43,969.50 Cr
Latest Net Profit: ₹2,198.48 Cr
Latest Profit Margin: 5.00%
Latest Free Cash Flow: ₹3,517.56 Cr
Latest FCF Margin: 8.00%
5-Year Average Revenue Growth: 11.68%
5-Year Average Profit Growth: 15.32%
5-Year Average FCF Growth: 12.45%
Return on Equity (ROE): 12.75%
Debt-to-Equity Ratio: 1.23
```

### DCF Valuation Summary

```
DCF Valuation Summary:
Enterprise Value: ₹77,020.67 Cr
Equity Value: ₹77,020.67 Cr
Per Share Value: ₹233.40
Terminal Value % of Total: 55.03%
```

### Terminal Value Calculation

The DCF model calculates terminal value using two methods:

1. **Perpetuity Growth Method**: Assumes the company will continue to grow at a constant rate forever.
   - Uses a hybrid approach combining historical FCF and terminal FCF margin
   - Validates terminal growth rate against long-term GDP growth
   - Ensures terminal growth rate is less than discount rate

2. **Exit Multiple Method**: Assumes the company will be valued at a multiple of its terminal year's FCF.
   - Suggests appropriate multiples based on growth rate
   - Adjusts multiples for low, medium, and high growth companies
   - Validates multiples against reasonable ranges

3. **Hybrid Approach**: Combines both methods with customizable weights.
   - Default is 50% perpetuity growth and 50% exit multiple
   - Weights can be adjusted based on company characteristics

### WACC Calculation

The Weighted Average Cost of Capital (WACC) is calculated using:

1. **Cost of Equity**: Calculated using the Capital Asset Pricing Model (CAPM)
   - Uses industry-specific betas for more accurate estimates
   - Includes size premium for smaller companies
   - Accounts for country risk premium for emerging markets

2. **Cost of Debt**: Calculated based on the company's interest expenses and debt
   - Uses average interest rates over multiple years for stability
   - Adjusts for current interest rate environment
   - Considers credit spread based on interest coverage ratio

3. **Capital Structure**: Determines the weights of equity and debt
   - Calculates weights based on market values when available
   - Ensures weights sum to 100%

### Sensitivity Analysis

| Growth Rate | WACC 10% | WACC 12% | WACC 14% |
|-------------|----------|----------|----------|
| 8%          | ₹280.08  | ₹233.40  | ₹198.39  |
| 10%         | ₹303.42  | ₹252.85  | ₹214.92  |
| 12%         | ₹329.21  | ₹274.34  | ₹233.19  |

## Advanced Features

### Custom Assumptions

You can customize key assumptions for the DCF analysis:

```bash
python analyze_tatamotors.py --growth-rate 0.15 --discount-rate 0.10 --terminal-growth 0.03
```

### Batch Analysis

Analyze multiple companies at once:

```bash
python batch_analysis.py --symbols TATAMOTORS,RELIANCE,TCS
```

Additional options for batch analysis:

```bash
# Fetch data if missing (default: false - will not fetch data if missing)
python batch_analysis.py --symbols TATAMOTORS,RELIANCE,TCS --fetch-if-missing

# Skip companies with missing data instead of failing
python batch_analysis.py --symbols TATAMOTORS,RELIANCE,TCS --skip-missing

# Run analyses in parallel
python batch_analysis.py --symbols TATAMOTORS,RELIANCE,TCS --parallel

# Combine options
python batch_analysis.py --symbols TATAMOTORS,RELIANCE,TCS --fetch-if-missing --parallel
```

By default, the system will not fetch data if it already exists. It will only use the existing data. If you want to force a refresh of the data, you can use the fetch_company_data.py script directly:

```bash
python fetch_company_data.py --symbol TATAMOTORS --force-update
```

### Comparative Analysis

Compare DCF valuations across multiple companies:

```bash
python batch_analysis.py --symbols TATAMOTORS,RELIANCE,TCS
```

The comparative analysis generates charts comparing:
- Per share values
- Growth rates vs. WACC
- FCF margins

### Web-based Interface

Launch the web-based user interface:

```bash
streamlit run dcf_web_app.py
```

### Machine Learning Forecasting

Use machine learning to improve forecasting accuracy:

```bash
python ml_forecasting.py --symbol TATAMOTORS
```

## File Structure

```
dcf/
├── data/                  # Directory for storing financial data
├── results/               # Directory for storing analysis results
├── screener_scraper.py    # Data collection module
├── financial_analyzer.py  # Financial analysis module
├── dcf_model.py           # DCF valuation module
├── sensitivity_analysis.py # Sensitivity analysis module
├── wacc_calculator.py     # WACC calculation module
├── visualization.py       # Visualization module
├── ml_forecasting.py      # Machine learning forecasting module
├── batch_analysis.py      # Batch analysis module
├── dcf_web_app.py         # Web-based user interface
├── fetch_tatamotors.py    # Script to fetch TATAMOTORS data
├── analyze_tatamotors.py  # Script to analyze TATAMOTORS
└── generate_report.py     # Script to generate comprehensive report
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
