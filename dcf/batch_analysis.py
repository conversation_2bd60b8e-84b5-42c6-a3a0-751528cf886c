#!/usr/bin/env python3
"""
Batch DCF Analysis

This script performs DCF analysis on multiple companies at once and
provides comparative analysis of the results.
"""

import os
import sys
import json
import logging
import traceback
import argparse
import subprocess
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional
import concurrent.futures

from financial_analyzer import FinancialAnalyzer
from dcf_model import DCFModel
from wacc_calculator import WACCCalculator
from sensitivity_analysis import SensitivityAnalyzer
from visualization import Visualizer

# Try to import optional dependencies
try:
    import pandas as pd
    import matplotlib.pyplot as plt
    import seaborn as sns
    HAS_PLOTTING = True
except ImportError:
    pd = None
    plt = None
    sns = None
    HAS_PLOTTING = False
    print("Warning: pandas, matplotlib, or seaborn not available. Plotting functionality will be limited.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('batch_analysis')

def analyze_company(symbol: str, data_dir: str, results_dir: str, projection_years: int = 10, fetch_if_missing: bool = True) -> Dict:
    """
    Analyze a single company

    Parameters:
    -----------
    symbol : str
        Company symbol
    data_dir : str
        Data directory
    results_dir : str
        Results directory
    projection_years : int
        Number of years to project cash flows
    fetch_if_missing : bool
        Whether to fetch data if it's missing

    Returns:
    --------
    Dictionary with analysis results
    """
    try:
        logger.info(f"Analyzing {symbol}")

        # Step 1: Financial Analysis
        analyzer = FinancialAnalyzer(data_dir=data_dir)

        # Load company data
        data = analyzer.load_company_data(symbol)

        # If data is missing and fetch_if_missing is True, try to fetch it
        if not data and fetch_if_missing:
            logger.info(f"Data for {symbol} not found. Attempting to fetch...")

            # Check if fetch script exists for this symbol
            fetch_script = f"fetch_{symbol.lower()}.py"
            generic_fetch_script = "fetch_company_data.py"

            if os.path.exists(fetch_script):
                # Run the symbol-specific fetch script
                logger.info(f"Running {fetch_script}...")
                try:
                    subprocess.run([sys.executable, fetch_script], check=True)
                    # Try to load data again
                    data = analyzer.load_company_data(symbol)
                except subprocess.CalledProcessError as e:
                    logger.error(f"Error running {fetch_script}: {str(e)}")
            elif os.path.exists(generic_fetch_script):
                # Run the generic fetch script with the symbol as an argument
                logger.info(f"Running {generic_fetch_script} for {symbol}...")
                try:
                    subprocess.run([sys.executable, generic_fetch_script, "--symbol", symbol], check=True)
                    # Try to load data again
                    data = analyzer.load_company_data(symbol)
                except subprocess.CalledProcessError as e:
                    logger.error(f"Error running {generic_fetch_script}: {str(e)}")

        if not data:
            logger.error(f"Failed to load data for {symbol}")
            return {"symbol": symbol, "status": "error", "message": "Failed to load data"}

        # Analyze financials
        analysis = analyzer.analyze_financials(symbol)
        if not analysis:
            logger.error(f"Failed to analyze financials for {symbol}")
            return {"symbol": symbol, "status": "error", "message": "Failed to analyze financials"}

        # Save analysis results
        analysis_file = os.path.join(results_dir, f"{symbol}_analysis.json")
        with open(analysis_file, 'w') as f:
            json.dump(analysis, f, indent=2)

        # Step 2: WACC Calculation
        wacc_calculator = WACCCalculator(data_dir=data_dir)
        wacc_results = wacc_calculator.calculate_wacc(symbol, data)

        # Step 3: DCF Valuation
        dcf_model = DCFModel(projection_years=projection_years)

        # Get DCF inputs from analysis
        dcf_inputs = analysis.get('dcf_inputs', {})

        # Use calculated WACC
        dcf_inputs['discount_rate'] = wacc_results['wacc']

        # If shares outstanding is not calculated, estimate it from market cap
        if 'shares_outstanding' not in dcf_inputs:
            logger.warning(f"Shares outstanding not found in DCF inputs for {symbol}. Using fallback calculation.")
            # Try to estimate from equity capital if available
            if 'equity_capital' in data.get('balance_sheet', {}):
                latest_year = max(data['balance_sheet']['equity_capital'].keys())
                equity_capital = data['balance_sheet']['equity_capital'][latest_year]
                # Assuming face value of Rs. 10 per share (common for Indian companies)
                estimated_shares = equity_capital * 100000  # Convert crores to number of shares
                dcf_inputs['shares_outstanding'] = estimated_shares
                logger.info(f"Estimated shares outstanding from equity capital: {estimated_shares:,.0f}")
            else:
                # Default fallback
                logger.warning(f"Using default shares outstanding for {symbol}")
                dcf_inputs['shares_outstanding'] = 400000000  # Default fallback

        # Run DCF valuation
        dcf_results = dcf_model.run_dcf_valuation(dcf_inputs)

        # Save DCF results
        dcf_file = os.path.join(results_dir, f"{symbol}_dcf.json")
        with open(dcf_file, 'w') as f:
            json.dump(dcf_results, f, indent=2)

        # Step 4: Sensitivity Analysis
        sensitivity_analyzer = SensitivityAnalyzer(dcf_model, results_dir=results_dir)
        sensitivity_results = sensitivity_analyzer.run_sensitivity_analysis(symbol, dcf_inputs)

        # Save sensitivity results
        sensitivity_file = os.path.join(results_dir, f"{symbol}_sensitivity.json")
        with open(sensitivity_file, 'w') as f:
            json.dump(sensitivity_results, f, indent=2)

        # Return summary results
        summary = {
            "symbol": symbol,
            "status": "success",
            "company_name": analysis.get('company_name', symbol),
            "valuation_date": dcf_results.get('valuation_date', datetime.now().strftime("%Y-%m-%d")),
            "per_share_value": dcf_results.get('valuation_summary', {}).get('per_share_value', 0),
            "enterprise_value": dcf_results.get('valuation_summary', {}).get('enterprise_value', 0),
            "wacc": wacc_results.get('wacc', 0),
            "terminal_growth_rate": dcf_inputs.get('terminal_growth_rate', 0),
            "revenue_growth_years_1_5": dcf_inputs.get('revenue_growth_years_1_5', 0),
            "fcf_margin_years_1_5": dcf_inputs.get('fcf_margin_years_1_5', 0),
            "base_fcf_margin": dcf_inputs.get('base_fcf_margin', 0),
            "is_bank": dcf_inputs.get('is_bank', False)
        }

        return summary

    except Exception as e:
        logger.error(f"Error analyzing {symbol}: {str(e)}")
        traceback.print_exc()
        return {"symbol": symbol, "status": "error", "message": str(e)}

def run_comparative_analysis(results: List[Dict], results_dir: str) -> Dict:
    """
    Run comparative analysis on multiple companies

    Parameters:
    -----------
    results : list
        List of company analysis results
    results_dir : str
        Results directory

    Returns:
    --------
    Dictionary with comparative analysis results
    """
    if not HAS_PLOTTING:
        logger.warning("Plotting libraries not available. Skipping comparative charts.")
        return {"status": "warning", "message": "Plotting libraries not available"}

    try:
        # Create a DataFrame from results
        df = pd.DataFrame(results)

        # Filter out failed analyses
        df = df[df['status'] == 'success']

        if len(df) == 0:
            logger.warning("No successful analyses to compare")
            return {"status": "warning", "message": "No successful analyses to compare"}

        # Create comparative charts directory
        charts_dir = os.path.join(results_dir, 'comparative_charts')
        os.makedirs(charts_dir, exist_ok=True)

        # 1. Valuation Comparison Chart
        plt.figure(figsize=(12, 8))
        ax = sns.barplot(x='symbol', y='per_share_value', data=df)
        plt.title('DCF Valuation Comparison', fontsize=16)
        plt.xlabel('Company', fontsize=14)
        plt.ylabel('Per Share Value (₹)', fontsize=14)
        plt.xticks(rotation=45)

        # Add value labels on top of bars
        for i, v in enumerate(df['per_share_value']):
            ax.text(i, v + 5, f"₹{v:.2f}", ha='center', fontsize=12)

        plt.tight_layout()
        valuation_chart = os.path.join(charts_dir, 'valuation_comparison.png')
        plt.savefig(valuation_chart, dpi=300)
        plt.close()

        # 2. Growth vs. WACC Chart
        plt.figure(figsize=(12, 8))
        sns.scatterplot(x='wacc', y='revenue_growth_years_1_5', size='per_share_value',
                        sizes=(100, 1000), alpha=0.7, data=df)

        # Add company labels
        for i, row in df.iterrows():
            plt.text(row['wacc'], row['revenue_growth_years_1_5'], row['symbol'],
                    fontsize=12, ha='center', va='center')

        plt.title('Growth Rate vs. WACC Comparison', fontsize=16)
        plt.xlabel('WACC', fontsize=14)
        plt.ylabel('Revenue Growth Rate (Years 1-5)', fontsize=14)
        plt.grid(True, linestyle='--', alpha=0.7)

        plt.tight_layout()
        growth_wacc_chart = os.path.join(charts_dir, 'growth_wacc_comparison.png')
        plt.savefig(growth_wacc_chart, dpi=300)
        plt.close()

        # 3. FCF Margin Comparison
        plt.figure(figsize=(12, 8))

        # Create a new column for display that shows the appropriate FCF margin
        # For banks, we use a different calculation method
        def get_display_margin(row):
            if row.get('is_bank', False):
                # For banks, use the bank-specific FCF margin
                return row.get('base_fcf_margin', 0.15)  # Default to 15% if not available
            else:
                # For non-banks, use the standard FCF margin
                return row.get('fcf_margin_years_1_5', 0.08)  # Default to 8% if not available

        df['display_fcf_margin'] = df.apply(get_display_margin, axis=1)

        # Create a new figure with a specific size
        plt.figure(figsize=(12, 8))

        # Create a bar plot manually to avoid seaborn warnings
        bar_positions = np.arange(len(df))
        colors = ['#1f77b4' if not row.get('is_bank', False) else '#ff7f0e' for _, row in df.iterrows()]

        # Create the bars
        bars = plt.bar(bar_positions, df['display_fcf_margin'], color=colors)

        # Set labels and title
        plt.title('FCF Margin Comparison', fontsize=16)
        plt.xlabel('Company', fontsize=14)
        plt.ylabel('FCF Margin', fontsize=14)
        plt.xticks(bar_positions, df['symbol'], rotation=45)

        # Add percentage labels on top of bars
        for i, row in enumerate(df.itertuples()):
            v = row.display_fcf_margin
            if not np.isnan(v):  # Check if the value is not NaN
                plt.text(i, v + 0.01, f"{v*100:.1f}%", ha='center', fontsize=12)

        # Add bank indicator for financial institutions
        for i, row in enumerate(df.itertuples()):
            if row.is_bank:
                if not np.isnan(row.display_fcf_margin):  # Check if the value is not NaN
                    plt.text(i, row.display_fcf_margin/2, "Bank", ha='center', fontsize=10, color='white', weight='bold')

        # Add legend
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='#1f77b4', label='Non-Bank'),
            Patch(facecolor='#ff7f0e', label='Bank')
        ]
        plt.legend(handles=legend_elements, loc='upper right')

        plt.tight_layout()
        margin_chart = os.path.join(charts_dir, 'fcf_margin_comparison.png')
        plt.savefig(margin_chart, dpi=300)
        plt.close()

        # Save comparative analysis results
        comparative_results = {
            "status": "success",
            "analysis_date": datetime.now().strftime("%Y-%m-%d"),
            "companies_analyzed": len(df),
            "charts": {
                "valuation_comparison": valuation_chart,
                "growth_wacc_comparison": growth_wacc_chart,
                "fcf_margin_comparison": margin_chart
            },
            "data": df.to_dict(orient='records')
        }

        comparative_file = os.path.join(results_dir, 'comparative_analysis.json')
        with open(comparative_file, 'w') as f:
            json.dump(comparative_results, f, indent=2)

        return comparative_results

    except Exception as e:
        logger.error(f"Error in comparative analysis: {str(e)}")
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

def main():
    """
    Main function to run batch DCF analysis
    """
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run batch DCF analysis for multiple companies')
    parser.add_argument('--symbols', type=str, required=True,
                        help='Comma-separated list of company symbols (e.g., TATAMOTORS,RELIANCE,TCS)')
    parser.add_argument('--data-dir', type=str, default='data', help='Data directory')
    parser.add_argument('--results-dir', type=str, default='results', help='Results directory')
    parser.add_argument('--projection-years', type=int, default=10, help='Number of years to project cash flows')
    parser.add_argument('--parallel', action='store_true', help='Run analyses in parallel')
    parser.add_argument('--max-workers', type=int, default=4, help='Maximum number of parallel workers')
    parser.add_argument('--fetch-if-missing', action='store_true', help='Fetch data if missing')
    parser.add_argument('--skip-missing', action='store_true', help='Skip companies with missing data instead of failing')
    args = parser.parse_args()

    try:
        # Create directories
        os.makedirs(args.data_dir, exist_ok=True)
        os.makedirs(args.results_dir, exist_ok=True)

        # Parse symbols
        symbols = [s.strip() for s in args.symbols.split(',')]

        if not symbols:
            logger.error("No symbols provided")
            return

        logger.info(f"Running batch analysis for {len(symbols)} companies: {', '.join(symbols)}")

        results = []

        # Run analyses
        if args.parallel and len(symbols) > 1:
            # Run in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=min(args.max_workers, len(symbols))) as executor:
                future_to_symbol = {
                    executor.submit(
                        analyze_company,
                        symbol,
                        args.data_dir,
                        args.results_dir,
                        args.projection_years,
                        args.fetch_if_missing
                    ): symbol for symbol in symbols
                }

                for future in concurrent.futures.as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        result = future.result()

                        # If skip_missing is True and the error is about missing data, skip this company
                        if args.skip_missing and result['status'] == 'error' and 'Failed to load data' in result.get('message', ''):
                            logger.warning(f"Skipping {symbol} due to missing data")
                            continue

                        results.append(result)
                        if result['status'] == 'success':
                            logger.info(f"Analysis completed for {symbol}")
                        else:
                            logger.error(f"Analysis failed for {symbol}: {result.get('message', 'Unknown error')}")
                    except Exception as e:
                        logger.error(f"Analysis failed for {symbol}: {str(e)}")
                        results.append({"symbol": symbol, "status": "error", "message": str(e)})
        else:
            # Run sequentially
            for symbol in symbols:
                result = analyze_company(symbol, args.data_dir, args.results_dir, args.projection_years, args.fetch_if_missing)

                # If skip_missing is True and the error is about missing data, skip this company
                if args.skip_missing and result['status'] == 'error' and 'Failed to load data' in result.get('message', ''):
                    logger.warning(f"Skipping {symbol} due to missing data")
                    continue

                results.append(result)
                if result['status'] == 'success':
                    logger.info(f"Analysis completed for {symbol}")
                else:
                    logger.error(f"Analysis failed for {symbol}: {result.get('message', 'Unknown error')}")

        # Run comparative analysis if there are at least 2 successful analyses
        successful_results = [r for r in results if r['status'] == 'success']

        if len(successful_results) > 1:
            logger.info(f"Running comparative analysis for {len(successful_results)} companies")
            comparative_results = run_comparative_analysis(successful_results, args.results_dir)

            if comparative_results['status'] == 'success':
                logger.info("Comparative analysis completed")
                print("\nComparative analysis completed. Charts saved to:")
                for chart_name, chart_path in comparative_results['charts'].items():
                    print(f"- {chart_name}: {chart_path}")
            else:
                logger.warning(f"Comparative analysis warning: {comparative_results.get('message', 'Unknown warning')}")
        elif len(successful_results) == 1:
            logger.warning("Only one successful analysis. Skipping comparative analysis.")
        else:
            logger.error("No successful analyses. Cannot run comparative analysis.")

        # Print summary
        print("\nBatch Analysis Summary:")
        print(f"Total companies analyzed: {len(symbols)}")
        print(f"Successful analyses: {sum(1 for r in results if r['status'] == 'success')}")
        print(f"Failed analyses: {sum(1 for r in results if r['status'] == 'error')}")
        print(f"Results saved to: {args.results_dir}")

    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
