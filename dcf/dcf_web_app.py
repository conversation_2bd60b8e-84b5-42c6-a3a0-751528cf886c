#!/usr/bin/env python3
"""
DCF Analysis Web Application

This script provides a web-based user interface for the DCF analysis system
using Streamlit. It allows users to run DCF analysis, view results, and
generate reports through a browser interface.
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime
import subprocess
from typing import Dict, List, Optional, Tuple

# Try to import Streamlit
try:
    import streamlit as st
    import pandas as pd
    import numpy as np
    import matplotlib.pyplot as plt
    import seaborn as sns
    from PIL import Image
    HAS_STREAMLIT = True
except ImportError:
    HAS_STREAMLIT = False
    print("Warning: Streamlit not available. Please install with 'pip install streamlit'")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('dcf_web_app')

# Set page configuration
st.set_page_config(
    page_title="DCF Analysis Tool",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Define directories
DATA_DIR = "data"
RESULTS_DIR = "results"

def run_command(command: List[str]) -> Tuple[int, str, str]:
    """
    Run a shell command and return the exit code, stdout, and stderr
    
    Parameters:
    -----------
    command : list
        Command to run as a list of strings
        
    Returns:
    --------
    Tuple of (exit_code, stdout, stderr)
    """
    try:
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        stdout, stderr = process.communicate()
        return process.returncode, stdout, stderr
    except Exception as e:
        return 1, "", str(e)

def fetch_company_data(symbol: str) -> bool:
    """
    Fetch company data from screener.in
    
    Parameters:
    -----------
    symbol : str
        Company symbol
        
    Returns:
    --------
    True if successful, False otherwise
    """
    st.info(f"Fetching data for {symbol}...")
    
    command = [sys.executable, "fetch_tatamotors.py", "--symbol", symbol]
    exit_code, stdout, stderr = run_command(command)
    
    if exit_code == 0:
        st.success(f"Data fetched successfully for {symbol}")
        return True
    else:
        st.error(f"Failed to fetch data for {symbol}: {stderr}")
        return False

def run_dcf_analysis(symbol: str, growth_rate: Optional[float] = None, 
                    discount_rate: Optional[float] = None, 
                    terminal_growth: Optional[float] = None) -> bool:
    """
    Run DCF analysis for a company
    
    Parameters:
    -----------
    symbol : str
        Company symbol
    growth_rate : float, optional
        Override revenue growth rate
    discount_rate : float, optional
        Override discount rate (WACC)
    terminal_growth : float, optional
        Override terminal growth rate
        
    Returns:
    --------
    True if successful, False otherwise
    """
    st.info(f"Running DCF analysis for {symbol}...")
    
    command = [sys.executable, "run_dcf_analysis.py", "--symbol", symbol]
    
    if growth_rate is not None:
        command.extend(["--growth-rate", str(growth_rate)])
    
    if discount_rate is not None:
        command.extend(["--discount-rate", str(discount_rate)])
    
    if terminal_growth is not None:
        command.extend(["--terminal-growth", str(terminal_growth)])
    
    exit_code, stdout, stderr = run_command(command)
    
    if exit_code == 0:
        st.success(f"DCF analysis completed for {symbol}")
        st.text(stdout)
        return True
    else:
        st.error(f"Failed to run DCF analysis for {symbol}: {stderr}")
        st.text(stdout)
        return False

def generate_report(symbol: str, skip_analysis: bool = False) -> bool:
    """
    Generate a comprehensive report for a company
    
    Parameters:
    -----------
    symbol : str
        Company symbol
    skip_analysis : bool
        Skip analysis and use existing results
        
    Returns:
    --------
    True if successful, False otherwise
    """
    st.info(f"Generating report for {symbol}...")
    
    command = [sys.executable, "generate_report.py", "--symbol", symbol]
    
    if skip_analysis:
        command.append("--skip-analysis")
    
    exit_code, stdout, stderr = run_command(command)
    
    if exit_code == 0:
        st.success(f"Report generated for {symbol}")
        
        # Check if report file exists
        report_file = os.path.join(RESULTS_DIR, f"{symbol}_dcf_report.pdf")
        if os.path.exists(report_file):
            st.info(f"Report saved to: {report_file}")
        
        return True
    else:
        st.error(f"Failed to generate report for {symbol}: {stderr}")
        st.text(stdout)
        return False

def run_batch_analysis(symbols: List[str], parallel: bool = False) -> bool:
    """
    Run batch analysis for multiple companies
    
    Parameters:
    -----------
    symbols : list
        List of company symbols
    parallel : bool
        Run analyses in parallel
        
    Returns:
    --------
    True if successful, False otherwise
    """
    symbols_str = ",".join(symbols)
    st.info(f"Running batch analysis for: {symbols_str}")
    
    command = [sys.executable, "batch_analysis.py", "--symbols", symbols_str]
    
    if parallel:
        command.append("--parallel")
    
    exit_code, stdout, stderr = run_command(command)
    
    if exit_code == 0:
        st.success("Batch analysis completed")
        st.text(stdout)
        
        # Check if comparative analysis file exists
        comparative_file = os.path.join(RESULTS_DIR, "comparative_analysis.json")
        if os.path.exists(comparative_file):
            st.info("Comparative analysis completed")
            
            # Display comparative charts
            charts_dir = os.path.join(RESULTS_DIR, "comparative_charts")
            if os.path.exists(charts_dir):
                st.subheader("Comparative Analysis Charts")
                
                col1, col2 = st.columns(2)
                
                # Valuation comparison chart
                valuation_chart = os.path.join(charts_dir, "valuation_comparison.png")
                if os.path.exists(valuation_chart):
                    with col1:
                        st.image(valuation_chart, caption="Valuation Comparison", use_column_width=True)
                
                # Growth vs WACC chart
                growth_wacc_chart = os.path.join(charts_dir, "growth_wacc_comparison.png")
                if os.path.exists(growth_wacc_chart):
                    with col2:
                        st.image(growth_wacc_chart, caption="Growth Rate vs. WACC", use_column_width=True)
                
                # FCF margin comparison chart
                margin_chart = os.path.join(charts_dir, "fcf_margin_comparison.png")
                if os.path.exists(margin_chart):
                    st.image(margin_chart, caption="FCF Margin Comparison", use_column_width=True)
        
        return True
    else:
        st.error(f"Failed to run batch analysis: {stderr}")
        st.text(stdout)
        return False

def display_dcf_results(symbol: str):
    """
    Display DCF analysis results for a company
    
    Parameters:
    -----------
    symbol : str
        Company symbol
    """
    # Check if DCF results file exists
    dcf_file = os.path.join(RESULTS_DIR, f"{symbol}_dcf.json")
    if not os.path.exists(dcf_file):
        st.warning(f"No DCF results found for {symbol}")
        return
    
    # Load DCF results
    with open(dcf_file, 'r') as f:
        dcf_results = json.load(f)
    
    # Display valuation summary
    if 'valuation_summary' in dcf_results:
        summary = dcf_results['valuation_summary']
        
        st.subheader("DCF Valuation Summary")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if 'enterprise_value' in summary:
                st.metric("Enterprise Value", f"₹{summary['enterprise_value'] / 10000000:.2f} Cr")
            
            if 'equity_value' in summary:
                st.metric("Equity Value", f"₹{summary['equity_value'] / 10000000:.2f} Cr")
        
        with col2:
            if 'per_share_value' in summary:
                st.metric("Per Share Value", f"₹{summary['per_share_value']:.2f}")
            
            if 'terminal_value_percent' in summary:
                st.metric("Terminal Value %", f"{summary['terminal_value_percent'] * 100:.2f}%")
        
        with col3:
            if 'present_value_percent' in summary:
                st.metric("Present Value of FCF %", f"{summary['present_value_percent'] * 100:.2f}%")
    
    # Display projected cash flows
    if 'projections' in dcf_results:
        projections = dcf_results['projections']
        
        st.subheader("Projected Cash Flows")
        
        # Convert projections to DataFrame
        data = []
        for year, projection in projections.items():
            data.append({
                'Year': year,
                'Revenue (Cr)': projection.get('revenue', 0) / 10000000,
                'Growth Rate': f"{projection.get('growth_rate', 0) * 100:.2f}%",
                'FCF (Cr)': projection.get('fcf', 0) / 10000000,
                'FCF Margin': f"{projection.get('fcf_margin', 0) * 100:.2f}%"
            })
        
        df = pd.DataFrame(data)
        st.dataframe(df)

def main():
    """
    Main function for the Streamlit app
    """
    # Create directories if they don't exist
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(RESULTS_DIR, exist_ok=True)
    
    # App title
    st.title("DCF Analysis Tool")
    st.markdown("A comprehensive tool for Discounted Cash Flow (DCF) analysis")
    
    # Sidebar
    st.sidebar.header("Navigation")
    page = st.sidebar.radio("Select a page", ["Single Company Analysis", "Batch Analysis", "View Results"])
    
    if page == "Single Company Analysis":
        st.header("Single Company Analysis")
        
        # Input form
        with st.form("single_analysis_form"):
            symbol = st.text_input("Company Symbol", "TATAMOTORS")
            
            st.subheader("Optional Parameters")
            col1, col2 = st.columns(2)
            
            with col1:
                growth_rate = st.number_input("Revenue Growth Rate (Years 1-5)", 
                                             min_value=0.0, max_value=0.5, value=0.10, format="%.2f")
                terminal_growth = st.number_input("Terminal Growth Rate", 
                                                min_value=0.0, max_value=0.1, value=0.03, format="%.2f")
            
            with col2:
                discount_rate = st.number_input("Discount Rate (WACC)", 
                                              min_value=0.05, max_value=0.25, value=0.12, format="%.2f")
                skip_analysis = st.checkbox("Skip Analysis (Use Existing Results)", value=False)
            
            submitted = st.form_submit_button("Run Analysis")
        
        if submitted:
            if not skip_analysis:
                # Fetch data
                if not fetch_company_data(symbol):
                    st.stop()
                
                # Run DCF analysis
                if not run_dcf_analysis(symbol, growth_rate, discount_rate, terminal_growth):
                    st.stop()
            
            # Generate report
            generate_report(symbol, skip_analysis)
            
            # Display results
            display_dcf_results(symbol)
    
    elif page == "Batch Analysis":
        st.header("Batch Analysis")
        
        # Input form
        with st.form("batch_analysis_form"):
            symbols_input = st.text_input("Company Symbols (comma-separated)", "TATAMOTORS,RELIANCE,TCS")
            parallel = st.checkbox("Run Analyses in Parallel", value=True)
            
            submitted = st.form_submit_button("Run Batch Analysis")
        
        if submitted:
            symbols = [s.strip() for s in symbols_input.split(',')]
            
            if not symbols:
                st.error("No symbols provided")
                st.stop()
            
            # Run batch analysis
            run_batch_analysis(symbols, parallel)
    
    elif page == "View Results":
        st.header("View Results")
        
        # Get list of companies with results
        dcf_files = [f for f in os.listdir(RESULTS_DIR) if f.endswith('_dcf.json')]
        companies = [f.split('_')[0] for f in dcf_files]
        
        if not companies:
            st.warning("No analysis results found")
            st.stop()
        
        # Select company
        symbol = st.selectbox("Select Company", companies)
        
        if symbol:
            display_dcf_results(symbol)
            
            # Option to view/download report
            report_file = os.path.join(RESULTS_DIR, f"{symbol}_dcf_report.pdf")
            if os.path.exists(report_file):
                st.subheader("DCF Report")
                st.info(f"Report available at: {report_file}")
                
                # Read PDF file
                try:
                    with open(report_file, "rb") as f:
                        pdf_bytes = f.read()
                    
                    st.download_button(
                        label="Download PDF Report",
                        data=pdf_bytes,
                        file_name=f"{symbol}_dcf_report.pdf",
                        mime="application/pdf"
                    )
                except Exception as e:
                    st.error(f"Error reading PDF file: {str(e)}")

if __name__ == "__main__":
    main()
