#!/usr/bin/env python3
"""
Generic Company Data Fetcher

This script fetches financial data for any company from screener.in.
It is a generic version of fetch_tatamotors.py that can be used for any company.
"""

import os
import sys
import json
import logging
import argparse
import traceback
from datetime import datetime
from typing import Dict, Optional

# Try to import the screener scraper
try:
    from screener_scraper import ScreenerScraper
except ImportError:
    # If not in the path, try to import from the current directory
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    try:
        from screener_scraper import ScreenerScraper
    except ImportError:
        print("Error: Could not import ScreenerScraper. Make sure screener_scraper.py is in the same directory.")
        sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('fetch_company_data')

def fetch_company_data(symbol: str, data_dir: str = 'data', force_update: bool = False) -> Optional[Dict]:
    """
    Fetch financial data for a company from screener.in

    Parameters:
    -----------
    symbol : str
        Company symbol (e.g., 'TATAMOTORS')
    data_dir : str
        Directory to store data
    force_update : bool
        Whether to force update even if data exists

    Returns:
    --------
    Dictionary with company data or None if failed
    """
    try:
        # Create data directory if it doesn't exist
        raw_dir = os.path.join(data_dir, 'raw')
        processed_dir = os.path.join(data_dir, 'processed')

        os.makedirs(raw_dir, exist_ok=True)
        os.makedirs(processed_dir, exist_ok=True)

        # Check if data already exists
        processed_file = os.path.join(processed_dir, f"{symbol}_full.json")

        if os.path.exists(processed_file) and not force_update:
            logger.info(f"Data for {symbol} already exists at {processed_file}")

            # Check if the data is recent (less than 7 days old)
            file_age = datetime.now().timestamp() - os.path.getmtime(processed_file)
            if file_age < 7 * 24 * 60 * 60:  # 7 days in seconds
                logger.info(f"Data is recent (less than 7 days old). Skipping update.")

                # Load and return the existing data
                with open(processed_file, 'r') as f:
                    return json.load(f)
            else:
                logger.info(f"Data is older than 7 days. Fetching fresh data.")

        # Initialize the scraper
        scraper = ScreenerScraper(data_dir=data_dir)

        # Fetch company data
        logger.info(f"Fetching data for {symbol} from screener.in")
        company_data = scraper.get_company_data(symbol, force_refresh=force_update)

        if not company_data:
            logger.error(f"Failed to fetch data for {symbol}")
            return None

        # The data is already processed and saved by get_company_data
        processed_data = company_data

        if not processed_data:
            logger.error(f"Failed to process data for {symbol}")
            return None

        logger.info(f"Successfully fetched and processed data for {symbol}")
        return processed_data

    except Exception as e:
        logger.error(f"Error fetching data for {symbol}: {str(e)}")
        traceback.print_exc()
        return None

def main():
    """
    Main function to fetch company data
    """
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Fetch company data from screener.in')
    parser.add_argument('--symbol', type=str, required=True, help='Company symbol (e.g., TATAMOTORS)')
    parser.add_argument('--data-dir', type=str, default='data', help='Data directory')
    parser.add_argument('--force-update', action='store_true', help='Force update even if data exists')
    args = parser.parse_args()

    # Fetch company data
    company_data = fetch_company_data(args.symbol, args.data_dir, args.force_update)

    if company_data:
        print(f"Successfully fetched data for {args.symbol}")

        # Print some basic information
        if 'overview' in company_data and 'name' in company_data['overview']:
            print(f"Company Name: {company_data['overview']['name']}")

        # Print latest financial year data
        if 'profit_loss' in company_data and 'sales' in company_data['profit_loss']:
            years = sorted(list(company_data['profit_loss']['sales'].keys()))
            if years:
                latest_year = years[-1]
                print(f"Latest Financial Year: {latest_year}")
                print(f"Revenue: ₹{company_data['profit_loss']['sales'][latest_year]:,.2f} Cr")

                if 'net_profit' in company_data['profit_loss'] and latest_year in company_data['profit_loss']['net_profit']:
                    print(f"Net Profit: ₹{company_data['profit_loss']['net_profit'][latest_year]:,.2f} Cr")

                if 'eps_in_rs' in company_data['profit_loss'] and latest_year in company_data['profit_loss']['eps_in_rs']:
                    print(f"EPS: ₹{company_data['profit_loss']['eps_in_rs'][latest_year]:.2f}")
    else:
        print(f"Failed to fetch data for {args.symbol}")
        sys.exit(1)

if __name__ == "__main__":
    main()
