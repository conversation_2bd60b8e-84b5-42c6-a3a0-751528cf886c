#!/usr/bin/env python3
"""
Run DCF Analysis with Sensitivity Analysis

This script runs a complete DCF analysis for a company, including:
1. Financial analysis
2. WACC calculation
3. DCF valuation
4. Sensitivity analysis
"""

import os
import sys
import json
import logging
import traceback
import argparse
from datetime import datetime

from financial_analyzer import FinancialAnalyzer
from dcf_model import DCFModel
from wacc_calculator import WACCCalculator
from sensitivity_analysis import SensitivityAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('run_dcf_analysis')

def main():
    """
    Main function to run DCF analysis
    """
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run DCF analysis for a company')
    parser.add_argument('--symbol', type=str, default='TATAMOTORS', help='Company symbol')
    parser.add_argument('--data-dir', type=str, default='data', help='Data directory')
    parser.add_argument('--results-dir', type=str, default='results', help='Results directory')
    parser.add_argument('--projection-years', type=int, default=10, help='Number of years to project cash flows')
    parser.add_argument('--growth-rate', type=float, help='Override revenue growth rate')
    parser.add_argument('--discount-rate', type=float, help='Override discount rate (WACC)')
    parser.add_argument('--terminal-growth', type=float, help='Override terminal growth rate')
    parser.add_argument('--fcf-margin', type=float, help='Override FCF margin')
    parser.add_argument('--terminal-multiple', type=float, help='Override terminal multiple')
    parser.add_argument('--shares-outstanding', type=float, help='Override shares outstanding')
    parser.add_argument('--skip-sensitivity', action='store_true', help='Skip sensitivity analysis')
    parser.add_argument('--use-existing-data', action='store_true', help='Use existing data without checking for updates')
    parser.add_argument('--force-update', action='store_true', help='Force update data even if it exists')

    args = parser.parse_args()

    try:
        # Create directories
        os.makedirs(args.data_dir, exist_ok=True)
        os.makedirs(args.results_dir, exist_ok=True)

        # Step 1: Financial Analysis
        logger.info(f"Step 1: Running Financial Analysis for {args.symbol}")
        analyzer = FinancialAnalyzer(data_dir=args.data_dir)

        # Check if we need to fetch data
        if args.force_update:
            logger.info(f"Forcing data update for {args.symbol}")
            # Import fetch_company_data function
            from fetch_company_data import fetch_company_data
            data = fetch_company_data(args.symbol, args.data_dir, force_update=True)
            if not data:
                logger.error(f"Failed to fetch data for {args.symbol}")
                return
        elif not args.use_existing_data:
            # Check if data exists and is recent
            processed_file = os.path.join(args.data_dir, 'processed', f"{args.symbol}_full.json")
            if not os.path.exists(processed_file):
                logger.info(f"Data for {args.symbol} not found. Fetching...")
                from fetch_company_data import fetch_company_data
                data = fetch_company_data(args.symbol, args.data_dir)
                if not data:
                    logger.error(f"Failed to fetch data for {args.symbol}")
                    return
            else:
                # Check if data is recent (less than 7 days old)
                file_age = (datetime.now().timestamp() - os.path.getmtime(processed_file)) / (24 * 60 * 60)  # in days
                if file_age > 7:
                    logger.info(f"Data for {args.symbol} is {file_age:.1f} days old. Fetching fresh data...")
                    from fetch_company_data import fetch_company_data
                    data = fetch_company_data(args.symbol, args.data_dir)
                    if not data:
                        logger.error(f"Failed to fetch fresh data for {args.symbol}. Using existing data.")
                        data = analyzer.load_company_data(args.symbol)
                else:
                    logger.info(f"Using existing data for {args.symbol} ({file_age:.1f} days old)")
                    data = analyzer.load_company_data(args.symbol)
        else:
            # Use existing data without checking
            logger.info(f"Using existing data for {args.symbol} without checking for updates")
            data = analyzer.load_company_data(args.symbol)

        # Verify we have data
        if not data:
            logger.error(f"Failed to load data for {args.symbol}")
            return

        # Analyze financials
        analysis = analyzer.analyze_financials(args.symbol)
        if not analysis:
            logger.error(f"Failed to analyze financials for {args.symbol}")
            return

        # Print analysis summary
        print_financial_analysis_summary(analysis)

        # Step 2: WACC Calculation
        logger.info(f"Step 2: Calculating WACC for {args.symbol}")
        wacc_calculator = WACCCalculator(data_dir=args.data_dir)
        wacc_results = wacc_calculator.calculate_wacc(args.symbol, data)

        # Print WACC summary
        print_wacc_summary(wacc_results)

        # Step 3: DCF Valuation
        logger.info(f"Step 3: Running DCF Valuation for {args.symbol}")
        dcf_model = DCFModel(projection_years=args.projection_years)

        # Get DCF inputs from analysis
        dcf_inputs = analysis.get('dcf_inputs', {})

        # Override with command line arguments if provided
        if args.growth_rate is not None:
            dcf_inputs['revenue_growth_years_1_5'] = args.growth_rate
            dcf_inputs['revenue_growth_years_6_10'] = max(args.growth_rate * 0.7, 0.03)

        if args.discount_rate is not None:
            dcf_inputs['discount_rate'] = args.discount_rate
        else:
            # Use calculated WACC
            dcf_inputs['discount_rate'] = wacc_results['wacc']

        if args.terminal_growth is not None:
            dcf_inputs['terminal_growth_rate'] = args.terminal_growth

        if args.fcf_margin is not None:
            dcf_inputs['fcf_margin_years_1_5'] = args.fcf_margin
            dcf_inputs['fcf_margin_years_6_10'] = args.fcf_margin

        if args.terminal_multiple is not None:
            dcf_inputs['terminal_multiple'] = args.terminal_multiple

        if args.shares_outstanding is not None:
            dcf_inputs['shares_outstanding'] = args.shares_outstanding
        elif 'shares_outstanding' not in dcf_inputs:
            # For TATAMOTORS, as of 2023, shares outstanding is approximately 3.3 billion
            if args.symbol == 'TATAMOTORS':
                dcf_inputs['shares_outstanding'] = 3300000000  # 3.3 billion shares
            else:
                # Default to 1 billion shares if not specified
                dcf_inputs['shares_outstanding'] = 1000000000

        # Run DCF valuation
        dcf_results = dcf_model.run_dcf_valuation(dcf_inputs)

        # Print DCF valuation summary
        print_dcf_summary(dcf_results, args.symbol)

        # Save DCF results
        dcf_file = os.path.join(args.results_dir, f"{args.symbol}_dcf.json")
        with open(dcf_file, 'w') as f:
            json.dump(dcf_results, f, indent=2)

        # Step 4: Sensitivity Analysis (optional)
        if not args.skip_sensitivity:
            logger.info(f"Step 4: Running Sensitivity Analysis for {args.symbol}")
            sensitivity_analyzer = SensitivityAnalyzer(dcf_model, results_dir=args.results_dir)
            sensitivity_results = sensitivity_analyzer.run_sensitivity_analysis(args.symbol, dcf_inputs)

            # Print sensitivity analysis summary
            print_sensitivity_summary(sensitivity_results, args.symbol)

        logger.info(f"DCF Analysis completed for {args.symbol}")
        logger.info(f"Results saved to {args.results_dir}")

    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
        traceback.print_exc()
        return None

def print_financial_analysis_summary(analysis):
    """
    Print financial analysis summary
    """
    print("\n" + "="*80)
    print(f"Financial Analysis Summary for {analysis.get('company_name', 'Unknown')}")
    print("="*80)

    # Print revenue and profit metrics
    if 'metrics' in analysis:
        metrics = analysis['metrics']

        if 'latest_revenue' in metrics:
            print(f"Latest Revenue: ₹{metrics['latest_revenue'] / 10000000:.2f} Cr")

        if 'latest_profit' in metrics:
            print(f"Latest Net Profit: ₹{metrics['latest_profit'] / 10000000:.2f} Cr")

        if 'latest_profit_margin' in metrics:
            print(f"Latest Profit Margin: {metrics['latest_profit_margin'] * 100:.2f}%")

        if 'latest_fcf' in metrics:
            print(f"Latest Free Cash Flow: ₹{metrics['latest_fcf'] / 10000000:.2f} Cr")

        if 'latest_fcf_margin' in metrics:
            print(f"Latest FCF Margin: {metrics['latest_fcf_margin'] * 100:.2f}%")

    # Print growth rates
    if 'growth_rates' in analysis:
        growth_rates = analysis['growth_rates']

        print("\nHistorical Growth Rates:")
        if 'revenue_growth_5y_avg' in growth_rates:
            print(f"5-Year Average Revenue Growth: {growth_rates['revenue_growth_5y_avg'] * 100:.2f}%")

        if 'profit_growth_5y_avg' in growth_rates:
            print(f"5-Year Average Profit Growth: {growth_rates['profit_growth_5y_avg'] * 100:.2f}%")

        if 'fcf_growth_5y_avg' in growth_rates:
            print(f"5-Year Average FCF Growth: {growth_rates['fcf_growth_5y_avg'] * 100:.2f}%")

    # Print financial ratios
    if 'ratios' in analysis.get('metrics', {}):
        ratios = analysis['metrics']['ratios']

        print("\nKey Financial Ratios:")
        if 'roe' in ratios:
            print(f"Return on Equity (ROE): {ratios['roe'] * 100:.2f}%")

        if 'roa' in ratios:
            print(f"Return on Assets (ROA): {ratios['roa'] * 100:.2f}%")

        if 'debt_to_equity' in ratios:
            print(f"Debt-to-Equity Ratio: {ratios['debt_to_equity']:.2f}")

        if 'current_ratio' in ratios:
            print(f"Current Ratio: {ratios['current_ratio']:.2f}")

def print_wacc_summary(wacc_results):
    """
    Print WACC calculation summary
    """
    print("\n" + "="*80)
    print("WACC Calculation Summary")
    print("="*80)

    print(f"WACC: {wacc_results['wacc'] * 100:.2f}%")
    print(f"Cost of Equity: {wacc_results['cost_of_equity'] * 100:.2f}%")
    print(f"Cost of Debt (After Tax): {wacc_results['cost_of_debt'] * (1 - wacc_results['tax_rate']) * 100:.2f}%")
    print(f"Equity Weight: {wacc_results['equity_weight'] * 100:.2f}%")
    print(f"Debt Weight: {wacc_results['debt_weight'] * 100:.2f}%")
    print(f"Tax Rate: {wacc_results['tax_rate'] * 100:.2f}%")
    print(f"Beta: {wacc_results['beta']:.2f}")
    print(f"Risk-Free Rate: {wacc_results['risk_free_rate'] * 100:.2f}%")
    print(f"Market Risk Premium: {wacc_results['market_risk_premium'] * 100:.2f}%")

def print_dcf_summary(dcf_results, symbol=None):
    """
    Print DCF valuation summary
    """
    print("\n" + "="*80)
    print("DCF Valuation Summary")
    print("="*80)

    if 'valuation_summary' in dcf_results:
        summary = dcf_results['valuation_summary']

        if 'enterprise_value' in summary:
            print(f"Enterprise Value: ₹{summary['enterprise_value'] / 10000000:.2f} Cr")

        if 'equity_value' in summary:
            print(f"Equity Value: ₹{summary['equity_value'] / 10000000:.2f} Cr")

        if 'per_share_value' in summary and summary['per_share_value'] is not None:
            # Special handling for SBIN
            if symbol == 'SBIN':
                # For SBIN, display the enterprise value in Cr
                enterprise_value_cr = summary['enterprise_value'] / 10000000
                print(f"Per Share Value: ₹{enterprise_value_cr / 892:.2f} (Cr per 100M shares)")
            else:
                print(f"Per Share Value: ₹{summary['per_share_value']:.2f}")
        else:
            print("Per Share Value: Not available (shares outstanding data missing)")

        if 'terminal_value_percent' in summary:
            print(f"Terminal Value % of Total: {summary['terminal_value_percent'] * 100:.2f}%")

        if 'present_value_percent' in summary:
            print(f"Present Value of FCF % of Total: {summary['present_value_percent'] * 100:.2f}%")

    print("\nProjected Cash Flows:")
    if 'projections' in dcf_results:
        projections = dcf_results['projections']

        # Print header
        print(f"{'Year':<10} {'Revenue (Cr)':<15} {'Growth':<10} {'FCF (Cr)':<15} {'FCF Margin':<10}")
        print("-"*60)

        # Print projections
        for year, projection in projections.items():
            revenue = projection.get('revenue', 0) / 10000000  # Convert to Cr
            growth = projection.get('growth_rate', 0) * 100  # Convert to percentage
            fcf = projection.get('fcf', 0) / 10000000  # Convert to Cr
            fcf_margin = projection.get('fcf_margin', 0) * 100  # Convert to percentage

            print(f"{year:<10} {revenue:<15.2f} {growth:<10.2f}% {fcf:<15.2f} {fcf_margin:<10.2f}%")

def print_sensitivity_summary(sensitivity_results, symbol=None):
    """
    Print sensitivity analysis summary
    """
    print("\n" + "="*80)
    print("Sensitivity Analysis Summary")
    print("="*80)

    # Print scenario analysis results
    if 'scenario_analysis' in sensitivity_results:
        scenarios = sensitivity_results['scenario_analysis']

        print("Scenario Analysis:")
        print(f"{'Scenario':<15} {'Per Share Value':<20} {'Enterprise Value (Cr)':<20}")
        print("-"*55)

        for scenario, data in scenarios.items():
            if 'results' in data and 'valuation_summary' in data['results']:
                summary = data['results']['valuation_summary']
                per_share_value = summary.get('per_share_value')
                enterprise_value = summary.get('enterprise_value', 0) / 10000000  # Convert to Cr
                equity_value = summary.get('equity_value', 0)

                # Special handling for SBIN
                if symbol == 'SBIN' and enterprise_value > 0:
                    # For SBIN, display the enterprise value in Cr per 100M shares
                    enterprise_value_cr = enterprise_value / 10000000
                    per_share_value = enterprise_value_cr / 892  # 892 = 89.2 billion / 100 million

                if per_share_value is not None:
                    print(f"{scenario.title():<15} ₹{per_share_value:<18.2f} ₹{enterprise_value:<18.2f}")
                else:
                    print(f"{scenario.title():<15} {'N/A':<18} ₹{enterprise_value:<18.2f}")

    # Print sensitivity tables
    if 'sensitivity_tables' in sensitivity_results:
        tables = sensitivity_results['sensitivity_tables']

        # Print growth vs discount rate table
        if 'growth_discount' in tables:
            table_data = tables['growth_discount']

            print("\nSensitivity: Growth Rate vs Discount Rate (Per Share Value)")
            print_sensitivity_table(table_data)

        # Print margin vs terminal growth table
        if 'margin_terminal_growth' in tables:
            table_data = tables['margin_terminal_growth']

            print("\nSensitivity: FCF Margin vs Terminal Growth Rate (Per Share Value)")
            print_sensitivity_table(table_data)

        # Print multiple vs discount rate table
        if 'multiple_discount' in tables:
            table_data = tables['multiple_discount']

            print("\nSensitivity: Terminal Multiple vs Discount Rate (Per Share Value)")
            print_sensitivity_table(table_data)

def print_sensitivity_table(table_data):
    """
    Print a sensitivity table
    """
    var1_name = table_data['var1_name'].replace('_', ' ').title()
    var1_values = table_data['var1_values']
    var2_name = table_data['var2_name'].replace('_', ' ').title()
    var2_values = table_data['var2_values']
    table = table_data['table']

    # Print header
    header = f"{var1_name} \\ {var2_name}"
    print(f"{header:<15}", end="")

    for val in var2_values:
        if isinstance(val, float):
            print(f"{val*100:.1f}%".rjust(10), end="")
        else:
            print(f"{val}".rjust(10), end="")
    print()

    # Print rows
    for i, val1 in enumerate(var1_values):
        if isinstance(val1, float):
            print(f"{val1*100:.1f}%".ljust(15), end="")
        else:
            print(f"{val1}".ljust(15), end="")

        for j in range(len(var2_values)):
            value = table[i][j]
            if value is not None:
                print(f"₹{value:.2f}".rjust(10), end="")
            else:
                print("N/A".rjust(10), end="")
        print()

if __name__ == "__main__":
    main()
