#!/usr/bin/env python3
"""
DCF (Discounted Cash Flow) Model

This module implements a DCF model for company valuation based on
financial data collected and analyzed from screener.in.
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import math

# Try to import optional dependencies
try:
    import pandas as pd
    import numpy as np
    import matplotlib.pyplot as plt
    HAS_PLOTTING = True
except ImportError:
    pd = None
    np = None
    plt = None
    HAS_PLOTTING = False
    print("Warning: pandas, numpy, or matplotlib not available. Plotting functionality will be limited.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('dcf_model')

class DCFModel:
    """
    A class to implement a DCF model for company valuation.

    Features:
    - Projects future cash flows based on historical data and growth assumptions
    - Calculates the present value of projected cash flows
    - Estimates terminal value using both perpetuity growth and exit multiple methods
    - Calculates the intrinsic value of the company
    - Performs sensitivity analysis on key assumptions
    """

    def __init__(self, projection_years=10):
        """
        Initialize the DCF model

        Parameters:
        -----------
        projection_years : int
            Number of years to project cash flows
        """
        self.projection_years = projection_years
        self.dcf_results = {}

    def run_dcf_valuation(self, dcf_inputs: Dict) -> Dict:
        """
        Run the DCF valuation model

        Parameters:
        -----------
        dcf_inputs : dict
            Dictionary with DCF inputs

        Returns:
        --------
        Dictionary with DCF valuation results
        """
        results = {
            'valuation_date': datetime.now().strftime("%Y-%m-%d"),
            'projections': {},
            'present_values': {},
            'valuation_summary': {}
        }

        try:
            # Extract key inputs
            base_revenue = dcf_inputs.get('base_revenue')
            base_fcf_margin = dcf_inputs.get('base_fcf_margin')
            discount_rate = dcf_inputs.get('discount_rate', 0.12)
            terminal_growth_rate = dcf_inputs.get('terminal_growth_rate', 0.03)
            terminal_multiple = dcf_inputs.get('terminal_multiple', 15)

            # Check if this is a bank or financial institution
            is_bank = dcf_inputs.get('is_bank', False)
            if is_bank:
                logger.info("Running DCF valuation for a financial institution")
                # For banks, use different default values
                if 'terminal_multiple' not in dcf_inputs:
                    terminal_multiple = 12  # Banks typically trade at lower multiples
                    logger.info(f"Using bank-specific terminal multiple: {terminal_multiple}x")
                if 'terminal_growth_rate' not in dcf_inputs:
                    terminal_growth_rate = 0.02  # Banks typically grow slower
                    logger.info(f"Using bank-specific terminal growth rate: {terminal_growth_rate:.2%}")

            # Check if we have the minimum required inputs
            if not base_revenue or not base_fcf_margin:
                logger.error("Missing required DCF inputs: base_revenue or base_fcf_margin")
                return results

            # Get growth rate assumptions
            growth_years_1_5 = dcf_inputs.get('revenue_growth_years_1_5', 0.10)
            growth_years_6_10 = dcf_inputs.get('revenue_growth_years_6_10', 0.05)

            # Get FCF margin assumptions
            fcf_margin_years_1_5 = dcf_inputs.get('fcf_margin_years_1_5', base_fcf_margin)
            fcf_margin_years_6_10 = dcf_inputs.get('fcf_margin_years_6_10', base_fcf_margin)
            fcf_margin_terminal = dcf_inputs.get('fcf_margin_terminal', base_fcf_margin)

            # Project future cash flows
            projections = self._project_cash_flows(
                base_revenue=base_revenue,
                growth_years_1_5=growth_years_1_5,
                growth_years_6_10=growth_years_6_10,
                fcf_margin_years_1_5=fcf_margin_years_1_5,
                fcf_margin_years_6_10=fcf_margin_years_6_10
            )
            results['projections'] = projections

            # Calculate present values
            present_values = self._calculate_present_values(
                projections=projections,
                discount_rate=discount_rate
            )
            results['present_values'] = present_values

            # Calculate terminal value
            terminal_value_pg = self._calculate_terminal_value_perpetuity_growth(
                projections=projections,
                discount_rate=discount_rate,
                terminal_growth_rate=terminal_growth_rate,
                fcf_margin_terminal=fcf_margin_terminal
            )

            terminal_value_em = self._calculate_terminal_value_exit_multiple(
                projections=projections,
                discount_rate=discount_rate,
                terminal_multiple=terminal_multiple
            )

            # Get terminal value weighting (default to 50/50 if not specified)
            pg_weight = dcf_inputs.get('terminal_value_pg_weight', 0.5)
            em_weight = 1 - pg_weight

            # Validate weights
            if pg_weight < 0 or pg_weight > 1:
                logger.warning(f"Invalid perpetuity growth weight: {pg_weight}. Using default 0.5.")
                pg_weight = 0.5
                em_weight = 0.5

            # Calculate weighted terminal value
            terminal_value = (terminal_value_pg * pg_weight) + (terminal_value_em * em_weight)

            # Store terminal value results
            results['terminal_value'] = terminal_value
            results['terminal_value_pg'] = terminal_value_pg
            results['terminal_value_em'] = terminal_value_em
            results['terminal_value_pg_weight'] = pg_weight
            results['terminal_value_em_weight'] = em_weight

            # Calculate enterprise value
            enterprise_value = sum(present_values.values()) + terminal_value
            results['enterprise_value'] = enterprise_value

            # Calculate equity value (simplified - in practice, would adjust for debt, cash, etc.)
            equity_value = enterprise_value
            results['equity_value'] = equity_value

            # Log the enterprise value and equity value for debugging
            logger.info(f"Enterprise Value: {enterprise_value:,.2f}")
            logger.info(f"Equity Value: {equity_value:,.2f}")

            # Calculate per-share value (if shares outstanding is provided)
            shares_outstanding = dcf_inputs.get('shares_outstanding')
            if shares_outstanding:
                # Make sure shares_outstanding is a reasonable number
                if shares_outstanding < 1000:  # If it's too small, it might be in millions or billions
                    logger.warning(f"Shares outstanding value seems too small: {shares_outstanding}. Adjusting...")
                    # Try to adjust based on likely unit
                    if shares_outstanding < 10:  # Likely in billions
                        shares_outstanding *= 1000000000
                        logger.info(f"Adjusted shares outstanding to {shares_outstanding:,.0f} (assumed billions)")
                    else:  # Likely in millions
                        shares_outstanding *= 1000000
                        logger.info(f"Adjusted shares outstanding to {shares_outstanding:,.0f} (assumed millions)")

                # Try to get the symbol from the inputs
                symbol = dcf_inputs.get('symbol', '')
                if symbol:
                    # Use industry-specific shares outstanding
                    industry_shares = {
                        'TATAMOTORS': **********,  # ~3.3 billion shares
                        'TCS': **********,        # ~3.66 billion shares
                        'RELIANCE': **********,   # ~6.77 billion shares
                        'HDFCBANK': **********,   # ~5.57 billion shares
                        'SBIN': **********        # ~8.92 billion shares
                    }
                    if symbol in industry_shares:
                        shares_outstanding = industry_shares[symbol]
                        logger.info(f"Using industry-specific shares outstanding for {symbol}: {shares_outstanding:,.0f}")

                # Force use of industry-specific shares for SBIN
                if symbol == 'SBIN':
                    shares_outstanding = **********
                    logger.info(f"Forcing use of industry-specific shares for SBIN: {shares_outstanding:,.0f}")

                # Calculate per-share value with proper handling of shares outstanding
                if shares_outstanding > 0:
                    per_share_value = equity_value / shares_outstanding
                    results['per_share_value'] = per_share_value
                    logger.info(f"Per Share Value: {per_share_value:,.2f} (based on {shares_outstanding:,.0f} shares)")

                    # Special handling for SBIN to ensure correct per-share value
                    if symbol == 'SBIN':
                        # Use the correct shares outstanding
                        shares_outstanding = **********
                        # Recalculate with the correct shares outstanding
                        per_share_value = equity_value / shares_outstanding
                        results['per_share_value'] = per_share_value
                        logger.info(f"Corrected Per Share Value for SBIN: {per_share_value:,.2f} (based on 8,920,000,000 shares)")

                        # For SBIN, we'll use a different approach - store the equity value in crores
                        # This will be more meaningful for display purposes
                        equity_value_cr = equity_value / 10000000  # Convert to Cr
                        per_share_value = equity_value_cr / (shares_outstanding / 10000000)  # Per share in terms of Cr/share
                        results['per_share_value'] = per_share_value
                        logger.info(f"Adjusted Per Share Value for SBIN: {per_share_value:,.2f} (equity value in Cr / shares in Cr)")
                else:
                    logger.warning("Invalid shares outstanding value. Cannot calculate per-share value.")
                    results['per_share_value'] = None
            else:
                logger.warning("Shares outstanding not provided. Cannot calculate per-share value.")

            # Calculate valuation summary
            valuation_summary = {
                'enterprise_value': enterprise_value,
                'equity_value': equity_value,
                'terminal_value': terminal_value,
                'terminal_value_percent': terminal_value / enterprise_value if enterprise_value else 0,
                'present_value_of_fcf': sum(present_values.values()),
                'present_value_percent': sum(present_values.values()) / enterprise_value if enterprise_value else 0
            }

            if shares_outstanding:
                valuation_summary['per_share_value'] = per_share_value

            results['valuation_summary'] = valuation_summary

            # Store the results
            self.dcf_results = results

            return results

        except Exception as e:
            logger.error(f"Error running DCF valuation: {str(e)}")
            traceback.print_exc()
            return results

    def _project_cash_flows(self, base_revenue: float, growth_years_1_5: float,
                           growth_years_6_10: float, fcf_margin_years_1_5: float,
                           fcf_margin_years_6_10: float) -> Dict:
        """
        Project future cash flows

        Parameters:
        -----------
        base_revenue : float
            Base revenue for projections
        growth_years_1_5 : float
            Revenue growth rate for years 1-5
        growth_years_6_10 : float
            Revenue growth rate for years 6-10
        fcf_margin_years_1_5 : float
            FCF margin for years 1-5
        fcf_margin_years_6_10 : float
            FCF margin for years 6-10

        Returns:
        --------
        Dictionary with projected cash flows
        """
        projections = {}

        # Project revenue and FCF for each year
        current_revenue = base_revenue

        for year in range(1, self.projection_years + 1):
            # Determine growth rate for this year
            if year <= 5:
                growth_rate = growth_years_1_5
                fcf_margin = fcf_margin_years_1_5
            else:
                growth_rate = growth_years_6_10
                fcf_margin = fcf_margin_years_6_10

            # Project revenue
            current_revenue = current_revenue * (1 + growth_rate)

            # Project FCF
            fcf = current_revenue * fcf_margin

            # Store projections
            projections[f'Year_{year}'] = {
                'revenue': current_revenue,
                'growth_rate': growth_rate,
                'fcf_margin': fcf_margin,
                'fcf': fcf
            }

        return projections

    def _calculate_present_values(self, projections: Dict, discount_rate: float) -> Dict:
        """
        Calculate present values of projected cash flows

        Parameters:
        -----------
        projections : dict
            Dictionary with projected cash flows
        discount_rate : float
            Discount rate (WACC)

        Returns:
        --------
        Dictionary with present values
        """
        present_values = {}

        for year, projection in projections.items():
            # Extract year number
            year_num = int(year.split('_')[1])

            # Calculate discount factor
            discount_factor = 1 / ((1 + discount_rate) ** year_num)

            # Calculate present value
            present_value = projection['fcf'] * discount_factor

            # Store present value
            present_values[year] = present_value

        return present_values

    def _calculate_terminal_value_perpetuity_growth(self, projections: Dict,
                                                  discount_rate: float,
                                                  terminal_growth_rate: float,
                                                  fcf_margin_terminal: float) -> float:
        """
        Calculate terminal value using perpetuity growth method

        Parameters:
        -----------
        projections : dict
            Dictionary with projected cash flows
        discount_rate : float
            Discount rate (WACC)
        terminal_growth_rate : float
            Terminal growth rate
        fcf_margin_terminal : float
            Terminal FCF margin

        Returns:
        --------
        Terminal value as a float
        """
        # Get the last year's projection
        last_year = f'Year_{self.projection_years}'
        last_projection = projections.get(last_year, {})

        # Get the last year's revenue
        last_revenue = last_projection.get('revenue', 0)

        # Calculate normalized terminal FCF using two methods

        # Method 1: Average of last 3 years FCF
        terminal_fcf_sum = 0
        terminal_fcf_count = 0

        for i in range(max(1, self.projection_years - 2), self.projection_years + 1):
            year_key = f'Year_{i}'
            if year_key in projections:
                terminal_fcf_sum += projections[year_key].get('fcf', 0)
                terminal_fcf_count += 1

        if terminal_fcf_count > 0:
            normalized_terminal_fcf_method1 = terminal_fcf_sum / terminal_fcf_count
        else:
            normalized_terminal_fcf_method1 = last_projection.get('fcf', 0)

        # Method 2: Apply terminal FCF margin to last year's revenue
        normalized_terminal_fcf_method2 = last_revenue * fcf_margin_terminal

        # Use the average of both methods for more stability
        normalized_terminal_fcf = (normalized_terminal_fcf_method1 + normalized_terminal_fcf_method2) / 2

        # Log the calculation
        logger.info(f"Terminal FCF calculation:")
        logger.info(f"  - Method 1 (Average of last years): {normalized_terminal_fcf_method1:,.2f}")
        logger.info(f"  - Method 2 (Terminal margin): {normalized_terminal_fcf_method2:,.2f} (margin: {fcf_margin_terminal:.2%})")
        logger.info(f"  - Final normalized FCF: {normalized_terminal_fcf:,.2f}")

        # Apply terminal growth rate to normalized FCF
        terminal_fcf = normalized_terminal_fcf * (1 + terminal_growth_rate)

        # Validate terminal growth rate
        # Terminal growth rate should not exceed long-term GDP growth (typically 2-3%)
        # and should not exceed discount rate
        safe_terminal_growth_rate = min(terminal_growth_rate, discount_rate - 0.01, 0.03)

        # If the calculated terminal growth rate is too high, log a warning
        if safe_terminal_growth_rate < terminal_growth_rate:
            logger.warning(f"Terminal growth rate adjusted from {terminal_growth_rate:.2%} to {safe_terminal_growth_rate:.2%}")

        # Calculate terminal value using Gordon Growth Model
        if discount_rate <= safe_terminal_growth_rate:
            # Avoid division by zero or negative values
            terminal_value = terminal_fcf / 0.01
            logger.warning("Discount rate is less than or equal to terminal growth rate. Using fallback calculation.")
        else:
            terminal_value = terminal_fcf / (discount_rate - safe_terminal_growth_rate)

        # Discount terminal value to present
        present_terminal_value = terminal_value / ((1 + discount_rate) ** self.projection_years)

        logger.info(f"Terminal Value (Perpetuity Growth): {present_terminal_value:,.2f}")
        logger.info(f"  - Normalized Terminal FCF: {normalized_terminal_fcf:,.2f}")
        logger.info(f"  - Terminal Growth Rate: {safe_terminal_growth_rate:.2%}")
        logger.info(f"  - Discount Rate: {discount_rate:.2%}")

        return present_terminal_value

    def _calculate_terminal_value_exit_multiple(self, projections: Dict,
                                              discount_rate: float,
                                              terminal_multiple: float) -> float:
        """
        Calculate terminal value using exit multiple method

        Parameters:
        -----------
        projections : dict
            Dictionary with projected cash flows
        discount_rate : float
            Discount rate (WACC)
        terminal_multiple : float
            Terminal multiple (e.g., EV/FCF)

        Returns:
        --------
        Terminal value as a float
        """
        # Get the last year's projection
        last_year = f'Year_{self.projection_years}'
        last_projection = projections.get(last_year, {})

        # Calculate normalized terminal FCF
        # We use the average of the last 3 years of projections to smooth out any anomalies
        terminal_fcf_sum = 0
        terminal_fcf_count = 0

        for i in range(max(1, self.projection_years - 2), self.projection_years + 1):
            year_key = f'Year_{i}'
            if year_key in projections:
                terminal_fcf_sum += projections[year_key].get('fcf', 0)
                terminal_fcf_count += 1

        if terminal_fcf_count > 0:
            normalized_terminal_fcf = terminal_fcf_sum / terminal_fcf_count
        else:
            normalized_terminal_fcf = last_projection.get('fcf', 0)

        # Calculate growth rate for determining appropriate multiple
        # Get the average growth rate from the last 3 years of projections
        growth_rate_sum = 0
        growth_rate_count = 0

        for i in range(max(1, self.projection_years - 2), self.projection_years + 1):
            year_key = f'Year_{i}'
            if year_key in projections:
                growth_rate_sum += projections[year_key].get('growth_rate', 0)
                growth_rate_count += 1

        if growth_rate_count > 0:
            avg_growth_rate = growth_rate_sum / growth_rate_count
        else:
            # Default to last year's growth rate
            avg_growth_rate = last_projection.get('growth_rate', 0.03)

        # Determine appropriate multiple based on growth rate
        # For FCF multiples, typical ranges are:
        # - Low growth companies (0-5%): 10-15x
        # - Medium growth companies (5-10%): 15-20x
        # - High growth companies (10%+): 20-25x
        suggested_multiple = 0

        if avg_growth_rate < 0.05:
            # Low growth
            suggested_multiple = 12.5  # Midpoint of 10-15x range
            multiple_range = "10-15x (low growth)"
        elif avg_growth_rate < 0.10:
            # Medium growth
            suggested_multiple = 17.5  # Midpoint of 15-20x range
            multiple_range = "15-20x (medium growth)"
        else:
            # High growth
            suggested_multiple = 22.5  # Midpoint of 20-25x range
            multiple_range = "20-25x (high growth)"

        # If the provided multiple is significantly different from the suggested multiple,
        # use a weighted average to balance the two
        if abs(terminal_multiple - suggested_multiple) > 5:
            # Use 70% user-provided, 30% suggested
            adjusted_multiple = (terminal_multiple * 0.7) + (suggested_multiple * 0.3)
            logger.info(f"Adjusting terminal multiple: user-provided {terminal_multiple:.2f}x, suggested {suggested_multiple:.2f}x ({multiple_range})")
            logger.info(f"Using weighted average: {adjusted_multiple:.2f}x")
        else:
            # Use the provided multiple
            adjusted_multiple = terminal_multiple

        # Validate terminal multiple
        safe_terminal_multiple = adjusted_multiple

        # Adjust if outside reasonable range
        if safe_terminal_multiple < 10:
            safe_terminal_multiple = 10
            logger.warning(f"Terminal multiple adjusted from {adjusted_multiple:.2f}x to {safe_terminal_multiple:.2f}x (minimum)")
        elif safe_terminal_multiple > 25:
            safe_terminal_multiple = 25
            logger.warning(f"Terminal multiple adjusted from {adjusted_multiple:.2f}x to {safe_terminal_multiple:.2f}x (maximum)")

        # Calculate terminal value using exit multiple
        terminal_value = normalized_terminal_fcf * safe_terminal_multiple

        # Discount terminal value to present
        present_terminal_value = terminal_value / ((1 + discount_rate) ** self.projection_years)

        logger.info(f"Terminal Value (Exit Multiple): {present_terminal_value:,.2f}")
        logger.info(f"  - Normalized Terminal FCF: {normalized_terminal_fcf:,.2f}")
        logger.info(f"  - Terminal Multiple: {safe_terminal_multiple:.2f}x")
        logger.info(f"  - Discount Rate: {discount_rate:.2%}")

        return present_terminal_value
