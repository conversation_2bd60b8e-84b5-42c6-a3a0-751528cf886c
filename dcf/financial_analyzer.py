#!/usr/bin/env python3
"""
Financial Data Analyzer for DCF Analysis

This module provides functionality to analyze financial data collected from
screener.in for DCF (Discounted Cash Flow) analysis. It processes the raw
financial data, calculates key metrics, and prepares the data for DCF modeling.
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import math

# Try to import optional dependencies
try:
    import pandas as pd
    import numpy as np
    import matplotlib.pyplot as plt
    HAS_PLOTTING = True
except ImportError:
    pd = None
    np = None
    plt = None
    HAS_PLOTTING = False
    print("Warning: pandas, numpy, or matplotlib not available. Plotting functionality will be limited.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('financial_analyzer')

class FinancialAnalyzer:
    """
    A class to analyze financial data for DCF analysis.

    Features:
    - Loads and processes financial data from JSON files
    - Calculates key financial metrics and ratios
    - Analyzes historical growth rates and trends
    - Prepares data for DCF modeling
    """

    def __init__(self, data_dir='data'):
        """
        Initialize the analyzer with a directory containing financial data

        Parameters:
        -----------
        data_dir : str
            Directory containing the financial data
        """
        self.data_dir = data_dir

        # Check if data directory exists
        if not os.path.exists(data_dir):
            logger.warning(f"Data directory {data_dir} does not exist")

        # Initialize data containers
        self.company_data = {}
        self.analysis_results = {}

    def load_company_data(self, symbol: str) -> Dict:
        """
        Load financial data for a company

        Parameters:
        -----------
        symbol : str
            Company symbol (e.g., 'TATAMOTORS')

        Returns:
        --------
        Dictionary with company financial data
        """
        try:
            # Try to load the full data file
            full_file_path = os.path.join(self.data_dir, 'processed', f"{symbol}_full.json")

            if os.path.exists(full_file_path):
                with open(full_file_path, 'r') as f:
                    data = json.load(f)
                logger.info(f"Loaded data for {symbol} from {full_file_path}")

                # Store the data
                self.company_data[symbol] = data

                return data
            else:
                logger.error(f"Data file for {symbol} not found at {full_file_path}")
                return {}

        except Exception as e:
            logger.error(f"Error loading data for {symbol}: {str(e)}")
            traceback.print_exc()
            return {}

    def analyze_financials(self, symbol: str) -> Dict:
        """
        Analyze financial data for a company

        Parameters:
        -----------
        symbol : str
            Company symbol (e.g., 'TATAMOTORS')

        Returns:
        --------
        Dictionary with analysis results
        """
        # Make sure we have the data
        if symbol not in self.company_data:
            data = self.load_company_data(symbol)
            if not data:
                logger.error(f"No data available for {symbol}")
                return {}
        else:
            data = self.company_data[symbol]

        # Initialize results dictionary
        results = {
            'company_name': data.get('overview', {}).get('name', symbol),
            'analysis_date': datetime.now().strftime("%Y-%m-%d"),
            'metrics': {},
            'growth_rates': {},
            'forecasts': {},
            'dcf_inputs': {}
        }

        # Extract and analyze key financial metrics
        try:
            # 1. Analyze revenue and profit trends
            revenue_analysis = self._analyze_revenue_profit(data)
            results['metrics'].update(revenue_analysis)

            # 2. Analyze balance sheet metrics
            balance_sheet_analysis = self._analyze_balance_sheet(data)
            results['metrics'].update(balance_sheet_analysis)

            # 3. Analyze cash flow metrics
            cash_flow_analysis = self._analyze_cash_flow(data)
            results['metrics'].update(cash_flow_analysis)

            # 4. Calculate growth rates
            growth_rates = self._calculate_growth_rates(data)
            results['growth_rates'] = growth_rates

            # 5. Calculate financial ratios
            ratios = self._calculate_financial_ratios(data)
            results['metrics']['ratios'] = ratios

            # 6. Prepare DCF inputs
            dcf_inputs = self._prepare_dcf_inputs(data, growth_rates)
            results['dcf_inputs'] = dcf_inputs

            # Store the results
            self.analysis_results[symbol] = results

            logger.info(f"Completed financial analysis for {symbol}")
            return results

        except Exception as e:
            logger.error(f"Error analyzing financials for {symbol}: {str(e)}")
            traceback.print_exc()
            return {}

    def _analyze_revenue_profit(self, data: Dict) -> Dict:
        """
        Analyze revenue and profit trends

        Parameters:
        -----------
        data : dict
            Company financial data

        Returns:
        --------
        Dictionary with revenue and profit analysis
        """
        results = {}

        # Add debug logging
        logger.info(f"Analyzing revenue and profit data: {list(data.keys())}")
        if 'profit_loss' in data:
            logger.info(f"Profit & Loss keys: {list(data['profit_loss'].keys())}")

        try:
            # Extract profit & loss data
            pl_data = data.get('profit_loss', {})

            if not pl_data:
                logger.warning("No profit & loss data available")
                return results

            # Get revenue data
            revenue_key = next((k for k in pl_data.keys() if 'sales' in k.lower()), None)
            if revenue_key:
                revenue_data = pl_data[revenue_key]

                # Convert to list of (year, value) tuples and sort by year
                revenue_series = [(year, value) for year, value in revenue_data.items()]
                revenue_series.sort(key=lambda x: x[0])

                # Store the revenue series
                results['revenue_series'] = revenue_series

                # Calculate key metrics
                if len(revenue_series) > 0:
                    results['latest_revenue'] = revenue_series[-1][1]
                    results['revenue_5y_cagr'] = self._calculate_cagr(revenue_series, 5)
                    results['revenue_3y_cagr'] = self._calculate_cagr(revenue_series, 3)

            # Get net profit data
            profit_key = next((k for k in pl_data.keys() if 'net profit' in k.lower() or 'pat' in k.lower()), None)
            if profit_key:
                profit_data = pl_data[profit_key]

                # Convert to list of (year, value) tuples and sort by year
                profit_series = [(year, value) for year, value in profit_data.items()]
                profit_series.sort(key=lambda x: x[0])

                # Store the profit series
                results['profit_series'] = profit_series

                # Calculate key metrics
                if len(profit_series) > 0:
                    results['latest_profit'] = profit_series[-1][1]
                    results['profit_5y_cagr'] = self._calculate_cagr(profit_series, 5)
                    results['profit_3y_cagr'] = self._calculate_cagr(profit_series, 3)

                    # Calculate profit margins
                    if 'revenue_series' in results and len(results['revenue_series']) > 0:
                        margins = []
                        for i, (year, profit) in enumerate(profit_series):
                            # Find matching revenue for the same year
                            matching_revenue = next((rev for yr, rev in results['revenue_series'] if yr == year), None)
                            if matching_revenue and matching_revenue != 0:
                                margin = profit / matching_revenue
                                margins.append((year, margin))

                        results['profit_margin_series'] = margins

                        if margins:
                            results['latest_profit_margin'] = margins[-1][1]
                            results['avg_profit_margin_5y'] = sum(margin for _, margin in margins[-5:]) / min(5, len(margins))

            return results

        except Exception as e:
            logger.error(f"Error analyzing revenue and profit: {str(e)}")
            traceback.print_exc()
            return {}

    def _analyze_balance_sheet(self, data: Dict) -> Dict:
        """
        Analyze balance sheet metrics

        Parameters:
        -----------
        data : dict
            Company financial data

        Returns:
        --------
        Dictionary with balance sheet analysis
        """
        results = {}

        try:
            # Extract balance sheet data
            bs_data = data.get('balance_sheet', {})

            if not bs_data:
                logger.warning("No balance sheet data available")
                return results

            # Get total assets
            assets_key = next((k for k in bs_data.keys() if 'total assets' in k.lower()), None)
            if assets_key:
                assets_data = bs_data[assets_key]

                # Convert to list of (year, value) tuples and sort by year
                assets_series = [(year, value) for year, value in assets_data.items()]
                assets_series.sort(key=lambda x: x[0])

                # Store the assets series
                results['assets_series'] = assets_series

                # Calculate key metrics
                if len(assets_series) > 0:
                    results['latest_assets'] = assets_series[-1][1]

            # Get total debt
            debt_key = next((k for k in bs_data.keys() if 'total debt' in k.lower() or 'borrowings' in k.lower()), None)
            if debt_key:
                debt_data = bs_data[debt_key]

                # Convert to list of (year, value) tuples and sort by year
                debt_series = [(year, value) for year, value in debt_data.items()]
                debt_series.sort(key=lambda x: x[0])

                # Store the debt series
                results['debt_series'] = debt_series

                # Calculate key metrics
                if len(debt_series) > 0:
                    results['latest_debt'] = debt_series[-1][1]

            # Get equity
            equity_key = next((k for k in bs_data.keys() if 'total equity' in k.lower() or 'net worth' in k.lower() or 'shareholder' in k.lower()), None)
            if not equity_key:
                # Try to calculate from equity capital and reserves
                equity_capital_key = next((k for k in bs_data.keys() if 'equity capital' in k.lower()), None)
                reserves_key = next((k for k in bs_data.keys() if 'reserves' in k.lower()), None)

                if equity_capital_key and reserves_key:
                    equity_capital = bs_data[equity_capital_key]
                    reserves = bs_data[reserves_key]

                    # Combine equity capital and reserves
                    equity_data = {}
                    for year in set(equity_capital.keys()) & set(reserves.keys()):
                        equity_data[year] = equity_capital[year] + reserves[year]
                else:
                    equity_data = {}
            else:
                equity_data = bs_data[equity_key]

            # Process equity data
            if equity_data:
                # Convert to list of (year, value) tuples and sort by year
                equity_series = [(year, value) for year, value in equity_data.items()]
                equity_series.sort(key=lambda x: x[0])

                # Store the equity series
                results['equity_series'] = equity_series

                # Calculate key metrics
                if len(equity_series) > 0:
                    results['latest_equity'] = equity_series[-1][1]

                    # Calculate debt-to-equity ratio
                    if 'debt_series' in results:
                        de_ratios = []
                        for i, (year, equity) in enumerate(equity_series):
                            # Find matching debt for the same year
                            matching_debt = next((debt for yr, debt in results['debt_series'] if yr == year), None)
                            if matching_debt is not None and equity != 0:
                                de_ratio = matching_debt / equity
                                de_ratios.append((year, de_ratio))

                        results['debt_equity_ratio_series'] = de_ratios

                        if de_ratios:
                            results['latest_debt_equity_ratio'] = de_ratios[-1][1]

            return results

        except Exception as e:
            logger.error(f"Error analyzing balance sheet: {str(e)}")
            traceback.print_exc()
            return {}

    def _analyze_cash_flow(self, data: Dict) -> Dict:
        """
        Analyze cash flow metrics

        Parameters:
        -----------
        data : dict
            Company financial data

        Returns:
        --------
        Dictionary with cash flow analysis
        """
        results = {}

        try:
            # Check if this is a bank or financial institution
            is_bank = self._is_financial_institution(data)

            # Extract cash flow data
            cf_data = data.get('cash_flow', {})
            pl_data = data.get('profit_loss', {})

            if not cf_data:
                logger.warning("No cash flow data available")

                # For banks, we'll use a different approach since traditional FCF is not applicable
                if is_bank:
                    logger.info("Detected financial institution. Using alternative FCF calculation.")
                    return self._analyze_bank_cash_flow(data)
                return results

            # Get operating cash flow
            ocf_key = next((k for k in cf_data.keys() if 'operating' in k.lower() and 'cash flow' in k.lower()), None)
            if ocf_key:
                ocf_data = cf_data[ocf_key]

                # Convert to list of (year, value) tuples and sort by year
                ocf_series = [(year, value) for year, value in ocf_data.items()]
                ocf_series.sort(key=lambda x: x[0])

                # Store the OCF series
                results['ocf_series'] = ocf_series

                # Calculate key metrics
                if len(ocf_series) > 0:
                    results['latest_ocf'] = ocf_series[-1][1]
                    results['ocf_5y_cagr'] = self._calculate_cagr(ocf_series, 5)
                    results['ocf_3y_cagr'] = self._calculate_cagr(ocf_series, 3)

            # Get capital expenditure (usually negative)
            capex_key = next((k for k in cf_data.keys() if 'capital expenditure' in k.lower() or 'capex' in k.lower() or 'investing' in k.lower()), None)
            if capex_key:
                capex_data = cf_data[capex_key]

                # Convert to list of (year, value) tuples and sort by year
                # Note: Convert capex to positive values for easier analysis
                capex_series = [(year, abs(value) if value < 0 else value) for year, value in capex_data.items()]
                capex_series.sort(key=lambda x: x[0])

                # Store the capex series
                results['capex_series'] = capex_series

                # Calculate key metrics
                if len(capex_series) > 0:
                    results['latest_capex'] = capex_series[-1][1]

            # Calculate free cash flow (OCF - Capex)
            if 'ocf_series' in results and 'capex_series' in results:
                fcf_series = []
                for i, (year, ocf) in enumerate(results['ocf_series']):
                    # Find matching capex for the same year
                    matching_capex = next((capex for yr, capex in results['capex_series'] if yr == year), None)
                    if matching_capex is not None:
                        fcf = ocf - matching_capex
                        fcf_series.append((year, fcf))

                results['fcf_series'] = fcf_series

                # Calculate key metrics
                if len(fcf_series) > 0:
                    results['latest_fcf'] = fcf_series[-1][1]
                    results['fcf_5y_cagr'] = self._calculate_cagr(fcf_series, 5)
                    results['fcf_3y_cagr'] = self._calculate_cagr(fcf_series, 3)

                    # Calculate FCF margin
                    if 'revenue_series' in results.get('metrics', {}):
                        fcf_margins = []
                        for year, fcf in fcf_series:
                            # Find matching revenue for the same year
                            matching_revenue = next((rev for yr, rev in results['metrics']['revenue_series'] if yr == year), None)
                            if matching_revenue and matching_revenue != 0:
                                margin = fcf / matching_revenue
                                fcf_margins.append((year, margin))

                        results['fcf_margin_series'] = fcf_margins

                        if fcf_margins:
                            results['latest_fcf_margin'] = fcf_margins[-1][1]
                            results['avg_fcf_margin_5y'] = sum(margin for _, margin in fcf_margins[-5:]) / min(5, len(fcf_margins))

            return results

        except Exception as e:
            logger.error(f"Error analyzing cash flow: {str(e)}")
            traceback.print_exc()
            return {}

    def _is_financial_institution(self, data: Dict) -> bool:
        """
        Determine if the company is a bank or financial institution

        Parameters:
        -----------
        data : dict
            Company financial data

        Returns:
        --------
        True if the company is a bank or financial institution, False otherwise
        """
        # Check if company name contains bank-related terms
        company_name = data.get('overview', {}).get('name', '').lower()
        if any(term in company_name for term in ['bank', 'financial', 'finance', 'insurance', 'invest']):
            return True

        # Check for bank-specific metrics in profit & loss
        pl_data = data.get('profit_loss', {})
        if any(key for key in pl_data.keys() if 'interest income' in key.lower() or 'net interest' in key.lower()):
            return True

        # Check for bank-specific metrics in balance sheet
        bs_data = data.get('balance_sheet', {})
        if any(key for key in bs_data.keys() if 'loans' in key.lower() or 'deposits' in key.lower() or 'advances' in key.lower()):
            return True

        return False

    def _analyze_bank_cash_flow(self, data: Dict) -> Dict:
        """
        Analyze cash flow for banks and financial institutions

        For banks, traditional FCF (OCF - Capex) is not as relevant.
        Instead, we use a modified approach based on net income and regulatory capital.

        Parameters:
        -----------
        data : dict
            Company financial data

        Returns:
        --------
        Dictionary with bank-specific cash flow analysis
        """
        results = {}

        try:
            # Extract profit & loss data
            pl_data = data.get('profit_loss', {})

            if not pl_data:
                logger.warning("No profit & loss data available for bank analysis")
                return results

            # For banks, we'll use net profit as a proxy for distributable cash flow
            profit_key = next((k for k in pl_data.keys() if 'net profit' in k.lower() or 'pat' in k.lower()), None)
            if profit_key:
                profit_data = pl_data[profit_key]

                # Convert to list of (year, value) tuples and sort by year
                profit_series = [(year, value) for year, value in profit_data.items()]
                profit_series.sort(key=lambda x: x[0])

                # Use net profit as a proxy for FCF, but apply a conversion factor
                # Banks typically retain 30-40% of profits for capital requirements
                fcf_series = []
                for year, profit in profit_series:
                    # Apply 65% factor to account for regulatory capital requirements
                    fcf = profit * 0.65
                    fcf_series.append((year, fcf))

                results['fcf_series'] = fcf_series
                logger.info("Applied 65% factor to net profit for bank FCF calculation")

                # Calculate key metrics
                if len(fcf_series) > 0:
                    results['latest_fcf'] = fcf_series[-1][1]
                    results['fcf_5y_cagr'] = self._calculate_cagr(fcf_series, 5)
                    results['fcf_3y_cagr'] = self._calculate_cagr(fcf_series, 3)

                    # Calculate FCF margin (Net Profit / Revenue)
                    revenue_key = next((k for k in pl_data.keys() if 'revenue' in k.lower() or 'sales' in k.lower() or 'income' in k.lower()), None)
                    if revenue_key:
                        revenue_data = pl_data[revenue_key]

                        fcf_margins = []
                        for year, fcf in fcf_series:
                            if year in revenue_data and revenue_data[year] != 0:
                                margin = fcf / revenue_data[year]
                                fcf_margins.append((year, margin))

                        results['fcf_margin_series'] = fcf_margins

                        if fcf_margins:
                            results['latest_fcf_margin'] = fcf_margins[-1][1]
                            results['avg_fcf_margin_5y'] = sum(margin for _, margin in fcf_margins[-5:]) / min(5, len(fcf_margins))

            # Also include operating cash flow if available
            cf_data = data.get('cash_flow', {})
            if cf_data:
                ocf_key = next((k for k in cf_data.keys() if 'operating' in k.lower() and 'cash flow' in k.lower()), None)
                if ocf_key:
                    ocf_data = cf_data[ocf_key]

                    # Convert to list of (year, value) tuples and sort by year
                    ocf_series = [(year, value) for year, value in ocf_data.items()]
                    ocf_series.sort(key=lambda x: x[0])

                    # Store the OCF series
                    results['ocf_series'] = ocf_series

                    # Calculate key metrics
                    if len(ocf_series) > 0:
                        results['latest_ocf'] = ocf_series[-1][1]
                        results['ocf_5y_cagr'] = self._calculate_cagr(ocf_series, 5)
                        results['ocf_3y_cagr'] = self._calculate_cagr(ocf_series, 3)

            return results

        except Exception as e:
            logger.error(f"Error analyzing bank cash flow: {str(e)}")
            traceback.print_exc()
            return {}

    def _calculate_growth_rates(self, data: Dict) -> Dict:
        """
        Calculate historical growth rates for key metrics

        Parameters:
        -----------
        data : dict
            Company financial data

        Returns:
        --------
        Dictionary with growth rates
        """
        growth_rates = {}

        try:
            # Extract profit & loss data for revenue growth
            pl_data = data.get('profit_loss', {})

            if pl_data:
                # Calculate revenue growth rates
                revenue_key = next((k for k in pl_data.keys() if 'sales' in k.lower()), None)
                if revenue_key:
                    revenue_data = pl_data[revenue_key]

                    # Convert to list of (year, value) tuples and sort by year
                    revenue_series = [(year, value) for year, value in revenue_data.items()]
                    revenue_series.sort(key=lambda x: x[0])

                    # Calculate year-over-year growth rates
                    revenue_growth = []
                    for i in range(1, len(revenue_series)):
                        prev_year, prev_value = revenue_series[i-1]
                        curr_year, curr_value = revenue_series[i]

                        if prev_value != 0:
                            growth = (curr_value - prev_value) / prev_value
                            revenue_growth.append((curr_year, growth))

                    growth_rates['revenue_yoy'] = revenue_growth

                    # Calculate average growth rates
                    if revenue_growth:
                        growth_rates['revenue_growth_5y_avg'] = sum(rate for _, rate in revenue_growth[-5:]) / min(5, len(revenue_growth))
                        growth_rates['revenue_growth_3y_avg'] = sum(rate for _, rate in revenue_growth[-3:]) / min(3, len(revenue_growth))
                        growth_rates['revenue_growth_latest'] = revenue_growth[-1][1] if revenue_growth else 0

                # Calculate profit growth rates
                profit_key = next((k for k in pl_data.keys() if 'net profit' in k.lower() or 'pat' in k.lower()), None)
                if profit_key:
                    profit_data = pl_data[profit_key]

                    # Convert to list of (year, value) tuples and sort by year
                    profit_series = [(year, value) for year, value in profit_data.items()]
                    profit_series.sort(key=lambda x: x[0])

                    # Calculate year-over-year growth rates
                    profit_growth = []
                    for i in range(1, len(profit_series)):
                        prev_year, prev_value = profit_series[i-1]
                        curr_year, curr_value = profit_series[i]

                        if prev_value != 0:
                            growth = (curr_value - prev_value) / prev_value
                            profit_growth.append((curr_year, growth))

                    growth_rates['profit_yoy'] = profit_growth

                    # Calculate average growth rates
                    if profit_growth:
                        growth_rates['profit_growth_5y_avg'] = sum(rate for _, rate in profit_growth[-5:]) / min(5, len(profit_growth))
                        growth_rates['profit_growth_3y_avg'] = sum(rate for _, rate in profit_growth[-3:]) / min(3, len(profit_growth))
                        growth_rates['profit_growth_latest'] = profit_growth[-1][1] if profit_growth else 0

            # Extract cash flow data for FCF growth
            cf_data = data.get('cash_flow', {})

            if cf_data:
                # Get operating cash flow
                ocf_key = next((k for k in cf_data.keys() if 'operating' in k.lower() and 'cash flow' in k.lower()), None)
                capex_key = next((k for k in cf_data.keys() if 'capital expenditure' in k.lower() or 'capex' in k.lower() or 'investing' in k.lower()), None)

                if ocf_key and capex_key:
                    ocf_data = cf_data[ocf_key]
                    capex_data = cf_data[capex_key]

                    # Calculate FCF for each year
                    fcf_data = {}
                    for year in set(ocf_data.keys()) & set(capex_data.keys()):
                        # Capex is usually negative, so we add it (or subtract its absolute value)
                        capex = capex_data[year]
                        capex_abs = abs(capex) if capex < 0 else capex
                        fcf_data[year] = ocf_data[year] - capex_abs

                    # Convert to list of (year, value) tuples and sort by year
                    fcf_series = [(year, value) for year, value in fcf_data.items()]
                    fcf_series.sort(key=lambda x: x[0])

                    # Calculate year-over-year growth rates
                    fcf_growth = []
                    for i in range(1, len(fcf_series)):
                        prev_year, prev_value = fcf_series[i-1]
                        curr_year, curr_value = fcf_series[i]

                        if prev_value != 0:
                            growth = (curr_value - prev_value) / prev_value
                            fcf_growth.append((curr_year, growth))

                    growth_rates['fcf_yoy'] = fcf_growth

                    # Calculate average growth rates
                    if fcf_growth:
                        growth_rates['fcf_growth_5y_avg'] = sum(rate for _, rate in fcf_growth[-5:]) / min(5, len(fcf_growth))
                        growth_rates['fcf_growth_3y_avg'] = sum(rate for _, rate in fcf_growth[-3:]) / min(3, len(fcf_growth))
                        growth_rates['fcf_growth_latest'] = fcf_growth[-1][1] if fcf_growth else 0

            return growth_rates

        except Exception as e:
            logger.error(f"Error calculating growth rates: {str(e)}")
            traceback.print_exc()
            return {}

    def _calculate_financial_ratios(self, data: Dict) -> Dict:
        """
        Calculate key financial ratios

        Parameters:
        -----------
        data : dict
            Company financial data

        Returns:
        --------
        Dictionary with financial ratios
        """
        ratios = {}

        try:
            # Extract data from different sections
            pl_data = data.get('profit_loss', {})
            bs_data = data.get('balance_sheet', {})
            cf_data = data.get('cash_flow', {})

            # Get the latest year's data
            latest_year = None

            # Try to find the latest year from revenue data
            revenue_key = next((k for k in pl_data.keys() if 'sales' in k.lower()), None)
            if revenue_key:
                revenue_data = pl_data[revenue_key]
                years = list(revenue_data.keys())
                if years:
                    years.sort()
                    latest_year = years[-1]

            if not latest_year:
                logger.warning("Could not determine latest year for financial ratios")
                return ratios

            # Calculate profitability ratios
            profit_key = next((k for k in pl_data.keys() if 'net profit' in k.lower() or 'pat' in k.lower()), None)
            ebit_key = next((k for k in pl_data.keys() if 'ebit' in k.lower() or 'operating profit' in k.lower()), None)

            if revenue_key and profit_key and latest_year in pl_data[revenue_key] and latest_year in pl_data[profit_key]:
                revenue = pl_data[revenue_key][latest_year]
                profit = pl_data[profit_key][latest_year]

                if revenue != 0:
                    ratios['net_profit_margin'] = profit / revenue

            if revenue_key and ebit_key and latest_year in pl_data[revenue_key] and latest_year in pl_data[ebit_key]:
                revenue = pl_data[revenue_key][latest_year]
                ebit = pl_data[ebit_key][latest_year]

                if revenue != 0:
                    ratios['operating_margin'] = ebit / revenue

            # Calculate return ratios
            assets_key = next((k for k in bs_data.keys() if 'total assets' in k.lower()), None)
            equity_key = next((k for k in bs_data.keys() if 'total equity' in k.lower() or 'net worth' in k.lower() or 'shareholder' in k.lower()), None)

            if not equity_key:
                # Try to calculate from equity capital and reserves
                equity_capital_key = next((k for k in bs_data.keys() if 'equity capital' in k.lower()), None)
                reserves_key = next((k for k in bs_data.keys() if 'reserves' in k.lower()), None)

                if equity_capital_key and reserves_key and latest_year in bs_data[equity_capital_key] and latest_year in bs_data[reserves_key]:
                    equity = bs_data[equity_capital_key][latest_year] + bs_data[reserves_key][latest_year]
                else:
                    equity = None
            else:
                equity = bs_data[equity_key][latest_year] if latest_year in bs_data[equity_key] else None

            if profit_key and equity and latest_year in pl_data[profit_key]:
                profit = pl_data[profit_key][latest_year]

                if equity != 0:
                    ratios['roe'] = profit / equity

            if profit_key and assets_key and latest_year in pl_data[profit_key] and latest_year in bs_data[assets_key]:
                profit = pl_data[profit_key][latest_year]
                assets = bs_data[assets_key][latest_year]

                if assets != 0:
                    ratios['roa'] = profit / assets

            # Calculate leverage ratios
            debt_key = next((k for k in bs_data.keys() if 'total debt' in k.lower() or 'borrowings' in k.lower()), None)

            if debt_key and equity and latest_year in bs_data[debt_key]:
                debt = bs_data[debt_key][latest_year]

                if equity != 0:
                    ratios['debt_to_equity'] = debt / equity

            if debt_key and assets_key and latest_year in bs_data[debt_key] and latest_year in bs_data[assets_key]:
                debt = bs_data[debt_key][latest_year]
                assets = bs_data[assets_key][latest_year]

                if assets != 0:
                    ratios['debt_to_assets'] = debt / assets

            # Calculate liquidity ratios
            current_assets_key = next((k for k in bs_data.keys() if 'current assets' in k.lower()), None)
            current_liabilities_key = next((k for k in bs_data.keys() if 'current liabilities' in k.lower()), None)

            if current_assets_key and current_liabilities_key and latest_year in bs_data[current_assets_key] and latest_year in bs_data[current_liabilities_key]:
                current_assets = bs_data[current_assets_key][latest_year]
                current_liabilities = bs_data[current_liabilities_key][latest_year]

                if current_liabilities != 0:
                    ratios['current_ratio'] = current_assets / current_liabilities

            # Calculate cash flow ratios
            ocf_key = next((k for k in cf_data.keys() if 'operating' in k.lower() and 'cash flow' in k.lower()), None)

            if ocf_key and revenue_key and latest_year in cf_data[ocf_key] and latest_year in pl_data[revenue_key]:
                ocf = cf_data[ocf_key][latest_year]
                revenue = pl_data[revenue_key][latest_year]

                if revenue != 0:
                    ratios['ocf_to_revenue'] = ocf / revenue

            if ocf_key and debt_key and latest_year in cf_data[ocf_key] and latest_year in bs_data[debt_key]:
                ocf = cf_data[ocf_key][latest_year]
                debt = bs_data[debt_key][latest_year]

                if debt != 0:
                    ratios['ocf_to_debt'] = ocf / debt

            return ratios

        except Exception as e:
            logger.error(f"Error calculating financial ratios: {str(e)}")
            traceback.print_exc()
            return {}

    def _prepare_dcf_inputs(self, data: Dict, growth_rates: Dict) -> Dict:
        """
        Prepare inputs for DCF model

        Parameters:
        -----------
        data : dict
            Company financial data
        growth_rates : dict
            Historical growth rates

        Returns:
        --------
        Dictionary with DCF inputs
        """
        dcf_inputs = {}

        # Add debug logging
        logger.info(f"Preparing DCF inputs with data keys: {list(data.keys())}")
        logger.info(f"Growth rates: {growth_rates}")

        # Check if this is a bank or financial institution
        is_bank = self._is_financial_institution(data)
        if is_bank:
            logger.info("Detected financial institution. Using bank-specific DCF inputs.")

        # Initialize with default values that will be overridden if real data is available
        # Use industry averages based on available data (in millions, not crores)
        symbol = data.get('overview', {}).get('symbol', '')
        # Add symbol to DCF inputs for use in other calculations
        dcf_inputs['symbol'] = symbol

        industry_revenues = {
            'TATAMOTORS': 3500000.0,  # ~350,000 Cr
            'TCS': 2200000.0,         # ~220,000 Cr
            'RELIANCE': 8000000.0,    # ~800,000 Cr
            'HDFCBANK': 1500000.0,    # ~150,000 Cr
            'SBIN': 4000000.0         # ~400,000 Cr
        }

        # Set default values
        if symbol in industry_revenues:
            dcf_inputs['base_revenue'] = industry_revenues[symbol]
            logger.info(f"Setting default base revenue for {symbol}: ₹{dcf_inputs['base_revenue']/********:,.2f} Cr")
        else:
            dcf_inputs['base_revenue'] = 1000000.0  # Default to 100,000 Cr
            logger.info(f"Setting default base revenue: ₹{dcf_inputs['base_revenue']/********:,.2f} Cr")

        # Set default FCF margin based on industry
        if is_bank:
            dcf_inputs['base_fcf_margin'] = 0.15  # 15% for banks
            logger.info(f"Setting default FCF margin for bank: {dcf_inputs['base_fcf_margin']:.1%}")
        else:
            dcf_inputs['base_fcf_margin'] = 0.08  # 8% for non-banks
            logger.info(f"Setting default FCF margin for non-bank: {dcf_inputs['base_fcf_margin']:.1%}")

        # For banks, set the is_bank flag
        if is_bank:
            dcf_inputs['is_bank'] = True  # Flag to indicate this is a bank

        try:
            # Extract latest financial data
            pl_data = data.get('profit_loss', {})
            bs_data = data.get('balance_sheet', {})
            cf_data = data.get('cash_flow', {})

            # Get the latest year's data
            latest_year = None

            # Try to find the latest year from revenue data
            revenue_key = next((k for k in pl_data.keys() if 'sales' in k.lower()), None)
            if revenue_key:
                revenue_data = pl_data[revenue_key]
                years = list(revenue_data.keys())
                if years:
                    years.sort()
                    latest_year = years[-1]

            if not latest_year:
                logger.warning("Could not determine latest year for DCF inputs")
                return dcf_inputs

            # Set base year for projections
            dcf_inputs['base_year'] = latest_year

            # Get latest revenue
            if revenue_key and latest_year in pl_data[revenue_key]:
                dcf_inputs['base_revenue'] = pl_data[revenue_key][latest_year]
            else:
                # If revenue data is missing, log a warning and use a reasonable estimate
                logger.warning(f"Revenue data missing for {latest_year}. Using estimate.")

                # Use industry averages based on available data (in millions, not crores)
                industry_revenues = {
                    'TATAMOTORS': 3500000.0,  # ~350,000 Cr
                    'TCS': 2200000.0,         # ~220,000 Cr
                    'RELIANCE': 8000000.0,    # ~800,000 Cr
                    'HDFCBANK': 1500000.0,    # ~150,000 Cr
                    'SBIN': 4000000.0         # ~400,000 Cr
                }

                # Get company symbol from data
                symbol = data.get('overview', {}).get('symbol', '')

                if symbol in industry_revenues:
                    dcf_inputs['base_revenue'] = industry_revenues[symbol]
                    logger.warning(f"Using estimated revenue for {symbol}: ₹{dcf_inputs['base_revenue']:,.2f} Cr")
                else:
                    # Default to a reasonable value if nothing else is available
                    dcf_inputs['base_revenue'] = 100000.0  # 100,000 Cr
                    logger.warning(f"Using default revenue: ₹{dcf_inputs['base_revenue']:,.2f} Cr")

            # Get latest profit margin
            profit_key = next((k for k in pl_data.keys() if 'net profit' in k.lower() or 'pat' in k.lower()), None)
            if revenue_key and profit_key and latest_year in pl_data[revenue_key] and latest_year in pl_data[profit_key]:
                revenue = pl_data[revenue_key][latest_year]
                profit = pl_data[profit_key][latest_year]

                if revenue != 0:
                    dcf_inputs['base_profit_margin'] = profit / revenue

            # Get latest FCF and FCF margin
            ocf_key = next((k for k in cf_data.keys() if 'operating' in k.lower() and 'cash flow' in k.lower()), None)
            capex_key = next((k for k in cf_data.keys() if 'capital expenditure' in k.lower() or 'capex' in k.lower() or 'investing' in k.lower()), None)

            if ocf_key and capex_key and latest_year in cf_data[ocf_key] and latest_year in cf_data[capex_key]:
                ocf = cf_data[ocf_key][latest_year]
                capex = cf_data[capex_key][latest_year]
                capex_abs = abs(capex) if capex < 0 else capex

                fcf = ocf - capex_abs
                dcf_inputs['base_fcf'] = fcf

                if revenue_key and latest_year in pl_data[revenue_key]:
                    revenue = pl_data[revenue_key][latest_year]

                    if revenue != 0:
                        dcf_inputs['base_fcf_margin'] = fcf / revenue

            # Set growth rate assumptions based on historical data
            if 'revenue_growth_5y_avg' in growth_rates:
                # Use 5-year average as a starting point
                base_growth = growth_rates['revenue_growth_5y_avg']

                # Cap the growth rate at reasonable levels
                base_growth = min(base_growth, 0.25)  # Cap at 25%
                base_growth = max(base_growth, 0.02)  # Floor at 2%

                dcf_inputs['revenue_growth_initial'] = base_growth

                # Set declining growth rates for future years
                dcf_inputs['revenue_growth_years_1_5'] = base_growth
                dcf_inputs['revenue_growth_years_6_10'] = max(base_growth * 0.7, 0.03)  # 70% of initial or 3%, whichever is higher
                dcf_inputs['revenue_growth_terminal'] = 0.03  # Long-term growth rate (3%)

            # Set FCF margin assumptions
            if 'base_fcf_margin' in dcf_inputs and dcf_inputs['base_fcf_margin'] is not None:
                base_margin = dcf_inputs['base_fcf_margin']
            else:
                # If FCF margin calculation failed, estimate it based on industry averages and company type
                if is_bank:
                    # Banks typically have higher margins after adjustment
                    base_margin = 0.15  # 15% for banks
                    logger.warning("Using estimated FCF margin for bank: 15%")
                else:
                    # For non-banks, use industry-specific estimates
                    symbol = data.get('overview', {}).get('symbol', '')

                    # Industry-specific FCF margins based on historical data
                    industry_margins = {
                        'TATAMOTORS': 0.06,  # Auto industry typically 5-7%
                        'TCS': 0.18,         # IT services typically 15-20%
                        'RELIANCE': 0.10,    # Conglomerate with mix of businesses
                        'HDFCBANK': 0.15,    # Bank
                        'SBIN': 0.15         # Bank
                    }

                    if symbol in industry_margins:
                        base_margin = industry_margins[symbol]
                        logger.warning(f"Using industry-specific FCF margin for {symbol}: {base_margin:.1%}")
                    else:
                        # Default to 8% if no specific data
                        base_margin = 0.08
                        logger.warning(f"Using default FCF margin: {base_margin:.1%}")

            # Use profit margin as a proxy if available and we still don't have a margin
            if base_margin is None and 'base_profit_margin' in dcf_inputs and dcf_inputs['base_profit_margin'] is not None:
                # FCF margin is typically lower than profit margin
                base_margin = dcf_inputs['base_profit_margin'] * 0.8
                logger.warning(f"Estimating FCF margin from profit margin: {base_margin:.2%}")

                # Store the estimated margin
                dcf_inputs['base_fcf_margin'] = base_margin
                logger.info(f"Final FCF margin used: {base_margin:.2%}")

            # Cap the margin at reasonable levels
            base_margin = min(base_margin, 0.30)  # Cap at 30%
            base_margin = max(base_margin, 0.05)  # Floor at 5%

            dcf_inputs['fcf_margin_years_1_5'] = base_margin
            dcf_inputs['fcf_margin_years_6_10'] = min(base_margin * 1.1, 0.35)  # Assume slight improvement over time
            dcf_inputs['fcf_margin_terminal'] = min(base_margin * 1.2, 0.35)  # Terminal margin

            # Set discount rate (WACC) assumptions
            # We'll use industry-specific WACCs based on real data
            industry_waccs = {
                'TATAMOTORS': 0.11,  # Auto industry ~11%
                'TCS': 0.10,         # IT services ~10%
                'RELIANCE': 0.12,    # Conglomerate ~12%
                'HDFCBANK': 0.09,    # Banking ~9%
                'SBIN': 0.09         # Banking ~9%
            }

            # Get company symbol from data
            symbol = data.get('overview', {}).get('symbol', '')

            if symbol in industry_waccs:
                dcf_inputs['discount_rate'] = industry_waccs[symbol]
                logger.info(f"Using industry-specific WACC for {symbol}: {dcf_inputs['discount_rate']:.2%}")
            else:
                # Default to 12% if nothing else is available
                dcf_inputs['discount_rate'] = 0.12  # Default to 12%
                logger.info(f"Using default WACC: {dcf_inputs['discount_rate']:.2%}")

            # Set terminal value assumptions based on industry and company characteristics

            # Terminal growth rates by industry
            industry_growth_rates = {
                'TATAMOTORS': 0.04,  # Auto industry ~4%
                'TCS': 0.05,         # IT services ~5%
                'RELIANCE': 0.04,    # Conglomerate ~4%
                'HDFCBANK': 0.03,    # Banking ~3%
                'SBIN': 0.03         # Banking ~3%
            }

            # Terminal multiples by industry
            industry_multiples = {
                'TATAMOTORS': 12,  # Auto industry ~12x
                'TCS': 20,         # IT services ~20x
                'RELIANCE': 15,    # Conglomerate ~15x
                'HDFCBANK': 12,    # Banking ~12x
                'SBIN': 10         # Banking ~10x
            }

            # Get company symbol from data
            symbol = data.get('overview', {}).get('symbol', '')

            # Set terminal growth rate
            if symbol in industry_growth_rates:
                dcf_inputs['terminal_growth_rate'] = industry_growth_rates[symbol]
                logger.info(f"Using industry-specific terminal growth rate for {symbol}: {dcf_inputs['terminal_growth_rate']:.2%}")
            else:
                # Default to 3% if nothing else is available
                dcf_inputs['terminal_growth_rate'] = 0.03  # Long-term growth rate (3%)
                logger.info(f"Using default terminal growth rate: {dcf_inputs['terminal_growth_rate']:.2%}")

            # Set terminal multiple
            if symbol in industry_multiples:
                dcf_inputs['terminal_multiple'] = industry_multiples[symbol]
                logger.info(f"Using industry-specific terminal multiple for {symbol}: {dcf_inputs['terminal_multiple']}x")
            else:
                # Default to 15x if nothing else is available
                dcf_inputs['terminal_multiple'] = 15  # Terminal EV/EBITDA multiple
                logger.info(f"Using default terminal multiple: {dcf_inputs['terminal_multiple']}x")

            # Calculate shares outstanding from net profit and EPS
            shares_calculated = False

            if 'net_profit' in pl_data and 'eps_in_rs' in pl_data:
                try:
                    latest_year = max(pl_data['net_profit'].keys())
                    if latest_year in pl_data['net_profit'] and latest_year in pl_data['eps_in_rs']:
                        net_profit = pl_data['net_profit'][latest_year]
                        eps = pl_data['eps_in_rs'][latest_year]

                        # Only calculate if both values are valid and non-zero
                        if eps != 0 and net_profit != 0:
                            # Calculate shares outstanding (in millions)
                            shares_outstanding = abs(net_profit / eps) * 1000000  # Convert to actual number of shares
                            dcf_inputs['shares_outstanding'] = shares_outstanding
                            logger.info(f"Calculated shares outstanding for {latest_year}: {shares_outstanding:,.0f}")
                            shares_calculated = True
                        else:
                            logger.warning(f"Cannot calculate shares outstanding: EPS or net profit is zero")
                except Exception as e:
                    logger.warning(f"Error calculating shares outstanding from EPS: {str(e)}")

            # If we couldn't calculate from EPS, try using equity capital and face value
            if not shares_calculated:
                try:
                    # Try to get equity capital
                    equity_capital_key = next((k for k in bs_data.keys() if 'equity capital' in k.lower() or 'share capital' in k.lower()), None)
                    face_value_key = next((k for k in data.get('overview', {}).keys() if 'face value' in k.lower()), None)

                    if equity_capital_key and latest_year in bs_data[equity_capital_key]:
                        equity_capital = bs_data[equity_capital_key][latest_year]

                        # Get face value (par value) of shares
                        face_value = 10.0  # Default to ₹10 per share
                        if face_value_key and face_value_key in data.get('overview', {}):
                            try:
                                face_value = float(data['overview'][face_value_key])
                            except:
                                logger.warning(f"Could not parse face value: {data['overview'].get(face_value_key)}")

                        # Calculate shares outstanding
                        if face_value > 0:
                            # Equity capital is in Cr, convert to actual number of shares
                            shares_outstanding = (equity_capital * ********) / face_value
                            dcf_inputs['shares_outstanding'] = shares_outstanding
                            logger.info(f"Calculated shares outstanding from equity capital: {shares_outstanding:,.0f}")
                            shares_calculated = True
                except Exception as e:
                    logger.warning(f"Error calculating shares outstanding from equity capital: {str(e)}")

            # If we still don't have shares outstanding, use industry data
            if not shares_calculated:
                # Use industry averages based on available data
                industry_shares = {
                    'TATAMOTORS': **********,  # ~3.3 billion shares
                    'TCS': **********,        # ~3.66 billion shares
                    'RELIANCE': **********,   # ~6.77 billion shares
                    'HDFCBANK': **********,   # ~5.57 billion shares
                    'SBIN': **********        # ~8.92 billion shares
                }

                # Get company symbol from data
                symbol = data.get('overview', {}).get('symbol', '')

                if symbol in industry_shares:
                    dcf_inputs['shares_outstanding'] = industry_shares[symbol]
                    logger.warning(f"Using known shares outstanding for {symbol}: {dcf_inputs['shares_outstanding']:,.0f}")
                else:
                    # Default to a reasonable value if nothing else is available
                    dcf_inputs['shares_outstanding'] = ********00  # 1 billion shares
                    logger.warning(f"Using default shares outstanding: {dcf_inputs['shares_outstanding']:,.0f}")

            return dcf_inputs

        except Exception as e:
            logger.error(f"Error preparing DCF inputs: {str(e)}")
            traceback.print_exc()
            return {}

    def _calculate_cagr(self, data_series: List[Tuple[str, float]], years: int = 5) -> float:
        """
        Calculate Compound Annual Growth Rate (CAGR)

        Parameters:
        -----------
        data_series : list
            List of (year, value) tuples
        years : int
            Number of years to calculate CAGR for

        Returns:
        --------
        CAGR as a float
        """
        if not data_series or len(data_series) < 2:
            return 0.0

        # Sort by year
        sorted_data = sorted(data_series, key=lambda x: x[0])

        # Get the latest value
        end_year, end_value = sorted_data[-1]

        # Get the start value (n years ago)
        start_idx = max(0, len(sorted_data) - years - 1)
        start_year, start_value = sorted_data[start_idx]

        # Calculate years difference
        try:
            # Try to parse years as dates
            end_date = datetime.strptime(end_year, '%b %Y')
            start_date = datetime.strptime(start_year, '%b %Y')
            years_diff = (end_date.year - start_date.year) + (end_date.month - start_date.month) / 12
        except:
            # If parsing fails, use the provided years parameter
            years_diff = min(years, len(sorted_data) - start_idx - 1)

        # Calculate CAGR
        if start_value <= 0 or years_diff <= 0:
            return 0.0

        cagr = (end_value / start_value) ** (1 / years_diff) - 1
        return cagr
