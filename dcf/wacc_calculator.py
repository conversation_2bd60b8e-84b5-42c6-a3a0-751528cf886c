#!/usr/bin/env python3
"""
WACC (Weighted Average Cost of Capital) Calculator

This module calculates the Weighted Average Cost of Capital (WACC) for use in DCF analysis.
It implements the Capital Asset Pricing Model (CAPM) for cost of equity calculation.
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import math

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('wacc_calculator')

class WACCCalculator:
    """
    A class to calculate the Weighted Average Cost of Capital (WACC).

    Features:
    - Calculates cost of equity using CAPM
    - Calculates cost of debt
    - Determines capital structure weights
    - Calculates WACC
    """

    def __init__(self, data_dir='data'):
        """
        Initialize the WACC calculator

        Parameters:
        -----------
        data_dir : str
            Directory containing financial data
        """
        self.data_dir = data_dir

        # Default market parameters for India
        self.risk_free_rate = 0.068  # 6.8% (current 10-year Indian government bond yield)
        self.market_risk_premium = 0.055  # 5.5% (current Indian market risk premium)
        self.default_beta = 1.0  # Default beta if not available

        # Industry-specific betas (based on Indian market)
        self.industry_betas = {
            'automotive': 1.2,
            'technology': 1.1,
            'banking': 1.3,
            'pharmaceuticals': 0.9,
            'energy': 1.15,
            'consumer_goods': 0.85,
            'telecom': 1.05,
            'infrastructure': 1.25
        }

        # Company to industry mapping
        self.company_industries = {
            'TATAMOTORS': 'automotive',
            'TCS': 'technology',
            'RELIANCE': 'energy',
            'HDFCBANK': 'banking',
            'SUNPHARMA': 'pharmaceuticals',
            'BHARTIARTL': 'telecom',
            'ITC': 'consumer_goods',
            'LT': 'infrastructure'
        }

    def calculate_wacc(self, symbol: str, financial_data: Optional[Dict] = None) -> Dict:
        """
        Calculate WACC for a company

        Parameters:
        -----------
        symbol : str
            Company symbol (e.g., 'TATAMOTORS')
        financial_data : dict, optional
            Financial data for the company. If None, will try to load from file.

        Returns:
        --------
        Dictionary with WACC calculation results
        """
        results = {
            'wacc': 0.12,  # Default WACC if calculation fails
            'cost_of_equity': 0.0,
            'cost_of_debt': 0.0,
            'equity_weight': 0.0,
            'debt_weight': 0.0,
            'tax_rate': 0.0,
            'beta': 0.0,
            'risk_free_rate': self.risk_free_rate,
            'market_risk_premium': self.market_risk_premium
        }

        try:
            # Load financial data if not provided
            if financial_data is None:
                financial_data = self._load_financial_data(symbol)

            if not financial_data:
                logger.warning(f"No financial data available for {symbol}")
                return results

            # Calculate cost of equity
            cost_of_equity, beta = self._calculate_cost_of_equity(symbol, financial_data)
            results['cost_of_equity'] = cost_of_equity
            results['beta'] = beta

            # Calculate cost of debt
            cost_of_debt, tax_rate = self._calculate_cost_of_debt(financial_data)
            results['cost_of_debt'] = cost_of_debt
            results['tax_rate'] = tax_rate

            # Get capital structure
            equity_weight, debt_weight = self._get_capital_structure(financial_data)
            results['equity_weight'] = equity_weight
            results['debt_weight'] = debt_weight

            # Calculate WACC
            wacc = (equity_weight * cost_of_equity) + (debt_weight * cost_of_debt * (1 - tax_rate))
            results['wacc'] = wacc

            logger.info(f"Calculated WACC for {symbol}: {wacc:.2%}")
            return results

        except Exception as e:
            logger.error(f"Error calculating WACC for {symbol}: {str(e)}")
            traceback.print_exc()
            return results

    def _load_financial_data(self, symbol: str) -> Dict:
        """
        Load financial data for a company

        Parameters:
        -----------
        symbol : str
            Company symbol

        Returns:
        --------
        Dictionary with financial data
        """
        try:
            # Try to load the full data file
            full_file_path = os.path.join(self.data_dir, 'processed', f"{symbol}_full.json")

            if os.path.exists(full_file_path):
                with open(full_file_path, 'r') as f:
                    data = json.load(f)
                logger.info(f"Loaded data for {symbol} from {full_file_path}")
                return data
            else:
                logger.error(f"Data file for {symbol} not found at {full_file_path}")
                return {}

        except Exception as e:
            logger.error(f"Error loading data for {symbol}: {str(e)}")
            traceback.print_exc()
            return {}

    def _calculate_cost_of_equity(self, symbol: str, financial_data: Dict) -> Tuple[float, float]:
        """
        Calculate cost of equity using CAPM

        Parameters:
        -----------
        symbol : str
            Company symbol
        financial_data : dict
            Financial data for the company

        Returns:
        --------
        Tuple of (cost_of_equity, beta)
        """
        # Try to get beta from financial data
        beta = None

        # Check if beta is available in ratios
        if 'ratios' in financial_data and 'ratios' in financial_data['ratios']:
            ratios = financial_data['ratios']['ratios']
            if 'beta' in ratios:
                beta = ratios['beta']
                logger.info(f"Using beta from financial data: {beta}")

        # If beta is not available, use industry-specific beta
        if not beta or beta == 0:
            # Get industry for the company
            industry = self.company_industries.get(symbol)

            if industry and industry in self.industry_betas:
                beta = self.industry_betas[industry]
                logger.info(f"Using industry-specific beta for {industry}: {beta}")
            else:
                # Try to determine industry from company name or financial data
                if 'overview' in financial_data and 'name' in financial_data['overview']:
                    company_name = financial_data['overview']['name'].lower()

                    # Simple industry detection based on company name
                    if any(term in company_name for term in ['motor', 'auto', 'car']):
                        beta = self.industry_betas['automotive']
                        logger.info(f"Detected automotive industry from name: {beta}")
                    elif any(term in company_name for term in ['tech', 'software', 'digital']):
                        beta = self.industry_betas['technology']
                        logger.info(f"Detected technology industry from name: {beta}")
                    elif any(term in company_name for term in ['bank', 'finance', 'financial']):
                        beta = self.industry_betas['banking']
                        logger.info(f"Detected banking industry from name: {beta}")
                    elif any(term in company_name for term in ['pharma', 'drug', 'healthcare']):
                        beta = self.industry_betas['pharmaceuticals']
                        logger.info(f"Detected pharmaceuticals industry from name: {beta}")
                    elif any(term in company_name for term in ['oil', 'gas', 'power', 'energy']):
                        beta = self.industry_betas['energy']
                        logger.info(f"Detected energy industry from name: {beta}")
                    else:
                        beta = self.default_beta
                        logger.info(f"Using default beta: {beta}")
                else:
                    beta = self.default_beta
                    logger.info(f"Using default beta: {beta}")

        # Calculate size premium based on market cap (smaller companies have higher risk)
        size_premium = 0.0

        # Try to estimate market cap
        market_cap = None
        if 'shares_outstanding' in financial_data and 'current_price' in financial_data:
            market_cap = financial_data['shares_outstanding'] * financial_data['current_price']

        # Apply size premium based on market cap
        if market_cap:
            if market_cap < 10000000000:  # Less than 1,000 crores
                size_premium = 0.03  # 3% premium for small cap
            elif market_cap < 50000000000:  # Less than 5,000 crores
                size_premium = 0.02  # 2% premium for mid cap
            elif market_cap < ************:  # Less than 20,000 crores
                size_premium = 0.01  # 1% premium for large cap
            logger.info(f"Applied size premium: {size_premium:.2%}")

        # Calculate country risk premium (for emerging markets like India)
        country_risk_premium = 0.01  # 1% additional premium for India

        # Calculate cost of equity using modified CAPM
        # Cost of Equity = Risk-Free Rate + Beta * Market Risk Premium + Size Premium + Country Risk Premium
        cost_of_equity = self.risk_free_rate + (beta * self.market_risk_premium) + size_premium + country_risk_premium

        # Ensure reasonable range for cost of equity (8% to 20%)
        cost_of_equity = max(0.08, min(0.20, cost_of_equity))

        logger.info(f"Calculated cost of equity for {symbol}: {cost_of_equity:.2%} (beta: {beta})")
        return cost_of_equity, beta

    def _calculate_cost_of_debt(self, financial_data: Dict) -> Tuple[float, float]:
        """
        Calculate cost of debt

        Parameters:
        -----------
        financial_data : dict
            Financial data for the company

        Returns:
        --------
        Tuple of (cost_of_debt, tax_rate)
        """
        # Default values
        cost_of_debt = 0.08  # 8% default cost of debt
        tax_rate = 0.25  # 25% default tax rate

        try:
            # Try to calculate cost of debt from financial data
            if 'profit_loss' in financial_data:
                pl_data = financial_data['profit_loss']

                # Get interest expense
                interest_key = next((k for k in pl_data.keys() if 'interest' in k.lower()), None)

                # Get total debt
                if 'balance_sheet' in financial_data:
                    bs_data = financial_data['balance_sheet']
                    debt_key = next((k for k in bs_data.keys() if 'total debt' in k.lower() or 'borrowings' in k.lower()), None)

                    if interest_key and debt_key:
                        # Calculate average interest rate over the last 3 years for stability
                        years = sorted(list(pl_data[interest_key].keys()), reverse=True)

                        if years:
                            # Use up to 3 most recent years
                            recent_years = years[:min(3, len(years))]
                            interest_rates = []

                            for year in recent_years:
                                if year in pl_data[interest_key] and year in bs_data[debt_key]:
                                    interest_expense = pl_data[interest_key][year]
                                    total_debt = bs_data[debt_key][year]

                                    if total_debt > 0:
                                        rate = interest_expense / total_debt
                                        interest_rates.append(rate)
                                        logger.info(f"Interest rate for {year}: {rate:.2%}")

                            # Calculate average interest rate
                            if interest_rates:
                                avg_interest_rate = sum(interest_rates) / len(interest_rates)
                                cost_of_debt = avg_interest_rate
                                logger.info(f"Average interest rate over {len(interest_rates)} years: {cost_of_debt:.2%}")

            # Try to calculate effective tax rate from financial data
            if 'profit_loss' in financial_data:
                pl_data = financial_data['profit_loss']

                # Get profit before tax and tax expense
                pbt_key = next((k for k in pl_data.keys() if 'profit_before_tax' in k.lower()), None)
                tax_key = next((k for k in pl_data.keys() if 'tax_%' in k.lower()), None)

                if pbt_key and tax_key:
                    # Calculate average tax rate over the last 3 years
                    years = sorted(list(pl_data[pbt_key].keys()), reverse=True)

                    if years:
                        # Use up to 3 most recent years with positive PBT
                        tax_rates = []

                        for year in years:
                            if year in pl_data[pbt_key] and year in pl_data[tax_key]:
                                pbt = pl_data[pbt_key][year]
                                tax_rate_str = pl_data[tax_key][year]

                                # Only use years with positive PBT
                                if pbt > 0:
                                    try:
                                        # Convert tax rate string to float
                                        year_tax_rate = float(tax_rate_str.strip('%')) / 100 if isinstance(tax_rate_str, str) else tax_rate_str

                                        # Only use reasonable tax rates
                                        if 0.15 <= year_tax_rate <= 0.35:
                                            tax_rates.append(year_tax_rate)
                                            logger.info(f"Tax rate for {year}: {year_tax_rate:.2%}")
                                    except (ValueError, TypeError):
                                        logger.warning(f"Could not convert tax rate '{tax_rate_str}' to float.")

                        # Calculate average tax rate
                        if tax_rates:
                            avg_tax_rate = sum(tax_rates) / len(tax_rates)
                            tax_rate = avg_tax_rate
                            logger.info(f"Average tax rate over {len(tax_rates)} years: {tax_rate:.2%}")
                        else:
                            # Use corporate tax rate for India if no valid tax rates found
                            tax_rate = 0.25  # 25% corporate tax rate in India
                            logger.info(f"Using default corporate tax rate: {tax_rate:.2%}")

            # Adjust cost of debt based on current interest rate environment
            # Current repo rate in India is around 6.5%
            base_rate = 0.065

            # Calculate credit spread based on financial health
            credit_spread = 0.02  # Default credit spread of 2%

            # Try to calculate interest coverage ratio
            if 'profit_loss' in financial_data:
                pl_data = financial_data['profit_loss']

                ebit_key = next((k for k in pl_data.keys() if 'operating_profit' in k.lower()), None)
                interest_key = next((k for k in pl_data.keys() if 'interest' in k.lower()), None)

                if ebit_key and interest_key:
                    years = sorted(list(pl_data[ebit_key].keys()), reverse=True)

                    if years:
                        latest_year = years[0]

                        if latest_year in pl_data[ebit_key] and latest_year in pl_data[interest_key]:
                            ebit = pl_data[ebit_key][latest_year]
                            interest = pl_data[interest_key][latest_year]

                            if interest > 0:
                                interest_coverage = ebit / interest

                                # Adjust credit spread based on interest coverage ratio
                                if interest_coverage > 8:
                                    credit_spread = 0.01  # 1% for very strong companies
                                elif interest_coverage > 4:
                                    credit_spread = 0.015  # 1.5% for strong companies
                                elif interest_coverage > 2:
                                    credit_spread = 0.02  # 2% for average companies
                                elif interest_coverage > 1:
                                    credit_spread = 0.03  # 3% for weak companies
                                else:
                                    credit_spread = 0.04  # 4% for very weak companies

                                logger.info(f"Interest coverage ratio: {interest_coverage:.2f}, credit spread: {credit_spread:.2%}")

            # If calculated cost of debt seems unreasonable, use market-based approach
            if cost_of_debt < 0.05 or cost_of_debt > 0.15:
                market_cost_of_debt = base_rate + credit_spread
                logger.info(f"Using market-based cost of debt: {market_cost_of_debt:.2%} (base rate: {base_rate:.2%}, credit spread: {credit_spread:.2%})")
                cost_of_debt = market_cost_of_debt

            # Ensure reasonable values
            cost_of_debt = max(0.06, min(0.15, cost_of_debt))  # Between 6% and 15%
            tax_rate = max(0.15, min(0.35, tax_rate))  # Between 15% and 35%

            logger.info(f"Final cost of debt: {cost_of_debt:.2%} (tax rate: {tax_rate:.2%})")
            return cost_of_debt, tax_rate

        except Exception as e:
            logger.error(f"Error calculating cost of debt: {str(e)}")
            traceback.print_exc()
            return cost_of_debt, tax_rate

    def _get_capital_structure(self, financial_data: Dict) -> Tuple[float, float]:
        """
        Determine capital structure weights

        Parameters:
        -----------
        financial_data : dict
            Financial data for the company

        Returns:
        --------
        Tuple of (equity_weight, debt_weight)
        """
        # Default values
        equity_weight = 0.7  # 70% equity by default
        debt_weight = 0.3  # 30% debt by default

        try:
            # Try to calculate capital structure from financial data
            if 'balance_sheet' in financial_data:
                bs_data = financial_data['balance_sheet']

                # Get total equity
                equity_key = next((k for k in bs_data.keys() if 'total equity' in k.lower() or 'net worth' in k.lower() or 'shareholder' in k.lower()), None)

                # If equity key not found, try to calculate from equity capital and reserves
                if not equity_key:
                    equity_capital_key = next((k for k in bs_data.keys() if 'equity capital' in k.lower()), None)
                    reserves_key = next((k for k in bs_data.keys() if 'reserves' in k.lower()), None)

                    if equity_capital_key and reserves_key:
                        # Get the latest year's data
                        years1 = list(bs_data[equity_capital_key].keys())
                        years2 = list(bs_data[reserves_key].keys())
                        common_years = set(years1) & set(years2)

                        if common_years:
                            latest_year = max(common_years)
                            equity = bs_data[equity_capital_key][latest_year] + bs_data[reserves_key][latest_year]
                        else:
                            equity = None
                    else:
                        equity = None
                else:
                    # Get the latest year's data
                    years = list(bs_data[equity_key].keys())
                    if years:
                        latest_year = max(years)
                        equity = bs_data[equity_key][latest_year]
                    else:
                        equity = None

                # Get total debt
                debt_key = next((k for k in bs_data.keys() if 'total debt' in k.lower() or 'borrowings' in k.lower()), None)

                if debt_key:
                    # Get the latest year's data
                    years = list(bs_data[debt_key].keys())
                    if years:
                        latest_year = max(years)
                        debt = bs_data[debt_key][latest_year]
                    else:
                        debt = None
                else:
                    debt = None

                # Calculate weights
                if equity is not None and debt is not None and equity + debt > 0:
                    total = equity + debt
                    equity_weight = equity / total
                    debt_weight = debt / total

            # Ensure weights sum to 1
            total_weight = equity_weight + debt_weight
            if total_weight != 1:
                equity_weight = equity_weight / total_weight
                debt_weight = debt_weight / total_weight

            logger.info(f"Calculated capital structure: {equity_weight:.2%} equity, {debt_weight:.2%} debt")
            return equity_weight, debt_weight

        except Exception as e:
            logger.error(f"Error determining capital structure: {str(e)}")
            traceback.print_exc()
            return equity_weight, debt_weight
