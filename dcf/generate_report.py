#!/usr/bin/env python3
"""
Generate Comprehensive DCF Analysis Report

This script generates a comprehensive PDF report for a company's DCF analysis,
including financial analysis, DCF valuation, and sensitivity analysis.
"""

import os
import sys
import json
import logging
import traceback
import argparse
from datetime import datetime
from typing import Dict, Optional

from financial_analyzer import FinancialAnalyzer
from dcf_model import DCFModel
from wacc_calculator import WACCCalculator
from sensitivity_analysis import SensitivityAnalyzer
from visualization import Visualizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('generate_report')

def main():
    """
    Main function to generate a comprehensive DCF analysis report
    """
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Generate a comprehensive DCF analysis report')
    parser.add_argument('--symbol', type=str, default='TATAMOTORS', help='Company symbol')
    parser.add_argument('--data-dir', type=str, default='data', help='Data directory')
    parser.add_argument('--results-dir', type=str, default='results', help='Results directory')
    parser.add_argument('--skip-analysis', action='store_true', help='Skip analysis and use existing results')
    parser.add_argument('--output-file', type=str, help='Output PDF file name (default: {symbol}_dcf_report.pdf)')
    args = parser.parse_args()

    try:
        # Create directories
        os.makedirs(args.data_dir, exist_ok=True)
        os.makedirs(args.results_dir, exist_ok=True)

        analysis_data = None
        dcf_results = None
        sensitivity_results = None

        # If not skipping analysis, run the full analysis
        if not args.skip_analysis:
            # Step 1: Financial Analysis
            logger.info(f"Step 1: Running Financial Analysis for {args.symbol}")
            analyzer = FinancialAnalyzer(data_dir=args.data_dir)

            # Load company data
            data = analyzer.load_company_data(args.symbol)
            if not data:
                logger.error(f"Failed to load data for {args.symbol}")
                return

            # Analyze financials
            analysis_data = analyzer.analyze_financials(args.symbol)
            if not analysis_data:
                logger.error(f"Failed to analyze financials for {args.symbol}")
                return

            # Save analysis results
            analysis_file = os.path.join(args.results_dir, f"{args.symbol}_analysis.json")
            with open(analysis_file, 'w') as f:
                json.dump(analysis_data, f, indent=2)

            # Step 2: WACC Calculation
            logger.info(f"Step 2: Calculating WACC for {args.symbol}")
            wacc_calculator = WACCCalculator(data_dir=args.data_dir)
            wacc_results = wacc_calculator.calculate_wacc(args.symbol, data)

            # Step 3: DCF Valuation
            logger.info(f"Step 3: Running DCF Valuation for {args.symbol}")
            dcf_model = DCFModel()

            # Get DCF inputs from analysis
            dcf_inputs = analysis_data.get('dcf_inputs', {})

            # Use calculated WACC
            dcf_inputs['discount_rate'] = wacc_results['wacc']

            # If shares outstanding is not calculated, estimate it from equity capital
            if 'shares_outstanding' not in dcf_inputs:
                logger.warning(f"Shares outstanding not found in DCF inputs for {args.symbol}. Using fallback calculation.")
                # Try to estimate from equity capital if available
                if 'balance_sheet' in data and 'equity_capital' in data['balance_sheet']:
                    latest_year = max(data['balance_sheet']['equity_capital'].keys())
                    equity_capital = data['balance_sheet']['equity_capital'][latest_year]
                    # Assuming face value of Rs. 10 per share (common for Indian companies)
                    estimated_shares = equity_capital * 100000  # Convert crores to number of shares
                    dcf_inputs['shares_outstanding'] = estimated_shares
                    logger.info(f"Estimated shares outstanding from equity capital: {estimated_shares:,.0f}")
                else:
                    # Default fallback
                    logger.warning(f"Using default shares outstanding for {args.symbol}")
                    dcf_inputs['shares_outstanding'] = 400000000  # Default fallback

            # Run DCF valuation
            dcf_results = dcf_model.run_dcf_valuation(dcf_inputs)

            # Save DCF results
            dcf_file = os.path.join(args.results_dir, f"{args.symbol}_dcf.json")
            with open(dcf_file, 'w') as f:
                json.dump(dcf_results, f, indent=2)

            # Step 4: Sensitivity Analysis
            logger.info(f"Step 4: Running Sensitivity Analysis for {args.symbol}")
            sensitivity_analyzer = SensitivityAnalyzer(dcf_model, results_dir=args.results_dir)
            sensitivity_results = sensitivity_analyzer.run_sensitivity_analysis(args.symbol, dcf_inputs)

            # Save sensitivity results
            sensitivity_file = os.path.join(args.results_dir, f"{args.symbol}_sensitivity.json")
            with open(sensitivity_file, 'w') as f:
                json.dump(sensitivity_results, f, indent=2)

        else:
            # Load existing results
            logger.info(f"Loading existing analysis results for {args.symbol}")

            # Load financial analysis
            analysis_file = os.path.join(args.results_dir, f"{args.symbol}_analysis.json")
            if os.path.exists(analysis_file):
                with open(analysis_file, 'r') as f:
                    analysis_data = json.load(f)
            else:
                logger.warning(f"Analysis file not found: {analysis_file}")

            # Load DCF results
            dcf_file = os.path.join(args.results_dir, f"{args.symbol}_dcf.json")
            if os.path.exists(dcf_file):
                with open(dcf_file, 'r') as f:
                    dcf_results = json.load(f)
            else:
                logger.warning(f"DCF file not found: {dcf_file}")

            # Load sensitivity results
            sensitivity_file = os.path.join(args.results_dir, f"{args.symbol}_sensitivity.json")
            if os.path.exists(sensitivity_file):
                with open(sensitivity_file, 'r') as f:
                    sensitivity_results = json.load(f)
            else:
                logger.warning(f"Sensitivity file not found: {sensitivity_file}")

        # Step 5: Generate Report
        logger.info(f"Step 5: Generating Report for {args.symbol}")
        visualizer = Visualizer(results_dir=args.results_dir)

        # Generate report
        report_file = visualizer.generate_report(
            symbol=args.symbol,
            analysis_data=analysis_data,
            dcf_data=dcf_results,
            sensitivity_data=sensitivity_results
        )

        if report_file:
            logger.info(f"Report generated successfully: {report_file}")
            print(f"\nReport generated successfully: {report_file}")
        else:
            logger.error("Failed to generate report")
            print("\nFailed to generate report")

    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
