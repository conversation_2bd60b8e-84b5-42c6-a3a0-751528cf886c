#!/usr/bin/env python3
"""
Machine Learning Forecasting Module for DCF Analysis

This module uses machine learning models to improve the accuracy of financial
forecasts used in DCF analysis, particularly for revenue growth, FCF margins,
and other key assumptions.
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import math

# Try to import ML dependencies
try:
    import pandas as pd
    import numpy as np
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split, GridSearchCV
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.linear_model import LinearRegression, Ridge, Lasso
    from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
    import matplotlib.pyplot as plt
    import seaborn as sns
    HAS_ML = True
except ImportError:
    pd = None
    np = None
    StandardScaler = None
    train_test_split = None
    GridSearchCV = None
    RandomForestRegressor = None
    GradientBoostingRegressor = None
    LinearRegression = None
    Ridge = None
    Lasso = None
    mean_squared_error = None
    r2_score = None
    mean_absolute_error = None
    plt = None
    sns = None
    HAS_ML = False
    print("Warning: Machine learning dependencies not available. Install with 'pip install scikit-learn pandas numpy matplotlib seaborn'")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ml_forecasting')

class MLForecaster:
    """
    A class to use machine learning for financial forecasting in DCF analysis.

    Features:
    - Uses historical financial data to train ML models
    - Forecasts revenue growth, FCF margins, and other key metrics
    - Provides confidence intervals for forecasts
    - Evaluates model performance and selects the best model
    """

    def __init__(self, data_dir='data', results_dir='results'):
        """
        Initialize the ML forecaster

        Parameters:
        -----------
        data_dir : str
            Directory containing financial data
        results_dir : str
            Directory to store results
        """
        self.data_dir = data_dir
        self.results_dir = results_dir
        self.models = {}
        self.best_models = {}
        self.forecasts = {}
        self.model_metrics = {}
        
        # Create directories if they don't exist
        os.makedirs(data_dir, exist_ok=True)
        os.makedirs(results_dir, exist_ok=True)
        
        # Check if ML dependencies are available
        if not HAS_ML:
            logger.warning("Machine learning dependencies not available. Functionality will be limited.")

    def load_financial_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        Load financial data for a company and prepare it for ML

        Parameters:
        -----------
        symbol : str
            Company symbol

        Returns:
        --------
        DataFrame with prepared financial data or None if data not available
        """
        if not HAS_ML:
            logger.error("Machine learning dependencies not available")
            return None
            
        try:
            # Load company data
            company_file = os.path.join(self.data_dir, 'processed', f"{symbol}_full.json")
            if not os.path.exists(company_file):
                logger.error(f"Company data file not found: {company_file}")
                return None
                
            with open(company_file, 'r') as f:
                company_data = json.load(f)
                
            # Extract financial data
            financial_data = {}
            
            # Extract balance sheet data
            if 'balance_sheet' in company_data:
                balance_sheet = company_data['balance_sheet']
                for key, values in balance_sheet.items():
                    financial_data[f"bs_{key}"] = values
                    
            # Extract profit & loss data
            if 'profit_loss' in company_data:
                profit_loss = company_data['profit_loss']
                for key, values in profit_loss.items():
                    financial_data[f"pl_{key}"] = values
                    
            # Extract cash flow data
            if 'cash_flow' in company_data:
                cash_flow = company_data['cash_flow']
                for key, values in cash_flow.items():
                    financial_data[f"cf_{key}"] = values
                    
            # Extract ratios
            if 'ratios' in company_data:
                ratios = company_data['ratios']
                if isinstance(ratios, dict) and 'ratios' in ratios:
                    for key, value in ratios['ratios'].items():
                        financial_data[f"ratio_{key}"] = {year: value for year in financial_data.get('bs_equity_capital', {})}
            
            # Convert to DataFrame
            df = pd.DataFrame(financial_data)
            
            # Calculate derived metrics
            if 'pl_sales' in df.columns and 'pl_net_profit' in df.columns:
                df['profit_margin'] = df['pl_net_profit'] / df['pl_sales']
                
            if 'cf_cash_from_operations' in df.columns and 'cf_capital_expenditure' in df.columns:
                df['fcf'] = df['cf_cash_from_operations'] - df['cf_capital_expenditure']
                
            if 'fcf' in df.columns and 'pl_sales' in df.columns:
                df['fcf_margin'] = df['fcf'] / df['pl_sales']
                
            # Calculate growth rates
            for col in ['pl_sales', 'pl_net_profit', 'fcf']:
                if col in df.columns:
                    df[f"{col}_growth"] = df[col].pct_change()
            
            # Drop rows with missing values
            df = df.dropna(how='all')
            
            return df
            
        except Exception as e:
            logger.error(f"Error loading financial data for {symbol}: {str(e)}")
            traceback.print_exc()
            return None

    def train_models(self, symbol: str, target_cols: List[str] = None) -> Dict:
        """
        Train ML models for forecasting

        Parameters:
        -----------
        symbol : str
            Company symbol
        target_cols : list, optional
            List of target columns to forecast. If None, will use default targets.

        Returns:
        --------
        Dictionary with training results
        """
        if not HAS_ML:
            logger.error("Machine learning dependencies not available")
            return {"status": "error", "message": "Machine learning dependencies not available"}
            
        try:
            # Load financial data
            df = self.load_financial_data(symbol)
            if df is None:
                return {"status": "error", "message": "Failed to load financial data"}
                
            # Default target columns if not specified
            if target_cols is None:
                target_cols = ['pl_sales_growth', 'profit_margin', 'fcf_margin']
                
            # Filter to only include target columns that exist in the data
            target_cols = [col for col in target_cols if col in df.columns]
            
            if not target_cols:
                return {"status": "error", "message": "No valid target columns found in data"}
                
            results = {
                "status": "success",
                "symbol": symbol,
                "training_date": datetime.now().strftime("%Y-%m-%d"),
                "targets": {},
                "best_models": {}
            }
            
            # Train models for each target
            for target in target_cols:
                target_results = self._train_target_models(df, target)
                results["targets"][target] = target_results
                
                # Store best model
                if target_results["status"] == "success":
                    best_model_name = target_results["best_model"]
                    self.best_models[target] = target_results["models"][best_model_name]["model"]
                    results["best_models"][target] = best_model_name
            
            # Save results
            results_file = os.path.join(self.results_dir, f"{symbol}_ml_training.json")
            with open(results_file, 'w') as f:
                # Convert model objects to string representations
                serializable_results = results.copy()
                for target, target_results in serializable_results["targets"].items():
                    if "models" in target_results:
                        for model_name, model_data in target_results["models"].items():
                            if "model" in model_data:
                                model_data["model"] = str(model_data["model"])
                
                json.dump(serializable_results, f, indent=2)
                
            return results
            
        except Exception as e:
            logger.error(f"Error training models for {symbol}: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}

    def _train_target_models(self, df: pd.DataFrame, target: str) -> Dict:
        """
        Train models for a specific target

        Parameters:
        -----------
        df : DataFrame
            Financial data
        target : str
            Target column to forecast

        Returns:
        --------
        Dictionary with training results for the target
        """
        try:
            # Prepare data
            y = df[target].dropna()
            
            if len(y) < 5:
                return {"status": "error", "message": f"Insufficient data for target {target}"}
                
            # Create features (use all numeric columns except the target)
            X = df.select_dtypes(include=[np.number]).drop(columns=[target], errors='ignore')
            X = X.loc[y.index]
            
            # Handle missing values
            X = X.fillna(X.mean())
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Define models to train
            models = {
                "linear": LinearRegression(),
                "ridge": Ridge(alpha=1.0),
                "lasso": Lasso(alpha=0.1),
                "random_forest": RandomForestRegressor(n_estimators=100, random_state=42),
                "gradient_boosting": GradientBoostingRegressor(n_estimators=100, random_state=42)
            }
            
            # Train and evaluate models
            model_results = {}
            best_model = None
            best_score = float('inf')
            
            for name, model in models.items():
                # Train model
                model.fit(X_train_scaled, y_train)
                
                # Make predictions
                y_pred = model.predict(X_test_scaled)
                
                # Calculate metrics
                mse = mean_squared_error(y_test, y_pred)
                rmse = math.sqrt(mse)
                mae = mean_absolute_error(y_test, y_pred)
                r2 = r2_score(y_test, y_pred)
                
                # Store results
                model_results[name] = {
                    "model": model,
                    "metrics": {
                        "mse": mse,
                        "rmse": rmse,
                        "mae": mae,
                        "r2": r2
                    }
                }
                
                # Update best model
                if rmse < best_score:
                    best_score = rmse
                    best_model = name
            
            # Store models
            self.models[target] = model_results
            self.model_metrics[target] = {name: data["metrics"] for name, data in model_results.items()}
            
            return {
                "status": "success",
                "models": model_results,
                "best_model": best_model,
                "feature_names": list(X.columns)
            }
            
        except Exception as e:
            logger.error(f"Error training models for target {target}: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}

    def forecast(self, symbol: str, projection_years: int = 10) -> Dict:
        """
        Generate forecasts using trained ML models

        Parameters:
        -----------
        symbol : str
            Company symbol
        projection_years : int
            Number of years to forecast

        Returns:
        --------
        Dictionary with forecast results
        """
        if not HAS_ML:
            logger.error("Machine learning dependencies not available")
            return {"status": "error", "message": "Machine learning dependencies not available"}
            
        if not self.best_models:
            logger.error("No trained models available. Call train_models() first.")
            return {"status": "error", "message": "No trained models available"}
            
        try:
            # Load financial data
            df = self.load_financial_data(symbol)
            if df is None:
                return {"status": "error", "message": "Failed to load financial data"}
                
            # Get the most recent data point
            latest_data = df.iloc[-1].copy()
            
            # Initialize forecast results
            forecasts = {
                "status": "success",
                "symbol": symbol,
                "forecast_date": datetime.now().strftime("%Y-%m-%d"),
                "projection_years": projection_years,
                "forecasts": {}
            }
            
            # Generate forecasts for each target
            for target, model in self.best_models.items():
                target_forecasts = []
                
                # Create initial features
                features = df.select_dtypes(include=[np.number]).drop(columns=[target], errors='ignore')
                feature_names = list(features.columns)
                
                # Scale features
                scaler = StandardScaler()
                scaler.fit(features)
                
                # Start with the latest data point
                current_data = latest_data.copy()
                
                # Generate forecasts for each year
                for year in range(1, projection_years + 1):
                    # Prepare features for prediction
                    X = np.array([current_data[feature_names]])
                    X_scaled = scaler.transform(X)
                    
                    # Make prediction
                    prediction = model.predict(X_scaled)[0]
                    
                    # Store forecast
                    target_forecasts.append({
                        "year": year,
                        "value": prediction
                    })
                    
                    # Update current data for next prediction
                    current_data[target] = prediction
                    
                    # Update derived metrics based on the prediction
                    if target == 'pl_sales_growth' and 'pl_sales' in current_data:
                        current_data['pl_sales'] = current_data['pl_sales'] * (1 + prediction)
                    
                    if target == 'profit_margin' and 'pl_sales' in current_data:
                        current_data['pl_net_profit'] = current_data['pl_sales'] * prediction
                    
                    if target == 'fcf_margin' and 'pl_sales' in current_data:
                        current_data['fcf'] = current_data['pl_sales'] * prediction
                
                # Store forecasts for this target
                forecasts["forecasts"][target] = target_forecasts
            
            # Save forecast results
            forecast_file = os.path.join(self.results_dir, f"{symbol}_ml_forecasts.json")
            with open(forecast_file, 'w') as f:
                json.dump(forecasts, f, indent=2)
                
            # Store forecasts
            self.forecasts[symbol] = forecasts
            
            return forecasts
            
        except Exception as e:
            logger.error(f"Error generating forecasts for {symbol}: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}

    def generate_dcf_inputs(self, symbol: str, forecast_data: Optional[Dict] = None) -> Dict:
        """
        Generate DCF inputs from ML forecasts

        Parameters:
        -----------
        symbol : str
            Company symbol
        forecast_data : dict, optional
            Forecast data. If None, will use stored forecasts.

        Returns:
        --------
        Dictionary with DCF inputs
        """
        if not HAS_ML:
            logger.error("Machine learning dependencies not available")
            return {"status": "error", "message": "Machine learning dependencies not available"}
            
        try:
            # Use provided forecast data or load from stored forecasts
            if forecast_data is None:
                if symbol in self.forecasts:
                    forecast_data = self.forecasts[symbol]
                else:
                    logger.error(f"No forecast data available for {symbol}")
                    return {"status": "error", "message": "No forecast data available"}
            
            # Initialize DCF inputs
            dcf_inputs = {
                "status": "success",
                "symbol": symbol,
                "generation_date": datetime.now().strftime("%Y-%m-%d"),
                "source": "ml_forecasting"
            }
            
            # Extract revenue growth forecasts
            if 'pl_sales_growth' in forecast_data.get('forecasts', {}):
                growth_forecasts = forecast_data['forecasts']['pl_sales_growth']
                
                # Calculate average growth for years 1-5
                years_1_5 = [f['value'] for f in growth_forecasts[:5]]
                dcf_inputs['revenue_growth_years_1_5'] = sum(years_1_5) / len(years_1_5)
                
                # Calculate average growth for years 6-10 (if available)
                if len(growth_forecasts) > 5:
                    years_6_10 = [f['value'] for f in growth_forecasts[5:10]]
                    dcf_inputs['revenue_growth_years_6_10'] = sum(years_6_10) / len(years_6_10)
                else:
                    # Use a reduced growth rate if not enough data
                    dcf_inputs['revenue_growth_years_6_10'] = dcf_inputs['revenue_growth_years_1_5'] * 0.7
            
            # Extract FCF margin forecasts
            if 'fcf_margin' in forecast_data.get('forecasts', {}):
                margin_forecasts = forecast_data['forecasts']['fcf_margin']
                
                # Calculate average margin for years 1-5
                years_1_5 = [f['value'] for f in margin_forecasts[:5]]
                dcf_inputs['fcf_margin_years_1_5'] = sum(years_1_5) / len(years_1_5)
                
                # Calculate average margin for years 6-10 (if available)
                if len(margin_forecasts) > 5:
                    years_6_10 = [f['value'] for f in margin_forecasts[5:10]]
                    dcf_inputs['fcf_margin_years_6_10'] = sum(years_6_10) / len(years_6_10)
                else:
                    # Use the same margin if not enough data
                    dcf_inputs['fcf_margin_years_6_10'] = dcf_inputs['fcf_margin_years_1_5']
                
                # Use the last year's margin for terminal value
                dcf_inputs['fcf_margin_terminal'] = margin_forecasts[-1]['value']
            
            # Set terminal growth rate (conservative estimate)
            dcf_inputs['terminal_growth_rate'] = 0.03  # 3% long-term growth
            
            # Save DCF inputs
            dcf_inputs_file = os.path.join(self.results_dir, f"{symbol}_ml_dcf_inputs.json")
            with open(dcf_inputs_file, 'w') as f:
                json.dump(dcf_inputs, f, indent=2)
                
            return dcf_inputs
            
        except Exception as e:
            logger.error(f"Error generating DCF inputs for {symbol}: {str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}

# Main function for testing
def main():
    """
    Main function for testing the ML forecaster
    """
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='ML Forecasting for DCF Analysis')
    parser.add_argument('--symbol', type=str, default='TATAMOTORS', help='Company symbol')
    parser.add_argument('--data-dir', type=str, default='data', help='Data directory')
    parser.add_argument('--results-dir', type=str, default='results', help='Results directory')
    parser.add_argument('--projection-years', type=int, default=10, help='Number of years to project')
    args = parser.parse_args()
    
    if not HAS_ML:
        print("Machine learning dependencies not available. Please install required packages.")
        return
    
    try:
        # Create ML forecaster
        forecaster = MLForecaster(data_dir=args.data_dir, results_dir=args.results_dir)
        
        # Train models
        print(f"Training ML models for {args.symbol}...")
        training_results = forecaster.train_models(args.symbol)
        
        if training_results['status'] == 'success':
            print("Model training completed successfully")
            print(f"Best models: {training_results['best_models']}")
            
            # Generate forecasts
            print(f"Generating forecasts for {args.symbol}...")
            forecast_results = forecaster.forecast(args.symbol, args.projection_years)
            
            if forecast_results['status'] == 'success':
                print("Forecasting completed successfully")
                
                # Generate DCF inputs
                print(f"Generating DCF inputs for {args.symbol}...")
                dcf_inputs = forecaster.generate_dcf_inputs(args.symbol, forecast_results)
                
                if dcf_inputs['status'] == 'success':
                    print("DCF inputs generated successfully")
                    print("\nML-generated DCF inputs:")
                    for key, value in dcf_inputs.items():
                        if key not in ['status', 'symbol', 'generation_date', 'source']:
                            print(f"  {key}: {value}")
                else:
                    print(f"Error generating DCF inputs: {dcf_inputs.get('message', 'Unknown error')}")
            else:
                print(f"Error generating forecasts: {forecast_results.get('message', 'Unknown error')}")
        else:
            print(f"Error training models: {training_results.get('message', 'Unknown error')}")
        
    except Exception as e:
        print(f"Error in main function: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
