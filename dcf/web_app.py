#!/usr/bin/env python3
"""
Web Application for DCF Analysis

This module provides a web interface for the DCF analysis system.
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

# Try to import optional dependencies
try:
    import streamlit as st
    import pandas as pd
    import numpy as np
    import matplotlib.pyplot as plt
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    HAS_PLOTTING = True
except ImportError:
    st = None
    pd = None
    np = None
    plt = None
    px = None
    go = None
    make_subplots = None
    HAS_PLOTTING = False
    print("Warning: streamlit, pandas, numpy, matplotlib, or plotly not available. Web app will not work.")

# Import DCF modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from dcf.financial_analyzer import FinancialAnalyzer
from dcf.dcf_model import DCFModel
from dcf.wacc_calculator import WACCCalculator
from dcf.sensitivity_analysis import SensitivityAnalyzer
from dcf.batch_analysis import analyze_company

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('web_app')

# Set page configuration
if st:
    st.set_page_config(
        page_title="DCF Analysis Tool",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded",
    )

def main():
    """
    Main function to run the web app
    """
    if not st:
        print("Error: streamlit not available. Please install streamlit to run the web app.")
        return

    # Set up sidebar
    st.sidebar.title("DCF Analysis Tool")
    st.sidebar.markdown("---")

    # Company selection
    data_dir = "data"
    results_dir = "results"

    # Get available companies
    companies = get_available_companies(data_dir)
    
    # Add option to analyze a new company
    companies.append("Analyze New Company")

    selected_company = st.sidebar.selectbox("Select Company", companies)

    if selected_company == "Analyze New Company":
        # Show form to analyze a new company
        with st.sidebar.form("new_company_form"):
            st.write("Enter Company Details")
            new_symbol = st.text_input("Company Symbol (e.g., TATAMOTORS)")
            fetch_data = st.checkbox("Fetch Data", value=True)
            submit_button = st.form_submit_button("Analyze")

            if submit_button and new_symbol:
                # Check if data already exists
                if not fetch_data and not check_data_exists(data_dir, new_symbol):
                    st.error(f"No data found for {new_symbol}. Please enable 'Fetch Data' option.")
                else:
                    # Run analysis
                    with st.spinner(f"Analyzing {new_symbol}..."):
                        result = analyze_company(new_symbol, data_dir, results_dir, fetch_if_missing=fetch_data)
                        if result['status'] == 'success':
                            st.success(f"Analysis completed for {new_symbol}")
                            # Redirect to the newly analyzed company
                            st.experimental_rerun()
                        else:
                            st.error(f"Analysis failed for {new_symbol}: {result.get('message', 'Unknown error')}")
    else:
        # Show analysis for selected company
        show_company_analysis(selected_company, data_dir, results_dir)

def get_available_companies(data_dir: str) -> List[str]:
    """
    Get list of companies with available data
    
    Parameters:
    -----------
    data_dir : str
        Data directory
        
    Returns:
    --------
    List of company symbols
    """
    companies = []
    processed_dir = os.path.join(data_dir, 'processed')
    
    if os.path.exists(processed_dir):
        for file in os.listdir(processed_dir):
            if file.endswith('_full.json'):
                company = file.split('_')[0]
                if company not in companies:
                    companies.append(company)
    
    return sorted(companies)

def check_data_exists(data_dir: str, symbol: str) -> bool:
    """
    Check if data exists for a company
    
    Parameters:
    -----------
    data_dir : str
        Data directory
    symbol : str
        Company symbol
        
    Returns:
    --------
    True if data exists, False otherwise
    """
    full_file_path = os.path.join(data_dir, 'processed', f"{symbol}_full.json")
    return os.path.exists(full_file_path)

def show_company_analysis(symbol: str, data_dir: str, results_dir: str):
    """
    Show analysis for a company
    
    Parameters:
    -----------
    symbol : str
        Company symbol
    data_dir : str
        Data directory
    results_dir : str
        Results directory
    """
    # Check if analysis results exist
    analysis_file = os.path.join(results_dir, f"{symbol}_analysis.json")
    dcf_file = os.path.join(results_dir, f"{symbol}_dcf.json")
    sensitivity_file = os.path.join(results_dir, f"{symbol}_sensitivity.json")
    
    if not os.path.exists(analysis_file) or not os.path.exists(dcf_file):
        # Run analysis if results don't exist
        with st.spinner(f"Analyzing {symbol}..."):
            result = analyze_company(symbol, data_dir, results_dir, fetch_if_missing=False)
            if result['status'] != 'success':
                st.error(f"Analysis failed for {symbol}: {result.get('message', 'Unknown error')}")
                return
    
    # Load analysis results
    with open(analysis_file, 'r') as f:
        analysis = json.load(f)
    
    with open(dcf_file, 'r') as f:
        dcf_results = json.load(f)
    
    sensitivity_results = None
    if os.path.exists(sensitivity_file):
        with open(sensitivity_file, 'r') as f:
            sensitivity_results = json.load(f)
    
    # Display company information
    st.title(f"{analysis.get('company_name', symbol)} DCF Analysis")
    
    # Create tabs
    tab1, tab2, tab3, tab4 = st.tabs(["Summary", "Projections", "Sensitivity Analysis", "Financial Data"])
    
    # Tab 1: Summary
    with tab1:
        show_summary_tab(symbol, analysis, dcf_results)
    
    # Tab 2: Projections
    with tab2:
        show_projections_tab(dcf_results)
    
    # Tab 3: Sensitivity Analysis
    with tab3:
        show_sensitivity_tab(symbol, sensitivity_results, results_dir)
    
    # Tab 4: Financial Data
    with tab4:
        show_financial_data_tab(symbol, analysis, data_dir)

def show_summary_tab(symbol: str, analysis: Dict, dcf_results: Dict):
    """
    Show summary tab
    
    Parameters:
    -----------
    symbol : str
        Company symbol
    analysis : dict
        Analysis results
    dcf_results : dict
        DCF results
    """
    # Create columns for summary
    col1, col2 = st.columns(2)
    
    # Column 1: Valuation Summary
    with col1:
        st.subheader("Valuation Summary")
        
        valuation_summary = dcf_results.get('valuation_summary', {})
        
        # Format values
        enterprise_value = valuation_summary.get('enterprise_value', 0)
        equity_value = valuation_summary.get('equity_value', 0)
        per_share_value = valuation_summary.get('per_share_value', 0)
        terminal_value_percent = valuation_summary.get('terminal_value_percent', 0) * 100
        
        # Create summary table
        summary_data = {
            'Metric': ['Enterprise Value', 'Equity Value', 'Per Share Value', 'Terminal Value %'],
            'Value': [
                f"₹{enterprise_value:,.2f} Cr",
                f"₹{equity_value:,.2f} Cr",
                f"₹{per_share_value:,.2f}",
                f"{terminal_value_percent:.2f}%"
            ]
        }
        
        summary_df = pd.DataFrame(summary_data)
        st.table(summary_df)
    
    # Column 2: Key Assumptions
    with col2:
        st.subheader("Key Assumptions")
        
        # Get key assumptions
        wacc = analysis.get('dcf_inputs', {}).get('discount_rate', 0) * 100
        terminal_growth = analysis.get('dcf_inputs', {}).get('terminal_growth_rate', 0) * 100
        revenue_growth = analysis.get('dcf_inputs', {}).get('revenue_growth_years_1_5', 0) * 100
        fcf_margin = analysis.get('dcf_inputs', {}).get('fcf_margin_years_1_5', 0) * 100
        
        # Create assumptions table
        assumptions_data = {
            'Assumption': ['WACC', 'Terminal Growth Rate', 'Revenue Growth (Years 1-5)', 'FCF Margin (Years 1-5)'],
            'Value': [
                f"{wacc:.2f}%",
                f"{terminal_growth:.2f}%",
                f"{revenue_growth:.2f}%",
                f"{fcf_margin:.2f}%"
            ]
        }
        
        assumptions_df = pd.DataFrame(assumptions_data)
        st.table(assumptions_df)
    
    # Terminal Value Methods
    st.subheader("Terminal Value Methods")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("Perpetuity Growth Method")
        terminal_value_pg = dcf_results.get('terminal_value_pg', 0)
        st.metric("Terminal Value (PG)", f"₹{terminal_value_pg:,.2f} Cr")
    
    with col2:
        st.write("Exit Multiple Method")
        terminal_value_em = dcf_results.get('terminal_value_em', 0)
        st.metric("Terminal Value (EM)", f"₹{terminal_value_em:,.2f} Cr")

def show_projections_tab(dcf_results: Dict):
    """
    Show projections tab
    
    Parameters:
    -----------
    dcf_results : dict
        DCF results
    """
    st.subheader("Projected Cash Flows")
    
    # Get projections
    projections = dcf_results.get('projections', {})
    
    if not projections:
        st.warning("No projection data available")
        return
    
    # Convert projections to DataFrame
    proj_data = []
    for year, data in projections.items():
        year_num = int(year.split('_')[1])
        proj_data.append({
            'Year': year_num,
            'Revenue': data.get('revenue', 0),
            'Growth Rate': data.get('growth_rate', 0) * 100,
            'FCF Margin': data.get('fcf_margin', 0) * 100,
            'FCF': data.get('fcf', 0),
            'PV of FCF': dcf_results.get('present_values', {}).get(year, 0)
        })
    
    proj_df = pd.DataFrame(proj_data)
    
    # Display projections table
    st.dataframe(proj_df.style.format({
        'Revenue': '₹{:,.2f} Cr',
        'Growth Rate': '{:.2f}%',
        'FCF Margin': '{:.2f}%',
        'FCF': '₹{:,.2f} Cr',
        'PV of FCF': '₹{:,.2f} Cr'
    }))
    
    # Create projections chart
    st.subheader("Projections Chart")
    
    fig = make_subplots(specs=[[{"secondary_y": True}]])
    
    # Add revenue bars
    fig.add_trace(
        go.Bar(
            x=proj_df['Year'],
            y=proj_df['Revenue'],
            name="Revenue",
            marker_color='lightblue'
        ),
        secondary_y=False
    )
    
    # Add FCF line
    fig.add_trace(
        go.Scatter(
            x=proj_df['Year'],
            y=proj_df['FCF'],
            name="FCF",
            marker_color='darkblue',
            line=dict(width=3)
        ),
        secondary_y=True
    )
    
    # Add PV of FCF line
    fig.add_trace(
        go.Scatter(
            x=proj_df['Year'],
            y=proj_df['PV of FCF'],
            name="PV of FCF",
            marker_color='green',
            line=dict(width=3, dash='dash')
        ),
        secondary_y=True
    )
    
    # Update layout
    fig.update_layout(
        title="Projected Revenue and Cash Flows",
        xaxis_title="Year",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    # Update y-axes
    fig.update_yaxes(title_text="Revenue (₹ Cr)", secondary_y=False)
    fig.update_yaxes(title_text="FCF / PV of FCF (₹ Cr)", secondary_y=True)
    
    st.plotly_chart(fig, use_container_width=True)

def show_sensitivity_tab(symbol: str, sensitivity_results: Dict, results_dir: str):
    """
    Show sensitivity analysis tab
    
    Parameters:
    -----------
    symbol : str
        Company symbol
    sensitivity_results : dict
        Sensitivity analysis results
    results_dir : str
        Results directory
    """
    st.subheader("Sensitivity Analysis")
    
    if not sensitivity_results:
        st.warning("No sensitivity analysis results available")
        return
    
    # Create tabs for different sensitivity analyses
    tab1, tab2, tab3, tab4 = st.tabs([
        "Growth vs. WACC", 
        "FCF Margin vs. Terminal Growth", 
        "Terminal Multiple vs. WACC",
        "Scenario Analysis"
    ])
    
    # Tab 1: Growth vs. WACC
    with tab1:
        growth_discount_table = sensitivity_results.get('growth_discount_table', {})
        if growth_discount_table:
            st.write("Impact of Revenue Growth Rate and Discount Rate on Per Share Value")
            
            # Convert to DataFrame
            growth_rates = growth_discount_table.get('growth_rates', [])
            discount_rates = growth_discount_table.get('discount_rates', [])
            values = growth_discount_table.get('values', [])
            
            # Create heatmap
            fig = go.Figure(data=go.Heatmap(
                z=values,
                x=[f"{r*100:.1f}%" for r in discount_rates],
                y=[f"{r*100:.1f}%" for r in growth_rates],
                colorscale='Blues',
                colorbar=dict(title="Per Share Value (₹)"),
                hoverongaps=False
            ))
            
            fig.update_layout(
                title="Sensitivity: Revenue Growth Rate vs. Discount Rate",
                xaxis_title="Discount Rate (WACC)",
                yaxis_title="Revenue Growth Rate (Years 1-5)"
            )
            
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("No growth vs. discount rate sensitivity data available")
    
    # Tab 2: FCF Margin vs. Terminal Growth
    with tab2:
        margin_growth_table = sensitivity_results.get('margin_growth_table', {})
        if margin_growth_table:
            st.write("Impact of FCF Margin and Terminal Growth Rate on Per Share Value")
            
            # Convert to DataFrame
            margins = margin_growth_table.get('margins', [])
            growth_rates = margin_growth_table.get('growth_rates', [])
            values = margin_growth_table.get('values', [])
            
            # Create heatmap
            fig = go.Figure(data=go.Heatmap(
                z=values,
                x=[f"{r*100:.1f}%" for r in growth_rates],
                y=[f"{m*100:.1f}%" for m in margins],
                colorscale='Greens',
                colorbar=dict(title="Per Share Value (₹)"),
                hoverongaps=False
            ))
            
            fig.update_layout(
                title="Sensitivity: FCF Margin vs. Terminal Growth Rate",
                xaxis_title="Terminal Growth Rate",
                yaxis_title="FCF Margin (Years 1-5)"
            )
            
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("No margin vs. terminal growth sensitivity data available")
    
    # Tab 3: Terminal Multiple vs. WACC
    with tab3:
        multiple_discount_table = sensitivity_results.get('multiple_discount_table', {})
        if multiple_discount_table:
            st.write("Impact of Terminal Multiple and Discount Rate on Per Share Value")
            
            # Convert to DataFrame
            multiples = multiple_discount_table.get('multiples', [])
            discount_rates = multiple_discount_table.get('discount_rates', [])
            values = multiple_discount_table.get('values', [])
            
            # Create heatmap
            fig = go.Figure(data=go.Heatmap(
                z=values,
                x=[f"{r*100:.1f}%" for r in discount_rates],
                y=[f"{m:.1f}x" for m in multiples],
                colorscale='Reds',
                colorbar=dict(title="Per Share Value (₹)"),
                hoverongaps=False
            ))
            
            fig.update_layout(
                title="Sensitivity: Terminal Multiple vs. Discount Rate",
                xaxis_title="Discount Rate (WACC)",
                yaxis_title="Terminal Multiple"
            )
            
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("No multiple vs. discount rate sensitivity data available")
    
    # Tab 4: Scenario Analysis
    with tab4:
        scenarios = sensitivity_results.get('scenarios', {})
        if scenarios:
            st.write("Scenario Analysis")
            
            # Convert to DataFrame
            scenario_data = []
            for scenario, data in scenarios.items():
                scenario_data.append({
                    'Scenario': scenario,
                    'Per Share Value': data.get('per_share_value', 0),
                    'Enterprise Value': data.get('enterprise_value', 0),
                    'Change': data.get('change', 0) * 100
                })
            
            scenario_df = pd.DataFrame(scenario_data)
            
            # Display scenario table
            st.dataframe(scenario_df.style.format({
                'Per Share Value': '₹{:,.2f}',
                'Enterprise Value': '₹{:,.2f} Cr',
                'Change': '{:+.2f}%'
            }))
            
            # Create scenario chart
            fig = px.bar(
                scenario_df,
                x='Scenario',
                y='Per Share Value',
                color='Change',
                color_continuous_scale='RdYlGn',
                text='Per Share Value'
            )
            
            fig.update_layout(
                title="Scenario Analysis: Impact on Per Share Value",
                yaxis_title="Per Share Value (₹)",
                coloraxis_colorbar=dict(title="Change (%)")
            )
            
            fig.update_traces(
                texttemplate='₹%{text:.2f}',
                textposition='outside'
            )
            
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("No scenario analysis data available")

def show_financial_data_tab(symbol: str, analysis: Dict, data_dir: str):
    """
    Show financial data tab
    
    Parameters:
    -----------
    symbol : str
        Company symbol
    analysis : dict
        Analysis results
    data_dir : str
        Data directory
    """
    st.subheader("Financial Data")
    
    # Load company data
    full_file_path = os.path.join(data_dir, 'processed', f"{symbol}_full.json")
    
    if not os.path.exists(full_file_path):
        st.warning(f"No financial data available for {symbol}")
        return
    
    with open(full_file_path, 'r') as f:
        company_data = json.load(f)
    
    # Create tabs for different financial data
    tab1, tab2, tab3 = st.tabs(["Profit & Loss", "Balance Sheet", "Cash Flow"])
    
    # Tab 1: Profit & Loss
    with tab1:
        pl_data = company_data.get('profit_loss', {})
        if pl_data:
            st.write("Profit & Loss Statement")
            
            # Convert to DataFrame
            pl_df = pd.DataFrame(pl_data)
            
            # Display P&L table
            st.dataframe(pl_df)
            
            # Create revenue and profit chart
            if 'sales\xa0+' in pl_data and 'net_profit\xa0+' in pl_data:
                years = sorted(list(pl_data['sales\xa0+'].keys()))
                revenue = [pl_data['sales\xa0+'].get(year, 0) for year in years]
                profit = [pl_data['net_profit\xa0+'].get(year, 0) for year in years]
                
                fig = make_subplots(specs=[[{"secondary_y": True}]])
                
                # Add revenue bars
                fig.add_trace(
                    go.Bar(
                        x=years,
                        y=revenue,
                        name="Revenue",
                        marker_color='lightblue'
                    ),
                    secondary_y=False
                )
                
                # Add profit line
                fig.add_trace(
                    go.Scatter(
                        x=years,
                        y=profit,
                        name="Net Profit",
                        marker_color='darkgreen',
                        line=dict(width=3)
                    ),
                    secondary_y=True
                )
                
                # Update layout
                fig.update_layout(
                    title="Revenue and Net Profit Trend",
                    xaxis_title="Year",
                    legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.02,
                        xanchor="right",
                        x=1
                    )
                )
                
                # Update y-axes
                fig.update_yaxes(title_text="Revenue (₹ Cr)", secondary_y=False)
                fig.update_yaxes(title_text="Net Profit (₹ Cr)", secondary_y=True)
                
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("No profit & loss data available")
    
    # Tab 2: Balance Sheet
    with tab2:
        bs_data = company_data.get('balance_sheet', {})
        if bs_data:
            st.write("Balance Sheet")
            
            # Convert to DataFrame
            bs_df = pd.DataFrame(bs_data)
            
            # Display balance sheet table
            st.dataframe(bs_df)
        else:
            st.warning("No balance sheet data available")
    
    # Tab 3: Cash Flow
    with tab3:
        cf_data = company_data.get('cash_flow', {})
        if cf_data:
            st.write("Cash Flow Statement")
            
            # Convert to DataFrame
            cf_df = pd.DataFrame(cf_data)
            
            # Display cash flow table
            st.dataframe(cf_df)
            
            # Create cash flow chart
            if 'cash_from_operating_activity\xa0+' in cf_data:
                years = sorted(list(cf_data['cash_from_operating_activity\xa0+'].keys()))
                ocf = [cf_data['cash_from_operating_activity\xa0+'].get(year, 0) for year in years]
                
                fig = px.line(
                    x=years,
                    y=ocf,
                    title="Operating Cash Flow Trend",
                    labels={'x': 'Year', 'y': 'Operating Cash Flow (₹ Cr)'}
                )
                
                fig.update_traces(line=dict(width=3, color='blue'))
                
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("No cash flow data available")

if __name__ == "__main__":
    main()
