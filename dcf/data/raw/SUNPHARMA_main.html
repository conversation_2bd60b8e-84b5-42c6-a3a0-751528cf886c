
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    
    <title>Sun Pharmaceutical Industries Ltd share price | About Sun Pharma.Inds. | Key Insights - Screener</title>
    

    <!-- plausible -->
    <script defer
            event-user="unregistered"
            
            
            data-domain="screener.in"
            src="https://plausible.screener.in/js/script.pageview-props.tagged-events.js"></script>
    <script>window.plausible = window.plausible || function () { (window.plausible.q = window.plausible.q || []).push(arguments) }</script>

    <!-- FONT -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/inter/inter.43d4904a3137.css" />
    <link rel="stylesheet" href="https://cdn-static.screener.in/fontello/css/fontello.bc317748a65f.css" />

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/normalize.a2444750a947.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/skeleton.9aa9c32992ef.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/custom.39beec5e86b6.css" media="all">

    
    
    
  

  
  <meta name="description" content="Sun Pharma.Inds. · Mkt Cap: 4,16,368 Crore (up 12.9% in 1 year) · Revenue: 51,602 Cr · Profit: 11,470 Cr · The company has delivered a poor sales growth of 10.8% over past five years. · Tax rate seems low · Promoter Holding: 54.5%">
  

  <style>
  tr[data-row-company-id="3245"] {
    font-weight: 500;
  }

  tr[data-row-company-id="3245"] a {
    color: var(--ink-900);
  }

  :target {
    scroll-margin-top: 90px;
  }

  @supports not (scroll-margin-top: 90px) {
    :target {
      padding-top: 108px;
    }
  }

  @media (min-width: 992px) {
    nav {
      margin-bottom: 0;
    }

    .company-nav {
      top: 50px;
      margin-bottom: 24px;
    }
  }
  </style>


    <meta name="author" content="Mittal Analytics Private Limited">

    <!-- PWA manifest -->
    <meta name="theme-color"
          content="#fff"
          media="(prefers-color-scheme: light)">
    <meta name="theme-color"
          content="#222530"
          media="(prefers-color-scheme: dark)">
    <link rel="manifest" href="/static/manifest.json">

    <!-- favicons -->
    <link rel="apple-touch-icon"
          sizes="180x180"
          href="https://cdn-static.screener.in/favicon/apple-touch-icon.b6e5f24bf7d3.png">
    <link rel="icon"
          type="image/png"
          sizes="32x32"
          href="https://cdn-static.screener.in/favicon/favicon-32x32.00205914303a.png">
    <link rel="icon"
          type="image/png"
          sizes="16x16"
          href="https://cdn-static.screener.in/favicon/favicon-16x16.2b35594b2f33.png">
    <link rel="mask-icon"
          href="https://cdn-static.screener.in/favicon/safari-pinned-tab.adbe3ed3c5ad.svg"
          color="#8ED443">
    <meta name="msapplication-TileColor" content="#da532c">
  </head>

  <body class="light flex-column">
    
      <nav class="u-full-width no-print">
        <div id="mobile-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

<ul class="bottom-menu-bar hide-from-tablet-landscape">
  
    <li class="">
      <a href="/">
        <i class="icon-home"></i>
        <br>
        Home
      </a>
    </li>
  

  <li class="">
    <a href="/explore/">
      <i class="icon-screens"></i>
      <br>
      Screens
    </a>
  </li>

  <li>
    <button type="button"
            onclick="MobileMenu.toggleSearch(this)"
            class="search-button"
            title="Search">
      <i class="icon-search" style="font-size: 18px"></i>
    </button>
  </li>

  <li>
    <button class="button-plain" onclick="Modal.showInModal('#nav-tools-menu')">
      <i class="icon-tools"></i>
      <br>
      Tools
    </button>
  </li>

  
    <li class="">
      <a href="/register/?">
        <i class="icon-user-plus"></i>
        <br>
        Login
      </a>
    </li>
  
</ul>

        <div class="top-nav-holder">
          <div class="container">



<div class="layer-holder top-navigation">
  <div class="layer flex flex-space-between"
       style="align-items: center;
              height: 50px">
    <div class="flex" style="align-items: center">
      <a href="/" class="logo-holder">
        <img alt="Screener Logo"
             class="logo"
             src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg" />
      </a>

      <div class="desktop-links" style="margin-left: 48px">
        <a href="/">
          
            Home
          
        </a>
        <a href="/explore/">Screens</a>
        <div class="dropdown-menu">
          <button class="button-plain"
                  type="button"
                  onclick="Utils.toggleDropdown(event)">
            Tools
            <i class="icon-down"></i>
          </button>
          <ul class="dropdown-content flex-column tools-menu"
              style="width: 350px"
              id="nav-tools-menu">
            <li>
              <a href="/screen/new/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/screens.e960a5daedf9.svg" alt="Create a stock screen">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Create a stock screen</div>
                  <div class="sub font-size-13">Run queries on 10 years of financial data</div>
                </div>
              </a>
            </li>
            <li class="flex flex-align-center no-hover" style="padding: 4px 0;">
              <div class="button button-primary premium-indicator flex flex-align-center">
                <img src="https://cdn-static.screener.in/icons/crown.fb0d2413ee43.svg"
                     alt="Premium icon"
                     style="margin-right: 8px">
                <span>Premium features</span>
              </div>
              <hr class="flex-grow" style="padding: 0; margin: 0;">
            </li>
            <li>
              <a href="/hs/" class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/export.d623f0029933.svg" alt="Commodity Prices">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Commodity Prices</div>
                  <div class="sub font-size-13">See prices and trends of over 10,000 commodities</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/people/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/shares.10acc20cb936.svg"
                       alt="Search shareholders by name">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Search shareholders</div>
                  <div class="sub font-size-13">See companies where a person holds over 1% of the shares</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/announcements/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/loudspeaker.88769927f6eb.svg" alt="Latest Announcements">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Latest Announcements</div>
                  <div class="sub font-size-13">Browse, filter and set alerts for announcements.</div>
                </div>
              </a>
            </li>
            
              <li style="padding: 16px;">
                <a href="/premium/?driver=top-nav-tools"
                   class="button button-primary text-align-center"
                   style="color: var(--white);
                          padding: 10px 24px">Upgrade to premium</a>
              </li>
            
          </ul>
        </div>
      </div>
    </div>

    <div class="show-from-tablet-landscape flex flex-gap-16"
         style="justify-content: flex-end;
                flex: 1 1 400px">
      <div class="search" id="desktop-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

      
        <div class="flex flex-gap-8" style="margin: 4px">
          <a class="button account"
             href="/login/?">
            <i class="icon-user-line blue-icon"></i>
            Login
          </a>
          <a class="button account button-secondary"
             href="/register/?">Get free account</a>
        </div>
      
    </div>
  </div>
</div>
</div>
        </div>
      </nav>
    

    
  <div class="bg-base sticky company-nav">
    <div class="flex flex-space-between container hide-from-tablet-landscape">
      <h1 class="h2 shrink-text" style="margin: 0.5em 0">Sun Pharmaceutical Industries Ltd</h1>
      <div class="flex flex-align-center gap-8">
        <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/3245/"
        data-title="Sun Pharmaceutical Industries Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

        
      </div>
    </div>

    <div class="sub-nav-holder">
      <div class="sub-nav">
        <a href="#top">
          <span class="shrink-text show-from-tablet-landscape">Sun Pharma.Inds.</span>
          <span class="hide-from-tablet-landscape">Summary</span>
        </a>
        <a href="#chart">Chart</a>
        <a href="#analysis">Analysis</a>
        <a href="#peers">Peers</a>
        <a href="#quarters">Quarters</a>
        <a href="#profit-loss">Profit &amp; Loss</a>
        <a href="#balance-sheet">Balance Sheet</a>
        <a href="#cash-flow">Cash Flow</a>
        <a href="#ratios">Ratios</a>
        <a href="#shareholding">Investors</a>
        <a href="#documents">Documents</a>
        <div class="flex-grow flex flex-align-center gap-8 show-from-tablet-landscape">
          <div class="flex-grow"></div>
          <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/3245/"
        data-title="Sun Pharmaceutical Industries Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

          
        </div>
      </div>
    </div>
  </div>


    
      <main class="flex-grow container">
        



  






        
        <div class="breadcrumb hidden-if-empty"></div>
        

        
  <div data-company-id="3245"
       data-warehouse-id="6599038"
       
       data-consolidated="true"
       id="company-info"></div>

  <div class="card card-large" id="top">
    <div class="flex flex-space-between flex-gap-8">
      <div class="flex-row flex-wrap flex-align-center flex-grow"
           style="flex-basis: 300px">
        <h1 class="margin-0 show-from-tablet-landscape">Sun Pharmaceutical Industries Ltd</h1>
        <div class="ink-600 show-from-tablet-landscape" style="opacity: 0.5;">
          <i class="icon-dot"></i>
        </div>
        <div class="font-size-18 strong line-height-14">
          <div class="flex flex-align-center">
            <span>₹ 1,735</span>
            
              <span class="font-size-12 down margin-left-4">
                <i class="icon-circle-down"></i>
                -0.30%
              </span>
            
          </div>
          <div class="ink-600 font-size-11 font-weight-500">
            
              16 May
              
                - close price
              
            
          </div>
        </div>
      </div>

      <form method="post" class="flex margin-0" style="align-items: center">
        <input type="hidden" name="csrfmiddlewaretoken" value="nzILKNe5TeigFtwnjNinDdlnRo6YIRmykMAbCquOobQ4wOxQunnQeoW2iYYoUebF">
        <input type="hidden" name="next" value="/company/SUNPHARMA/consolidated/">

        
          <button class="margin-right-8 plausible-event-name=Excel+Company plausible-event-user=unregistered"
                  formaction="/user/company/export/6599038/"
                  aria-label="Export to Excel">
            <i class="icon-download"></i>
            <span class="hide-on-mobile">Export to Excel</span>
          </button>
        

        <div class="flex dropdown-filter">
          
            <button class="button-primary "
                    formaction="/api/company/3245/add/">
              <i class="icon-plus"></i> Follow
            </button>
          

          
        </div>
      </form>
    </div>

    
    <div class="company-links show-from-tablet-landscape"
         style="font-weight: 500;
                margin-top: 8px">
      
        <a href="http://www.sunpharma.com"
           target="_blank"
           rel="noopener noreferrer">
          <i class="icon-link"></i>
          <span style="font-size: 1.8rem;">sunpharma.com</span>
        </a>
      

      
        <a href="https://www.bseindia.com/stock-share-price/sun-pharmaceutical-industries-ltd/SUNPHARMA/524715/" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            BSE:
            524715
          </span>
        </a>
      

      
        <a href="https://www.nseindia.com/get-quotes/equity?symbol=SUNPHARMA" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            NSE:
            SUNPHARMA
          </span>
        </a>
      
    </div>
    

    <div class="company-info">
      
      <div class="company-profile">
        <div class="flex flex-column" style="flex: 1 1;">
          <div class="title">About</div>
          <div class="sub show-more-box about" style="flex-basis: 100px"><p>Sun Pharmaceutical Industries Ltd is engaged in the business of manufacturing, developing and marketing a wide range of branded and generic formulations and Active Pharma Ingredients (APIs). The company and its subsidiaries has various manufacturing facilities spread across the world with trading and other incidental and related activities extending to global market.<sup><a href="https://www.bseindia.com/bseplus/AnnualReport/524715/***********.pdf#page=193" target="_blank" rel="noopener noreferrer">[1]</a></sup> <strong>It is the largest pharmaceutical company in India.</strong><sup><a href="https://sunpharma.com/wp-content/uploads/2021/07/SPIL-IR-Presentation-June-2021.pdf#page=4" target="_blank" rel="noopener noreferrer">[2]</a></sup></p></div>

          
            <div class="title">Key Points</div>
            <div class="sub commentary always-show-more-box">
              <p><strong>Product Offerings</strong><br>
The company produces a range of generic and specialty medications for chronic and acute conditions. Its product portfolio includes generics, branded generics, specialty pharmaceuticals, advanced technology-based products, antiretrovirals, active pharmaceutical ingredients (APIs), and intermediates. It offers medications in multiple dosage forms, such as injectables, sprays, ointments, creams, liquids, tablets, and capsules. <sup><a href="https://sunpharma.com/products/" target="_blank" rel="noopener noreferrer">[1]</a></sup> <sup><a href="https://www.bseindia.com/xml-data/corpfiling/AttachHis/34807459-abf9-47bd-ae8e-0c309ade4bfc.pdf#page=61" target="_blank" rel="noopener noreferrer">[2]</a></sup>The company offers 26+ products in the global specialty market, with a strategic focus on dermatology, ophthalmology, and onco-dermatology segments. <sup><a href="https://www.bseindia.com/xml-data/corpfiling/AttachHis/34807459-abf9-47bd-ae8e-0c309ade4bfc.pdf#page=15" target="_blank" rel="noopener noreferrer">[3]</a></sup> </p>
              <div class="show-more-button" style="height: 20px"></div>
            </div>

            <button class="a upper font-weight-500 letter-spacing plausible-event-name=Key+Insight plausible-event-user=free"
                    style="font-size: 1.4rem"
                    type="button"
                    onclick="Modal.openInModal(event)"
                    data-title="Sun Pharmaceutical Industries Ltd"
                    data-url="/wiki/company/3245/commentary/v2/"
                    data-is-right-modal="true">
              Read More <i class="icon-right"></i>
            </button>
          
        </div>

        <div class="company-links hide-from-tablet-landscape margin-top-20"
             style="font-weight: 500">
          
            <a href="http://www.sunpharma.com"
               target="_blank"
               rel="noopener noreferrer">
              <i class="icon-link"></i>
              <span style="font-size: 1.6rem;">Website</span>
            </a>
          

          
            <a href="https://www.bseindia.com/stock-share-price/sun-pharmaceutical-industries-ltd/SUNPHARMA/524715/" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">BSE</span>
            </a>
          

          
            <a href="https://www.nseindia.com/get-quotes/equity?symbol=SUNPHARMA" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">
                NSE
                
              </span>
            </a>
          
        </div>
      </div>
      

      <div class="company-ratios">
        <ul id="top-ratios">
          


  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Market Cap
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">4,16,368</span>
        
          Cr.
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Current Price
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">1,735</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        High / Low
      
    </span>

    
      <span class="nowrap value">₹ <span class="number">1,960</span> / <span class="number">1,377</span></span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Stock P/E
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">35.3</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Book Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">288</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Dividend Yield
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">0.78</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROCE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">17.3</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">16.7</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Face Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">1.00</span>
        
      </span>
    
  </li>






        </ul>

        <div style="margin-top: 32px;">
          <label for="quick-ratio-search">Add ratio to table</label>

          <div class="flex-row flex-space-between flex-baseline flex-gap-16">
            <div class="dropdown-typeahead flex-grow" style="max-width: 480px;">
              <input id="quick-ratio-search"
                     type="text"
                     placeholder="eg. Promoter holding"
                     style="width: 100%">
            </div>

            
            <a href="/user/quick_ratios/?next=/company/SUNPHARMA/consolidated/"
               class="smaller">
              <i class="icon-pencil"></i>
              <span class="upper font-weight-500 letter-spacing">Edit ratios</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  
    <section id="chart" class="card card-large" style="margin-bottom: 2.5rem">
      <div id="chart-menu"
     class="flex flex-space-between flex-wrap"
     style="margin-bottom: 16px">
  <div class="options" id="company-chart-days">
    <button name="days"
            value="30"
            onclick="CompanyChart.setActive(event)"
            type="button">1M</button>
    <button name="days"
            value="180"
            onclick="CompanyChart.setActive(event)"
            type="button">6M</button>
    <button name="days"
            value="365"
            onclick="CompanyChart.setActive(event)"
            type="button">1Yr</button>
    <button name="days"
            value="1095"
            onclick="CompanyChart.setActive(event)"
            type="button">3Yr</button>
    <button name="days"
            value="1825"
            onclick="CompanyChart.setActive(event)"
            type="button">5Yr</button>
    <button name="days"
            value="3652"
            onclick="CompanyChart.setActive(event)"
            type="button">10Yr</button>
    <button name="days"
            value="10000"
            onclick="CompanyChart.setActive(event)"
            type="button">Max</button>
  </div>

  <div class="flex margin-bottom-24">
    <div class="options margin-right-8"
         id="company-chart-metrics"
         style="margin-bottom: 0">
      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Price"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price-DMA50-DMA200-Volume"
                  type="button">Price</button>
        

      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PE"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price to Earning-Median PE-EPS"
                  type="button">PE Ratio</button>
        

      

        

      

        

      

        

      

        

      
      <button class="hidden"
              id="company-chart-metrics-more-active"
              onclick="CompanyChart.setActive(event)"
              name="metrics"
              value=""
              type="button">Hidden</button>
      <div class="dropdown-menu" id="metrics-dropdown">
        <button type="button"
                class="button-plain"
                onclick="Utils.toggleDropdown(event)">
          More <i class="icon-down"></i>
        </button>
        <ul class="dropdown-content" style="max-width: 160px; right: 0">
          

            

          

            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="GPM-OPM-NPM-Quarter Sales"
                        type="button">Sales &amp; Margin</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=EV-EBITDA"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="EV Multiple-Median EV Multiple-EBITDA"
                        type="button">EV / EBITDA</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PBV"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Price to book value-Median PBV-Book value"
                        type="button">Price to Book</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Market+Cap+to+Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Market Cap to Sales-Median Market Cap to Sales-Sales"
                        type="button">Market Cap / Sales</button>
              </li>
            

          
        </ul>
      </div>
    </div>
    
      <button type="button"
              class="button-small button-secondary flex flex-align-center plausible-event-name=Stock+Alert"
              style="border-color: var(--button-border);
                     border-radius: 3px"
              data-title="Alerts for Sun Pharmaceutical Industries Ltd"
              onclick="Modal.openInModal(event)"
              data-url="/alerts/stock-6599038/"
              aria-label="Add alerts for company">
        <i class="icon-bell"></i>
        
        <span class="margin-left-4 normal-case letter-spacing-0">Alerts</span>
      </button>
    
  </div>
</div>

<div class="flex no-select">
  <div class="hide-on-mobile chart-label">
    <div id="chart-label-left"></div>
  </div>

  <div id="chart-area" style="flex-grow: 1">
    <div id="chart-info" class="invisible">
      <div id="chart-crosshair"></div>
      <div id="chart-tooltip" class="font-size-14">
        <div id="chart-tooltip-meta" class="ink-700 font-size-12"></div>
        <div class="font-weight-500 font-size-15" id="chart-tooltip-title"></div>
        <div id="chart-tooltip-info"></div>
      </div>
    </div>
    <div id="canvas-chart-holder" style="height: 375px"></div>
  </div>

  <div class="hide-on-mobile chart-label">
    <div id="chart-label-right"></div>
  </div>
</div>
<div id="chart-legend"></div>

    </section>

    
    <section id="analysis" class="card card-large">
      <div class="flex flex-column-mobile flex-gap-32">
        <div class="pros" style="flex-basis: 50%;">
          <p class="title">Pros</p>
          <ul>
            <li>Company has reduced debt.</li><li>Company is almost debt free.</li><li>Company has delivered good profit growth of 23.4% CAGR over last 5 years</li><li>Company has been maintaining a healthy dividend payout of 46.6%</li>
          </ul>
        </div>
        <div class="cons" style="flex-basis: 50%;">
          <p class="title">Cons</p>
          <ul>
            <li>The company has delivered a poor sales growth of 10.8% over past five years.</li><li>Tax rate seems low</li>
          </ul>
        </div>
      </div>
      <p class="sub font-size-14 margin-top-16 margin-bottom-0">
        <span style="vertical-align: sub;">*</span>
        <span class="font-weight-500">The pros and cons are machine generated.</span>
        <span class="has-tooltip">
          <i class="icon-info"></i>
          <span class="tooltip" style="width: 300px; left: -100px;">
            Pros / cons are based on a checklist to highlight important points. Please exercise caution and do your own analysis.
          </span>
        </span>
      </p>
    </section>

    <section id="peers" class="card card-large">
      <div class="flex flex-space-between" style="align-items: center;">
        <div>
          <h2>Peer comparison</h2>

          
            <p class="sub">
              Sector:
              <a href="/company/compare/00000046/"
                 target="_blank">Pharmaceuticals</a>
              <span style="margin: 16px"></span>
              Industry:
              <a href="/company/compare/00000046/00000070/"
                 target="_blank">Pharmaceuticals - Indian - Bulk Drugs &amp; Formln</a>
            </p>
          

          
            <p class="flex flex-wrap gap-8 flex-align-center line-height-1 sub"
               id="benchmarks">
              Part of
              
                <a href="/company/1001/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Sensex"
                   target="_blank">BSE Sensex</a>
              
                <a href="/company/NIFTY/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50"
                   target="_blank">Nifty 50</a>
              
                <a href="/company/1005/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+500"
                   target="_blank">BSE 500</a>
              
                <a href="/company/1010/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Healthcare"
                   target="_blank">BSE Healthcare</a>
              
                <a href="/company/1002/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100"
                   target="_blank">BSE 100</a>
              
                <a href="/company/1003/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+200"
                   target="_blank">BSE 200</a>
              
                <a href="/company/1004/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dollex+200"
                   target="_blank">BSE Dollex 200</a>
              
                <a href="/company/CNX500/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500"
                   target="_blank">Nifty 500</a>
              
                <a href="/company/CNXPHARMA/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Pharma"
                   target="_blank">Nifty Pharma</a>
              
                <a href="/company/CNX100/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100"
                   target="_blank">Nifty 100</a>
              
                <a href="/company/CNX200INDE/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+200"
                   target="_blank">Nifty 200</a>
              
                <a href="/company/NI15/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Growth+Sectors+15"
                   target="_blank">Nifty Growth Sectors 15</a>
              
                <a href="/company/NFT100EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Equal+Weight"
                   target="_blank">Nifty 100 Equal Weight</a>
              
                <a href="/company/1153/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Allcap"
                   target="_blank">BSE Allcap</a>
              
                <a href="/company/1155/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+LargeCap"
                   target="_blank">BSE LargeCap</a>
              
                <a href="/company/1166/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+India+Manufacturing+Index"
                   target="_blank">BSE India Manufacturing Index</a>
              
                <a href="/company/1125/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+SENSEX+50"
                   target="_blank">BSE SENSEX 50</a>
              
                <a href="/company/1017/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100+ESG+Index+(INR)"
                   target="_blank">BSE 100 ESG Index (INR)</a>
              
                <a href="/company/1181/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+250+LargeMidCap+Index"
                   target="_blank">BSE 250 LargeMidCap Index</a>
              
                <a href="/company/1183/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dividend+Stability+Index"
                   target="_blank">BSE Dividend Stability Index</a>
              
                <a href="/company/1185/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Low+Volatility+Index"
                   target="_blank">BSE Low Volatility Index</a>
              
                <a href="/company/1186/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Momentum+Index"
                   target="_blank">BSE Momentum Index</a>
              
                <a href="/company/NFT50EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50+Equal+Weight"
                   target="_blank">Nifty 50 Equal Weight</a>
              
                <a href="/company/NFT100LV30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Low+Volatility+30"
                   target="_blank">Nifty 100 Low Volatility 30</a>
              
                <a href="/company/1130/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100+LargeCap+TMC+Index"
                   target="_blank">BSE 100 LargeCap TMC Index</a>
              
                <a href="/company/NI200MOM30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty200+Momentum+30"
                   target="_blank">Nifty200 Momentum 30</a>
              
                <a href="/company/LMIDCAP250/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+LargeMidcap+250"
                   target="_blank">Nifty LargeMidcap 250</a>
              
                <a href="/company/NFT500MULT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500+Multicap+50:25:25"
                   target="_blank">Nifty 500 Multicap 50:25:25</a>
              
                <a href="/company/NFTHEALTHC/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Healthcare+Index"
                   target="_blank">Nifty Healthcare Index</a>
              
                <a href="/company/NIFT100ESG/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty100+ESG"
                   target="_blank">Nifty100 ESG</a>
              
                <a href="/company/NFINDIAMFG/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+India+Manufacturing"
                   target="_blank">Nifty India Manufacturing</a>
              
                <a href="/company/NFTYTOTMKT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Total+Market"
                   target="_blank">Nifty Total Market</a>
              
                <a href="/company/ALPHALOWVO/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Alpha+Low-Volatility+30"
                   target="_blank">Nifty Alpha Low-Volatility 30</a>
              
                <a href="/company/NMIM503020/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty500+Multicap+India+Manufacturing+50:30:20"
                   target="_blank">Nifty500 Multicap India Manufacturing 50:30:20</a>
              
              
                <button type="button"
                        class="a small font-weight-500 plausible-event-name=Show+More+Benchmarks"
                        onclick="Utils.toggleClass('#benchmarks', 'show-hidden')">show all</button>
              
            </p>
          
        </div>

        <div class="flex-reverse">
          <a href="/user/columns/?next=/company/SUNPHARMA/consolidated/#peers"
             class="button button-small button-secondary">
            <i class="icon-cog"></i>
            <span class="hide-on-mobile">Edit</span>
            Columns
          </a>
        </div>
      </div>

      <div id="peers-table-placeholder">Loading peers table ...</div>

      <div class="flex-row flex-baseline">
        <label for="search-peer-comparison" style="padding-right: 1rem">Detailed Comparison with:</label>
        <div class="dropdown-typeahead">
          <input type="text"
                 id="search-peer-comparison"
                 placeholder="eg. Infosys"
                 class="small">
        </div>
      </div>
    </section>

    <section id="quarters" class="card card-large">
      
        


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Quarterly Results</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/SUNPHARMA/#quarters"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    
      <button data-segment-button-close
              onclick="Segment.closeSegments('quarters')"
              class="button-small hidden">
        <i class="icon-cancel"></i>
        Close Segments
      </button>

      
        <button data-segment-button="3"
                onclick="Segment.showSegment('quarters', '3')"
                class="button-small button-secondary plausible-event-name=Segment+Results plausible-event-user=free">
          <i class="icon-chart-pie"></i>
          Mixed Segments
        </button>
      
    

    
  </div>
</div>

<div class="responsive-holder hidden" data-segment-table></div>

<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="highlight-cell">
            Dec 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Jun 2022
            
          </th>
        
          <th class="">
            Sep 2022
            
          </th>
        
          <th class="highlight-cell">
            Dec 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Jun 2023
            
          </th>
        
          <th class="">
            Sep 2023
            
          </th>
        
          <th class="highlight-cell">
            Dec 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Jun 2024
            
          </th>
        
          <th class="">
            Sep 2024
            
          </th>
        
          <th class="highlight-cell">
            Dec 2024
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Sales', 'quarters', this)">
                  Sales&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">9,863</td>
            
              <td class="">9,447</td>
            
              <td class="">10,762</td>
            
              <td class="">10,952</td>
            
              <td class="highlight-cell">11,241</td>
            
              <td class="">10,931</td>
            
              <td class="">11,941</td>
            
              <td class="">12,192</td>
            
              <td class="highlight-cell">12,381</td>
            
              <td class="">11,983</td>
            
              <td class="">12,653</td>
            
              <td class="">13,291</td>
            
              <td class="highlight-cell">13,675</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'quarters', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">7,257</td>
            
              <td class="">7,106</td>
            
              <td class="">7,877</td>
            
              <td class="">7,996</td>
            
              <td class="highlight-cell">8,237</td>
            
              <td class="">8,129</td>
            
              <td class="">8,609</td>
            
              <td class="">9,013</td>
            
              <td class="highlight-cell">8,904</td>
            
              <td class="">8,948</td>
            
              <td class="">9,045</td>
            
              <td class="">9,352</td>
            
              <td class="highlight-cell">9,666</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Operating Profit
              
            </td>
            
              <td class="highlight-cell">2,606</td>
            
              <td class="">2,340</td>
            
              <td class="">2,884</td>
            
              <td class="">2,957</td>
            
              <td class="highlight-cell">3,004</td>
            
              <td class="">2,802</td>
            
              <td class="">3,332</td>
            
              <td class="">3,179</td>
            
              <td class="highlight-cell">3,477</td>
            
              <td class="">3,035</td>
            
              <td class="">3,608</td>
            
              <td class="">3,939</td>
            
              <td class="highlight-cell">4,009</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                OPM %
              
            </td>
            
              <td class="highlight-cell">26%</td>
            
              <td class="">25%</td>
            
              <td class="">27%</td>
            
              <td class="">27%</td>
            
              <td class="highlight-cell">27%</td>
            
              <td class="">26%</td>
            
              <td class="">28%</td>
            
              <td class="">26%</td>
            
              <td class="highlight-cell">28%</td>
            
              <td class="">25%</td>
            
              <td class="">29%</td>
            
              <td class="">30%</td>
            
              <td class="highlight-cell">29%</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'quarters', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">433</td>
            
              <td class="">-3,822</td>
            
              <td class="">2</td>
            
              <td class="">85</td>
            
              <td class="highlight-cell">174</td>
            
              <td class="">202</td>
            
              <td class="">-118</td>
            
              <td class="">294</td>
            
              <td class="highlight-cell">180</td>
            
              <td class="">504</td>
            
              <td class="">533</td>
            
              <td class="">354</td>
            
              <td class="highlight-cell">149</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="highlight-cell">19</td>
            
              <td class="">37</td>
            
              <td class="">14</td>
            
              <td class="">19</td>
            
              <td class="highlight-cell">46</td>
            
              <td class="">93</td>
            
              <td class="">81</td>
            
              <td class="">49</td>
            
              <td class="highlight-cell">35</td>
            
              <td class="">74</td>
            
              <td class="">62</td>
            
              <td class="">69</td>
            
              <td class="highlight-cell">52</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="highlight-cell">554</td>
            
              <td class="">556</td>
            
              <td class="">588</td>
            
              <td class="">610</td>
            
              <td class="highlight-cell">660</td>
            
              <td class="">672</td>
            
              <td class="">651</td>
            
              <td class="">633</td>
            
              <td class="highlight-cell">622</td>
            
              <td class="">650</td>
            
              <td class="">655</td>
            
              <td class="">626</td>
            
              <td class="highlight-cell">631</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="highlight-cell">2,466</td>
            
              <td class="">-2,076</td>
            
              <td class="">2,285</td>
            
              <td class="">2,412</td>
            
              <td class="highlight-cell">2,471</td>
            
              <td class="">2,240</td>
            
              <td class="">2,481</td>
            
              <td class="">2,791</td>
            
              <td class="highlight-cell">3,000</td>
            
              <td class="">2,816</td>
            
              <td class="">3,424</td>
            
              <td class="">3,598</td>
            
              <td class="highlight-cell">3,476</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="highlight-cell">14%</td>
            
              <td class="">7%</td>
            
              <td class="">8%</td>
            
              <td class="">6%</td>
            
              <td class="highlight-cell">11%</td>
            
              <td class="">10%</td>
            
              <td class="">19%</td>
            
              <td class="">14%</td>
            
              <td class="highlight-cell">14%</td>
            
              <td class="">5%</td>
            
              <td class="">16%</td>
            
              <td class="">16%</td>
            
              <td class="highlight-cell">16%</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'quarters', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">2,126</td>
            
              <td class="">-2,227</td>
            
              <td class="">2,093</td>
            
              <td class="">2,256</td>
            
              <td class="highlight-cell">2,181</td>
            
              <td class="">1,983</td>
            
              <td class="">2,006</td>
            
              <td class="">2,385</td>
            
              <td class="highlight-cell">2,561</td>
            
              <td class="">2,659</td>
            
              <td class="">2,861</td>
            
              <td class="">3,037</td>
            
              <td class="highlight-cell">2,913</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="highlight-cell">8.58</td>
            
              <td class="">-9.49</td>
            
              <td class="">8.59</td>
            
              <td class="">9.43</td>
            
              <td class="highlight-cell">9.03</td>
            
              <td class="">8.27</td>
            
              <td class="">8.43</td>
            
              <td class="">9.90</td>
            
              <td class="highlight-cell">10.52</td>
            
              <td class="">11.06</td>
            
              <td class="">11.82</td>
            
              <td class="">12.67</td>
            
              <td class="highlight-cell">12.10</td>
            
          </tr>
        
      

      
        <tr class="font-size-14 ink-600">
          <td class="text ink-600">Raw PDF</td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/3245/12/2021/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3245/3/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3245/6/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3245/9/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/3245/12/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3245/3/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3245/6/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3245/9/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/3245/12/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3245/3/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3245/6/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3245/9/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/3245/12/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=free">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
        </tr>
      
    </tbody>
  </table>
</div>

      

      
        <div>
          <span class="badge">
            Upcoming result date: <strong>22 May 2025</strong>
          </span>
        </div>
      
    </section>

    <section id="profit-loss" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Profit & Loss</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/SUNPHARMA/#profit-loss"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    
      <button data-url="/results/rpt/3245/consolidated/"
              data-title="Related Party Transactions"
              onclick="Modal.openInModal(event)"
              class="button-small button-secondary plausible-event-name=Related+party plausible-event-user=free">
        <i class="small icon-users"></i>
        Related Party
      </button>
    

    
      <button data-segment-button-close
              onclick="Segment.closeSegments('profit-loss')"
              class="button-small hidden">
        <i class="icon-cancel"></i>
        Close Segments
      </button>

      
        <button data-segment-button="2"
                onclick="Segment.showSegment('profit-loss', '2')"
                class="button-small button-secondary plausible-event-name=Segment+Results plausible-event-user=free">
          <i class="icon-chart-pie"></i>
          Geo Segments
        </button>
      
        <button data-segment-button="3"
                onclick="Segment.showSegment('profit-loss', '3')"
                class="button-small button-secondary plausible-event-name=Segment+Results plausible-event-user=free">
          <i class="icon-chart-pie"></i>
          Mixed Segments
        </button>
      
    

    
  </div>
</div>

<div class="responsive-holder hidden" data-segment-table></div>

<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2013
            
          </th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            TTM
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Sales', 'profit-loss', this)">
                  Sales&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">11,131</td>
            
              <td class="">16,080</td>
            
              <td class="">27,392</td>
            
              <td class="">28,487</td>
            
              <td class="">31,578</td>
            
              <td class="">26,489</td>
            
              <td class="">29,066</td>
            
              <td class="">32,838</td>
            
              <td class="">33,498</td>
            
              <td class="">38,654</td>
            
              <td class="">43,886</td>
            
              <td class="">48,497</td>
            
              <td class="">51,602</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'profit-loss', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">6,346</td>
            
              <td class="">9,078</td>
            
              <td class="">19,498</td>
            
              <td class="">20,313</td>
            
              <td class="">21,476</td>
            
              <td class="">20,858</td>
            
              <td class="">22,689</td>
            
              <td class="">25,855</td>
            
              <td class="">25,028</td>
            
              <td class="">28,397</td>
            
              <td class="">32,235</td>
            
              <td class="">35,479</td>
            
              <td class="">37,012</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Operating Profit
              
            </td>
            
              <td class="">4,784</td>
            
              <td class="">7,003</td>
            
              <td class="">7,894</td>
            
              <td class="">8,174</td>
            
              <td class="">10,102</td>
            
              <td class="">5,631</td>
            
              <td class="">6,377</td>
            
              <td class="">6,983</td>
            
              <td class="">8,470</td>
            
              <td class="">10,258</td>
            
              <td class="">11,650</td>
            
              <td class="">13,018</td>
            
              <td class="">14,591</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                OPM %
              
            </td>
            
              <td class="">43%</td>
            
              <td class="">44%</td>
            
              <td class="">29%</td>
            
              <td class="">29%</td>
            
              <td class="">32%</td>
            
              <td class="">21%</td>
            
              <td class="">22%</td>
            
              <td class="">21%</td>
            
              <td class="">25%</td>
            
              <td class="">27%</td>
            
              <td class="">27%</td>
            
              <td class="">27%</td>
            
              <td class="">28%</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'profit-loss', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">-90</td>
            
              <td class="">-1,968</td>
            
              <td class="">283</td>
            
              <td class="">-42</td>
            
              <td class="">610</td>
            
              <td class="">-135</td>
            
              <td class="">-258</td>
            
              <td class="">382</td>
            
              <td class="">-3,449</td>
            
              <td class="">-3,505</td>
            
              <td class="">459</td>
            
              <td class="">865</td>
            
              <td class="">1,540</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="">43</td>
            
              <td class="">44</td>
            
              <td class="">579</td>
            
              <td class="">523</td>
            
              <td class="">400</td>
            
              <td class="">518</td>
            
              <td class="">555</td>
            
              <td class="">303</td>
            
              <td class="">141</td>
            
              <td class="">127</td>
            
              <td class="">172</td>
            
              <td class="">238</td>
            
              <td class="">256</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="">336</td>
            
              <td class="">409</td>
            
              <td class="">1,195</td>
            
              <td class="">1,038</td>
            
              <td class="">1,265</td>
            
              <td class="">1,500</td>
            
              <td class="">1,753</td>
            
              <td class="">2,053</td>
            
              <td class="">2,080</td>
            
              <td class="">2,144</td>
            
              <td class="">2,529</td>
            
              <td class="">2,557</td>
            
              <td class="">2,562</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="">4,315</td>
            
              <td class="">4,581</td>
            
              <td class="">6,403</td>
            
              <td class="">6,571</td>
            
              <td class="">9,048</td>
            
              <td class="">3,479</td>
            
              <td class="">3,810</td>
            
              <td class="">5,010</td>
            
              <td class="">2,799</td>
            
              <td class="">4,481</td>
            
              <td class="">9,408</td>
            
              <td class="">11,088</td>
            
              <td class="">13,313</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="">20%</td>
            
              <td class="">15%</td>
            
              <td class="">14%</td>
            
              <td class="">14%</td>
            
              <td class="">13%</td>
            
              <td class="">26%</td>
            
              <td class="">16%</td>
            
              <td class="">16%</td>
            
              <td class="">18%</td>
            
              <td class="">24%</td>
            
              <td class="">9%</td>
            
              <td class="">13%</td>
            
              <td class=""></td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'profit-loss', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">3,469</td>
            
              <td class="">3,879</td>
            
              <td class="">5,476</td>
            
              <td class="">5,658</td>
            
              <td class="">7,846</td>
            
              <td class="">2,542</td>
            
              <td class="">3,208</td>
            
              <td class="">4,172</td>
            
              <td class="">2,272</td>
            
              <td class="">3,389</td>
            
              <td class="">8,513</td>
            
              <td class="">9,610</td>
            
              <td class="">11,470</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="">14.40</td>
            
              <td class="">15.17</td>
            
              <td class="">21.92</td>
            
              <td class="">18.89</td>
            
              <td class="">29.03</td>
            
              <td class="">8.73</td>
            
              <td class="">11.11</td>
            
              <td class="">15.69</td>
            
              <td class="">12.10</td>
            
              <td class="">13.64</td>
            
              <td class="">35.32</td>
            
              <td class="">39.91</td>
            
              <td class="">47.65</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Dividend Payout %
              
            </td>
            
              <td class="">9%</td>
            
              <td class="">10%</td>
            
              <td class="">14%</td>
            
              <td class="">5%</td>
            
              <td class="">12%</td>
            
              <td class="">23%</td>
            
              <td class="">25%</td>
            
              <td class="">25%</td>
            
              <td class="">62%</td>
            
              <td class="">73%</td>
            
              <td class="">33%</td>
            
              <td class="">34%</td>
            
              <td class=""></td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>


      <div style="display: grid;
                  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                  gap: 2%">
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Sales Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>12%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>11%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>13%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>9%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Profit Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>8%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>23%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>25%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>26%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Stock Price CAGR</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>6%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>31%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>25%</td>
          </tr>
          <tr>
            <td>1 Year:</td>
            <td>13%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Return on Equity</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>14%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>14%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>16%</td>
          </tr>
          <tr>
            <td>Last Year:</td>
            <td>17%</td>
          </tr>
        </table>
      </div>
    </section>

    <section id="balance-sheet" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Balance Sheet</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/SUNPHARMA/#balance-sheet"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
      <button type="button"
              class="button-small button-secondary plausible-event-name=Corporate+Actions plausible-event-user=free"
              onclick="Modal.openInModal(event)"
              data-title="Corporate actions"
              data-url="/company/actions/3245/">Corporate actions</button>
    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2013
            
          </th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Sep 2024
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                Equity Capital
              
            </td>
            
              <td class="">104</td>
            
              <td class="">207</td>
            
              <td class="">207</td>
            
              <td class="">241</td>
            
              <td class="">240</td>
            
              <td class="">240</td>
            
              <td class="">240</td>
            
              <td class="">240</td>
            
              <td class="">240</td>
            
              <td class="">240</td>
            
              <td class="">240</td>
            
              <td class="">240</td>
            
              <td class="">240</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Reserves
              
            </td>
            
              <td class="">14,886</td>
            
              <td class="">18,318</td>
            
              <td class="">25,431</td>
            
              <td class="">32,742</td>
            
              <td class="">36,400</td>
            
              <td class="">38,074</td>
            
              <td class="">41,169</td>
            
              <td class="">45,025</td>
            
              <td class="">46,223</td>
            
              <td class="">47,771</td>
            
              <td class="">55,755</td>
            
              <td class="">63,427</td>
            
              <td class="">68,875</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Borrowings', 'balance-sheet', this)">
                  Borrowings&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">260</td>
            
              <td class="">2,561</td>
            
              <td class="">8,996</td>
            
              <td class="">8,497</td>
            
              <td class="">9,832</td>
            
              <td class="">10,385</td>
            
              <td class="">10,514</td>
            
              <td class="">8,315</td>
            
              <td class="">3,869</td>
            
              <td class="">1,290</td>
            
              <td class="">6,886</td>
            
              <td class="">3,274</td>
            
              <td class="">2,572</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Liabilities', 'balance-sheet', this)">
                  Other Liabilities&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">5,128</td>
            
              <td class="">8,009</td>
            
              <td class="">14,089</td>
            
              <td class="">13,948</td>
            
              <td class="">14,624</td>
            
              <td class="">15,598</td>
            
              <td class="">12,666</td>
            
              <td class="">14,615</td>
            
              <td class="">17,291</td>
            
              <td class="">20,474</td>
            
              <td class="">17,831</td>
            
              <td class="">18,367</td>
            
              <td class="">16,429</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Total Liabilities
              
            </td>
            
              <td class="">20,377</td>
            
              <td class="">29,095</td>
            
              <td class="">48,723</td>
            
              <td class="">55,428</td>
            
              <td class="">61,095</td>
            
              <td class="">64,297</td>
            
              <td class="">64,590</td>
            
              <td class="">68,194</td>
            
              <td class="">67,622</td>
            
              <td class="">69,776</td>
            
              <td class="">80,712</td>
            
              <td class="">85,308</td>
            
              <td class="">88,116</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Fixed Assets', 'balance-sheet', this)">
                  Fixed Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">5,647</td>
            
              <td class="">6,817</td>
            
              <td class="">12,682</td>
            
              <td class="">15,872</td>
            
              <td class="">17,675</td>
            
              <td class="">18,853</td>
            
              <td class="">21,837</td>
            
              <td class="">22,847</td>
            
              <td class="">21,553</td>
            
              <td class="">22,665</td>
            
              <td class="">24,065</td>
            
              <td class="">23,211</td>
            
              <td class="">28,318</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                CWIP
              
            </td>
            
              <td class="">563</td>
            
              <td class="">842</td>
            
              <td class="">2,039</td>
            
              <td class="">2,175</td>
            
              <td class="">2,801</td>
            
              <td class="">2,465</td>
            
              <td class="">1,411</td>
            
              <td class="">1,220</td>
            
              <td class="">1,567</td>
            
              <td class="">1,287</td>
            
              <td class="">4,973</td>
            
              <td class="">5,354</td>
            
              <td class="">1,150</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Investments
              
            </td>
            
              <td class="">2,412</td>
            
              <td class="">2,786</td>
            
              <td class="">2,716</td>
            
              <td class="">1,830</td>
            
              <td class="">1,192</td>
            
              <td class="">7,143</td>
            
              <td class="">7,903</td>
            
              <td class="">10,143</td>
            
              <td class="">9,612</td>
            
              <td class="">12,849</td>
            
              <td class="">14,824</td>
            
              <td class="">15,026</td>
            
              <td class="">17,744</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Assets', 'balance-sheet', this)">
                  Other Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">11,756</td>
            
              <td class="">18,650</td>
            
              <td class="">31,286</td>
            
              <td class="">35,550</td>
            
              <td class="">39,427</td>
            
              <td class="">35,837</td>
            
              <td class="">33,439</td>
            
              <td class="">33,984</td>
            
              <td class="">34,890</td>
            
              <td class="">32,975</td>
            
              <td class="">36,849</td>
            
              <td class="">41,717</td>
            
              <td class="">40,904</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Total Assets
              
            </td>
            
              <td class="">20,377</td>
            
              <td class="">29,095</td>
            
              <td class="">48,723</td>
            
              <td class="">55,428</td>
            
              <td class="">61,095</td>
            
              <td class="">64,297</td>
            
              <td class="">64,590</td>
            
              <td class="">68,194</td>
            
              <td class="">67,622</td>
            
              <td class="">69,776</td>
            
              <td class="">80,712</td>
            
              <td class="">85,308</td>
            
              <td class="">88,116</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="cash-flow" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Cash Flows</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/SUNPHARMA/#cash-flow"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2013
            
          </th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Operating Activity', 'cash-flow', this)">
                  Cash from Operating Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">3,357</td>
            
              <td class="">3,959</td>
            
              <td class="">5,616</td>
            
              <td class="">6,686</td>
            
              <td class="">7,082</td>
            
              <td class="">3,907</td>
            
              <td class="">2,196</td>
            
              <td class="">6,555</td>
            
              <td class="">6,170</td>
            
              <td class="">8,985</td>
            
              <td class="">4,959</td>
            
              <td class="">12,135</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Investing Activity', 'cash-flow', this)">
                  Cash from Investing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">-2,375</td>
            
              <td class="">-2,176</td>
            
              <td class="">-1,502</td>
            
              <td class="">-3,949</td>
            
              <td class="">-4,186</td>
            
              <td class="">-3,104</td>
            
              <td class="">-310</td>
            
              <td class="">-2,225</td>
            
              <td class="">407</td>
            
              <td class="">-5,556</td>
            
              <td class="">-7,220</td>
            
              <td class="">-763</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Financing Activity', 'cash-flow', this)">
                  Cash from Financing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">-665</td>
            
              <td class="">507</td>
            
              <td class="">-1,187</td>
            
              <td class="">-1,889</td>
            
              <td class="">-2,285</td>
            
              <td class="">-1,539</td>
            
              <td class="">-2,731</td>
            
              <td class="">-5,715</td>
            
              <td class="">-5,980</td>
            
              <td class="">-5,193</td>
            
              <td class="">2,376</td>
            
              <td class="">-6,710</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Net Cash Flow
              
            </td>
            
              <td class="">316</td>
            
              <td class="">2,290</td>
            
              <td class="">2,927</td>
            
              <td class="">848</td>
            
              <td class="">611</td>
            
              <td class="">-736</td>
            
              <td class="">-844</td>
            
              <td class="">-1,386</td>
            
              <td class="">596</td>
            
              <td class="">-1,765</td>
            
              <td class="">115</td>
            
              <td class="">4,662</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="ratios" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Ratios</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/SUNPHARMA/#ratios"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2013
            
          </th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                Debtor Days
              
            </td>
            
              <td class="">79</td>
            
              <td class="">50</td>
            
              <td class="">68</td>
            
              <td class="">87</td>
            
              <td class="">83</td>
            
              <td class="">108</td>
            
              <td class="">112</td>
            
              <td class="">105</td>
            
              <td class="">99</td>
            
              <td class="">99</td>
            
              <td class="">95</td>
            
              <td class="">85</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Inventory Days
              
            </td>
            
              <td class="">454</td>
            
              <td class="">410</td>
            
              <td class="">307</td>
            
              <td class="">370</td>
            
              <td class="">307</td>
            
              <td class="">338</td>
            
              <td class="">366</td>
            
              <td class="">311</td>
            
              <td class="">378</td>
            
              <td class="">315</td>
            
              <td class="">360</td>
            
              <td class="">338</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Days Payable
              
            </td>
            
              <td class="">186</td>
            
              <td class="">174</td>
            
              <td class="">178</td>
            
              <td class="">207</td>
            
              <td class="">197</td>
            
              <td class="">234</td>
            
              <td class="">192</td>
            
              <td class="">142</td>
            
              <td class="">167</td>
            
              <td class="">158</td>
            
              <td class="">194</td>
            
              <td class="">194</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Cash Conversion Cycle
              
            </td>
            
              <td class="">347</td>
            
              <td class="">286</td>
            
              <td class="">197</td>
            
              <td class="">251</td>
            
              <td class="">193</td>
            
              <td class="">212</td>
            
              <td class="">285</td>
            
              <td class="">274</td>
            
              <td class="">310</td>
            
              <td class="">255</td>
            
              <td class="">261</td>
            
              <td class="">229</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Working Capital Days
              
            </td>
            
              <td class="">111</td>
            
              <td class="">126</td>
            
              <td class="">76</td>
            
              <td class="">104</td>
            
              <td class="">73</td>
            
              <td class="">79</td>
            
              <td class="">137</td>
            
              <td class="">112</td>
            
              <td class="">84</td>
            
              <td class="">55</td>
            
              <td class="">93</td>
            
              <td class="">78</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                ROCE %
              
            </td>
            
              <td class="">32%</td>
            
              <td class="">34%</td>
            
              <td class="">23%</td>
            
              <td class="">18%</td>
            
              <td class="">20%</td>
            
              <td class="">10%</td>
            
              <td class="">10%</td>
            
              <td class="">10%</td>
            
              <td class="">13%</td>
            
              <td class="">17%</td>
            
              <td class="">16%</td>
            
              <td class="">17%</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="shareholding" class="card card-large">
      <div class="flex flex-space-between flex-wrap margin-bottom-8 flex-align-center">
  <div>
    <h2 class="margin-0">Shareholding Pattern</h2>
    <p class="sub">Numbers in percentages</p>
  </div>
  <div class="flex">
    <div class="options small margin-0">
      <button
        onclick="Utils.setActiveTab(event)"
        class="active"
        data-tab-id="quarterly-shp"
        type="button">
        Quarterly
      </button>
      <button
        onclick="Utils.setActiveTab(event)"
        data-tab-id="yearly-shp"
        type="button">
        Yearly
      </button>
    </div>
    
    <div class="text-align-center margin-left-12">
      <button
        type="button"
        class="button-secondary button-small"
        onclick="Modal.openInModal(event)"
        data-title="Sun Pharma.Inds. trades"
        data-url="/trades/company-3245/"
      >
        Trades
      </button>
      
    </div>
    
  </div>
</div>

<div id="quarterly-shp">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Jun 2022</th><th>Sep 2022</th><th>Dec 2022</th><th>Mar 2023</th><th>Jun 2023</th><th>Sep 2023</th><th>Dec 2023</th><th>Mar 2024</th><th>Jun 2024</th><th>Sep 2024</th><th>Dec 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=free plausible-event-classification=promoters plausible-event-period=quarterly"
                      onclick="Company.showShareholders('promoters', 'quarterly', this)">
                Promoters&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=free plausible-event-classification=foreign_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('foreign_institutions', 'quarterly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>14.95%</td>
            
              <td>16.00%</td>
            
              <td>16.87%</td>
            
              <td>16.88%</td>
            
              <td>16.48%</td>
            
              <td>16.78%</td>
            
              <td>17.08%</td>
            
              <td>17.72%</td>
            
              <td>17.23%</td>
            
              <td>18.01%</td>
            
              <td>18.04%</td>
            
              <td>17.96%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=free plausible-event-classification=domestic_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('domestic_institutions', 'quarterly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>19.66%</td>
            
              <td>19.64%</td>
            
              <td>19.08%</td>
            
              <td>19.18%</td>
            
              <td>19.65%</td>
            
              <td>19.56%</td>
            
              <td>19.42%</td>
            
              <td>18.71%</td>
            
              <td>19.17%</td>
            
              <td>18.48%</td>
            
              <td>18.43%</td>
            
              <td>18.58%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=free plausible-event-classification=government plausible-event-period=quarterly"
                      onclick="Company.showShareholders('government', 'quarterly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.00%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.09%</td>
            
              <td>0.10%</td>
            
              <td>0.11%</td>
            
              <td>0.11%</td>
            
              <td>0.11%</td>
            
              <td>0.11%</td>
            
              <td>0.11%</td>
            
              <td>0.11%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=free plausible-event-classification=public plausible-event-period=quarterly"
                      onclick="Company.showShareholders('public', 'quarterly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>10.91%</td>
            
              <td>9.80%</td>
            
              <td>9.52%</td>
            
              <td>9.38%</td>
            
              <td>9.28%</td>
            
              <td>9.05%</td>
            
              <td>8.92%</td>
            
              <td>8.97%</td>
            
              <td>9.00%</td>
            
              <td>8.89%</td>
            
              <td>8.92%</td>
            
              <td>8.86%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>6,47,167</td><td>6,43,512</td><td>6,32,337</td><td>6,25,252</td><td>6,24,561</td><td>6,16,588</td><td>6,12,626</td><td>6,31,392</td><td>6,73,217</td><td>6,57,317</td><td>6,89,623</td><td>7,04,983</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>

<div id="yearly-shp" class="hidden">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Mar 2017</th><th>Mar 2018</th><th>Mar 2019</th><th>Mar 2020</th><th>Mar 2021</th><th>Mar 2022</th><th>Mar 2023</th><th>Mar 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=free plausible-event-classification=promoters plausible-event-period=yearly"
                      onclick="Company.showShareholders('promoters', 'yearly', this)">
                Promoters&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>54.39%</td>
            
              <td>54.38%</td>
            
              <td>54.38%</td>
            
              <td>54.69%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
              <td>54.48%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=free plausible-event-classification=foreign_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('foreign_institutions', 'yearly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>21.28%</td>
            
              <td>16.32%</td>
            
              <td>15.54%</td>
            
              <td>12.81%</td>
            
              <td>11.67%</td>
            
              <td>14.44%</td>
            
              <td>16.88%</td>
            
              <td>17.72%</td>
            
              <td>17.96%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=free plausible-event-classification=domestic_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('domestic_institutions', 'yearly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>12.21%</td>
            
              <td>16.23%</td>
            
              <td>17.32%</td>
            
              <td>19.56%</td>
            
              <td>21.62%</td>
            
              <td>20.02%</td>
            
              <td>19.18%</td>
            
              <td>18.71%</td>
            
              <td>18.58%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=free plausible-event-classification=government plausible-event-period=yearly"
                      onclick="Company.showShareholders('government', 'yearly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.06%</td>
            
              <td>0.11%</td>
            
              <td>0.11%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=free plausible-event-classification=public plausible-event-period=yearly"
                      onclick="Company.showShareholders('public', 'yearly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>12.13%</td>
            
              <td>13.07%</td>
            
              <td>12.76%</td>
            
              <td>12.94%</td>
            
              <td>12.23%</td>
            
              <td>11.06%</td>
            
              <td>9.38%</td>
            
              <td>8.97%</td>
            
              <td>8.86%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>5,71,998</td><td>5,93,863</td><td>6,19,712</td><td>6,31,219</td><td>7,09,045</td><td>6,62,384</td><td>6,25,252</td><td>6,31,392</td><td>7,04,983</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>


<p class="sub small">
  * The classifications might have changed from Sep'2022 onwards.
  <span class="has-tooltip">
    <i class="icon-info"></i>
    <span class="tooltip" style="width: 300px">
      The new XBRL format added more details from Sep'22 onwards.
      <br>
      <br>
      Classifications such as banks and foreign portfolio investors were not available earlier. The sudden changes in FII or DII can be because of these changes.
      <br>
      <br>
      Click on the line-items to see the names of individual entities.
    </span>
  </span>
</p>


    </section>

    <section id="documents" class="card card-large">
      <div class="flex flex-space-between margin-bottom-16">
        <h2 class="margin-0">Documents</h2>
      </div>

      <div class="flex-row flex-gap-small">
        
          <div class="documents flex-column" style="flex: 2 1 250px;">
            <h3 class="margin-bottom-8">Announcements</h3>

            <div class="show-more-box" style="flex-basis: 300px">
              <div id="company-announcements-tab">
                <div class="flex">
  <div class="options small" style="margin-bottom: 0">
    <button
      type="button"
      class="active"
      
      onclick="Utils.ajaxLoad(event, '/announcements/recent/3245/')"
      data-swap="#company-announcements-tab"
    >
      Recent
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/important/3245/')"
      data-swap="#company-announcements-tab"
    >
      Important
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/search/3245/')"
      data-swap="#company-announcements-tab"
    >
      Search
    </button>
    <a class="button" href="https://www.bseindia.com/stock-share-price/sun-pharmaceutical-industries-ltd/sunpharma/524715/corp-announcements/" target="_blank" rel="noopener noreferrer">
      All
      <i class="icon-link-ext smaller"></i>
    </a>
  </div>
</div>


  
  <ul class="list-links">
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=314a871d-122a-4298-b8f6-5082ec9f2313.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Press Release / Media Release

          
            <div class="ink-600 smaller">1h - FDA approves Sun Pharma&#x27;s next-gen LED BLU-U device for actinic keratosis treatment.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=69e938a5-c495-451b-81c6-716c7261c43f.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Press Release / Media Release

          
            <div class="ink-600 smaller">12 May - Sun Pharma to announce Q4 FY25 results and hold earnings call on May 22, 2025.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=0f6c3754-709f-4f11-8c36-401378a6a35e.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Board Meeting Intimation for Approval Of Audited Financial Results For The Year Ended 31 March 2025, And Final Dividend, If Any

          
            <div class="ink-600 smaller">12 May - Board meeting on 22 May to approve FY25 results and consider final dividend; trading window closed till 24 May.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=b0d5ec57-5d00-4f80-ace2-97ad1a178496.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Press Release / Media Release

          
            <div class="ink-600 smaller">8 May - Sun Pharma launches multi-language corporate brand campaign &#x27;touching 1,000 lives every minute&#x27; in India.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=51ed859f-936e-4d48-a748-304022ce934f.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Change in Management

          
            <div class="ink-600 smaller">6 May - Sun Pharma appoints Jayashree Satagopan as CFO effective 1 July 2025, succeeding retiring CFO Muralidharan.</div>
          
        </a>
      </li>
    
  </ul>




              </div>
            </div>
          </div>
        

        <div class="documents annual-reports flex-column"
             style="flex: 1 1 200px;
                    max-width: 210px">
          <h3>Annual reports</h3>
          <div class="show-more-box">
            
<ul class="list-links">

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=a1e3c7fc-c47e-4ce8-ad92-b4affd2a9cf8.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2024
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=\981e6db0-ea49-40ad-b906-7f924d264765.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2023
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/74133524715.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2022
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/***********.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2021
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/5247150320.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2020
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/5247150319.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2019
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/5247150318.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2018
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/5247150317.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2017
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/5247150316.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2016
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/5247150315.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2015
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/5247150314.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2014
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/5247150313.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2013
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/5247150312.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2012
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://archives.nseindia.com/annual_reports/AR_SUNPHARMA_2010_2011_20092011024040.zip" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2011
      <div class="ink-600 smaller">
        from nse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/524715/5247150311.pdf" class="plausible-event-name=Annual+Report plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Financial Year 2011
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

</ul>


          </div>
          
        </div>

        
          <div class="documents credit-ratings flex-column"
               style="flex: 1 1 200px;
                      max-width: 220px">
            <h3>Credit ratings</h3>
            <div class="show-more-box">
              
<ul class="list-links">

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=133718" class="plausible-event-name=Credit+Rating plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        19 Mar from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/SunPharmaceuticalIndustriesLimited_March 18_ 2025_RR_364878.html" class="plausible-event-name=Credit+Rating plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        18 Mar from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=133505" class="plausible-event-name=Credit+Rating plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        6 Mar from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/SunPharmaceuticalIndustriesLimited_December 27_ 2024_RR_358964.html" class="plausible-event-name=Credit+Rating plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        27 Dec 2024 from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=126004" class="plausible-event-name=Credit+Rating plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        4 Mar 2024 from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/SunPharmaceuticalIndustriesLimited_January 25, 2024_RR_335722.html" class="plausible-event-name=Credit+Rating plausible-event-user=free" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        25 Jan 2024 from crisil
      </div>
    </a>
  </li>

</ul>


            </div>
          </div>
        

        
          <div class="documents concalls flex-column"
               style="flex: 2 1 250px;
                      max-width: fit-content">
            <div class="flex flex-space-between flex-align-center margin-bottom-8">
              <h3 class="margin-0">Concalls</h3>
              <button class="a font-size-12 font-weight-500 sub"
                      onclick="Modal.openInModal(event)"
                      data-url="/concalls/add-3245/"
                      data-title="Sun Pharma.Inds. - Add Link">
                <i class="icon-flag"></i>
                <span class="underline-link">Add Missing</span>
              </button>
            </div>
            <div class="show-more-box"><ul class="list-links">

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Mar 2025
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=c64727af-4734-40b3-9dd8-dab45dd96e83.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/22978369/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Feb 2025"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=34807459-abf9-47bd-ae8e-0c309ade4bfc.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2025
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <div class="concall-link">PPT</div>
    

    
    <a href="https://youtu.be/spPWPtIYvXo" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Nov 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=3bef8197-b43b-4a13-8773-13ec2f811511.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/22955279/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Nov 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=589346d5-d3f2-4e8b-b7fc-6eb84985195c.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=0605915b-66be-46c5-b33f-ee6c256091a8.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Sep 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d1a69652-c883-440b-9e3f-81881aaf438e.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=9bca3c3b-b79b-4111-af68-679a2038beae.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/22935835/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Aug 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=c61aaa1c-2fbc-4700-a3aa-ae2a122aca3d.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://sunpharma.com/wp-content/uploads/2024/08/SUN0320240801154212_Q1FY25-Earnings-Call-Recording.mp3" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      May 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=ee40a7ec-017e-41e7-9f33-2453153e8620.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/22920304/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - May 2024"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Mar 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=ff736ef8-33c8-4fe9-a862-2cf0b401de14.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=8d1a8ff3-6340-4b43-b2fa-35c24c711620.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/19129548/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Feb 2024"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=22f3cd0c-85d8-4c55-9983-e0c8b5d3a9fd.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Nov 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=788eb9c4-2be4-4e46-a1bc-045cc355fed6.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/15951746/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Nov 2023"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Sep 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=f5893923-3593-415f-a2b6-0e52610a6264.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=c02c76d9-0d38-41a3-8a4c-ae60c04d7c6b.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/12973540/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Aug 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=186c7eb4-fbec-475c-b752-496a796a1850.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      May 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=146e73bc-f72d-4970-8bf4-85e4a62ab2f7.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/10801046/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - May 2023"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
    <a href="https://sunpharma.com/wp-content/uploads/2023/05/SUN0320230526146870-Q4FY23-Conf.-Call-Audio.mp3" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=8698f2bc-87dd-4f1d-89c3-9e9e378d051d.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=e485b9cb-e649-4a5b-a99b-bdf8198f3bf1.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/7204607/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Feb 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=a746248f-0105-4089-98c4-ba102ee0cc86.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Nov 2022
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=5ce16a4d-da5c-414e-ae88-048e3e9386e0.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/4886738/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Nov 2022"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Sep 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=1917fd81-3d5b-4335-86c8-e03a677b571c.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2022
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=4d6dc697-3de4-4ebd-98c7-bb4a1eae4f4c.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/2603091/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Aug 2022"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jun 2022
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=29210495-3f9c-418e-ae83-9e7328b8e485.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/1682159/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jun 2022"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Mar 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=339a0c90-0174-4550-afb8-583c664106d2.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2022
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=c96f5127-18f1-4664-8ac5-5cca09a0d4be.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/1148599/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Feb 2022"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Nov 2021
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=7a4d3ebd-410c-47fc-964a-76d80ffb8f53.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/809949/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Nov 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2021
    </div>

    
    <a
      class="concall-link"
      href="https://sunpharma.com/wp-content/uploads/2021/08/FY22-Q1-Earnings-Call-Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/519386/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Aug 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2021
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=11d850b2-ea51-4b85-b41a-6fbe75675d86.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/453765/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Aug 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jun 2021
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d619b226-40c2-4d83-b719-81a9d1ced632.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/235724/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jun 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2021
    </div>

    
    <a
      class="concall-link"
      href="https://sunpharma.com/wp-content/uploads/2021/02/FY21-Q3-Earnings-Call-Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/519385/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2020
    </div>

    
    <a
      class="concall-link"
      href="https://sunpharma.com/wp-content/uploads/2021/01/FY20-21-Q2-Earnings-Call-Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=free"
      type="button"
      data-url="/concalls/summary/519384/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2020"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

</ul>
</div>
          </div>
        
      </div>
    </section>
    <!-- cache time 2025-05-16 18:56:43 -->
    
  

      </main>
    

    
      

<footer id="large-footer">
  <div class="flex-row flex-space-between flex-column-tablet container no-print">
    <div class="hide-from-tablet" style="margin-bottom: 16px;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
    </div>

    <div class="show-from-tablet" style="padding: 0 64px 0 0;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
      <p class="font-size-19" style="font-weight: 500;">Stock analysis and screening tool</p>
      <p class="sub">
        Mittal Analytics Private Ltd &copy; 2009-2025
        <br>
        Made with <i class="icon-heart red"></i> in India.
      </p>
      <p class="sub font-size-13">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="font-size-13 sub">
        <a href="/guides/terms/" class="underline-link">Terms</a> & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>

    <div class="flex flex-end flex-space-between flex-grow"
         style="max-width: 600px">
      <div class="flex-grow">
        <div class="title">Product</div>
        <ul class="items">
          <li>
            <a href="/premium/?driver=footer">Premium</a>
          </li>
          <li>
            <a href="/docs/changelog/">What's new?</a>
          </li>
          <li>
            <a href="https://bit.ly/learnscreener">Learn</a>
          </li>
          <li>
            <button class="a2hs button-secondary button-small">
              <i class="icon-flash"></i>
              Install
            </button>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Team</div>
        <ul class="items">
          <li>
            <a href="/guides/about-us/">About us</a>
          </li>
          <li>
            <a href="/support/">Support</a>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Theme</div>
        <ul class="items">
          <li>
            <button onclick="SetTheme('light')"
                    aria-label="Set light theme"
                    class="button-plain">
              <i class="icon-sun"></i>
              Light
            </button>
          </li>
          <li>
            <button onclick="SetTheme('dark')"
                    aria-label="Set dark theme"
                    class="button-plain">
              <i class="icon-moon"></i>
              Dark
            </button>
          </li>
          <li>
            <button onclick="SetTheme('auto')"
                    aria-label="Set auto theme"
                    class="button-plain">
              <i class="icon-monitor"></i>
              Auto
            </button>
          </li>
        </ul>
      </div>
    </div>

    <div class="hide-from-tablet">
      <hr>
      <p class="sub">Mittal Analytics Private Ltd &copy; 2009-2024</p>
      <p class="sub">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="sub">
        <a href="/guides/terms/" class="underline-link">Terms</a>
        & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>
  </div>
</footer>

    

    <!-- Important JS (which provide global objects)- check below too -->
    <script src="https://cdn-static.screener.in/js/dialog-polyfill.min.2b5bf9a032d2.js"></script>
    <script src="https://cdn-static.screener.in/js/utils.02a9ce812e49.js"></script>
    <script src="https://cdn-static.screener.in/js/modal.4f872e091355.js"></script>
    <script src="https://cdn-static.screener.in/js/typeahead.8dcc9ac93685.js"></script>
    <script src="https://cdn-static.screener.in/js/filter.component.5add9ef3b7a9.js"></script>

    <!-- Independent JS scripts for page interactions -->
    <script src="https://cdn-static.screener.in/js/navbar.3fe7f0f4a630.js"></script>
    <script src="https://cdn-static.screener.in/js/company.search.e48f94642999.js"></script>
    
  <script src="https://cdn-static.screener.in/js/scroll-aid.5ed2d38a133e.js"></script>
  <script src="https://cdn-static.screener.in/js/company.customisation.d7c717b0de15.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.min.10a29a29621c.js"></script>
  <script src="https://cdn-static.screener.in/js/chartjs-adapter-date-fns.bundle.min.ebae23730a6e.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.e20c5f42613a.js"></script>
  <script src="https://cdn-static.screener.in/js/segment-result.b6af4191fd6f.js"></script>


    <!-- Service worker installer for PWA -->
    <script src="https://cdn-static.screener.in/js/pwa.933118758337.js"></script>
  </body>
</html>
