
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    
    <title>Maruti Suzuki India Ltd share price | About Maruti Suzuki | Key Insights - Screener</title>
    

    <!-- plausible -->
    <script defer
            event-user="unregistered"
            
            
            data-domain="screener.in"
            src="https://plausible.screener.in/js/script.pageview-props.tagged-events.js"></script>
    <script>window.plausible = window.plausible || function () { (window.plausible.q = window.plausible.q || []).push(arguments) }</script>

    <!-- FONT -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/inter/inter.43d4904a3137.css" />
    <link rel="stylesheet" href="https://cdn-static.screener.in/fontello/css/fontello.bc317748a65f.css" />

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/normalize.a2444750a947.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/skeleton.9aa9c32992ef.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/custom.39beec5e86b6.css" media="all">

    
    
    
  

  
  <meta name="description" content="Maruti Suzuki · Mkt Cap: 4,08,714 Crore (up 3.98% in 1 year) · Revenue: 1,52,913 Cr · Profit: 14,500 Cr · Promoter Holding: 58.3%">
  

  <style>
  tr[data-row-company-id="2023"] {
    font-weight: 500;
  }

  tr[data-row-company-id="2023"] a {
    color: var(--ink-900);
  }

  :target {
    scroll-margin-top: 90px;
  }

  @supports not (scroll-margin-top: 90px) {
    :target {
      padding-top: 108px;
    }
  }

  @media (min-width: 992px) {
    nav {
      margin-bottom: 0;
    }

    .company-nav {
      top: 50px;
      margin-bottom: 24px;
    }
  }
  </style>


    <meta name="author" content="Mittal Analytics Private Limited">

    <!-- PWA manifest -->
    <meta name="theme-color"
          content="#fff"
          media="(prefers-color-scheme: light)">
    <meta name="theme-color"
          content="#222530"
          media="(prefers-color-scheme: dark)">
    <link rel="manifest" href="/static/manifest.json">

    <!-- favicons -->
    <link rel="apple-touch-icon"
          sizes="180x180"
          href="https://cdn-static.screener.in/favicon/apple-touch-icon.b6e5f24bf7d3.png">
    <link rel="icon"
          type="image/png"
          sizes="32x32"
          href="https://cdn-static.screener.in/favicon/favicon-32x32.00205914303a.png">
    <link rel="icon"
          type="image/png"
          sizes="16x16"
          href="https://cdn-static.screener.in/favicon/favicon-16x16.2b35594b2f33.png">
    <link rel="mask-icon"
          href="https://cdn-static.screener.in/favicon/safari-pinned-tab.adbe3ed3c5ad.svg"
          color="#8ED443">
    <meta name="msapplication-TileColor" content="#da532c">
  </head>

  <body class="light flex-column">
    
      <nav class="u-full-width no-print">
        <div id="mobile-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

<ul class="bottom-menu-bar hide-from-tablet-landscape">
  
    <li class="">
      <a href="/">
        <i class="icon-home"></i>
        <br>
        Home
      </a>
    </li>
  

  <li class="">
    <a href="/explore/">
      <i class="icon-screens"></i>
      <br>
      Screens
    </a>
  </li>

  <li>
    <button type="button"
            onclick="MobileMenu.toggleSearch(this)"
            class="search-button"
            title="Search">
      <i class="icon-search" style="font-size: 18px"></i>
    </button>
  </li>

  <li>
    <button class="button-plain" onclick="Modal.showInModal('#nav-tools-menu')">
      <i class="icon-tools"></i>
      <br>
      Tools
    </button>
  </li>

  
    <li class="">
      <a href="/register/?">
        <i class="icon-user-plus"></i>
        <br>
        Login
      </a>
    </li>
  
</ul>

        <div class="top-nav-holder">
          <div class="container">



<div class="layer-holder top-navigation">
  <div class="layer flex flex-space-between"
       style="align-items: center;
              height: 50px">
    <div class="flex" style="align-items: center">
      <a href="/" class="logo-holder">
        <img alt="Screener Logo"
             class="logo"
             src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg" />
      </a>

      <div class="desktop-links" style="margin-left: 48px">
        <a href="/">
          
            Home
          
        </a>
        <a href="/explore/">Screens</a>
        <div class="dropdown-menu">
          <button class="button-plain"
                  type="button"
                  onclick="Utils.toggleDropdown(event)">
            Tools
            <i class="icon-down"></i>
          </button>
          <ul class="dropdown-content flex-column tools-menu"
              style="width: 350px"
              id="nav-tools-menu">
            <li>
              <a href="/screen/new/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/screens.e960a5daedf9.svg" alt="Create a stock screen">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Create a stock screen</div>
                  <div class="sub font-size-13">Run queries on 10 years of financial data</div>
                </div>
              </a>
            </li>
            <li class="flex flex-align-center no-hover" style="padding: 4px 0;">
              <div class="button button-primary premium-indicator flex flex-align-center">
                <img src="https://cdn-static.screener.in/icons/crown.fb0d2413ee43.svg"
                     alt="Premium icon"
                     style="margin-right: 8px">
                <span>Premium features</span>
              </div>
              <hr class="flex-grow" style="padding: 0; margin: 0;">
            </li>
            <li>
              <a href="/hs/" class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/export.d623f0029933.svg" alt="Commodity Prices">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Commodity Prices</div>
                  <div class="sub font-size-13">See prices and trends of over 10,000 commodities</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/people/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/shares.10acc20cb936.svg"
                       alt="Search shareholders by name">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Search shareholders</div>
                  <div class="sub font-size-13">See companies where a person holds over 1% of the shares</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/announcements/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/loudspeaker.88769927f6eb.svg" alt="Latest Announcements">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Latest Announcements</div>
                  <div class="sub font-size-13">Browse, filter and set alerts for announcements.</div>
                </div>
              </a>
            </li>
            
              <li style="padding: 16px;">
                <a href="/premium/?driver=top-nav-tools"
                   class="button button-primary text-align-center"
                   style="color: var(--white);
                          padding: 10px 24px">Upgrade to premium</a>
              </li>
            
          </ul>
        </div>
      </div>
    </div>

    <div class="show-from-tablet-landscape flex flex-gap-16"
         style="justify-content: flex-end;
                flex: 1 1 400px">
      <div class="search" id="desktop-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

      
        <div class="flex flex-gap-8" style="margin: 4px">
          <a class="button account"
             href="/login/?">
            <i class="icon-user-line blue-icon"></i>
            Login
          </a>
          <a class="button account button-secondary"
             href="/register/?">Get free account</a>
        </div>
      
    </div>
  </div>
</div>
</div>
        </div>
      </nav>
    

    
  <div class="bg-base sticky company-nav">
    <div class="flex flex-space-between container hide-from-tablet-landscape">
      <h1 class="h2 shrink-text" style="margin: 0.5em 0">Maruti Suzuki India Ltd</h1>
      <div class="flex flex-align-center gap-8">
        <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/2023/"
        data-title="Maruti Suzuki India Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

        
      </div>
    </div>

    <div class="sub-nav-holder">
      <div class="sub-nav">
        <a href="#top">
          <span class="shrink-text show-from-tablet-landscape">Maruti Suzuki</span>
          <span class="hide-from-tablet-landscape">Summary</span>
        </a>
        <a href="#chart">Chart</a>
        <a href="#analysis">Analysis</a>
        <a href="#peers">Peers</a>
        <a href="#quarters">Quarters</a>
        <a href="#profit-loss">Profit &amp; Loss</a>
        <a href="#balance-sheet">Balance Sheet</a>
        <a href="#cash-flow">Cash Flow</a>
        <a href="#ratios">Ratios</a>
        <a href="#shareholding">Investors</a>
        <a href="#documents">Documents</a>
        <div class="flex-grow flex flex-align-center gap-8 show-from-tablet-landscape">
          <div class="flex-grow"></div>
          <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/2023/"
        data-title="Maruti Suzuki India Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

          
        </div>
      </div>
    </div>
  </div>


    
      <main class="flex-grow container">
        



  






        
        <div class="breadcrumb hidden-if-empty"></div>
        

        
  <div data-company-id="2023"
       data-warehouse-id="6597252"
       
       data-consolidated="true"
       id="company-info"></div>

  <div class="card card-large" id="top">
    <div class="flex flex-space-between flex-gap-8">
      <div class="flex-row flex-wrap flex-align-center flex-grow"
           style="flex-basis: 300px">
        <h1 class="margin-0 show-from-tablet-landscape">Maruti Suzuki India Ltd</h1>
        <div class="ink-600 show-from-tablet-landscape" style="opacity: 0.5;">
          <i class="icon-dot"></i>
        </div>
        <div class="font-size-18 strong line-height-14">
          <div class="flex flex-align-center">
            <span>₹ 12,995</span>
            
              <span class="font-size-12 up margin-left-4">
                <i class="icon-circle-up"></i>
                0.33%
              </span>
            
          </div>
          <div class="ink-600 font-size-11 font-weight-500">
            
              16 May
              
                - close price
              
            
          </div>
        </div>
      </div>

      <form method="post" class="flex margin-0" style="align-items: center">
        <input type="hidden" name="csrfmiddlewaretoken" value="GFxKX5M3K3rJObrfcFDAWBzi7PROyiGuo8MAVeVBfqxcchGhj2nEQZZr7Ul9zUBj">
        <input type="hidden" name="next" value="/company/MARUTI/consolidated/">

        
          <button class="margin-right-8 plausible-event-name=Excel+Company plausible-event-user=unregistered"
                  formaction="/user/company/export/6597252/"
                  aria-label="Export to Excel">
            <i class="icon-download"></i>
            <span class="hide-on-mobile">Export to Excel</span>
          </button>
        

        <div class="flex dropdown-filter">
          
            <button class="button-primary "
                    formaction="/api/company/2023/add/">
              <i class="icon-plus"></i> Follow
            </button>
          

          
        </div>
      </form>
    </div>

    
    <div class="company-links show-from-tablet-landscape"
         style="font-weight: 500;
                margin-top: 8px">
      
        <a href="http://www.marutisuzuki.com"
           target="_blank"
           rel="noopener noreferrer">
          <i class="icon-link"></i>
          <span style="font-size: 1.8rem;">marutisuzuki.com</span>
        </a>
      

      
        <a href="https://www.bseindia.com/stock-share-price/maruti-suzuki-india-ltd/MARUTI/532500/" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            BSE:
            532500
          </span>
        </a>
      

      
        <a href="https://www.nseindia.com/get-quotes/equity?symbol=MARUTI" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            NSE:
            MARUTI
          </span>
        </a>
      
    </div>
    

    <div class="company-info">
      
      <div class="company-profile">
        <div class="flex flex-column" style="flex: 1 1;">
          <div class="title">About</div>
          <div class="sub show-more-box about" style="flex-basis: 100px"><p>The Company was established in 1981. A joint venture agreement was signed between the Government of India and Suzuki Motor<br>
Corporation (SMC), Japan in 1982. The Company became a subsidiary of SMC in 2002.It is the market leader in passenger vehicle segment in India. In terms of production volume and sales, the Company is now SMC’s largest subsidiary. SMC currently holds 56.28% of its equity stake.<br>
The principal activities of the Company are manufacturing, purchase and sale of motor vehicles, components and spare parts.</p></div>

          
            <div class="title">Key Points</div>
            <div class="sub commentary always-show-more-box">
              <p><strong>Market share of major car manufacturers (%)</strong><sup><a href="https://www.team-bhp.com/forum/indian-car-scene/264442-q1-2023-indian-passenger-vehicle-market-analysis.html" target="_blank" rel="noopener noreferrer">[1]</a></sup><br>
Maruti Suzuki - 45.0 <br>
Hyundai - 15<br>
Tata - 13<br>
Mahindra - 10<br>
Kia - 7<br>
Others: 13%</p>
              <div class="show-more-button" style="height: 20px"></div>
            </div>

            <button class="a upper font-weight-500 letter-spacing plausible-event-name=Key+Insight plausible-event-user=unregistered"
                    style="font-size: 1.4rem"
                    type="button"
                    onclick="Modal.openInModal(event)"
                    data-title="Maruti Suzuki India Ltd"
                    data-url="/wiki/company/2023/commentary/v2/"
                    data-is-right-modal="true">
              Read More <i class="icon-right"></i>
            </button>
          
        </div>

        <div class="company-links hide-from-tablet-landscape margin-top-20"
             style="font-weight: 500">
          
            <a href="http://www.marutisuzuki.com"
               target="_blank"
               rel="noopener noreferrer">
              <i class="icon-link"></i>
              <span style="font-size: 1.6rem;">Website</span>
            </a>
          

          
            <a href="https://www.bseindia.com/stock-share-price/maruti-suzuki-india-ltd/MARUTI/532500/" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">BSE</span>
            </a>
          

          
            <a href="https://www.nseindia.com/get-quotes/equity?symbol=MARUTI" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">
                NSE
                
              </span>
            </a>
          
        </div>
      </div>
      

      <div class="company-ratios">
        <ul id="top-ratios">
          


  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Market Cap
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">4,08,714</span>
        
          Cr.
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Current Price
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">12,995</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        High / Low
      
    </span>

    
      <span class="nowrap value">₹ <span class="number">13,680</span> / <span class="number">10,725</span></span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Stock P/E
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">28.2</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Book Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">3,061</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Dividend Yield
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">0.96</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROCE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">21.8</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">16.0</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Face Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">5.00</span>
        
      </span>
    
  </li>






        </ul>

        <div style="margin-top: 32px;">
          <label for="quick-ratio-search">Add ratio to table</label>

          <div class="flex-row flex-space-between flex-baseline flex-gap-16">
            <div class="dropdown-typeahead flex-grow" style="max-width: 480px;">
              <input id="quick-ratio-search"
                     type="text"
                     placeholder="eg. Promoter holding"
                     style="width: 100%">
            </div>

            
            <a href="/user/quick_ratios/?next=/company/MARUTI/consolidated/"
               class="smaller">
              <i class="icon-pencil"></i>
              <span class="upper font-weight-500 letter-spacing">Edit ratios</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  
    <section id="chart" class="card card-large" style="margin-bottom: 2.5rem">
      <div id="chart-menu"
     class="flex flex-space-between flex-wrap"
     style="margin-bottom: 16px">
  <div class="options" id="company-chart-days">
    <button name="days"
            value="30"
            onclick="CompanyChart.setActive(event)"
            type="button">1M</button>
    <button name="days"
            value="180"
            onclick="CompanyChart.setActive(event)"
            type="button">6M</button>
    <button name="days"
            value="365"
            onclick="CompanyChart.setActive(event)"
            type="button">1Yr</button>
    <button name="days"
            value="1095"
            onclick="CompanyChart.setActive(event)"
            type="button">3Yr</button>
    <button name="days"
            value="1825"
            onclick="CompanyChart.setActive(event)"
            type="button">5Yr</button>
    <button name="days"
            value="3652"
            onclick="CompanyChart.setActive(event)"
            type="button">10Yr</button>
    <button name="days"
            value="10000"
            onclick="CompanyChart.setActive(event)"
            type="button">Max</button>
  </div>

  <div class="flex margin-bottom-24">
    <div class="options margin-right-8"
         id="company-chart-metrics"
         style="margin-bottom: 0">
      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Price"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price-DMA50-DMA200-Volume"
                  type="button">Price</button>
        

      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PE"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price to Earning-Median PE-EPS"
                  type="button">PE Ratio</button>
        

      

        

      

        

      

        

      

        

      
      <button class="hidden"
              id="company-chart-metrics-more-active"
              onclick="CompanyChart.setActive(event)"
              name="metrics"
              value=""
              type="button">Hidden</button>
      <div class="dropdown-menu" id="metrics-dropdown">
        <button type="button"
                class="button-plain"
                onclick="Utils.toggleDropdown(event)">
          More <i class="icon-down"></i>
        </button>
        <ul class="dropdown-content" style="max-width: 160px; right: 0">
          

            

          

            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="GPM-OPM-NPM-Quarter Sales"
                        type="button">Sales &amp; Margin</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=EV-EBITDA"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="EV Multiple-Median EV Multiple-EBITDA"
                        type="button">EV / EBITDA</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PBV"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Price to book value-Median PBV-Book value"
                        type="button">Price to Book</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Market+Cap+to+Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Market Cap to Sales-Median Market Cap to Sales-Sales"
                        type="button">Market Cap / Sales</button>
              </li>
            

          
        </ul>
      </div>
    </div>
    
      <button type="button"
              class="button-small button-secondary flex flex-align-center plausible-event-name=Stock+Alert"
              style="border-color: var(--button-border);
                     border-radius: 3px"
              data-title="Alerts for Maruti Suzuki India Ltd"
              onclick="Modal.openInModal(event)"
              data-url="/alerts/stock-6597252/"
              aria-label="Add alerts for company">
        <i class="icon-bell"></i>
        
        <span class="margin-left-4 normal-case letter-spacing-0">Alerts</span>
      </button>
    
  </div>
</div>

<div class="flex no-select">
  <div class="hide-on-mobile chart-label">
    <div id="chart-label-left"></div>
  </div>

  <div id="chart-area" style="flex-grow: 1">
    <div id="chart-info" class="invisible">
      <div id="chart-crosshair"></div>
      <div id="chart-tooltip" class="font-size-14">
        <div id="chart-tooltip-meta" class="ink-700 font-size-12"></div>
        <div class="font-weight-500 font-size-15" id="chart-tooltip-title"></div>
        <div id="chart-tooltip-info"></div>
      </div>
    </div>
    <div id="canvas-chart-holder" style="height: 375px"></div>
  </div>

  <div class="hide-on-mobile chart-label">
    <div id="chart-label-right"></div>
  </div>
</div>
<div id="chart-legend"></div>

    </section>

    
    <section id="analysis" class="card card-large">
      <div class="flex flex-column-mobile flex-gap-32">
        <div class="pros" style="flex-basis: 50%;">
          <p class="title">Pros</p>
          <ul>
            <li>Company has reduced debt.</li><li>Company is almost debt free.</li><li>Company has delivered good profit growth of 34.8% CAGR over last 5 years</li><li>Company has been maintaining a healthy dividend payout of 30.9%</li>
          </ul>
        </div>
        <div class="cons" style="flex-basis: 50%;">
          <p class="title">Cons</p>
          <ul>
            
          </ul>
        </div>
      </div>
      <p class="sub font-size-14 margin-top-16 margin-bottom-0">
        <span style="vertical-align: sub;">*</span>
        <span class="font-weight-500">The pros and cons are machine generated.</span>
        <span class="has-tooltip">
          <i class="icon-info"></i>
          <span class="tooltip" style="width: 300px; left: -100px;">
            Pros / cons are based on a checklist to highlight important points. Please exercise caution and do your own analysis.
          </span>
        </span>
      </p>
    </section>

    <section id="peers" class="card card-large">
      <div class="flex flex-space-between" style="align-items: center;">
        <div>
          <h2>Peer comparison</h2>

          
            <p class="sub">
              Sector:
              <a href="/company/compare/00000005/"
                 target="_blank">Automobile</a>
              <span style="margin: 16px"></span>
              Industry:
              <a href="/company/compare/00000005/00000006/"
                 target="_blank">Automobiles - Passenger Cars</a>
            </p>
          

          
            <p class="flex flex-wrap gap-8 flex-align-center line-height-1 sub"
               id="benchmarks">
              Part of
              
                <a href="/company/1001/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Sensex"
                   target="_blank">BSE Sensex</a>
              
                <a href="/company/NIFTY/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50"
                   target="_blank">Nifty 50</a>
              
                <a href="/company/1005/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+500"
                   target="_blank">BSE 500</a>
              
                <a href="/company/1002/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100"
                   target="_blank">BSE 100</a>
              
                <a href="/company/1003/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+200"
                   target="_blank">BSE 200</a>
              
                <a href="/company/1004/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dollex+200"
                   target="_blank">BSE Dollex 200</a>
              
                <a href="/company/CNX500/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500"
                   target="_blank">Nifty 500</a>
              
                <a href="/company/1123/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Auto"
                   target="_blank">BSE Auto</a>
              
                <a href="/company/CNX100/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100"
                   target="_blank">Nifty 100</a>
              
                <a href="/company/CNXMNC/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+MNC"
                   target="_blank">Nifty MNC</a>
              
                <a href="/company/CNXCONSUMP/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+India+Consumption"
                   target="_blank">Nifty India Consumption</a>
              
                <a href="/company/CNXAUTO/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Auto"
                   target="_blank">Nifty Auto</a>
              
                <a href="/company/CNX200INDE/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+200"
                   target="_blank">Nifty 200</a>
              
                <a href="/company/LIX15/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Liquid+15"
                   target="_blank">Nifty 100 Liquid 15</a>
              
                <a href="/company/NV5020/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty50+Value+20"
                   target="_blank">Nifty50 Value 20</a>
              
                <a href="/company/NFT100EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Equal+Weight"
                   target="_blank">Nifty 100 Equal Weight</a>
              
                <a href="/company/1153/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Allcap"
                   target="_blank">BSE Allcap</a>
              
                <a href="/company/1155/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+LargeCap"
                   target="_blank">BSE LargeCap</a>
              
                <a href="/company/1160/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Consumer+Discretionary"
                   target="_blank">BSE Consumer Discretionary</a>
              
                <a href="/company/1166/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+India+Manufacturing+Index"
                   target="_blank">BSE India Manufacturing Index</a>
              
                <a href="/company/1125/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+SENSEX+50"
                   target="_blank">BSE SENSEX 50</a>
              
                <a href="/company/NIFTYQLY30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty100+Quality+30"
                   target="_blank">Nifty100 Quality 30</a>
              
                <a href="/company/1017/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100+ESG+Index+(INR)"
                   target="_blank">BSE 100 ESG Index (INR)</a>
              
                <a href="/company/1181/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+250+LargeMidCap+Index"
                   target="_blank">BSE 250 LargeMidCap Index</a>
              
                <a href="/company/1183/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dividend+Stability+Index"
                   target="_blank">BSE Dividend Stability Index</a>
              
                <a href="/company/1185/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Low+Volatility+Index"
                   target="_blank">BSE Low Volatility Index</a>
              
                <a href="/company/NFT50EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50+Equal+Weight"
                   target="_blank">Nifty 50 Equal Weight</a>
              
                <a href="/company/NFT100LV30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Low+Volatility+30"
                   target="_blank">Nifty 100 Low Volatility 30</a>
              
                <a href="/company/1130/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100+LargeCap+TMC+Index"
                   target="_blank">BSE 100 LargeCap TMC Index</a>
              
                <a href="/company/LMIDCAP250/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+LargeMidcap+250"
                   target="_blank">Nifty LargeMidcap 250</a>
              
                <a href="/company/NFT500MULT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500+Multicap+50:25:25"
                   target="_blank">Nifty 500 Multicap 50:25:25</a>
              
                <a href="/company/NIFT100ESG/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty100+ESG"
                   target="_blank">Nifty100 ESG</a>
              
                <a href="/company/NFINDIAMFG/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+India+Manufacturing"
                   target="_blank">Nifty India Manufacturing</a>
              
                <a href="/company/NFTYTOTMKT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Total+Market"
                   target="_blank">Nifty Total Market</a>
              
                <a href="/company/NMIM503020/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty500+Multicap+India+Manufacturing+50:30:20"
                   target="_blank">Nifty500 Multicap India Manufacturing 50:30:20</a>
              
              
                <button type="button"
                        class="a small font-weight-500 plausible-event-name=Show+More+Benchmarks"
                        onclick="Utils.toggleClass('#benchmarks', 'show-hidden')">show all</button>
              
            </p>
          
        </div>

        <div class="flex-reverse">
          <a href="/user/columns/?next=/company/MAruti/consolidated/#peers"
             class="button button-small button-secondary">
            <i class="icon-cog"></i>
            <span class="hide-on-mobile">Edit</span>
            Columns
          </a>
        </div>
      </div>

      <div id="peers-table-placeholder">Loading peers table ...</div>

      <div class="flex-row flex-baseline">
        <label for="search-peer-comparison" style="padding-right: 1rem">Detailed Comparison with:</label>
        <div class="dropdown-typeahead">
          <input type="text"
                 id="search-peer-comparison"
                 placeholder="eg. Infosys"
                 class="small">
        </div>
      </div>
    </section>

    <section id="quarters" class="card card-large">
      
        


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Quarterly Results</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/MARUTI/#quarters"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="highlight-cell">
            Mar 2022
            
          </th>
        
          <th class="">
            Jun 2022
            
          </th>
        
          <th class="">
            Sep 2022
            
          </th>
        
          <th class="">
            Dec 2022
            
          </th>
        
          <th class="highlight-cell">
            Mar 2023
            
          </th>
        
          <th class="">
            Jun 2023
            
          </th>
        
          <th class="">
            Sep 2023
            
          </th>
        
          <th class="">
            Dec 2023
            
          </th>
        
          <th class="highlight-cell">
            Mar 2024
            
          </th>
        
          <th class="">
            Jun 2024
            
          </th>
        
          <th class="">
            Sep 2024
            
          </th>
        
          <th class="">
            Dec 2024
            
          </th>
        
          <th class="highlight-cell">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Sales', 'quarters', this)">
                  Sales&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">26,749</td>
            
              <td class="">26,512</td>
            
              <td class="">29,942</td>
            
              <td class="">29,251</td>
            
              <td class="highlight-cell">32,214</td>
            
              <td class="">32,535</td>
            
              <td class="">37,339</td>
            
              <td class="">33,513</td>
            
              <td class="highlight-cell">38,471</td>
            
              <td class="">35,779</td>
            
              <td class="">37,449</td>
            
              <td class="">38,764</td>
            
              <td class="highlight-cell">40,920</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'quarters', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">24,320</td>
            
              <td class="">24,597</td>
            
              <td class="">27,172</td>
            
              <td class="">25,878</td>
            
              <td class="highlight-cell">28,320</td>
            
              <td class="">29,015</td>
            
              <td class="">32,028</td>
            
              <td class="">29,073</td>
            
              <td class="highlight-cell">33,250</td>
            
              <td class="">30,673</td>
            
              <td class="">32,450</td>
            
              <td class="">33,688</td>
            
              <td class="highlight-cell">36,076</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Operating Profit
              
            </td>
            
              <td class="highlight-cell">2,429</td>
            
              <td class="">1,915</td>
            
              <td class="">2,771</td>
            
              <td class="">3,373</td>
            
              <td class="highlight-cell">3,894</td>
            
              <td class="">3,520</td>
            
              <td class="">5,312</td>
            
              <td class="">4,440</td>
            
              <td class="highlight-cell">5,221</td>
            
              <td class="">5,107</td>
            
              <td class="">4,999</td>
            
              <td class="">5,076</td>
            
              <td class="highlight-cell">4,844</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                OPM %
              
            </td>
            
              <td class="highlight-cell">9%</td>
            
              <td class="">7%</td>
            
              <td class="">9%</td>
            
              <td class="">12%</td>
            
              <td class="highlight-cell">12%</td>
            
              <td class="">11%</td>
            
              <td class="">14%</td>
            
              <td class="">13%</td>
            
              <td class="highlight-cell">14%</td>
            
              <td class="">14%</td>
            
              <td class="">13%</td>
            
              <td class="">13%</td>
            
              <td class="highlight-cell">12%</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'quarters', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">514</td>
            
              <td class="">112</td>
            
              <td class="">662</td>
            
              <td class="">893</td>
            
              <td class="highlight-cell">850</td>
            
              <td class="">1,110</td>
            
              <td class="">958</td>
            
              <td class="">1,053</td>
            
              <td class="highlight-cell">1,261</td>
            
              <td class="">1,118</td>
            
              <td class="">1,570</td>
            
              <td class="">1,125</td>
            
              <td class="highlight-cell">1,583</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="highlight-cell">56</td>
            
              <td class="">28</td>
            
              <td class="">31</td>
            
              <td class="">30</td>
            
              <td class="highlight-cell">164</td>
            
              <td class="">46</td>
            
              <td class="">35</td>
            
              <td class="">36</td>
            
              <td class="highlight-cell">76</td>
            
              <td class="">57</td>
            
              <td class="">43</td>
            
              <td class="">46</td>
            
              <td class="highlight-cell">48</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="highlight-cell">648</td>
            
              <td class="">652</td>
            
              <td class="">723</td>
            
              <td class="">1,209</td>
            
              <td class="highlight-cell">1,240</td>
            
              <td class="">1,314</td>
            
              <td class="">1,342</td>
            
              <td class="">1,302</td>
            
              <td class="highlight-cell">1,298</td>
            
              <td class="">1,332</td>
            
              <td class="">1,386</td>
            
              <td class="">1,429</td>
            
              <td class="highlight-cell">1,462</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="highlight-cell">2,239</td>
            
              <td class="">1,347</td>
            
              <td class="">2,679</td>
            
              <td class="">3,027</td>
            
              <td class="highlight-cell">3,341</td>
            
              <td class="">3,269</td>
            
              <td class="">4,892</td>
            
              <td class="">4,156</td>
            
              <td class="highlight-cell">5,108</td>
            
              <td class="">4,836</td>
            
              <td class="">5,141</td>
            
              <td class="">4,726</td>
            
              <td class="highlight-cell">4,918</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="highlight-cell">16%</td>
            
              <td class="">23%</td>
            
              <td class="">21%</td>
            
              <td class="">21%</td>
            
              <td class="highlight-cell">20%</td>
            
              <td class="">22%</td>
            
              <td class="">23%</td>
            
              <td class="">23%</td>
            
              <td class="highlight-cell">23%</td>
            
              <td class="">22%</td>
            
              <td class="">40%</td>
            
              <td class="">21%</td>
            
              <td class="highlight-cell">20%</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'quarters', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">1,876</td>
            
              <td class="">1,036</td>
            
              <td class="">2,112</td>
            
              <td class="">2,406</td>
            
              <td class="highlight-cell">2,688</td>
            
              <td class="">2,543</td>
            
              <td class="">3,786</td>
            
              <td class="">3,207</td>
            
              <td class="highlight-cell">3,952</td>
            
              <td class="">3,760</td>
            
              <td class="">3,102</td>
            
              <td class="">3,727</td>
            
              <td class="highlight-cell">3,911</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="highlight-cell">62.10</td>
            
              <td class="">34.30</td>
            
              <td class="">69.93</td>
            
              <td class="">79.65</td>
            
              <td class="highlight-cell">88.97</td>
            
              <td class="">84.18</td>
            
              <td class="">125.34</td>
            
              <td class="">102.00</td>
            
              <td class="highlight-cell">125.71</td>
            
              <td class="">119.58</td>
            
              <td class="">98.68</td>
            
              <td class="">118.54</td>
            
              <td class="highlight-cell">124.40</td>
            
          </tr>
        
      

      
        <tr class="font-size-14 ink-600">
          <td class="text ink-600">Raw PDF</td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/2023/3/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2023/6/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2023/9/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2023/12/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/2023/3/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2023/6/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2023/9/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2023/12/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/2023/3/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2023/6/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2023/9/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2023/12/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/2023/3/2025/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
        </tr>
      
    </tbody>
  </table>
</div>

      

      
    </section>

    <section id="profit-loss" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Profit & Loss</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/MARUTI/#profit-loss"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    
      <button data-url="/results/rpt/2023/consolidated/"
              data-title="Related Party Transactions"
              onclick="Modal.openInModal(event)"
              class="button-small button-secondary plausible-event-name=Related+party plausible-event-user=unregistered">
        <i class="small icon-users"></i>
        Related Party
      </button>
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Sales', 'profit-loss', this)">
                  Sales&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">44,542</td>
            
              <td class="">50,801</td>
            
              <td class="">57,589</td>
            
              <td class="">68,085</td>
            
              <td class="">79,809</td>
            
              <td class="">86,068</td>
            
              <td class="">75,660</td>
            
              <td class="">70,372</td>
            
              <td class="">88,330</td>
            
              <td class="">118,410</td>
            
              <td class="">141,858</td>
            
              <td class="">152,913</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'profit-loss', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">39,232</td>
            
              <td class="">43,909</td>
            
              <td class="">48,565</td>
            
              <td class="">57,664</td>
            
              <td class="">67,692</td>
            
              <td class="">75,012</td>
            
              <td class="">68,305</td>
            
              <td class="">64,961</td>
            
              <td class="">82,578</td>
            
              <td class="">105,288</td>
            
              <td class="">123,232</td>
            
              <td class="">132,757</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Operating Profit
              
            </td>
            
              <td class="">5,310</td>
            
              <td class="">6,892</td>
            
              <td class="">9,024</td>
            
              <td class="">10,421</td>
            
              <td class="">12,118</td>
            
              <td class="">11,056</td>
            
              <td class="">7,355</td>
            
              <td class="">5,411</td>
            
              <td class="">5,752</td>
            
              <td class="">13,122</td>
            
              <td class="">18,626</td>
            
              <td class="">20,156</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                OPM %
              
            </td>
            
              <td class="">12%</td>
            
              <td class="">14%</td>
            
              <td class="">16%</td>
            
              <td class="">15%</td>
            
              <td class="">15%</td>
            
              <td class="">13%</td>
            
              <td class="">10%</td>
            
              <td class="">8%</td>
            
              <td class="">7%</td>
            
              <td class="">11%</td>
            
              <td class="">13%</td>
            
              <td class="">13%</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'profit-loss', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">724</td>
            
              <td class="">817</td>
            
              <td class="">1,464</td>
            
              <td class="">2,399</td>
            
              <td class="">2,155</td>
            
              <td class="">2,664</td>
            
              <td class="">3,410</td>
            
              <td class="">3,046</td>
            
              <td class="">1,861</td>
            
              <td class="">2,415</td>
            
              <td class="">4,248</td>
            
              <td class="">5,266</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="">184</td>
            
              <td class="">218</td>
            
              <td class="">82</td>
            
              <td class="">89</td>
            
              <td class="">346</td>
            
              <td class="">76</td>
            
              <td class="">134</td>
            
              <td class="">102</td>
            
              <td class="">127</td>
            
              <td class="">252</td>
            
              <td class="">194</td>
            
              <td class="">194</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="">2,116</td>
            
              <td class="">2,515</td>
            
              <td class="">2,822</td>
            
              <td class="">2,604</td>
            
              <td class="">2,760</td>
            
              <td class="">3,021</td>
            
              <td class="">3,528</td>
            
              <td class="">3,034</td>
            
              <td class="">2,789</td>
            
              <td class="">4,846</td>
            
              <td class="">5,256</td>
            
              <td class="">5,608</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="">3,734</td>
            
              <td class="">4,976</td>
            
              <td class="">7,585</td>
            
              <td class="">10,127</td>
            
              <td class="">11,167</td>
            
              <td class="">10,624</td>
            
              <td class="">7,103</td>
            
              <td class="">5,321</td>
            
              <td class="">4,697</td>
            
              <td class="">10,438</td>
            
              <td class="">17,424</td>
            
              <td class="">19,620</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="">24%</td>
            
              <td class="">24%</td>
            
              <td class="">28%</td>
            
              <td class="">26%</td>
            
              <td class="">29%</td>
            
              <td class="">28%</td>
            
              <td class="">20%</td>
            
              <td class="">18%</td>
            
              <td class="">17%</td>
            
              <td class="">21%</td>
            
              <td class="">23%</td>
            
              <td class="">26%</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'profit-loss', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">2,854</td>
            
              <td class="">3,809</td>
            
              <td class="">5,497</td>
            
              <td class="">7,511</td>
            
              <td class="">7,881</td>
            
              <td class="">7,651</td>
            
              <td class="">5,678</td>
            
              <td class="">4,389</td>
            
              <td class="">3,880</td>
            
              <td class="">8,264</td>
            
              <td class="">13,488</td>
            
              <td class="">14,500</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="">94.44</td>
            
              <td class="">126.04</td>
            
              <td class="">181.98</td>
            
              <td class="">248.61</td>
            
              <td class="">260.86</td>
            
              <td class="">253.21</td>
            
              <td class="">187.90</td>
            
              <td class="">145.30</td>
            
              <td class="">128.43</td>
            
              <td class="">273.56</td>
            
              <td class="">429.01</td>
            
              <td class="">461.20</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Dividend Payout %
              
            </td>
            
              <td class="">13%</td>
            
              <td class="">20%</td>
            
              <td class="">19%</td>
            
              <td class="">30%</td>
            
              <td class="">31%</td>
            
              <td class="">32%</td>
            
              <td class="">32%</td>
            
              <td class="">31%</td>
            
              <td class="">47%</td>
            
              <td class="">34%</td>
            
              <td class="">29%</td>
            
              <td class="">29%</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>


      <div style="display: grid;
                  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                  gap: 2%">
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Sales Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>12%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>15%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>20%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>8%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Profit Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>16%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>35%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>73%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>7%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Stock Price CAGR</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>13%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>21%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>20%</td>
          </tr>
          <tr>
            <td>1 Year:</td>
            <td>4%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Return on Equity</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>13%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>13%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>15%</td>
          </tr>
          <tr>
            <td>Last Year:</td>
            <td>16%</td>
          </tr>
        </table>
      </div>
    </section>

    <section id="balance-sheet" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Balance Sheet</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/MARUTI/#balance-sheet"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
      <button type="button"
              class="button-small button-secondary plausible-event-name=Corporate+Actions plausible-event-user=unregistered"
              onclick="Modal.openInModal(event)"
              data-title="Corporate actions"
              data-url="/company/actions/2023/">Corporate actions</button>
    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                Equity Capital
              
            </td>
            
              <td class="">151</td>
            
              <td class="">151</td>
            
              <td class="">151</td>
            
              <td class="">151</td>
            
              <td class="">151</td>
            
              <td class="">151</td>
            
              <td class="">151</td>
            
              <td class="">151</td>
            
              <td class="">151</td>
            
              <td class="">157</td>
            
              <td class="">157</td>
            
              <td class="">157</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Reserves
              
            </td>
            
              <td class="">21,345</td>
            
              <td class="">24,167</td>
            
              <td class="">30,465</td>
            
              <td class="">36,924</td>
            
              <td class="">42,408</td>
            
              <td class="">46,941</td>
            
              <td class="">49,262</td>
            
              <td class="">52,350</td>
            
              <td class="">55,182</td>
            
              <td class="">74,443</td>
            
              <td class="">85,479</td>
            
              <td class="">96,083</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Borrowings', 'balance-sheet', this)">
                  Borrowings&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">2,004</td>
            
              <td class="">666</td>
            
              <td class="">231</td>
            
              <td class="">484</td>
            
              <td class="">121</td>
            
              <td class="">160</td>
            
              <td class="">184</td>
            
              <td class="">541</td>
            
              <td class="">426</td>
            
              <td class="">1,248</td>
            
              <td class="">119</td>
            
              <td class="">87</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Liabilities', 'balance-sheet', this)">
                  Other Liabilities&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">7,975</td>
            
              <td class="">9,492</td>
            
              <td class="">11,879</td>
            
              <td class="">14,402</td>
            
              <td class="">17,568</td>
            
              <td class="">16,717</td>
            
              <td class="">14,031</td>
            
              <td class="">18,335</td>
            
              <td class="">18,896</td>
            
              <td class="">24,258</td>
            
              <td class="">29,550</td>
            
              <td class="">35,645</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Total Liabilities
              
            </td>
            
              <td class="">31,476</td>
            
              <td class="">34,477</td>
            
              <td class="">42,726</td>
            
              <td class="">51,960</td>
            
              <td class="">60,248</td>
            
              <td class="">63,969</td>
            
              <td class="">63,628</td>
            
              <td class="">71,376</td>
            
              <td class="">74,656</td>
            
              <td class="">100,106</td>
            
              <td class="">115,304</td>
            
              <td class="">131,972</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Fixed Assets', 'balance-sheet', this)">
                  Fixed Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">11,034</td>
            
              <td class="">12,490</td>
            
              <td class="">12,530</td>
            
              <td class="">13,311</td>
            
              <td class="">13,389</td>
            
              <td class="">15,437</td>
            
              <td class="">15,744</td>
            
              <td class="">14,989</td>
            
              <td class="">13,747</td>
            
              <td class="">27,941</td>
            
              <td class="">27,865</td>
            
              <td class="">33,384</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                CWIP
              
            </td>
            
              <td class="">2,640</td>
            
              <td class="">1,890</td>
            
              <td class="">1,007</td>
            
              <td class="">1,252</td>
            
              <td class="">2,132</td>
            
              <td class="">1,607</td>
            
              <td class="">1,415</td>
            
              <td class="">1,497</td>
            
              <td class="">2,936</td>
            
              <td class="">4,143</td>
            
              <td class="">7,735</td>
            
              <td class="">7,527</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Investments
              
            </td>
            
              <td class="">10,527</td>
            
              <td class="">13,298</td>
            
              <td class="">20,676</td>
            
              <td class="">29,151</td>
            
              <td class="">36,123</td>
            
              <td class="">37,504</td>
            
              <td class="">37,488</td>
            
              <td class="">42,945</td>
            
              <td class="">42,035</td>
            
              <td class="">49,184</td>
            
              <td class="">57,296</td>
            
              <td class="">66,265</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Assets', 'balance-sheet', this)">
                  Other Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">7,275</td>
            
              <td class="">6,800</td>
            
              <td class="">8,513</td>
            
              <td class="">8,247</td>
            
              <td class="">8,604</td>
            
              <td class="">9,421</td>
            
              <td class="">8,980</td>
            
              <td class="">11,946</td>
            
              <td class="">15,937</td>
            
              <td class="">18,837</td>
            
              <td class="">22,408</td>
            
              <td class="">24,795</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Total Assets
              
            </td>
            
              <td class="">31,476</td>
            
              <td class="">34,477</td>
            
              <td class="">42,726</td>
            
              <td class="">51,960</td>
            
              <td class="">60,248</td>
            
              <td class="">63,969</td>
            
              <td class="">63,628</td>
            
              <td class="">71,376</td>
            
              <td class="">74,656</td>
            
              <td class="">100,106</td>
            
              <td class="">115,304</td>
            
              <td class="">131,972</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="cash-flow" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Cash Flows</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/MARUTI/#cash-flow"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Operating Activity', 'cash-flow', this)">
                  Cash from Operating Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">4,995</td>
            
              <td class="">6,449</td>
            
              <td class="">8,482</td>
            
              <td class="">10,282</td>
            
              <td class="">11,788</td>
            
              <td class="">6,601</td>
            
              <td class="">3,496</td>
            
              <td class="">8,856</td>
            
              <td class="">1,840</td>
            
              <td class="">10,815</td>
            
              <td class="">16,801</td>
            
              <td class="">16,136</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Investing Activity', 'cash-flow', this)">
                  Cash from Investing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">-4,997</td>
            
              <td class="">-4,491</td>
            
              <td class="">-7,230</td>
            
              <td class="">-9,173</td>
            
              <td class="">-8,302</td>
            
              <td class="">-3,540</td>
            
              <td class="">-557</td>
            
              <td class="">-7,291</td>
            
              <td class="">-239</td>
            
              <td class="">-8,820</td>
            
              <td class="">-11,865</td>
            
              <td class="">-14,456</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Financing Activity', 'cash-flow', this)">
                  Cash from Financing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">-74</td>
            
              <td class="">-2,004</td>
            
              <td class="">-1,237</td>
            
              <td class="">-1,129</td>
            
              <td class="">-3,436</td>
            
              <td class="">-2,948</td>
            
              <td class="">-3,104</td>
            
              <td class="">-1,545</td>
            
              <td class="">-1,607</td>
            
              <td class="">-1,214</td>
            
              <td class="">-4,062</td>
            
              <td class="">-4,155</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Net Cash Flow
              
            </td>
            
              <td class="">-76</td>
            
              <td class="">-45</td>
            
              <td class="">16</td>
            
              <td class="">-20</td>
            
              <td class="">50</td>
            
              <td class="">113</td>
            
              <td class="">-165</td>
            
              <td class="">20</td>
            
              <td class="">-6</td>
            
              <td class="">780</td>
            
              <td class="">874</td>
            
              <td class="">-2,475</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="ratios" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Ratios</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/MARUTI/#ratios"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                Debtor Days
              
            </td>
            
              <td class="">12</td>
            
              <td class="">8</td>
            
              <td class="">8</td>
            
              <td class="">6</td>
            
              <td class="">7</td>
            
              <td class="">10</td>
            
              <td class="">10</td>
            
              <td class="">7</td>
            
              <td class="">8</td>
            
              <td class="">10</td>
            
              <td class="">12</td>
            
              <td class="">16</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Inventory Days
              
            </td>
            
              <td class="">20</td>
            
              <td class="">28</td>
            
              <td class="">30</td>
            
              <td class="">25</td>
            
              <td class="">21</td>
            
              <td class="">20</td>
            
              <td class="">22</td>
            
              <td class="">22</td>
            
              <td class="">20</td>
            
              <td class="">23</td>
            
              <td class="">19</td>
            
              <td class="">23</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Days Payable
              
            </td>
            
              <td class="">57</td>
            
              <td class="">57</td>
            
              <td class="">70</td>
            
              <td class="">65</td>
            
              <td class="">70</td>
            
              <td class="">59</td>
            
              <td class="">51</td>
            
              <td class="">73</td>
            
              <td class="">54</td>
            
              <td class="">58</td>
            
              <td class="">62</td>
            
              <td class="">70</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Cash Conversion Cycle
              
            </td>
            
              <td class="">-25</td>
            
              <td class="">-20</td>
            
              <td class="">-32</td>
            
              <td class="">-33</td>
            
              <td class="">-42</td>
            
              <td class="">-29</td>
            
              <td class="">-20</td>
            
              <td class="">-44</td>
            
              <td class="">-26</td>
            
              <td class="">-25</td>
            
              <td class="">-31</td>
            
              <td class="">-31</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Working Capital Days
              
            </td>
            
              <td class="">-18</td>
            
              <td class="">-26</td>
            
              <td class="">-27</td>
            
              <td class="">-33</td>
            
              <td class="">-40</td>
            
              <td class="">-29</td>
            
              <td class="">-19</td>
            
              <td class="">-47</td>
            
              <td class="">-29</td>
            
              <td class="">-27</td>
            
              <td class="">-26</td>
            
              <td class="">-4</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                ROCE %
              
            </td>
            
              <td class="">16%</td>
            
              <td class="">19%</td>
            
              <td class="">23%</td>
            
              <td class="">24%</td>
            
              <td class="">24%</td>
            
              <td class="">19%</td>
            
              <td class="">9%</td>
            
              <td class="">11%</td>
            
              <td class="">6%</td>
            
              <td class="">16%</td>
            
              <td class="">22%</td>
            
              <td class="">22%</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="shareholding" class="card card-large">
      <div class="flex flex-space-between flex-wrap margin-bottom-8 flex-align-center">
  <div>
    <h2 class="margin-0">Shareholding Pattern</h2>
    <p class="sub">Numbers in percentages</p>
  </div>
  <div class="flex">
    <div class="options small margin-0">
      <button
        onclick="Utils.setActiveTab(event)"
        class="active"
        data-tab-id="quarterly-shp"
        type="button">
        Quarterly
      </button>
      <button
        onclick="Utils.setActiveTab(event)"
        data-tab-id="yearly-shp"
        type="button">
        Yearly
      </button>
    </div>
    
    <div class="text-align-center margin-left-12">
      <button
        type="button"
        class="button-secondary button-small"
        onclick="Modal.openInModal(event)"
        data-title="Maruti Suzuki trades"
        data-url="/trades/company-2023/"
      >
        Trades
      </button>
      
    </div>
    
  </div>
</div>

<div id="quarterly-shp">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Jun 2022</th><th>Sep 2022</th><th>Dec 2022</th><th>Mar 2023</th><th>Jun 2023</th><th>Sep 2023</th><th>Dec 2023</th><th>Mar 2024</th><th>Jun 2024</th><th>Sep 2024</th><th>Dec 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=promoters plausible-event-period=quarterly"
                      onclick="Company.showShareholders('promoters', 'quarterly', this)">
                Promoters&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>56.37%</td>
            
              <td>56.37%</td>
            
              <td>56.37%</td>
            
              <td>56.48%</td>
            
              <td>56.48%</td>
            
              <td>56.48%</td>
            
              <td>58.19%</td>
            
              <td>58.19%</td>
            
              <td>58.19%</td>
            
              <td>58.19%</td>
            
              <td>58.28%</td>
            
              <td>58.28%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=foreign_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('foreign_institutions', 'quarterly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>21.89%</td>
            
              <td>21.84%</td>
            
              <td>21.49%</td>
            
              <td>21.11%</td>
            
              <td>21.87%</td>
            
              <td>21.85%</td>
            
              <td>20.60%</td>
            
              <td>19.64%</td>
            
              <td>18.98%</td>
            
              <td>17.68%</td>
            
              <td>15.47%</td>
            
              <td>14.96%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=domestic_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('domestic_institutions', 'quarterly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>18.01%</td>
            
              <td>18.10%</td>
            
              <td>18.19%</td>
            
              <td>18.57%</td>
            
              <td>18.07%</td>
            
              <td>18.15%</td>
            
              <td>17.64%</td>
            
              <td>18.86%</td>
            
              <td>19.37%</td>
            
              <td>20.75%</td>
            
              <td>22.89%</td>
            
              <td>23.56%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=government plausible-event-period=quarterly"
                      onclick="Company.showShareholders('government', 'quarterly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.00%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.07%</td>
            
              <td>0.07%</td>
            
              <td>0.07%</td>
            
              <td>0.08%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=public plausible-event-period=quarterly"
                      onclick="Company.showShareholders('public', 'quarterly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>3.73%</td>
            
              <td>3.62%</td>
            
              <td>3.89%</td>
            
              <td>3.78%</td>
            
              <td>3.51%</td>
            
              <td>3.45%</td>
            
              <td>3.50%</td>
            
              <td>3.24%</td>
            
              <td>3.41%</td>
            
              <td>3.31%</td>
            
              <td>3.28%</td>
            
              <td>3.12%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>3,95,441</td><td>3,92,551</td><td>4,18,288</td><td>4,15,127</td><td>3,78,323</td><td>3,68,608</td><td>3,87,767</td><td>3,64,375</td><td>3,89,314</td><td>3,82,573</td><td>4,06,570</td><td>3,78,893</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>

<div id="yearly-shp" class="hidden">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Mar 2017</th><th>Mar 2018</th><th>Mar 2019</th><th>Mar 2020</th><th>Mar 2021</th><th>Mar 2022</th><th>Mar 2023</th><th>Mar 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=promoters plausible-event-period=yearly"
                      onclick="Company.showShareholders('promoters', 'yearly', this)">
                Promoters&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>56.21%</td>
            
              <td>56.21%</td>
            
              <td>56.21%</td>
            
              <td>56.28%</td>
            
              <td>56.37%</td>
            
              <td>56.37%</td>
            
              <td>56.48%</td>
            
              <td>58.19%</td>
            
              <td>58.28%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=foreign_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('foreign_institutions', 'yearly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>24.55%</td>
            
              <td>25.19%</td>
            
              <td>22.30%</td>
            
              <td>21.63%</td>
            
              <td>23.11%</td>
            
              <td>22.57%</td>
            
              <td>21.11%</td>
            
              <td>19.64%</td>
            
              <td>14.96%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=domestic_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('domestic_institutions', 'yearly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>12.27%</td>
            
              <td>11.46%</td>
            
              <td>13.41%</td>
            
              <td>16.69%</td>
            
              <td>15.08%</td>
            
              <td>16.25%</td>
            
              <td>18.57%</td>
            
              <td>18.86%</td>
            
              <td>23.56%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=government plausible-event-period=yearly"
                      onclick="Company.showShareholders('government', 'yearly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.08%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=public plausible-event-period=yearly"
                      onclick="Company.showShareholders('public', 'yearly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>6.98%</td>
            
              <td>7.15%</td>
            
              <td>8.09%</td>
            
              <td>5.40%</td>
            
              <td>5.44%</td>
            
              <td>4.81%</td>
            
              <td>3.78%</td>
            
              <td>3.24%</td>
            
              <td>3.12%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>1,75,076</td><td>2,83,738</td><td>4,38,699</td><td>4,06,100</td><td>4,61,379</td><td>4,31,889</td><td>4,15,127</td><td>3,64,375</td><td>3,78,893</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>


<p class="sub small">
  * The classifications might have changed from Sep'2022 onwards.
  <span class="has-tooltip">
    <i class="icon-info"></i>
    <span class="tooltip" style="width: 300px">
      The new XBRL format added more details from Sep'22 onwards.
      <br>
      <br>
      Classifications such as banks and foreign portfolio investors were not available earlier. The sudden changes in FII or DII can be because of these changes.
      <br>
      <br>
      Click on the line-items to see the names of individual entities.
    </span>
  </span>
</p>


    </section>

    <section id="documents" class="card card-large">
      <div class="flex flex-space-between margin-bottom-16">
        <h2 class="margin-0">Documents</h2>
      </div>

      <div class="flex-row flex-gap-small">
        
          <div class="documents flex-column" style="flex: 2 1 250px;">
            <h3 class="margin-bottom-8">Announcements</h3>

            <div class="show-more-box" style="flex-basis: 300px">
              <div id="company-announcements-tab">
                <div class="flex">
  <div class="options small" style="margin-bottom: 0">
    <button
      type="button"
      class="active"
      
      onclick="Utils.ajaxLoad(event, '/announcements/recent/2023/')"
      data-swap="#company-announcements-tab"
    >
      Recent
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/important/2023/')"
      data-swap="#company-announcements-tab"
    >
      Important
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/search/2023/')"
      data-swap="#company-announcements-tab"
    >
      Search
    </button>
    <a class="button" href="https://www.bseindia.com/stock-share-price/maruti-suzuki-india-ltd/maruti/532500/corp-announcements/" target="_blank" rel="noopener noreferrer">
      All
      <i class="icon-link-ext smaller"></i>
    </a>
  </div>
</div>


  
  <ul class="list-links">
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=628898ef-2497-4cd8-aeaa-ef7ef32a18ba.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Shareholder Meeting / Postal Ballot-Notice of Postal Ballot

          
            <span class="ink-600 smaller">4h</span>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=bf20ce31-f1c7-49e5-9d02-1d5633bd21b4.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Analyst / Investor Meet - Intimation

          
            <div class="ink-600 smaller">1d - Investor meeting scheduled for 20th May 2025, date subject to change.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d5ae4ca2-dcd3-43ca-b418-a721a8d2baee.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Intimation Under Regulation 30 Of The SEBI (LODR) Regulations, 2015

          
            <div class="ink-600 smaller">2d - NCLAT hearing on CCI order adjourned again; next date to be notified later.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=22770241-ec97-460e-84dd-ddfa71c52256.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Change In Statutory Auditors Of Material Subsidiary Of The Company

          
            <div class="ink-600 smaller">12 May - Deloitte resigns as auditor of Suzuki Motor Gujarat; Price Waterhouse appointed effective May 12, 2025.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=13996887-1732-45d6-b42f-76b779111f7f.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Consolidated Financial Results Issued By Suzuki Motor Corporation In Japan

          
            <div class="ink-600 smaller">12 May - Suzuki FY2024 consolidated results show revenue up 8.7%, profit up 31.2%; FY2025 profit forecast down 23.1%.</div>
          
        </a>
      </li>
    
  </ul>




              </div>
            </div>
          </div>
        

        <div class="documents annual-reports flex-column"
             style="flex: 1 1 200px;
                    max-width: 210px">
          <h3>Annual reports</h3>
          <div class="show-more-box">
            
<ul class="list-links">

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=4b14c577-1d8d-4eb0-a79e-f94640af8a67.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2024
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=\68f18376-59dd-4bc2-9aee-48e50ec97f27.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2023
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/74208532500.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2022
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/69235532500.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2021
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/5325000320.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2020
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/5325000319.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2019
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/5325000318.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2018
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/5325000317.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2017
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/5325000316.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2016
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/5325000315.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2015
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/5325000314.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2014
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://archives.nseindia.com/annual_reports/AR_898_MARUTI_2012_2013_07082013140623.zip" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2013
      <div class="ink-600 smaller">
        from nse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/5325000313.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2013
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/5325000312.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2012
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532500/5325000311.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2011
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

</ul>


          </div>
          
        </div>

        
          <div class="documents credit-ratings flex-column"
               style="flex: 1 1 200px;
                      max-width: 220px">
            <h3>Credit ratings</h3>
            <div class="show-more-box">
              
<ul class="list-links">

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/MarutiSuzukiIndiaLimited_January 09_ 2025_RR_359879.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        9 Jan from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/MarutiSuzukiIndiaLimited_December 05, 2023_RR_331868.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        5 Dec 2023 from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/MarutiSuzukiIndiaLimited_August 11, 2023_RR_325632.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        11 Aug 2023 from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/MarutiSuzukiIndiaLimited_March 30, 2023_RR_315935.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        30 Mar 2023 from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/MarutiSuzukiIndiaLimited_September 15, 2022_RR_301571.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        15 Sep 2022 from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/MarutiSuzukiIndiaLimited_September 01, 2022_RR_298347.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        1 Sep 2022 from crisil
      </div>
    </a>
  </li>

</ul>


            </div>
          </div>
        

        
          <div class="documents concalls flex-column"
               style="flex: 2 1 250px;
                      max-width: fit-content">
            <div class="flex flex-space-between flex-align-center margin-bottom-8">
              <h3 class="margin-0">Concalls</h3>
              <button class="a font-size-12 font-weight-500 sub"
                      onclick="Modal.openInModal(event)"
                      data-url="/concalls/add-2023/"
                      data-title="Maruti Suzuki - Add Link">
                <i class="icon-flag"></i>
                <span class="underline-link">Add Missing</span>
              </button>
            </div>
            <div class="show-more-box"><ul class="list-links">

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      May 2025
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q4_FY_25.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22994812/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - May 2025"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=fe4b9735-c370-47bc-b884-b16ba0b45689.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.youtube.com/watch?v=fuTZjfg2dZM" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2025
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q3_FY_25.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22979670/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2025"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=61a166ec-37a8-42f6-b5d3-5168529c73a3.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Audio-Recording-Q3-FY-2024-25-financial-results-investor-and-analyst-con-call-29th-January-2025.mp3?s=08" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2024
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q2_FY_25.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22971741/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=04f99f6b-4fc0-4493-88df-33c93345272f.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://youtu.be/cer6aaSgYH4?si=rZZyZAry6R7pUD5K" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2024
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q1_FY_25.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22947270/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=759f552a-69ee-4a40-92b6-2bcb6f2690b1.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2024
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q4_FY_24.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22388072/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=cc92b5fa-9912-4dd5-8649-5a7767b7714b.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Audio-Recording-Q4-FY-2023-24-financial-results-investor-and-analyst-con-call-31st-March-2024.mp3" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2024
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q3_FY_24.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/19275759/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=9e9acec2-5971-42ff-952b-bf5002f0d612.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2023
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q2_FY24.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22927857/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=381628ec-6f81-4bb9-a800-fa59cb7f5fbd.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=25fd3811-3e9d-44b1-b62c-44c8e000ea59.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Presentation_Acquisition_of_SMG.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2023
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q1_FY24.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/15279581/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=a1a98a12-592a-4d04-9a62-8448987b030c.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      May 2023
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call-Maruti_Suzuki_Q4_FY23_New.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/10119122/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - May 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=a90ec056-59e5-4f04-b3dc-49a3807d238b.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2023
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call-Maruti_Suzuki_Q3_FY_23.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/10954141/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=7d583d26-07f3-4eb5-9cd5-c77db2ee22d5.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Nov 2022
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q2_FY_23.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/11447308/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2022"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=0d23816e-08c1-46e5-9b1d-401c456bc690.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2022
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q1_FY23.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/11447309/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2022"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2022
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call-Maruti_Suzuki_Q3_FY22.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/1326756/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2022"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2021
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/MSIL_Final_transcript_2021.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22948477/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Aug 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2021
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call-Maruti_Suzuki_Q1_FY&#x27;22.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/518962/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Mar 2021
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript-earnings-call-Maruti-Suzuki-Q4-FY-21.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307119/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Mar 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2021
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q3_FY&#x27;21.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307118/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2021"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=7e0eac5e-4341-46c8-9402-4fbe608acabb.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2020
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call_Maruti_Suzuki_Q2_FY&#x27;21.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307117/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2020"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2020
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call-Maruti_Suzuki_Q1FY&#x27;21.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307116/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2020"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      May 2020
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcript_earnings_call-Maruti_Suzuki_Q4F_20_and_full_year_FY20.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307115/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - May 2020"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2019
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Maruti%20Suzuki_Q2FY20_and_H1FY20_Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307113/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2019"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2019
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Maruti_Suzuki_Q1FY20_Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307112/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2019"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2019
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Maruti_Suzuki_Q4FY19_Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307111/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2019"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2019
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Maruti_Suzuki_Q3FY19_Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307110/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2019"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2018
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Q2FY19_and_H1FY19_Investor_Conference_Call_Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307109/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2018"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2018
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Maruti_Suzuki_Q1FY19_Investor_Concall_Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307108/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2018"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=358b1f48-a0ff-4ee1-9ae4-617f2205db1e.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2018
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Maruti_Suzuki_Transcript-for-Q3FY18-Concall.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307106/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2018"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2017
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Maruti_Suzuki_Q2FY18_Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307105/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2017"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2017
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Maruti_Suzuki_Q4FY17-Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307104/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2017"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2017
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Maruti_Suzuki_Q3FY17_Transcript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307103/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2017"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2016
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcription_of_Maruti_Suzuki-Q1_FY_17.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307102/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2016"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2016
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcription_of_Maruti_Suzuki-26th_April_2016.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307101/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2016"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2016
    </div>

    
    <a
      class="concall-link"
      href="https://marutistoragenew.blob.core.windows.net/msilintiwebpdf/Transcription_of_Maruti-28th_Jan_2016_final.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307100/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2016"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

</ul>
</div>
          </div>
        
      </div>
    </section>
    <!-- cache time 2025-05-16 18:56:50 -->
    
  

      </main>
    

    
      

<footer id="large-footer">
  <div class="flex-row flex-space-between flex-column-tablet container no-print">
    <div class="hide-from-tablet" style="margin-bottom: 16px;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
    </div>

    <div class="show-from-tablet" style="padding: 0 64px 0 0;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
      <p class="font-size-19" style="font-weight: 500;">Stock analysis and screening tool</p>
      <p class="sub">
        Mittal Analytics Private Ltd &copy; 2009-2025
        <br>
        Made with <i class="icon-heart red"></i> in India.
      </p>
      <p class="sub font-size-13">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="font-size-13 sub">
        <a href="/guides/terms/" class="underline-link">Terms</a> & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>

    <div class="flex flex-end flex-space-between flex-grow"
         style="max-width: 600px">
      <div class="flex-grow">
        <div class="title">Product</div>
        <ul class="items">
          <li>
            <a href="/premium/?driver=footer">Premium</a>
          </li>
          <li>
            <a href="/docs/changelog/">What's new?</a>
          </li>
          <li>
            <a href="https://bit.ly/learnscreener">Learn</a>
          </li>
          <li>
            <button class="a2hs button-secondary button-small">
              <i class="icon-flash"></i>
              Install
            </button>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Team</div>
        <ul class="items">
          <li>
            <a href="/guides/about-us/">About us</a>
          </li>
          <li>
            <a href="/support/">Support</a>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Theme</div>
        <ul class="items">
          <li>
            <button onclick="SetTheme('light')"
                    aria-label="Set light theme"
                    class="button-plain">
              <i class="icon-sun"></i>
              Light
            </button>
          </li>
          <li>
            <button onclick="SetTheme('dark')"
                    aria-label="Set dark theme"
                    class="button-plain">
              <i class="icon-moon"></i>
              Dark
            </button>
          </li>
          <li>
            <button onclick="SetTheme('auto')"
                    aria-label="Set auto theme"
                    class="button-plain">
              <i class="icon-monitor"></i>
              Auto
            </button>
          </li>
        </ul>
      </div>
    </div>

    <div class="hide-from-tablet">
      <hr>
      <p class="sub">Mittal Analytics Private Ltd &copy; 2009-2024</p>
      <p class="sub">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="sub">
        <a href="/guides/terms/" class="underline-link">Terms</a>
        & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>
  </div>
</footer>

    

    <!-- Important JS (which provide global objects)- check below too -->
    <script src="https://cdn-static.screener.in/js/dialog-polyfill.min.2b5bf9a032d2.js"></script>
    <script src="https://cdn-static.screener.in/js/utils.02a9ce812e49.js"></script>
    <script src="https://cdn-static.screener.in/js/modal.4f872e091355.js"></script>
    <script src="https://cdn-static.screener.in/js/typeahead.8dcc9ac93685.js"></script>
    <script src="https://cdn-static.screener.in/js/filter.component.5add9ef3b7a9.js"></script>

    <!-- Independent JS scripts for page interactions -->
    <script src="https://cdn-static.screener.in/js/navbar.3fe7f0f4a630.js"></script>
    <script src="https://cdn-static.screener.in/js/company.search.e48f94642999.js"></script>
    
  <script src="https://cdn-static.screener.in/js/scroll-aid.5ed2d38a133e.js"></script>
  <script src="https://cdn-static.screener.in/js/company.customisation.d7c717b0de15.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.min.10a29a29621c.js"></script>
  <script src="https://cdn-static.screener.in/js/chartjs-adapter-date-fns.bundle.min.ebae23730a6e.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.e20c5f42613a.js"></script>
  <script src="https://cdn-static.screener.in/js/segment-result.b6af4191fd6f.js"></script>


    <!-- Service worker installer for PWA -->
    <script src="https://cdn-static.screener.in/js/pwa.933118758337.js"></script>
  </body>
</html>
