
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    
    <title>Tata Consultancy Services Ltd share price | About TCS | Key Insights - Screener</title>
    

    <!-- plausible -->
    <script defer
            event-user="unregistered"
            
            
            data-domain="screener.in"
            src="https://plausible.screener.in/js/script.pageview-props.tagged-events.js"></script>
    <script>window.plausible = window.plausible || function () { (window.plausible.q = window.plausible.q || []).push(arguments) }</script>

    <!-- FONT -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/inter/inter.43d4904a3137.css" />
    <link rel="stylesheet" href="https://cdn-static.screener.in/fontello/css/fontello.bc317748a65f.css" />

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/normalize.a2444750a947.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/skeleton.9aa9c32992ef.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/custom.39beec5e86b6.css" media="all">

    
    
    
  

  
  <meta name="description" content="TCS · Mkt Cap: 12,88,204 Crore (down -8.71% in 1 year) · Revenue: 2,55,324 Cr · Profit: 48,797 Cr · Stock is trading at 13.6 times its book value · The company has delivered a poor sales growth of 10.2% over past five years. · Promoter Holding: 71.8%">
  

  <style>
  tr[data-row-company-id="3365"] {
    font-weight: 500;
  }

  tr[data-row-company-id="3365"] a {
    color: var(--ink-900);
  }

  :target {
    scroll-margin-top: 90px;
  }

  @supports not (scroll-margin-top: 90px) {
    :target {
      padding-top: 108px;
    }
  }

  @media (min-width: 992px) {
    nav {
      margin-bottom: 0;
    }

    .company-nav {
      top: 50px;
      margin-bottom: 24px;
    }
  }
  </style>


    <meta name="author" content="Mittal Analytics Private Limited">

    <!-- PWA manifest -->
    <meta name="theme-color"
          content="#fff"
          media="(prefers-color-scheme: light)">
    <meta name="theme-color"
          content="#222530"
          media="(prefers-color-scheme: dark)">
    <link rel="manifest" href="/static/manifest.json">

    <!-- favicons -->
    <link rel="apple-touch-icon"
          sizes="180x180"
          href="https://cdn-static.screener.in/favicon/apple-touch-icon.b6e5f24bf7d3.png">
    <link rel="icon"
          type="image/png"
          sizes="32x32"
          href="https://cdn-static.screener.in/favicon/favicon-32x32.00205914303a.png">
    <link rel="icon"
          type="image/png"
          sizes="16x16"
          href="https://cdn-static.screener.in/favicon/favicon-16x16.2b35594b2f33.png">
    <link rel="mask-icon"
          href="https://cdn-static.screener.in/favicon/safari-pinned-tab.adbe3ed3c5ad.svg"
          color="#8ED443">
    <meta name="msapplication-TileColor" content="#da532c">
  </head>

  <body class="light flex-column">
    
      <nav class="u-full-width no-print">
        <div id="mobile-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

<ul class="bottom-menu-bar hide-from-tablet-landscape">
  
    <li class="">
      <a href="/">
        <i class="icon-home"></i>
        <br>
        Home
      </a>
    </li>
  

  <li class="">
    <a href="/explore/">
      <i class="icon-screens"></i>
      <br>
      Screens
    </a>
  </li>

  <li>
    <button type="button"
            onclick="MobileMenu.toggleSearch(this)"
            class="search-button"
            title="Search">
      <i class="icon-search" style="font-size: 18px"></i>
    </button>
  </li>

  <li>
    <button class="button-plain" onclick="Modal.showInModal('#nav-tools-menu')">
      <i class="icon-tools"></i>
      <br>
      Tools
    </button>
  </li>

  
    <li class="">
      <a href="/register/?">
        <i class="icon-user-plus"></i>
        <br>
        Login
      </a>
    </li>
  
</ul>

        <div class="top-nav-holder">
          <div class="container">



<div class="layer-holder top-navigation">
  <div class="layer flex flex-space-between"
       style="align-items: center;
              height: 50px">
    <div class="flex" style="align-items: center">
      <a href="/" class="logo-holder">
        <img alt="Screener Logo"
             class="logo"
             src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg" />
      </a>

      <div class="desktop-links" style="margin-left: 48px">
        <a href="/">
          
            Home
          
        </a>
        <a href="/explore/">Screens</a>
        <div class="dropdown-menu">
          <button class="button-plain"
                  type="button"
                  onclick="Utils.toggleDropdown(event)">
            Tools
            <i class="icon-down"></i>
          </button>
          <ul class="dropdown-content flex-column tools-menu"
              style="width: 350px"
              id="nav-tools-menu">
            <li>
              <a href="/screen/new/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/screens.e960a5daedf9.svg" alt="Create a stock screen">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Create a stock screen</div>
                  <div class="sub font-size-13">Run queries on 10 years of financial data</div>
                </div>
              </a>
            </li>
            <li class="flex flex-align-center no-hover" style="padding: 4px 0;">
              <div class="button button-primary premium-indicator flex flex-align-center">
                <img src="https://cdn-static.screener.in/icons/crown.fb0d2413ee43.svg"
                     alt="Premium icon"
                     style="margin-right: 8px">
                <span>Premium features</span>
              </div>
              <hr class="flex-grow" style="padding: 0; margin: 0;">
            </li>
            <li>
              <a href="/hs/" class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/export.d623f0029933.svg" alt="Commodity Prices">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Commodity Prices</div>
                  <div class="sub font-size-13">See prices and trends of over 10,000 commodities</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/people/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/shares.10acc20cb936.svg"
                       alt="Search shareholders by name">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Search shareholders</div>
                  <div class="sub font-size-13">See companies where a person holds over 1% of the shares</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/announcements/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/loudspeaker.88769927f6eb.svg" alt="Latest Announcements">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Latest Announcements</div>
                  <div class="sub font-size-13">Browse, filter and set alerts for announcements.</div>
                </div>
              </a>
            </li>
            
              <li style="padding: 16px;">
                <a href="/premium/?driver=top-nav-tools"
                   class="button button-primary text-align-center"
                   style="color: var(--white);
                          padding: 10px 24px">Upgrade to premium</a>
              </li>
            
          </ul>
        </div>
      </div>
    </div>

    <div class="show-from-tablet-landscape flex flex-gap-16"
         style="justify-content: flex-end;
                flex: 1 1 400px">
      <div class="search" id="desktop-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

      
        <div class="flex flex-gap-8" style="margin: 4px">
          <a class="button account"
             href="/login/?">
            <i class="icon-user-line blue-icon"></i>
            Login
          </a>
          <a class="button account button-secondary"
             href="/register/?">Get free account</a>
        </div>
      
    </div>
  </div>
</div>
</div>
        </div>
      </nav>
    

    
  <div class="bg-base sticky company-nav">
    <div class="flex flex-space-between container hide-from-tablet-landscape">
      <h1 class="h2 shrink-text" style="margin: 0.5em 0">Tata Consultancy Services Ltd</h1>
      <div class="flex flex-align-center gap-8">
        <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/3365/"
        data-title="Tata Consultancy Services Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

        
      </div>
    </div>

    <div class="sub-nav-holder">
      <div class="sub-nav">
        <a href="#top">
          <span class="shrink-text show-from-tablet-landscape">TCS</span>
          <span class="hide-from-tablet-landscape">Summary</span>
        </a>
        <a href="#chart">Chart</a>
        <a href="#analysis">Analysis</a>
        <a href="#peers">Peers</a>
        <a href="#quarters">Quarters</a>
        <a href="#profit-loss">Profit &amp; Loss</a>
        <a href="#balance-sheet">Balance Sheet</a>
        <a href="#cash-flow">Cash Flow</a>
        <a href="#ratios">Ratios</a>
        <a href="#shareholding">Investors</a>
        <a href="#documents">Documents</a>
        <div class="flex-grow flex flex-align-center gap-8 show-from-tablet-landscape">
          <div class="flex-grow"></div>
          <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/3365/"
        data-title="Tata Consultancy Services Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

          
        </div>
      </div>
    </div>
  </div>


    
      <main class="flex-grow container">
        



  






        
        <div class="breadcrumb hidden-if-empty"></div>
        

        
  <div data-company-id="3365"
       data-warehouse-id="6599230"
       
       data-consolidated="true"
       id="company-info"></div>

  <div class="card card-large" id="top">
    <div class="flex flex-space-between flex-gap-8">
      <div class="flex-row flex-wrap flex-align-center flex-grow"
           style="flex-basis: 300px">
        <h1 class="margin-0 show-from-tablet-landscape">Tata Consultancy Services Ltd</h1>
        <div class="ink-600 show-from-tablet-landscape" style="opacity: 0.5;">
          <i class="icon-dot"></i>
        </div>
        <div class="font-size-18 strong line-height-14">
          <div class="flex flex-align-center">
            <span>₹ 3,561</span>
            
              <span class="font-size-12 down margin-left-4">
                <i class="icon-circle-down"></i>
                -0.52%
              </span>
            
          </div>
          <div class="ink-600 font-size-11 font-weight-500">
            
              16 May
              
                - close price
              
            
          </div>
        </div>
      </div>

      <form method="post" class="flex margin-0" style="align-items: center">
        <input type="hidden" name="csrfmiddlewaretoken" value="6rFgSENj3kazIjjMdqIqfvUF5WUZLK3hO7WXEfiUA5yv4fSuZV6PhgwfANCFQkfv">
        <input type="hidden" name="next" value="/company/TCS/consolidated/">

        
          <button class="margin-right-8 plausible-event-name=Excel+Company plausible-event-user=unregistered"
                  formaction="/user/company/export/6599230/"
                  aria-label="Export to Excel">
            <i class="icon-download"></i>
            <span class="hide-on-mobile">Export to Excel</span>
          </button>
        

        <div class="flex dropdown-filter">
          
            <button class="button-primary "
                    formaction="/api/company/3365/add/">
              <i class="icon-plus"></i> Follow
            </button>
          

          
        </div>
      </form>
    </div>

    
    <div class="company-links show-from-tablet-landscape"
         style="font-weight: 500;
                margin-top: 8px">
      
        <a href="http://www.tcs.com"
           target="_blank"
           rel="noopener noreferrer">
          <i class="icon-link"></i>
          <span style="font-size: 1.8rem;">tcs.com</span>
        </a>
      

      
        <a href="https://www.bseindia.com/stock-share-price/tata-consultancy-services-ltd/TCS/532540/" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            BSE:
            532540
          </span>
        </a>
      

      
        <a href="https://www.nseindia.com/get-quotes/equity?symbol=TCS" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            NSE:
            TCS
          </span>
        </a>
      
    </div>
    

    <div class="company-info">
      
      <div class="company-profile">
        <div class="flex flex-column" style="flex: 1 1;">
          <div class="title">About</div>
          <div class="sub show-more-box about" style="flex-basis: 100px"><p>Tata Consultancy Services is the flagship company and a part of Tata group. It is an IT services, consulting and business solutions organization that has been partnering with many of the world's largest businesses in their transformation journeys for over 50 years. TCS offers a consulting-led, cognitive powered, integrated portfolio of business, technology and engineering services and solutions. <sup><a href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2019-20/ar/annual-report-2019-2020.pdf#page=3" target="_blank" rel="noopener noreferrer">[1]</a></sup></p></div>

          
            <div class="title">Key Points</div>
            <div class="sub commentary always-show-more-box">
              <p><strong>Revenue Breakup Q2FY24</strong><sup><a href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2023-24/q2/Presentations/Q2%202023-24%20Fact%20Sheet.pdf#page=8" target="_blank" rel="noopener noreferrer">[1]</a></sup><br>
BFSI : 32.6%<br>
Consumer Business : 15.9%<br>
Life Sciences &amp; Healthcare : 10.9%<br>
Technology &amp; Services : 8.6%<br>
Manufacturing : 8.5%<br>
Communication &amp; Media : 6.9%<br>
Energy, Resources and Utilities : 5.6%<br>
Regional Markets &amp; Others : 11%</p>
              <div class="show-more-button" style="height: 20px"></div>
            </div>

            <button class="a upper font-weight-500 letter-spacing plausible-event-name=Key+Insight plausible-event-user=unregistered"
                    style="font-size: 1.4rem"
                    type="button"
                    onclick="Modal.openInModal(event)"
                    data-title="Tata Consultancy Services Ltd"
                    data-url="/wiki/company/3365/commentary/v2/"
                    data-is-right-modal="true">
              Read More <i class="icon-right"></i>
            </button>
          
        </div>

        <div class="company-links hide-from-tablet-landscape margin-top-20"
             style="font-weight: 500">
          
            <a href="http://www.tcs.com"
               target="_blank"
               rel="noopener noreferrer">
              <i class="icon-link"></i>
              <span style="font-size: 1.6rem;">Website</span>
            </a>
          

          
            <a href="https://www.bseindia.com/stock-share-price/tata-consultancy-services-ltd/TCS/532540/" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">BSE</span>
            </a>
          

          
            <a href="https://www.nseindia.com/get-quotes/equity?symbol=TCS" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">
                NSE
                
              </span>
            </a>
          
        </div>
      </div>
      

      <div class="company-ratios">
        <ul id="top-ratios">
          


  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Market Cap
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">12,88,204</span>
        
          Cr.
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Current Price
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">3,561</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        High / Low
      
    </span>

    
      <span class="nowrap value">₹ <span class="number">4,592</span> / <span class="number">3,056</span></span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Stock P/E
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">26.6</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Book Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">262</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Dividend Yield
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">1.54</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROCE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">64.6</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">52.4</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Face Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">1.00</span>
        
      </span>
    
  </li>






        </ul>

        <div style="margin-top: 32px;">
          <label for="quick-ratio-search">Add ratio to table</label>

          <div class="flex-row flex-space-between flex-baseline flex-gap-16">
            <div class="dropdown-typeahead flex-grow" style="max-width: 480px;">
              <input id="quick-ratio-search"
                     type="text"
                     placeholder="eg. Promoter holding"
                     style="width: 100%">
            </div>

            
            <a href="/user/quick_ratios/?next=/company/TCS/consolidated/"
               class="smaller">
              <i class="icon-pencil"></i>
              <span class="upper font-weight-500 letter-spacing">Edit ratios</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  
    <section id="chart" class="card card-large" style="margin-bottom: 2.5rem">
      <div id="chart-menu"
     class="flex flex-space-between flex-wrap"
     style="margin-bottom: 16px">
  <div class="options" id="company-chart-days">
    <button name="days"
            value="30"
            onclick="CompanyChart.setActive(event)"
            type="button">1M</button>
    <button name="days"
            value="180"
            onclick="CompanyChart.setActive(event)"
            type="button">6M</button>
    <button name="days"
            value="365"
            onclick="CompanyChart.setActive(event)"
            type="button">1Yr</button>
    <button name="days"
            value="1095"
            onclick="CompanyChart.setActive(event)"
            type="button">3Yr</button>
    <button name="days"
            value="1825"
            onclick="CompanyChart.setActive(event)"
            type="button">5Yr</button>
    <button name="days"
            value="3652"
            onclick="CompanyChart.setActive(event)"
            type="button">10Yr</button>
    <button name="days"
            value="10000"
            onclick="CompanyChart.setActive(event)"
            type="button">Max</button>
  </div>

  <div class="flex margin-bottom-24">
    <div class="options margin-right-8"
         id="company-chart-metrics"
         style="margin-bottom: 0">
      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Price"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price-DMA50-DMA200-Volume"
                  type="button">Price</button>
        

      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PE"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price to Earning-Median PE-EPS"
                  type="button">PE Ratio</button>
        

      

        

      

        

      

        

      

        

      
      <button class="hidden"
              id="company-chart-metrics-more-active"
              onclick="CompanyChart.setActive(event)"
              name="metrics"
              value=""
              type="button">Hidden</button>
      <div class="dropdown-menu" id="metrics-dropdown">
        <button type="button"
                class="button-plain"
                onclick="Utils.toggleDropdown(event)">
          More <i class="icon-down"></i>
        </button>
        <ul class="dropdown-content" style="max-width: 160px; right: 0">
          

            

          

            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="GPM-OPM-NPM-Quarter Sales"
                        type="button">Sales &amp; Margin</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=EV-EBITDA"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="EV Multiple-Median EV Multiple-EBITDA"
                        type="button">EV / EBITDA</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PBV"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Price to book value-Median PBV-Book value"
                        type="button">Price to Book</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Market+Cap+to+Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Market Cap to Sales-Median Market Cap to Sales-Sales"
                        type="button">Market Cap / Sales</button>
              </li>
            

          
        </ul>
      </div>
    </div>
    
      <button type="button"
              class="button-small button-secondary flex flex-align-center plausible-event-name=Stock+Alert"
              style="border-color: var(--button-border);
                     border-radius: 3px"
              data-title="Alerts for Tata Consultancy Services Ltd"
              onclick="Modal.openInModal(event)"
              data-url="/alerts/stock-6599230/"
              aria-label="Add alerts for company">
        <i class="icon-bell"></i>
        
        <span class="margin-left-4 normal-case letter-spacing-0">Alerts</span>
      </button>
    
  </div>
</div>

<div class="flex no-select">
  <div class="hide-on-mobile chart-label">
    <div id="chart-label-left"></div>
  </div>

  <div id="chart-area" style="flex-grow: 1">
    <div id="chart-info" class="invisible">
      <div id="chart-crosshair"></div>
      <div id="chart-tooltip" class="font-size-14">
        <div id="chart-tooltip-meta" class="ink-700 font-size-12"></div>
        <div class="font-weight-500 font-size-15" id="chart-tooltip-title"></div>
        <div id="chart-tooltip-info"></div>
      </div>
    </div>
    <div id="canvas-chart-holder" style="height: 375px"></div>
  </div>

  <div class="hide-on-mobile chart-label">
    <div id="chart-label-right"></div>
  </div>
</div>
<div id="chart-legend"></div>

    </section>

    
    <section id="analysis" class="card card-large">
      <div class="flex flex-column-mobile flex-gap-32">
        <div class="pros" style="flex-basis: 50%;">
          <p class="title">Pros</p>
          <ul>
            <li>Company has a good return on equity (ROE) track record: 3 Years ROE 50.3%</li><li>Company has been maintaining a healthy dividend payout of 83.3%</li>
          </ul>
        </div>
        <div class="cons" style="flex-basis: 50%;">
          <p class="title">Cons</p>
          <ul>
            <li>Stock is trading at 13.6 times its book value</li><li>The company has delivered a poor sales growth of 10.2% over past five years.</li>
          </ul>
        </div>
      </div>
      <p class="sub font-size-14 margin-top-16 margin-bottom-0">
        <span style="vertical-align: sub;">*</span>
        <span class="font-weight-500">The pros and cons are machine generated.</span>
        <span class="has-tooltip">
          <i class="icon-info"></i>
          <span class="tooltip" style="width: 300px; left: -100px;">
            Pros / cons are based on a checklist to highlight important points. Please exercise caution and do your own analysis.
          </span>
        </span>
      </p>
    </section>

    <section id="peers" class="card card-large">
      <div class="flex flex-space-between" style="align-items: center;">
        <div>
          <h2>Peer comparison</h2>

          
            <p class="sub">
              Sector:
              <a href="/company/compare/00000034/"
                 target="_blank">IT - Software</a>
              <span style="margin: 16px"></span>
              Industry:
              <a href="/company/compare/00000034/00000027/"
                 target="_blank">Computers - Software - Large</a>
            </p>
          

          
            <p class="flex flex-wrap gap-8 flex-align-center line-height-1 sub"
               id="benchmarks">
              Part of
              
                <a href="/company/1001/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Sensex"
                   target="_blank">BSE Sensex</a>
              
                <a href="/company/NIFTY/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50"
                   target="_blank">Nifty 50</a>
              
                <a href="/company/1005/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+500"
                   target="_blank">BSE 500</a>
              
                <a href="/company/1006/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Information+Technology"
                   target="_blank">BSE Information Technology</a>
              
                <a href="/company/1002/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100"
                   target="_blank">BSE 100</a>
              
                <a href="/company/1003/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+200"
                   target="_blank">BSE 200</a>
              
                <a href="/company/1004/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dollex+200"
                   target="_blank">BSE Dollex 200</a>
              
                <a href="/company/1012/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Teck"
                   target="_blank">BSE Teck</a>
              
                <a href="/company/CNX500/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500"
                   target="_blank">Nifty 500</a>
              
                <a href="/company/CNXIT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+IT"
                   target="_blank">Nifty IT</a>
              
                <a href="/company/CNX100/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100"
                   target="_blank">Nifty 100</a>
              
                <a href="/company/CNXSERVICE/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Services+Sector"
                   target="_blank">Nifty Services Sector</a>
              
                <a href="/company/CNX200INDE/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+200"
                   target="_blank">Nifty 200</a>
              
                <a href="/company/CNXDIVIDEN/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Dividend+Opportunities+50"
                   target="_blank">Nifty Dividend Opportunities 50</a>
              
                <a href="/company/NV5020/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty50+Value+20"
                   target="_blank">Nifty50 Value 20</a>
              
                <a href="/company/NFT100EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Equal+Weight"
                   target="_blank">Nifty 100 Equal Weight</a>
              
                <a href="/company/1153/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Allcap"
                   target="_blank">BSE Allcap</a>
              
                <a href="/company/1155/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+LargeCap"
                   target="_blank">BSE LargeCap</a>
              
                <a href="/company/1125/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+SENSEX+50"
                   target="_blank">BSE SENSEX 50</a>
              
                <a href="/company/NIFTYQLY30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty100+Quality+30"
                   target="_blank">Nifty100 Quality 30</a>
              
                <a href="/company/1181/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+250+LargeMidCap+Index"
                   target="_blank">BSE 250 LargeMidCap Index</a>
              
                <a href="/company/1183/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dividend+Stability+Index"
                   target="_blank">BSE Dividend Stability Index</a>
              
                <a href="/company/1185/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Low+Volatility+Index"
                   target="_blank">BSE Low Volatility Index</a>
              
                <a href="/company/1187/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Quality+Index"
                   target="_blank">BSE Quality Index</a>
              
                <a href="/company/NFT50EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50+Equal+Weight"
                   target="_blank">Nifty 50 Equal Weight</a>
              
                <a href="/company/NFT100LV30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Low+Volatility+30"
                   target="_blank">Nifty 100 Low Volatility 30</a>
              
                <a href="/company/1130/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100+LargeCap+TMC+Index"
                   target="_blank">BSE 100 LargeCap TMC Index</a>
              
                <a href="/company/LMIDCAP250/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+LargeMidcap+250"
                   target="_blank">Nifty LargeMidcap 250</a>
              
                <a href="/company/NFT500MULT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500+Multicap+50:25:25"
                   target="_blank">Nifty 500 Multicap 50:25:25</a>
              
                <a href="/company/NQUALITY30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty200+Quality+30"
                   target="_blank">Nifty200 Quality 30</a>
              
                <a href="/company/NIFT100ESG/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty100+ESG"
                   target="_blank">Nifty100 ESG</a>
              
                <a href="/company/NFINDDIGIT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+India+Digital+Index"
                   target="_blank">Nifty India Digital Index</a>
              
                <a href="/company/NFTYTOTMKT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Total+Market"
                   target="_blank">Nifty Total Market</a>
              
                <a href="/company/ALPHALOWVO/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Alpha+Low-Volatility+30"
                   target="_blank">Nifty Alpha Low-Volatility 30</a>
              
                <a href="/company/NFTYTATA25/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Tata+Group+25%+Cap"
                   target="_blank">Nifty Tata Group 25% Cap</a>
              
              
                <button type="button"
                        class="a small font-weight-500 plausible-event-name=Show+More+Benchmarks"
                        onclick="Utils.toggleClass('#benchmarks', 'show-hidden')">show all</button>
              
            </p>
          
        </div>

        <div class="flex-reverse">
          <a href="/user/columns/?next=/company/TCS/consolidated/#peers"
             class="button button-small button-secondary">
            <i class="icon-cog"></i>
            <span class="hide-on-mobile">Edit</span>
            Columns
          </a>
        </div>
      </div>

      <div id="peers-table-placeholder">Loading peers table ...</div>

      <div class="flex-row flex-baseline">
        <label for="search-peer-comparison" style="padding-right: 1rem">Detailed Comparison with:</label>
        <div class="dropdown-typeahead">
          <input type="text"
                 id="search-peer-comparison"
                 placeholder="eg. Infosys"
                 class="small">
        </div>
      </div>
    </section>

    <section id="quarters" class="card card-large">
      
        


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Quarterly Results</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/TCS/#quarters"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    
      <button data-segment-button-close
              onclick="Segment.closeSegments('quarters')"
              class="button-small hidden">
        <i class="icon-cancel"></i>
        Close Segments
      </button>

      
        <button data-segment-button="1"
                onclick="Segment.showSegment('quarters', '1')"
                class="button-small button-secondary plausible-event-name=Segment+Results plausible-event-user=unregistered">
          <i class="icon-chart-pie"></i>
          Product Segments
        </button>
      
    

    
  </div>
</div>

<div class="responsive-holder hidden" data-segment-table></div>

<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="highlight-cell">
            Mar 2022
            
          </th>
        
          <th class="">
            Jun 2022
            
          </th>
        
          <th class="">
            Sep 2022
            
          </th>
        
          <th class="">
            Dec 2022
            
          </th>
        
          <th class="highlight-cell">
            Mar 2023
            
          </th>
        
          <th class="">
            Jun 2023
            
          </th>
        
          <th class="">
            Sep 2023
            
          </th>
        
          <th class="">
            Dec 2023
            
          </th>
        
          <th class="highlight-cell">
            Mar 2024
            
          </th>
        
          <th class="">
            Jun 2024
            
          </th>
        
          <th class="">
            Sep 2024
            
          </th>
        
          <th class="">
            Dec 2024
            
          </th>
        
          <th class="highlight-cell">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Sales', 'quarters', this)">
                  Sales&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">50,591</td>
            
              <td class="">52,758</td>
            
              <td class="">55,309</td>
            
              <td class="">58,229</td>
            
              <td class="highlight-cell">59,162</td>
            
              <td class="">59,381</td>
            
              <td class="">59,692</td>
            
              <td class="">60,583</td>
            
              <td class="highlight-cell">61,237</td>
            
              <td class="">62,613</td>
            
              <td class="">64,259</td>
            
              <td class="">63,973</td>
            
              <td class="highlight-cell">64,479</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'quarters', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">36,746</td>
            
              <td class="">39,342</td>
            
              <td class="">40,793</td>
            
              <td class="">42,676</td>
            
              <td class="highlight-cell">43,388</td>
            
              <td class="">44,383</td>
            
              <td class="">43,946</td>
            
              <td class="">44,195</td>
            
              <td class="highlight-cell">44,073</td>
            
              <td class="">45,951</td>
            
              <td class="">47,528</td>
            
              <td class="">46,939</td>
            
              <td class="highlight-cell">47,499</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Operating Profit
              
            </td>
            
              <td class="highlight-cell">13,845</td>
            
              <td class="">13,416</td>
            
              <td class="">14,516</td>
            
              <td class="">15,553</td>
            
              <td class="highlight-cell">15,774</td>
            
              <td class="">14,998</td>
            
              <td class="">15,746</td>
            
              <td class="">16,388</td>
            
              <td class="highlight-cell">17,164</td>
            
              <td class="">16,662</td>
            
              <td class="">16,731</td>
            
              <td class="">17,034</td>
            
              <td class="highlight-cell">16,980</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                OPM %
              
            </td>
            
              <td class="highlight-cell">27%</td>
            
              <td class="">25%</td>
            
              <td class="">26%</td>
            
              <td class="">27%</td>
            
              <td class="highlight-cell">27%</td>
            
              <td class="">25%</td>
            
              <td class="">26%</td>
            
              <td class="">27%</td>
            
              <td class="highlight-cell">28%</td>
            
              <td class="">27%</td>
            
              <td class="">26%</td>
            
              <td class="">27%</td>
            
              <td class="highlight-cell">26%</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'quarters', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">981</td>
            
              <td class="">789</td>
            
              <td class="">965</td>
            
              <td class="">520</td>
            
              <td class="highlight-cell">1,175</td>
            
              <td class="">1,397</td>
            
              <td class="">1,006</td>
            
              <td class="">-96</td>
            
              <td class="highlight-cell">1,157</td>
            
              <td class="">962</td>
            
              <td class="">729</td>
            
              <td class="">1,243</td>
            
              <td class="highlight-cell">1,028</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="highlight-cell">245</td>
            
              <td class="">199</td>
            
              <td class="">148</td>
            
              <td class="">160</td>
            
              <td class="highlight-cell">272</td>
            
              <td class="">163</td>
            
              <td class="">159</td>
            
              <td class="">230</td>
            
              <td class="highlight-cell">226</td>
            
              <td class="">173</td>
            
              <td class="">162</td>
            
              <td class="">234</td>
            
              <td class="highlight-cell">227</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="highlight-cell">1,217</td>
            
              <td class="">1,230</td>
            
              <td class="">1,237</td>
            
              <td class="">1,269</td>
            
              <td class="highlight-cell">1,286</td>
            
              <td class="">1,243</td>
            
              <td class="">1,263</td>
            
              <td class="">1,233</td>
            
              <td class="highlight-cell">1,246</td>
            
              <td class="">1,220</td>
            
              <td class="">1,266</td>
            
              <td class="">1,377</td>
            
              <td class="highlight-cell">1,379</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="highlight-cell">13,364</td>
            
              <td class="">12,776</td>
            
              <td class="">14,096</td>
            
              <td class="">14,644</td>
            
              <td class="highlight-cell">15,391</td>
            
              <td class="">14,989</td>
            
              <td class="">15,330</td>
            
              <td class="">14,829</td>
            
              <td class="highlight-cell">16,849</td>
            
              <td class="">16,231</td>
            
              <td class="">16,032</td>
            
              <td class="">16,666</td>
            
              <td class="highlight-cell">16,402</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="highlight-cell">25%</td>
            
              <td class="">25%</td>
            
              <td class="">26%</td>
            
              <td class="">26%</td>
            
              <td class="highlight-cell">26%</td>
            
              <td class="">26%</td>
            
              <td class="">26%</td>
            
              <td class="">25%</td>
            
              <td class="highlight-cell">26%</td>
            
              <td class="">25%</td>
            
              <td class="">25%</td>
            
              <td class="">25%</td>
            
              <td class="highlight-cell">25%</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'quarters', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">9,959</td>
            
              <td class="">9,519</td>
            
              <td class="">10,465</td>
            
              <td class="">10,883</td>
            
              <td class="highlight-cell">11,436</td>
            
              <td class="">11,120</td>
            
              <td class="">11,380</td>
            
              <td class="">11,097</td>
            
              <td class="highlight-cell">12,502</td>
            
              <td class="">12,105</td>
            
              <td class="">11,955</td>
            
              <td class="">12,444</td>
            
              <td class="highlight-cell">12,293</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="highlight-cell">27.13</td>
            
              <td class="">25.90</td>
            
              <td class="">28.51</td>
            
              <td class="">29.64</td>
            
              <td class="highlight-cell">31.13</td>
            
              <td class="">30.26</td>
            
              <td class="">31.00</td>
            
              <td class="">30.56</td>
            
              <td class="highlight-cell">34.37</td>
            
              <td class="">33.28</td>
            
              <td class="">32.92</td>
            
              <td class="">34.22</td>
            
              <td class="highlight-cell">33.79</td>
            
          </tr>
        
      

      
        <tr class="font-size-14 ink-600">
          <td class="text ink-600">Raw PDF</td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/3365/3/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3365/6/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3365/9/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3365/12/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/3365/3/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3365/6/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3365/9/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3365/12/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/3365/3/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3365/6/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3365/9/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/3365/12/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/3365/3/2025/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
        </tr>
      
    </tbody>
  </table>
</div>

      

      
    </section>

    <section id="profit-loss" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Profit & Loss</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/TCS/#profit-loss"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    
      <button data-url="/results/rpt/3365/consolidated/"
              data-title="Related Party Transactions"
              onclick="Modal.openInModal(event)"
              class="button-small button-secondary plausible-event-name=Related+party plausible-event-user=unregistered">
        <i class="small icon-users"></i>
        Related Party
      </button>
    

    
      <button data-segment-button-close
              onclick="Segment.closeSegments('profit-loss')"
              class="button-small hidden">
        <i class="icon-cancel"></i>
        Close Segments
      </button>

      
        <button data-segment-button="1"
                onclick="Segment.showSegment('profit-loss', '1')"
                class="button-small button-secondary plausible-event-name=Segment+Results plausible-event-user=unregistered">
          <i class="icon-chart-pie"></i>
          Product Segments
        </button>
      
    

    
  </div>
</div>

<div class="responsive-holder hidden" data-segment-table></div>

<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Sales', 'profit-loss', this)">
                  Sales&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">81,809</td>
            
              <td class="">94,648</td>
            
              <td class="">108,646</td>
            
              <td class="">117,966</td>
            
              <td class="">123,104</td>
            
              <td class="">146,463</td>
            
              <td class="">156,949</td>
            
              <td class="">164,177</td>
            
              <td class="">191,754</td>
            
              <td class="">225,458</td>
            
              <td class="">240,893</td>
            
              <td class="">255,324</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'profit-loss', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">56,657</td>
            
              <td class="">70,167</td>
            
              <td class="">77,969</td>
            
              <td class="">85,655</td>
            
              <td class="">90,588</td>
            
              <td class="">106,957</td>
            
              <td class="">114,840</td>
            
              <td class="">117,631</td>
            
              <td class="">138,697</td>
            
              <td class="">166,199</td>
            
              <td class="">176,597</td>
            
              <td class="">187,917</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Operating Profit
              
            </td>
            
              <td class="">25,153</td>
            
              <td class="">24,482</td>
            
              <td class="">30,677</td>
            
              <td class="">32,311</td>
            
              <td class="">32,516</td>
            
              <td class="">39,506</td>
            
              <td class="">42,109</td>
            
              <td class="">46,546</td>
            
              <td class="">53,057</td>
            
              <td class="">59,259</td>
            
              <td class="">64,296</td>
            
              <td class="">67,407</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                OPM %
              
            </td>
            
              <td class="">31%</td>
            
              <td class="">26%</td>
            
              <td class="">28%</td>
            
              <td class="">27%</td>
            
              <td class="">26%</td>
            
              <td class="">27%</td>
            
              <td class="">27%</td>
            
              <td class="">28%</td>
            
              <td class="">28%</td>
            
              <td class="">26%</td>
            
              <td class="">27%</td>
            
              <td class="">26%</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'profit-loss', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">1,637</td>
            
              <td class="">3,720</td>
            
              <td class="">3,084</td>
            
              <td class="">4,221</td>
            
              <td class="">3,642</td>
            
              <td class="">4,311</td>
            
              <td class="">4,592</td>
            
              <td class="">1,916</td>
            
              <td class="">4,018</td>
            
              <td class="">3,449</td>
            
              <td class="">3,464</td>
            
              <td class="">3,962</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="">39</td>
            
              <td class="">104</td>
            
              <td class="">33</td>
            
              <td class="">32</td>
            
              <td class="">52</td>
            
              <td class="">198</td>
            
              <td class="">924</td>
            
              <td class="">637</td>
            
              <td class="">784</td>
            
              <td class="">779</td>
            
              <td class="">778</td>
            
              <td class="">796</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="">1,349</td>
            
              <td class="">1,799</td>
            
              <td class="">1,888</td>
            
              <td class="">1,987</td>
            
              <td class="">2,014</td>
            
              <td class="">2,056</td>
            
              <td class="">3,529</td>
            
              <td class="">4,065</td>
            
              <td class="">4,604</td>
            
              <td class="">5,022</td>
            
              <td class="">4,985</td>
            
              <td class="">5,242</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="">25,402</td>
            
              <td class="">26,298</td>
            
              <td class="">31,840</td>
            
              <td class="">34,513</td>
            
              <td class="">34,092</td>
            
              <td class="">41,563</td>
            
              <td class="">42,248</td>
            
              <td class="">43,760</td>
            
              <td class="">51,687</td>
            
              <td class="">56,907</td>
            
              <td class="">61,997</td>
            
              <td class="">65,331</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="">24%</td>
            
              <td class="">24%</td>
            
              <td class="">24%</td>
            
              <td class="">24%</td>
            
              <td class="">24%</td>
            
              <td class="">24%</td>
            
              <td class="">23%</td>
            
              <td class="">26%</td>
            
              <td class="">26%</td>
            
              <td class="">26%</td>
            
              <td class="">26%</td>
            
              <td class="">25%</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'profit-loss', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">19,332</td>
            
              <td class="">20,060</td>
            
              <td class="">24,338</td>
            
              <td class="">26,357</td>
            
              <td class="">25,880</td>
            
              <td class="">31,562</td>
            
              <td class="">32,447</td>
            
              <td class="">32,562</td>
            
              <td class="">38,449</td>
            
              <td class="">42,303</td>
            
              <td class="">46,099</td>
            
              <td class="">48,797</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="">48.92</td>
            
              <td class="">50.68</td>
            
              <td class="">61.58</td>
            
              <td class="">66.71</td>
            
              <td class="">67.46</td>
            
              <td class="">83.87</td>
            
              <td class="">86.19</td>
            
              <td class="">87.67</td>
            
              <td class="">104.75</td>
            
              <td class="">115.19</td>
            
              <td class="">126.88</td>
            
              <td class="">134.20</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Dividend Payout %
              
            </td>
            
              <td class="">33%</td>
            
              <td class="">78%</td>
            
              <td class="">35%</td>
            
              <td class="">35%</td>
            
              <td class="">37%</td>
            
              <td class="">36%</td>
            
              <td class="">85%</td>
            
              <td class="">43%</td>
            
              <td class="">41%</td>
            
              <td class="">100%</td>
            
              <td class="">58%</td>
            
              <td class="">92%</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>


      <div style="display: grid;
                  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                  gap: 2%">
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Sales Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>10%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>10%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>10%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>6%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Profit Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>10%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>8%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>8%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>4%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Stock Price CAGR</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>11%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>13%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>1%</td>
          </tr>
          <tr>
            <td>1 Year:</td>
            <td>-9%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Return on Equity</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>41%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>47%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>50%</td>
          </tr>
          <tr>
            <td>Last Year:</td>
            <td>52%</td>
          </tr>
        </table>
      </div>
    </section>

    <section id="balance-sheet" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Balance Sheet</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/TCS/#balance-sheet"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
      <button type="button"
              class="button-small button-secondary plausible-event-name=Corporate+Actions plausible-event-user=unregistered"
              onclick="Modal.openInModal(event)"
              data-title="Corporate actions"
              data-url="/company/actions/3365/">Corporate actions</button>
    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                Equity Capital
              
            </td>
            
              <td class="">196</td>
            
              <td class="">196</td>
            
              <td class="">197</td>
            
              <td class="">197</td>
            
              <td class="">191</td>
            
              <td class="">375</td>
            
              <td class="">375</td>
            
              <td class="">370</td>
            
              <td class="">366</td>
            
              <td class="">366</td>
            
              <td class="">362</td>
            
              <td class="">362</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Reserves
              
            </td>
            
              <td class="">48,999</td>
            
              <td class="">50,439</td>
            
              <td class="">70,875</td>
            
              <td class="">86,017</td>
            
              <td class="">84,937</td>
            
              <td class="">89,071</td>
            
              <td class="">83,751</td>
            
              <td class="">86,063</td>
            
              <td class="">88,773</td>
            
              <td class="">90,058</td>
            
              <td class="">90,127</td>
            
              <td class="">94,394</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Borrowings', 'balance-sheet', this)">
                  Borrowings&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">297</td>
            
              <td class="">358</td>
            
              <td class="">245</td>
            
              <td class="">289</td>
            
              <td class="">247</td>
            
              <td class="">62</td>
            
              <td class="">8,174</td>
            
              <td class="">7,795</td>
            
              <td class="">7,818</td>
            
              <td class="">7,688</td>
            
              <td class="">8,021</td>
            
              <td class="">9,392</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Liabilities', 'balance-sheet', this)">
                  Other Liabilities&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">17,337</td>
            
              <td class="">22,325</td>
            
              <td class="">16,974</td>
            
              <td class="">15,830</td>
            
              <td class="">19,751</td>
            
              <td class="">24,393</td>
            
              <td class="">27,827</td>
            
              <td class="">35,764</td>
            
              <td class="">43,967</td>
            
              <td class="">44,747</td>
            
              <td class="">46,962</td>
            
              <td class="">54,501</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Total Liabilities
              
            </td>
            
              <td class="">66,829</td>
            
              <td class="">73,318</td>
            
              <td class="">88,291</td>
            
              <td class="">102,333</td>
            
              <td class="">105,126</td>
            
              <td class="">113,901</td>
            
              <td class="">120,127</td>
            
              <td class="">129,992</td>
            
              <td class="">140,924</td>
            
              <td class="">142,859</td>
            
              <td class="">145,472</td>
            
              <td class="">158,649</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Fixed Assets', 'balance-sheet', this)">
                  Fixed Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">9,544</td>
            
              <td class="">11,638</td>
            
              <td class="">11,774</td>
            
              <td class="">11,701</td>
            
              <td class="">11,973</td>
            
              <td class="">12,290</td>
            
              <td class="">20,928</td>
            
              <td class="">21,021</td>
            
              <td class="">21,298</td>
            
              <td class="">20,515</td>
            
              <td class="">19,604</td>
            
              <td class="">23,053</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                CWIP
              
            </td>
            
              <td class="">3,168</td>
            
              <td class="">2,766</td>
            
              <td class="">1,670</td>
            
              <td class="">1,541</td>
            
              <td class="">1,278</td>
            
              <td class="">963</td>
            
              <td class="">906</td>
            
              <td class="">926</td>
            
              <td class="">1,205</td>
            
              <td class="">1,234</td>
            
              <td class="">1,564</td>
            
              <td class="">1,546</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Investments
              
            </td>
            
              <td class="">3,434</td>
            
              <td class="">1,662</td>
            
              <td class="">22,822</td>
            
              <td class="">41,980</td>
            
              <td class="">36,008</td>
            
              <td class="">29,330</td>
            
              <td class="">26,356</td>
            
              <td class="">29,373</td>
            
              <td class="">30,485</td>
            
              <td class="">37,163</td>
            
              <td class="">31,762</td>
            
              <td class="">30,964</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Assets', 'balance-sheet', this)">
                  Other Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">50,682</td>
            
              <td class="">57,252</td>
            
              <td class="">52,025</td>
            
              <td class="">47,111</td>
            
              <td class="">55,867</td>
            
              <td class="">71,318</td>
            
              <td class="">71,937</td>
            
              <td class="">78,672</td>
            
              <td class="">87,936</td>
            
              <td class="">83,947</td>
            
              <td class="">92,542</td>
            
              <td class="">103,086</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Total Assets
              
            </td>
            
              <td class="">66,829</td>
            
              <td class="">73,318</td>
            
              <td class="">88,291</td>
            
              <td class="">102,333</td>
            
              <td class="">105,126</td>
            
              <td class="">113,901</td>
            
              <td class="">120,127</td>
            
              <td class="">129,992</td>
            
              <td class="">140,924</td>
            
              <td class="">142,859</td>
            
              <td class="">145,472</td>
            
              <td class="">158,649</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="cash-flow" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Cash Flows</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/TCS/#cash-flow"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Operating Activity', 'cash-flow', this)">
                  Cash from Operating Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">14,751</td>
            
              <td class="">19,369</td>
            
              <td class="">19,109</td>
            
              <td class="">25,223</td>
            
              <td class="">25,067</td>
            
              <td class="">28,593</td>
            
              <td class="">32,369</td>
            
              <td class="">38,802</td>
            
              <td class="">39,949</td>
            
              <td class="">41,965</td>
            
              <td class="">44,338</td>
            
              <td class="">48,908</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Investing Activity', 'cash-flow', this)">
                  Cash from Investing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">-9,452</td>
            
              <td class="">-1,807</td>
            
              <td class="">-5,010</td>
            
              <td class="">-16,895</td>
            
              <td class="">3,104</td>
            
              <td class="">1,645</td>
            
              <td class="">8,968</td>
            
              <td class="">-7,956</td>
            
              <td class="">-738</td>
            
              <td class="">548</td>
            
              <td class="">6,091</td>
            
              <td class="">-2,144</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Financing Activity', 'cash-flow', this)">
                  Cash from Financing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">-5,673</td>
            
              <td class="">-17,168</td>
            
              <td class="">-9,666</td>
            
              <td class="">-11,026</td>
            
              <td class="">-26,885</td>
            
              <td class="">-27,897</td>
            
              <td class="">-39,915</td>
            
              <td class="">-32,634</td>
            
              <td class="">-33,581</td>
            
              <td class="">-47,878</td>
            
              <td class="">-48,536</td>
            
              <td class="">-47,438</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Net Cash Flow
              
            </td>
            
              <td class="">-374</td>
            
              <td class="">394</td>
            
              <td class="">4,433</td>
            
              <td class="">-2,698</td>
            
              <td class="">1,286</td>
            
              <td class="">2,341</td>
            
              <td class="">1,422</td>
            
              <td class="">-1,788</td>
            
              <td class="">5,630</td>
            
              <td class="">-5,365</td>
            
              <td class="">1,893</td>
            
              <td class="">-674</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="ratios" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Ratios</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/TCS/#ratios"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                Debtor Days
              
            </td>
            
              <td class="">81</td>
            
              <td class="">79</td>
            
              <td class="">81</td>
            
              <td class="">70</td>
            
              <td class="">74</td>
            
              <td class="">68</td>
            
              <td class="">71</td>
            
              <td class="">67</td>
            
              <td class="">80</td>
            
              <td class="">81</td>
            
              <td class="">81</td>
            
              <td class="">84</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Inventory Days
              
            </td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Days Payable
              
            </td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Cash Conversion Cycle
              
            </td>
            
              <td class="">81</td>
            
              <td class="">79</td>
            
              <td class="">81</td>
            
              <td class="">70</td>
            
              <td class="">74</td>
            
              <td class="">68</td>
            
              <td class="">71</td>
            
              <td class="">67</td>
            
              <td class="">80</td>
            
              <td class="">81</td>
            
              <td class="">81</td>
            
              <td class="">84</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Working Capital Days
              
            </td>
            
              <td class="">52</td>
            
              <td class="">33</td>
            
              <td class="">62</td>
            
              <td class="">63</td>
            
              <td class="">61</td>
            
              <td class="">70</td>
            
              <td class="">67</td>
            
              <td class="">62</td>
            
              <td class="">36</td>
            
              <td class="">33</td>
            
              <td class="">36</td>
            
              <td class="">36</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                ROCE %
              
            </td>
            
              <td class="">56%</td>
            
              <td class="">50%</td>
            
              <td class="">51%</td>
            
              <td class="">44%</td>
            
              <td class="">39%</td>
            
              <td class="">47%</td>
            
              <td class="">47%</td>
            
              <td class="">49%</td>
            
              <td class="">54%</td>
            
              <td class="">59%</td>
            
              <td class="">64%</td>
            
              <td class="">65%</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="shareholding" class="card card-large">
      <div class="flex flex-space-between flex-wrap margin-bottom-8 flex-align-center">
  <div>
    <h2 class="margin-0">Shareholding Pattern</h2>
    <p class="sub">Numbers in percentages</p>
  </div>
  <div class="flex">
    <div class="options small margin-0">
      <button
        onclick="Utils.setActiveTab(event)"
        class="active"
        data-tab-id="quarterly-shp"
        type="button">
        Quarterly
      </button>
      <button
        onclick="Utils.setActiveTab(event)"
        data-tab-id="yearly-shp"
        type="button">
        Yearly
      </button>
    </div>
    
    <div class="text-align-center margin-left-12">
      <button
        type="button"
        class="button-secondary button-small"
        onclick="Modal.openInModal(event)"
        data-title="TCS trades"
        data-url="/trades/company-3365/"
      >
        Trades
      </button>
      
    </div>
    
  </div>
</div>

<div id="quarterly-shp">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Jun 2022</th><th>Sep 2022</th><th>Dec 2022</th><th>Mar 2023</th><th>Jun 2023</th><th>Sep 2023</th><th>Dec 2023</th><th>Mar 2024</th><th>Jun 2024</th><th>Sep 2024</th><th>Dec 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=promoters plausible-event-period=quarterly"
                      onclick="Company.showShareholders('promoters', 'quarterly', this)">
                Promoters&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>72.30%</td>
            
              <td>72.30%</td>
            
              <td>72.30%</td>
            
              <td>72.30%</td>
            
              <td>72.30%</td>
            
              <td>72.30%</td>
            
              <td>72.41%</td>
            
              <td>71.77%</td>
            
              <td>71.77%</td>
            
              <td>71.77%</td>
            
              <td>71.77%</td>
            
              <td>71.77%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=foreign_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('foreign_institutions', 'quarterly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>13.50%</td>
            
              <td>13.05%</td>
            
              <td>12.94%</td>
            
              <td>12.72%</td>
            
              <td>12.46%</td>
            
              <td>12.47%</td>
            
              <td>12.46%</td>
            
              <td>12.70%</td>
            
              <td>12.35%</td>
            
              <td>12.66%</td>
            
              <td>12.68%</td>
            
              <td>12.04%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=domestic_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('domestic_institutions', 'quarterly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>8.36%</td>
            
              <td>8.53%</td>
            
              <td>9.25%</td>
            
              <td>9.58%</td>
            
              <td>9.80%</td>
            
              <td>10.01%</td>
            
              <td>10.03%</td>
            
              <td>10.61%</td>
            
              <td>11.00%</td>
            
              <td>10.86%</td>
            
              <td>10.86%</td>
            
              <td>11.49%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=government plausible-event-period=quarterly"
                      onclick="Company.showShareholders('government', 'quarterly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.05%</td>
            
              <td>0.05%</td>
            
              <td>0.05%</td>
            
              <td>0.05%</td>
            
              <td>0.05%</td>
            
              <td>0.05%</td>
            
              <td>0.05%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=public plausible-event-period=quarterly"
                      onclick="Company.showShareholders('public', 'quarterly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>5.80%</td>
            
              <td>6.06%</td>
            
              <td>5.45%</td>
            
              <td>5.36%</td>
            
              <td>5.38%</td>
            
              <td>5.17%</td>
            
              <td>5.03%</td>
            
              <td>4.86%</td>
            
              <td>4.82%</td>
            
              <td>4.66%</td>
            
              <td>4.63%</td>
            
              <td>4.63%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>25,02,385</td><td>27,03,966</td><td>25,56,970</td><td>24,99,384</td><td>25,02,835</td><td>23,67,003</td><td>23,36,016</td><td>22,03,209</td><td>21,81,391</td><td>20,93,962</td><td>20,75,117</td><td>21,15,093</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>

<div id="yearly-shp" class="hidden">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Mar 2017</th><th>Mar 2018</th><th>Mar 2019</th><th>Mar 2020</th><th>Mar 2021</th><th>Mar 2022</th><th>Mar 2023</th><th>Mar 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=promoters plausible-event-period=yearly"
                      onclick="Company.showShareholders('promoters', 'yearly', this)">
                Promoters&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>73.31%</td>
            
              <td>71.92%</td>
            
              <td>72.05%</td>
            
              <td>72.05%</td>
            
              <td>72.19%</td>
            
              <td>72.30%</td>
            
              <td>72.30%</td>
            
              <td>71.77%</td>
            
              <td>71.77%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=foreign_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('foreign_institutions', 'yearly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>16.90%</td>
            
              <td>16.88%</td>
            
              <td>15.80%</td>
            
              <td>15.74%</td>
            
              <td>15.62%</td>
            
              <td>14.15%</td>
            
              <td>12.72%</td>
            
              <td>12.70%</td>
            
              <td>12.04%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=domestic_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('domestic_institutions', 'yearly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>5.36%</td>
            
              <td>6.93%</td>
            
              <td>7.78%</td>
            
              <td>8.00%</td>
            
              <td>7.83%</td>
            
              <td>7.81%</td>
            
              <td>9.58%</td>
            
              <td>10.61%</td>
            
              <td>11.49%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=government plausible-event-period=yearly"
                      onclick="Company.showShareholders('government', 'yearly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.05%</td>
            
              <td>0.05%</td>
            
              <td>0.05%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
              <td>0.04%</td>
            
              <td>0.05%</td>
            
              <td>0.06%</td>
            
              <td>0.06%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=public plausible-event-period=yearly"
                      onclick="Company.showShareholders('public', 'yearly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>4.38%</td>
            
              <td>4.22%</td>
            
              <td>4.32%</td>
            
              <td>4.14%</td>
            
              <td>4.31%</td>
            
              <td>5.70%</td>
            
              <td>5.36%</td>
            
              <td>4.86%</td>
            
              <td>4.63%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>6,37,799</td><td>5,50,651</td><td>7,23,894</td><td>8,58,825</td><td>10,58,901</td><td>24,74,310</td><td>24,99,384</td><td>22,03,209</td><td>21,15,093</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>


<p class="sub small">
  * The classifications might have changed from Sep'2022 onwards.
  <span class="has-tooltip">
    <i class="icon-info"></i>
    <span class="tooltip" style="width: 300px">
      The new XBRL format added more details from Sep'22 onwards.
      <br>
      <br>
      Classifications such as banks and foreign portfolio investors were not available earlier. The sudden changes in FII or DII can be because of these changes.
      <br>
      <br>
      Click on the line-items to see the names of individual entities.
    </span>
  </span>
</p>


    </section>

    <section id="documents" class="card card-large">
      <div class="flex flex-space-between margin-bottom-16">
        <h2 class="margin-0">Documents</h2>
      </div>

      <div class="flex-row flex-gap-small">
        
          <div class="documents flex-column" style="flex: 2 1 250px;">
            <h3 class="margin-bottom-8">Announcements</h3>

            <div class="show-more-box" style="flex-basis: 300px">
              <div id="company-announcements-tab">
                <div class="flex">
  <div class="options small" style="margin-bottom: 0">
    <button
      type="button"
      class="active"
      
      onclick="Utils.ajaxLoad(event, '/announcements/recent/3365/')"
      data-swap="#company-announcements-tab"
    >
      Recent
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/important/3365/')"
      data-swap="#company-announcements-tab"
    >
      Important
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/search/3365/')"
      data-swap="#company-announcements-tab"
    >
      Search
    </button>
    <a class="button" href="https://www.bseindia.com/stock-share-price/tata-consultancy-services-ltd/tcs/532540/corp-announcements/" target="_blank" rel="noopener noreferrer">
      All
      <i class="icon-link-ext smaller"></i>
    </a>
  </div>
</div>


  
  <ul class="list-links">
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=2b25e0f3-d9f9-4979-80ad-9cb7d731d11d.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Press Release-TCS Recognized As One Of The World&#x27;&#x27;s Top 50 Brands By Kantar Brandz; Brand Value Soars 28% Yoy To $57.3Bn

          
            <div class="ink-600 smaller">1d - TCS ranked #45 globally; brand value up 28% to $57.3B in Kantar BrandZ 2025 report.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=6435d1cc-bc36-4193-8070-e3763c63c3a0.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Press Release-TCS Partners With Khan Bank To Future-Proof Its Operations, Enhance Customer Experience And Propel Innovation Leveraging AI And ML

          
            <div class="ink-600 smaller">2d - TCS partners with Khan Bank to deploy BaNCS platform, enhancing digital banking for 2.9M customers.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=94bf6bc9-e843-49ff-8435-a31472eefe09.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Press Release - TCS Partners With Dhofar Insurance Company To Transform Its Core Insurance Platform

          
            <div class="ink-600 smaller">12 May - TCS partners with Dhofar Insurance to deploy TCS BaNCS™, transforming insurance platform and customer experience.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=5b564e8c-c02d-4fd5-84c8-1b7eb19136cc.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Newspaper Publication

          
            <div class="ink-600 smaller">8 May - Reminder Letter to shareholders - Unclaimed Dividend (First Interim Dividend 2018-19)</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=e1d57fee-b406-4ba1-8b45-2fdbd414e831.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Press Release - TCS Launches Genai And Agentic AI-Powered TCS Mastercraft TM To Help Enterprises Swiftly Modernize Legacy Systems

          
            <div class="ink-600 smaller">6 May - TCS launches GenAI-powered MasterCraft™, cutting modernization costs 70%, doubling speed, aiding major clients&#x27; legacy system upgrades.</div>
          
        </a>
      </li>
    
  </ul>




              </div>
            </div>
          </div>
        

        <div class="documents annual-reports flex-column"
             style="flex: 1 1 200px;
                    max-width: 210px">
          <h3>Annual reports</h3>
          <div class="show-more-box">
            
<ul class="list-links">

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d57e49b6-fd89-4fa8-b591-4f590100db1e.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2024
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=\3837c1b7-9308-436d-9636-1d6856edf74e.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2023
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/72996532540_17_06_22.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2022
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/68468532540_25_05_21.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2021
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/5325400320.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2020
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/5325400319.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2019
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/5325400318.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2018
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/5325400317.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2017
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/5325400316.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2016
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/5325400315.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2015
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/5325400314.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2014
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://archives.nseindia.com/annual_reports/AR_49_TCS_2012_2013_30052013104153.zip" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2013
      <div class="ink-600 smaller">
        from nse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/5325400313.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2013
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/5325400312.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2012
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/532540/5325400311.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2011
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

</ul>


          </div>
          
        </div>

        
          <div class="documents credit-ratings flex-column"
               style="flex: 1 1 200px;
                      max-width: 220px">
            <h3>Credit ratings</h3>
            <div class="show-more-box">
              
<ul class="list-links">

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=129826" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        10 Sep 2024 from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=121087" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        24 Jul 2023 from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=112038" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        30 May 2022 from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=106448" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        20 Sep 2021 from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=102320" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        30 Mar 2021 from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=93380" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        24 Mar 2020 from icra
      </div>
    </a>
  </li>

</ul>


            </div>
          </div>
        

        
          <div class="documents concalls flex-column"
               style="flex: 2 1 250px;
                      max-width: fit-content">
            <div class="flex flex-space-between flex-align-center margin-bottom-8">
              <h3 class="margin-0">Concalls</h3>
              <button class="a font-size-12 font-weight-500 sub"
                      onclick="Modal.openInModal(event)"
                      data-url="/concalls/add-3365/"
                      data-title="TCS - Add Link">
                <i class="icon-flag"></i>
                <span class="underline-link">Add Missing</span>
              </button>
            </div>
            <div class="show-more-box"><ul class="list-links">

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2025
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d3f70d49-123f-40d6-a0a9-b584546c8c63.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22993200/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2025"
    >
      Notes
    </button>
    

    
    <a href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2024-25/q4/Presentations/Q4%202024-25%20Fact%20Sheet.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.tcs.com/investor-relations/financial-statements#type=overlay&amp;page=/discover-tcs/investor-relations/overlays/management-commentary-overlays/fy25-q4-management-commentry-audio-overlay.html" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2025
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=5f89d612-0771-4c26-a565-546c1d46ba9c.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22971846/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2025"
    >
      Notes
    </button>
    

    
    <a href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2024-25/q3/Presentations/Q3%202024-25%20Fact%20Sheet.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.youtube.com/watch?v=G_-sWzs0pag" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=a7066eb7-d5d8-4d9e-87fd-ed222315999d.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22949370/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2024-25/q2/Presentations/Q2%202024-25%20Fact%20Sheet.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.tcs.com/investor-relations/financial-statements#type=overlay&amp;page=/discover-tcs/investor-relations/overlays/management-commentary-overlays/fy25-q2-management-commentry-audio-overlay.html" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=0a723c36-afac-4687-af0d-c01fd45c8815.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22929148/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/xml-data/corpfiling/AttachLive/8dd38adb-a021-420f-bff8-1b9f0fe1351b.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.tcs.com/investor-relations/financial-statements#type=overlay&amp;page=/discover-tcs/investor-relations/overlays/management-commentary-overlays/fy25-q1-management-commentry-audio-overlay.html" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2024-25/q1/Presentations/Q1%202024-25%20Fact%20Sheet.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=5c6ff602-30d9-490a-b147-1d824b5b6665.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/21784345/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2023-24/q4/Presentations/Q4%202023-24%20Fact%20Sheet.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.tcs.com/investor-relations/financial-statements?n=t#type=overlay&amp;page=/discover-tcs/investor-relations/overlays/management-commentary-overlays/fy24-q4-management-commentry-audio-overlay.html" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=215a0356-bb8c-4b2e-8c13-4e904e90a618.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/18378912/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2023-24/q3/Presentations/Q3%202023-24%20Fact%20Sheet.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.tcs.com/investor-relations/financial-statements#type=overlay&amp;page=/discover-tcs/investor-relations/overlays/management-commentary-overlays/fy24-q3-management-commentry-audio-overlay.html" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=e617aaed-ae26-4fc8-8a6e-d79f40adc7f0.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/15080779/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2023-24/q2/Presentations/Q2%202023-24%20Fact%20Sheet.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=46524061-48f9-497c-8217-2a4ca8479900.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/12158440/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2023"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=0a54627e-6d57-4e58-a672-5f15cbeb3f7e.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/9207811/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2022-23/q4/Presentations/Q4%20FY%202022-23%20Fact%20sheet.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=76ba7959-5943-41bd-9410-a53f3de41e26.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/6645203/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2023"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2022
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=745d6bd9-e798-419a-8d49-b6beef99ab47.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/4342845/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2022"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2022
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=76487354-b76f-4d21-8d70-d929f6b480a9.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/2126544/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2022"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2022
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=f424c104-aab6-47ba-9659-65a2ce5689e1.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/1463285/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2022"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2022
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2021-22/q3/Management%20Commentary/Transcript%20of%20the%20Q3%202021-22%20Earnings%20Conference%20Call%20held%20at%202000%20hrs%20IST%20on%20Jan%2012,%202022.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/1326778/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2022"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2021
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2021-22/q2/Management%20Commentary/Transcript%20of%20the%20Q2%202021-22%20Earnings%20Conference%20Call%20held%20at%202000%20hrs%20IST%20on%20Oct%208,%202021.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/1326777/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2021
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2021-22/q1/Management%20Commentary/Transcript%20of%20the%20Q1%202021-22%20Earnings%20Conference%20Call%20held%20at%202000%20hrs%20IST%20on%20Jul%208,%202021.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/519447/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2021
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2020-21/q4/Management%20Commentary/Transcript%20of%20the%20Q4%202020-21%20Earnings%20Conference%20Call%20held%20at%202000%20hrs%20IST%20on%20Apr%2012,%202021.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/519446/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2021
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2020-21/q3/Management%20Commentary/Transcript%20of%20the%20Q3%202020-21%20Earnings%20Conference%20Call%20held%20at%202000%20hrs%20IST%20on%20Jan%208,%202021.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307717/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Feb 2021"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2020
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2020-21/q2/Management%20Commentary/Transcript%20of%20the%20Q2%202020-21%20Earnings%20Conference%20Call%20held%20at%202000%20hrs%20IST%20on%20Oct%20%207,%202020.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307716/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2020"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2020
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2020-21/q1/Management%20Commentary/Transcript%20of%20the%20Q1%202020-21%20Earnings%20Conference%20Call%20held%20at%202000%20hrs%20IST%20on%20July%209,%202020.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307715/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2020"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2020
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2019-20/q4/Management%20Commentary/Transcript%20of%20the%20Q4%202019-20%20Earnings%20Conference%20Call%20held%20at%202000%20hrs%20IST%20on%20April%2016,%202020.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307714/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2020"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2020
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2019-20/q3/Management%20Commentary/Transcript%20of%20the%20Q3%202019-20%20Earnings%20Conference%20Call%20held%20at%201730%20hrs%20IST%20on%20January%2017,%202020.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307713/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2020"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Nov 2019
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2019-20/q2/Management%20Commentary/Transcript%20of%20the%20Q2%202019-20%20Earnings%20Conference%20Call%20held%20at%201900%20hrs%20IST%20on%20October%2010,%202019.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307712/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Nov 2019"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2019
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2019-20/q1/Management%20Commentary/Transcript%20of%20the%20Q1%202019-20%20Earnings%20Conference%20Call%20held%20at%201900%20hrs%20IST%20on%20July%209,%202019.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307711/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Aug 2019"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2019
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2018-19/q4/Management%20Commentary/Transcript%20of%20the%20Q4%202018-19%20Earnings%20Conference%20Call%20held%20at%201930%20hrs%20IST%20on%20April%2012,%202019.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307710/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2019"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2019
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2018-19/q3/Management%20Commentary/Transcript%20of%20the%20Q3%202018-19%20Earnings%20Conference%20Call%20held%20at%201900%20hrs%20IST%20on%20January%2010,%202019.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307709/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2019"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2018
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2018-19/q2/Management%20Commentary/Transcript%20of%20the%20Q2%202018-19%20Earnings%20Conference%20Call%20held%20at%2019%2000%20hrs%20IST%20on%20October%2011,%202018.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307708/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2018"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2018
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2018-19/q1/Management%20Commentary/Transcript%20of%20the%20Q1%202018-19%20Earnings%20Conference%20Call%20held%20at%2019%2000%20hrs%20IST%20on%20July%2010,%202018.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307707/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2018"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2018
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2017-18/q4/Management%20Commentary/Transcript%20of%20the%20Q4%202017-18%20Earnings%20Conference%20Call%20held%20at%2019%2000%20hrs%20IST%20on%20April%2019,%202018.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307706/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2018"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2018
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2017-18/q3/Management%20Commentary/Transcript%20of%20the%20Q3%202017-18%20Earnings%20Conference%20Call%20held%20at%2019%2000%20hrs%20IST%20on%20January%2011,%202018.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307705/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Feb 2018"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2017
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2017-18/q2/Management%20Commentary/Transcript%20of%20the%20Q2%202017-18%20Earnings%20Conference%20Call%20held%20at%2019%2000%20hrs%20IST%20on%20October%2012,%202017.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307704/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2017"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2017
    </div>

    
    <a
      class="concall-link"
      href="https://www.tcs.com/content/dam/tcs/investor-relations/financial-statements/2017-18/q1/Management%20Commentary/Transcript%20of%20the%20Q1%202017-18%20Earnings%20Conference%20Call%20held%20at%2019%2000%20hrs%20IST%20on%20July%2013,%202017.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/307703/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Aug 2017"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2016
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=6601AB63_2D6D_4FB1_B039_2EE56EC8A7B6_081341.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

</ul>
</div>
          </div>
        
      </div>
    </section>
    <!-- cache time 2025-05-16 18:56:46 -->
    
  

      </main>
    

    
      

<footer id="large-footer">
  <div class="flex-row flex-space-between flex-column-tablet container no-print">
    <div class="hide-from-tablet" style="margin-bottom: 16px;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
    </div>

    <div class="show-from-tablet" style="padding: 0 64px 0 0;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
      <p class="font-size-19" style="font-weight: 500;">Stock analysis and screening tool</p>
      <p class="sub">
        Mittal Analytics Private Ltd &copy; 2009-2025
        <br>
        Made with <i class="icon-heart red"></i> in India.
      </p>
      <p class="sub font-size-13">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="font-size-13 sub">
        <a href="/guides/terms/" class="underline-link">Terms</a> & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>

    <div class="flex flex-end flex-space-between flex-grow"
         style="max-width: 600px">
      <div class="flex-grow">
        <div class="title">Product</div>
        <ul class="items">
          <li>
            <a href="/premium/?driver=footer">Premium</a>
          </li>
          <li>
            <a href="/docs/changelog/">What's new?</a>
          </li>
          <li>
            <a href="https://bit.ly/learnscreener">Learn</a>
          </li>
          <li>
            <button class="a2hs button-secondary button-small">
              <i class="icon-flash"></i>
              Install
            </button>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Team</div>
        <ul class="items">
          <li>
            <a href="/guides/about-us/">About us</a>
          </li>
          <li>
            <a href="/support/">Support</a>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Theme</div>
        <ul class="items">
          <li>
            <button onclick="SetTheme('light')"
                    aria-label="Set light theme"
                    class="button-plain">
              <i class="icon-sun"></i>
              Light
            </button>
          </li>
          <li>
            <button onclick="SetTheme('dark')"
                    aria-label="Set dark theme"
                    class="button-plain">
              <i class="icon-moon"></i>
              Dark
            </button>
          </li>
          <li>
            <button onclick="SetTheme('auto')"
                    aria-label="Set auto theme"
                    class="button-plain">
              <i class="icon-monitor"></i>
              Auto
            </button>
          </li>
        </ul>
      </div>
    </div>

    <div class="hide-from-tablet">
      <hr>
      <p class="sub">Mittal Analytics Private Ltd &copy; 2009-2024</p>
      <p class="sub">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="sub">
        <a href="/guides/terms/" class="underline-link">Terms</a>
        & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>
  </div>
</footer>

    

    <!-- Important JS (which provide global objects)- check below too -->
    <script src="https://cdn-static.screener.in/js/dialog-polyfill.min.2b5bf9a032d2.js"></script>
    <script src="https://cdn-static.screener.in/js/utils.02a9ce812e49.js"></script>
    <script src="https://cdn-static.screener.in/js/modal.4f872e091355.js"></script>
    <script src="https://cdn-static.screener.in/js/typeahead.8dcc9ac93685.js"></script>
    <script src="https://cdn-static.screener.in/js/filter.component.5add9ef3b7a9.js"></script>

    <!-- Independent JS scripts for page interactions -->
    <script src="https://cdn-static.screener.in/js/navbar.3fe7f0f4a630.js"></script>
    <script src="https://cdn-static.screener.in/js/company.search.e48f94642999.js"></script>
    
  <script src="https://cdn-static.screener.in/js/scroll-aid.5ed2d38a133e.js"></script>
  <script src="https://cdn-static.screener.in/js/company.customisation.d7c717b0de15.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.min.10a29a29621c.js"></script>
  <script src="https://cdn-static.screener.in/js/chartjs-adapter-date-fns.bundle.min.ebae23730a6e.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.e20c5f42613a.js"></script>
  <script src="https://cdn-static.screener.in/js/segment-result.b6af4191fd6f.js"></script>


    <!-- Service worker installer for PWA -->
    <script src="https://cdn-static.screener.in/js/pwa.933118758337.js"></script>
  </body>
</html>
