
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    
    <title>ITC Ltd share price | About ITC | Key Insights - Screener</title>
    

    <!-- plausible -->
    <script defer
            event-user="unregistered"
            
            
            data-domain="screener.in"
            src="https://plausible.screener.in/js/script.pageview-props.tagged-events.js"></script>
    <script>window.plausible = window.plausible || function () { (window.plausible.q = window.plausible.q || []).push(arguments) }</script>

    <!-- FONT -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/inter/inter.43d4904a3137.css" />
    <link rel="stylesheet" href="https://cdn-static.screener.in/fontello/css/fontello.bc317748a65f.css" />

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/normalize.a2444750a947.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/skeleton.9aa9c32992ef.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/custom.39beec5e86b6.css" media="all">

    
    
    
  

  
  <meta name="description" content="ITC · Mkt Cap: 5,45,492 Crore (up 7.73% in 1 year) · Revenue: 75,161 Cr · Profit: 20,435 Cr · Stock is trading at 7.24 times its book value · The company has delivered a poor sales growth of 7.95% over past five years.">
  

  <style>
  tr[data-row-company-id="1552"] {
    font-weight: 500;
  }

  tr[data-row-company-id="1552"] a {
    color: var(--ink-900);
  }

  :target {
    scroll-margin-top: 90px;
  }

  @supports not (scroll-margin-top: 90px) {
    :target {
      padding-top: 108px;
    }
  }

  @media (min-width: 992px) {
    nav {
      margin-bottom: 0;
    }

    .company-nav {
      top: 50px;
      margin-bottom: 24px;
    }
  }
  </style>


    <meta name="author" content="Mittal Analytics Private Limited">

    <!-- PWA manifest -->
    <meta name="theme-color"
          content="#fff"
          media="(prefers-color-scheme: light)">
    <meta name="theme-color"
          content="#222530"
          media="(prefers-color-scheme: dark)">
    <link rel="manifest" href="/static/manifest.json">

    <!-- favicons -->
    <link rel="apple-touch-icon"
          sizes="180x180"
          href="https://cdn-static.screener.in/favicon/apple-touch-icon.b6e5f24bf7d3.png">
    <link rel="icon"
          type="image/png"
          sizes="32x32"
          href="https://cdn-static.screener.in/favicon/favicon-32x32.00205914303a.png">
    <link rel="icon"
          type="image/png"
          sizes="16x16"
          href="https://cdn-static.screener.in/favicon/favicon-16x16.2b35594b2f33.png">
    <link rel="mask-icon"
          href="https://cdn-static.screener.in/favicon/safari-pinned-tab.adbe3ed3c5ad.svg"
          color="#8ED443">
    <meta name="msapplication-TileColor" content="#da532c">
  </head>

  <body class="light flex-column">
    
      <nav class="u-full-width no-print">
        <div id="mobile-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

<ul class="bottom-menu-bar hide-from-tablet-landscape">
  
    <li class="">
      <a href="/">
        <i class="icon-home"></i>
        <br>
        Home
      </a>
    </li>
  

  <li class="">
    <a href="/explore/">
      <i class="icon-screens"></i>
      <br>
      Screens
    </a>
  </li>

  <li>
    <button type="button"
            onclick="MobileMenu.toggleSearch(this)"
            class="search-button"
            title="Search">
      <i class="icon-search" style="font-size: 18px"></i>
    </button>
  </li>

  <li>
    <button class="button-plain" onclick="Modal.showInModal('#nav-tools-menu')">
      <i class="icon-tools"></i>
      <br>
      Tools
    </button>
  </li>

  
    <li class="">
      <a href="/register/?">
        <i class="icon-user-plus"></i>
        <br>
        Login
      </a>
    </li>
  
</ul>

        <div class="top-nav-holder">
          <div class="container">



<div class="layer-holder top-navigation">
  <div class="layer flex flex-space-between"
       style="align-items: center;
              height: 50px">
    <div class="flex" style="align-items: center">
      <a href="/" class="logo-holder">
        <img alt="Screener Logo"
             class="logo"
             src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg" />
      </a>

      <div class="desktop-links" style="margin-left: 48px">
        <a href="/">
          
            Home
          
        </a>
        <a href="/explore/">Screens</a>
        <div class="dropdown-menu">
          <button class="button-plain"
                  type="button"
                  onclick="Utils.toggleDropdown(event)">
            Tools
            <i class="icon-down"></i>
          </button>
          <ul class="dropdown-content flex-column tools-menu"
              style="width: 350px"
              id="nav-tools-menu">
            <li>
              <a href="/screen/new/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/screens.e960a5daedf9.svg" alt="Create a stock screen">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Create a stock screen</div>
                  <div class="sub font-size-13">Run queries on 10 years of financial data</div>
                </div>
              </a>
            </li>
            <li class="flex flex-align-center no-hover" style="padding: 4px 0;">
              <div class="button button-primary premium-indicator flex flex-align-center">
                <img src="https://cdn-static.screener.in/icons/crown.fb0d2413ee43.svg"
                     alt="Premium icon"
                     style="margin-right: 8px">
                <span>Premium features</span>
              </div>
              <hr class="flex-grow" style="padding: 0; margin: 0;">
            </li>
            <li>
              <a href="/hs/" class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/export.d623f0029933.svg" alt="Commodity Prices">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Commodity Prices</div>
                  <div class="sub font-size-13">See prices and trends of over 10,000 commodities</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/people/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/shares.10acc20cb936.svg"
                       alt="Search shareholders by name">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Search shareholders</div>
                  <div class="sub font-size-13">See companies where a person holds over 1% of the shares</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/announcements/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/loudspeaker.88769927f6eb.svg" alt="Latest Announcements">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Latest Announcements</div>
                  <div class="sub font-size-13">Browse, filter and set alerts for announcements.</div>
                </div>
              </a>
            </li>
            
              <li style="padding: 16px;">
                <a href="/premium/?driver=top-nav-tools"
                   class="button button-primary text-align-center"
                   style="color: var(--white);
                          padding: 10px 24px">Upgrade to premium</a>
              </li>
            
          </ul>
        </div>
      </div>
    </div>

    <div class="show-from-tablet-landscape flex flex-gap-16"
         style="justify-content: flex-end;
                flex: 1 1 400px">
      <div class="search" id="desktop-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

      
        <div class="flex flex-gap-8" style="margin: 4px">
          <a class="button account"
             href="/login/?">
            <i class="icon-user-line blue-icon"></i>
            Login
          </a>
          <a class="button account button-secondary"
             href="/register/?">Get free account</a>
        </div>
      
    </div>
  </div>
</div>
</div>
        </div>
      </nav>
    

    
  <div class="bg-base sticky company-nav">
    <div class="flex flex-space-between container hide-from-tablet-landscape">
      <h1 class="h2 shrink-text" style="margin: 0.5em 0">ITC Ltd</h1>
      <div class="flex flex-align-center gap-8">
        <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/1552/"
        data-title="ITC Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

        
      </div>
    </div>

    <div class="sub-nav-holder">
      <div class="sub-nav">
        <a href="#top">
          <span class="shrink-text show-from-tablet-landscape">ITC</span>
          <span class="hide-from-tablet-landscape">Summary</span>
        </a>
        <a href="#chart">Chart</a>
        <a href="#analysis">Analysis</a>
        <a href="#peers">Peers</a>
        <a href="#quarters">Quarters</a>
        <a href="#profit-loss">Profit &amp; Loss</a>
        <a href="#balance-sheet">Balance Sheet</a>
        <a href="#cash-flow">Cash Flow</a>
        <a href="#ratios">Ratios</a>
        <a href="#shareholding">Investors</a>
        <a href="#documents">Documents</a>
        <div class="flex-grow flex flex-align-center gap-8 show-from-tablet-landscape">
          <div class="flex-grow"></div>
          <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/1552/"
        data-title="ITC Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

          
        </div>
      </div>
    </div>
  </div>


    
      <main class="flex-grow container">
        



  






        
        <div class="breadcrumb hidden-if-empty"></div>
        

        
  <div data-company-id="1552"
       data-warehouse-id="6596626"
       
       data-consolidated="true"
       id="company-info"></div>

  <div class="card card-large" id="top">
    <div class="flex flex-space-between flex-gap-8">
      <div class="flex-row flex-wrap flex-align-center flex-grow"
           style="flex-basis: 300px">
        <h1 class="margin-0 show-from-tablet-landscape">ITC Ltd</h1>
        <div class="ink-600 show-from-tablet-landscape" style="opacity: 0.5;">
          <i class="icon-dot"></i>
        </div>
        <div class="font-size-18 strong line-height-14">
          <div class="flex flex-align-center">
            <span>₹ 436</span>
            
              <span class="font-size-12 up margin-left-4">
                <i class="icon-circle-up"></i>
                0.70%
              </span>
            
          </div>
          <div class="ink-600 font-size-11 font-weight-500">
            
              16 May
              
                - close price
              
            
          </div>
        </div>
      </div>

      <form method="post" class="flex margin-0" style="align-items: center">
        <input type="hidden" name="csrfmiddlewaretoken" value="6slenryPyelIqk1U9veWl0hnsKYrZr4GSl6OZp1qLplgFWoksYnGV2tB8IKty4dA">
        <input type="hidden" name="next" value="/company/ITC/consolidated/">

        
          <button class="margin-right-8 plausible-event-name=Excel+Company plausible-event-user=unregistered"
                  formaction="/user/company/export/6596626/"
                  aria-label="Export to Excel">
            <i class="icon-download"></i>
            <span class="hide-on-mobile">Export to Excel</span>
          </button>
        

        <div class="flex dropdown-filter">
          
            <button class="button-primary "
                    formaction="/api/company/1552/add/">
              <i class="icon-plus"></i> Follow
            </button>
          

          
        </div>
      </form>
    </div>

    
    <div class="company-links show-from-tablet-landscape"
         style="font-weight: 500;
                margin-top: 8px">
      
        <a href="https://www.itcportal.com"
           target="_blank"
           rel="noopener noreferrer">
          <i class="icon-link"></i>
          <span style="font-size: 1.8rem;">itcportal.com</span>
        </a>
      

      
        <a href="https://www.bseindia.com/stock-share-price/itc-ltd/ITC/500875/" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            BSE:
            500875
          </span>
        </a>
      

      
        <a href="https://www.nseindia.com/get-quotes/equity?symbol=ITC" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            NSE:
            ITC
          </span>
        </a>
      
    </div>
    

    <div class="company-info">
      
      <div class="company-profile">
        <div class="flex flex-column" style="flex: 1 1;">
          <div class="title">About</div>
          <div class="sub show-more-box about" style="flex-basis: 100px"><p>Established in 1910, ITC is the largest cigarette manufacturer and seller in the country. ITC operates in five business segments at present — FMCG Cigarettes, FMCG Others, Hotels, Paperboards, Paper and Packaging, and Agri Business. <sup><a href="https://www.itcportal.com/about-itc/profile/index.aspx" target="_blank" rel="noopener noreferrer">[1]</a></sup></p></div>

          
            <div class="title">Key Points</div>
            <div class="sub commentary always-show-more-box">
              <p><strong>Business Segments</strong><br>
<strong>1) FMCG Cigarettes (42% in H1 FY25 vs 40% in FY22):</strong> <sup><a href="https://www.bseindia.com/xml-data/corpfiling/AttachHis/c1deda1f-c15b-4920-8c66-f99eeca08175.pdf#page=10" target="_blank" rel="noopener noreferrer">[1]</a></sup> <sup><a href="https://www.bseindia.com/bseplus/AnnualReport/500875/***********.pdf#page=333" target="_blank" rel="noopener noreferrer">[2]</a></sup> The company holds an <strong>80% market share</strong> in the organized domestic cigarette market, offering brands like Insignia, India Kings, Classic, Gold Flake, American Club, Wills Navy Cut, Players, Scissors, Capstan, Berkeley, etc. This vertical is the <strong>most profitable business of the company with 78% contribution towards PBIT.</strong> In Q2 FY25, the segment revenue grew by 7% YoY, supported by volume growth and a relatively stable tax regime. <sup><a href="https://www.itcportal.com/businesses/fmcg/cigarettes.aspx" target="_blank" rel="noopener noreferrer">[3]</a></sup> <sup><a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=131524#page=2" target="_blank" rel="noopener noreferrer">[4]</a></sup> <sup><a href="https://www.bseindia.com/xml-data/corpfiling/AttachHis/d69065ea-9ed8-4c10-8c73-8a808e67b8af.pdf#page=30" target="_blank" rel="noopener noreferrer">[5]</a></sup> </p>
              <div class="show-more-button" style="height: 20px"></div>
            </div>

            <button class="a upper font-weight-500 letter-spacing plausible-event-name=Key+Insight plausible-event-user=unregistered"
                    style="font-size: 1.4rem"
                    type="button"
                    onclick="Modal.openInModal(event)"
                    data-title="ITC Ltd"
                    data-url="/wiki/company/1552/commentary/v2/"
                    data-is-right-modal="true">
              Read More <i class="icon-right"></i>
            </button>
          
        </div>

        <div class="company-links hide-from-tablet-landscape margin-top-20"
             style="font-weight: 500">
          
            <a href="https://www.itcportal.com"
               target="_blank"
               rel="noopener noreferrer">
              <i class="icon-link"></i>
              <span style="font-size: 1.6rem;">Website</span>
            </a>
          

          
            <a href="https://www.bseindia.com/stock-share-price/itc-ltd/ITC/500875/" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">BSE</span>
            </a>
          

          
            <a href="https://www.nseindia.com/get-quotes/equity?symbol=ITC" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">
                NSE
                
              </span>
            </a>
          
        </div>
      </div>
      

      <div class="company-ratios">
        <ul id="top-ratios">
          


  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Market Cap
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">5,45,492</span>
        
          Cr.
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Current Price
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">436</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        High / Low
      
    </span>

    
      <span class="nowrap value">₹ <span class="number">495</span> / <span class="number">378</span></span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Stock P/E
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">27.4</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Book Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">60.2</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Dividend Yield
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">3.16</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROCE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">37.5</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">28.4</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Face Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">1.00</span>
        
      </span>
    
  </li>






        </ul>

        <div style="margin-top: 32px;">
          <label for="quick-ratio-search">Add ratio to table</label>

          <div class="flex-row flex-space-between flex-baseline flex-gap-16">
            <div class="dropdown-typeahead flex-grow" style="max-width: 480px;">
              <input id="quick-ratio-search"
                     type="text"
                     placeholder="eg. Promoter holding"
                     style="width: 100%">
            </div>

            
            <a href="/user/quick_ratios/?next=/company/ITC/consolidated/"
               class="smaller">
              <i class="icon-pencil"></i>
              <span class="upper font-weight-500 letter-spacing">Edit ratios</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  
    <section id="chart" class="card card-large" style="margin-bottom: 2.5rem">
      <div id="chart-menu"
     class="flex flex-space-between flex-wrap"
     style="margin-bottom: 16px">
  <div class="options" id="company-chart-days">
    <button name="days"
            value="30"
            onclick="CompanyChart.setActive(event)"
            type="button">1M</button>
    <button name="days"
            value="180"
            onclick="CompanyChart.setActive(event)"
            type="button">6M</button>
    <button name="days"
            value="365"
            onclick="CompanyChart.setActive(event)"
            type="button">1Yr</button>
    <button name="days"
            value="1095"
            onclick="CompanyChart.setActive(event)"
            type="button">3Yr</button>
    <button name="days"
            value="1825"
            onclick="CompanyChart.setActive(event)"
            type="button">5Yr</button>
    <button name="days"
            value="3652"
            onclick="CompanyChart.setActive(event)"
            type="button">10Yr</button>
    <button name="days"
            value="10000"
            onclick="CompanyChart.setActive(event)"
            type="button">Max</button>
  </div>

  <div class="flex margin-bottom-24">
    <div class="options margin-right-8"
         id="company-chart-metrics"
         style="margin-bottom: 0">
      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Price"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price-DMA50-DMA200-Volume"
                  type="button">Price</button>
        

      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PE"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price to Earning-Median PE-EPS"
                  type="button">PE Ratio</button>
        

      

        

      

        

      

        

      

        

      
      <button class="hidden"
              id="company-chart-metrics-more-active"
              onclick="CompanyChart.setActive(event)"
              name="metrics"
              value=""
              type="button">Hidden</button>
      <div class="dropdown-menu" id="metrics-dropdown">
        <button type="button"
                class="button-plain"
                onclick="Utils.toggleDropdown(event)">
          More <i class="icon-down"></i>
        </button>
        <ul class="dropdown-content" style="max-width: 160px; right: 0">
          

            

          

            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="GPM-OPM-NPM-Quarter Sales"
                        type="button">Sales &amp; Margin</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=EV-EBITDA"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="EV Multiple-Median EV Multiple-EBITDA"
                        type="button">EV / EBITDA</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PBV"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Price to book value-Median PBV-Book value"
                        type="button">Price to Book</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Market+Cap+to+Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Market Cap to Sales-Median Market Cap to Sales-Sales"
                        type="button">Market Cap / Sales</button>
              </li>
            

          
        </ul>
      </div>
    </div>
    
      <button type="button"
              class="button-small button-secondary flex flex-align-center plausible-event-name=Stock+Alert"
              style="border-color: var(--button-border);
                     border-radius: 3px"
              data-title="Alerts for ITC Ltd"
              onclick="Modal.openInModal(event)"
              data-url="/alerts/stock-6596626/"
              aria-label="Add alerts for company">
        <i class="icon-bell"></i>
        
        <span class="margin-left-4 normal-case letter-spacing-0">Alerts</span>
      </button>
    
  </div>
</div>

<div class="flex no-select">
  <div class="hide-on-mobile chart-label">
    <div id="chart-label-left"></div>
  </div>

  <div id="chart-area" style="flex-grow: 1">
    <div id="chart-info" class="invisible">
      <div id="chart-crosshair"></div>
      <div id="chart-tooltip" class="font-size-14">
        <div id="chart-tooltip-meta" class="ink-700 font-size-12"></div>
        <div class="font-weight-500 font-size-15" id="chart-tooltip-title"></div>
        <div id="chart-tooltip-info"></div>
      </div>
    </div>
    <div id="canvas-chart-holder" style="height: 375px"></div>
  </div>

  <div class="hide-on-mobile chart-label">
    <div id="chart-label-right"></div>
  </div>
</div>
<div id="chart-legend"></div>

    </section>

    
    <section id="analysis" class="card card-large">
      <div class="flex flex-column-mobile flex-gap-32">
        <div class="pros" style="flex-basis: 50%;">
          <p class="title">Pros</p>
          <ul>
            <li>Company is almost debt free.</li><li>Stock is providing a good dividend yield of 3.16%.</li><li>Company has a good return on equity (ROE) track record: 3 Years ROE 27.5%</li><li>Company has been maintaining a healthy dividend payout of 92.4%</li>
          </ul>
        </div>
        <div class="cons" style="flex-basis: 50%;">
          <p class="title">Cons</p>
          <ul>
            <li>Stock is trading at 7.24 times its book value</li><li>The company has delivered a poor sales growth of 7.95% over past five years.</li>
          </ul>
        </div>
      </div>
      <p class="sub font-size-14 margin-top-16 margin-bottom-0">
        <span style="vertical-align: sub;">*</span>
        <span class="font-weight-500">The pros and cons are machine generated.</span>
        <span class="has-tooltip">
          <i class="icon-info"></i>
          <span class="tooltip" style="width: 300px; left: -100px;">
            Pros / cons are based on a checklist to highlight important points. Please exercise caution and do your own analysis.
          </span>
        </span>
      </p>
    </section>

    <section id="peers" class="card card-large">
      <div class="flex flex-space-between" style="align-items: center;">
        <div>
          <h2>Peer comparison</h2>

          
            <p class="sub">
              Sector:
              <a href="/company/compare/00000063/"
                 target="_blank">Tobacco Products</a>
              <span style="margin: 16px"></span>
              Industry:
              <a href="/company/compare/00000063/00000024/"
                 target="_blank">Cigarettes</a>
            </p>
          

          
            <p class="flex flex-wrap gap-8 flex-align-center line-height-1 sub"
               id="benchmarks">
              Part of
              
                <a href="/company/1001/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Sensex"
                   target="_blank">BSE Sensex</a>
              
                <a href="/company/NIFTY/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50"
                   target="_blank">Nifty 50</a>
              
                <a href="/company/1005/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+500"
                   target="_blank">BSE 500</a>
              
                <a href="/company/1007/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Fast+Moving+Consumer+Goods"
                   target="_blank">BSE Fast Moving Consumer Goods</a>
              
                <a href="/company/1002/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100"
                   target="_blank">BSE 100</a>
              
                <a href="/company/1003/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+200"
                   target="_blank">BSE 200</a>
              
                <a href="/company/1004/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dollex+200"
                   target="_blank">BSE Dollex 200</a>
              
                <a href="/company/CNX500/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500"
                   target="_blank">Nifty 500</a>
              
                <a href="/company/CNX100/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100"
                   target="_blank">Nifty 100</a>
              
                <a href="/company/CNXFMCG/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+FMCG"
                   target="_blank">Nifty FMCG</a>
              
                <a href="/company/CNXCONSUMP/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+India+Consumption"
                   target="_blank">Nifty India Consumption</a>
              
                <a href="/company/CNX200INDE/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+200"
                   target="_blank">Nifty 200</a>
              
                <a href="/company/CNXDIVIDEN/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Dividend+Opportunities+50"
                   target="_blank">Nifty Dividend Opportunities 50</a>
              
                <a href="/company/NV5020/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty50+Value+20"
                   target="_blank">Nifty50 Value 20</a>
              
                <a href="/company/NFT100EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Equal+Weight"
                   target="_blank">Nifty 100 Equal Weight</a>
              
                <a href="/company/1153/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Allcap"
                   target="_blank">BSE Allcap</a>
              
                <a href="/company/1155/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+LargeCap"
                   target="_blank">BSE LargeCap</a>
              
                <a href="/company/1166/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+India+Manufacturing+Index"
                   target="_blank">BSE India Manufacturing Index</a>
              
                <a href="/company/1125/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+SENSEX+50"
                   target="_blank">BSE SENSEX 50</a>
              
                <a href="/company/NIFTYQLY30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty100+Quality+30"
                   target="_blank">Nifty100 Quality 30</a>
              
                <a href="/company/1016/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Bharat+22+Index"
                   target="_blank">BSE Bharat 22 Index</a>
              
                <a href="/company/1181/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+250+LargeMidCap+Index"
                   target="_blank">BSE 250 LargeMidCap Index</a>
              
                <a href="/company/1183/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dividend+Stability+Index"
                   target="_blank">BSE Dividend Stability Index</a>
              
                <a href="/company/1185/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Low+Volatility+Index"
                   target="_blank">BSE Low Volatility Index</a>
              
                <a href="/company/NFT50EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50+Equal+Weight"
                   target="_blank">Nifty 50 Equal Weight</a>
              
                <a href="/company/NFT100LV30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Low+Volatility+30"
                   target="_blank">Nifty 100 Low Volatility 30</a>
              
                <a href="/company/1130/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100+LargeCap+TMC+Index"
                   target="_blank">BSE 100 LargeCap TMC Index</a>
              
                <a href="/company/LMIDCAP250/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+LargeMidcap+250"
                   target="_blank">Nifty LargeMidcap 250</a>
              
                <a href="/company/NFT500MULT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500+Multicap+50:25:25"
                   target="_blank">Nifty 500 Multicap 50:25:25</a>
              
                <a href="/company/NQUALITY30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty200+Quality+30"
                   target="_blank">Nifty200 Quality 30</a>
              
                <a href="/company/NFTYTOTMKT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Total+Market"
                   target="_blank">Nifty Total Market</a>
              
                <a href="/company/ALPHALOWVO/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Alpha+Low-Volatility+30"
                   target="_blank">Nifty Alpha Low-Volatility 30</a>
              
              
                <button type="button"
                        class="a small font-weight-500 plausible-event-name=Show+More+Benchmarks"
                        onclick="Utils.toggleClass('#benchmarks', 'show-hidden')">show all</button>
              
            </p>
          
        </div>

        <div class="flex-reverse">
          <a href="/user/columns/?next=/company/ITC/consolidated/#peers"
             class="button button-small button-secondary">
            <i class="icon-cog"></i>
            <span class="hide-on-mobile">Edit</span>
            Columns
          </a>
        </div>
      </div>

      <div id="peers-table-placeholder">Loading peers table ...</div>

      <div class="flex-row flex-baseline">
        <label for="search-peer-comparison" style="padding-right: 1rem">Detailed Comparison with:</label>
        <div class="dropdown-typeahead">
          <input type="text"
                 id="search-peer-comparison"
                 placeholder="eg. Infosys"
                 class="small">
        </div>
      </div>
    </section>

    <section id="quarters" class="card card-large">
      
        


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Quarterly Results</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/ITC/#quarters"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    
      <button data-segment-button-close
              onclick="Segment.closeSegments('quarters')"
              class="button-small hidden">
        <i class="icon-cancel"></i>
        Close Segments
      </button>

      
        <button data-segment-button="1"
                onclick="Segment.showSegment('quarters', '1')"
                class="button-small button-secondary plausible-event-name=Segment+Results plausible-event-user=unregistered">
          <i class="icon-chart-pie"></i>
          Product Segments
        </button>
      
    

    
  </div>
</div>

<div class="responsive-holder hidden" data-segment-table></div>

<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="highlight-cell">
            Dec 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Jun 2022
            
          </th>
        
          <th class="">
            Sep 2022
            
          </th>
        
          <th class="highlight-cell">
            Dec 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Jun 2023
            
          </th>
        
          <th class="">
            Sep 2023
            
          </th>
        
          <th class="highlight-cell">
            Dec 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Jun 2024
            
          </th>
        
          <th class="">
            Sep 2024
            
          </th>
        
          <th class="highlight-cell">
            Dec 2024
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Sales', 'quarters', this)">
                  Sales&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">17,108</td>
            
              <td class="">16,556</td>
            
              <td class="">18,489</td>
            
              <td class="">17,108</td>
            
              <td class="highlight-cell">17,705</td>
            
              <td class="">17,635</td>
            
              <td class="">17,164</td>
            
              <td class="">17,774</td>
            
              <td class="highlight-cell">17,195</td>
            
              <td class="">17,923</td>
            
              <td class="">18,457</td>
            
              <td class="">19,990</td>
            
              <td class="highlight-cell">18,790</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'quarters', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">11,510</td>
            
              <td class="">10,956</td>
            
              <td class="">12,412</td>
            
              <td class="">10,849</td>
            
              <td class="highlight-cell">11,000</td>
            
              <td class="">11,011</td>
            
              <td class="">10,494</td>
            
              <td class="">11,320</td>
            
              <td class="highlight-cell">10,985</td>
            
              <td class="">11,296</td>
            
              <td class="">11,709</td>
            
              <td class="">13,438</td>
            
              <td class="highlight-cell">12,428</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Operating Profit
              
            </td>
            
              <td class="highlight-cell">5,598</td>
            
              <td class="">5,599</td>
            
              <td class="">6,077</td>
            
              <td class="">6,259</td>
            
              <td class="highlight-cell">6,705</td>
            
              <td class="">6,624</td>
            
              <td class="">6,670</td>
            
              <td class="">6,454</td>
            
              <td class="highlight-cell">6,210</td>
            
              <td class="">6,626</td>
            
              <td class="">6,748</td>
            
              <td class="">6,552</td>
            
              <td class="highlight-cell">6,362</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                OPM %
              
            </td>
            
              <td class="highlight-cell">33%</td>
            
              <td class="">34%</td>
            
              <td class="">33%</td>
            
              <td class="">37%</td>
            
              <td class="highlight-cell">38%</td>
            
              <td class="">38%</td>
            
              <td class="">39%</td>
            
              <td class="">36%</td>
            
              <td class="highlight-cell">36%</td>
            
              <td class="">37%</td>
            
              <td class="">37%</td>
            
              <td class="">33%</td>
            
              <td class="highlight-cell">34%</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'quarters', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">422</td>
            
              <td class="">499</td>
            
              <td class="">321</td>
            
              <td class="">455</td>
            
              <td class="highlight-cell">595</td>
            
              <td class="">683</td>
            
              <td class="">722</td>
            
              <td class="">674</td>
            
              <td class="highlight-cell">820</td>
            
              <td class="">682</td>
            
              <td class="">699</td>
            
              <td class="">690</td>
            
              <td class="highlight-cell">803</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="highlight-cell">10</td>
            
              <td class="">11</td>
            
              <td class="">9</td>
            
              <td class="">13</td>
            
              <td class="highlight-cell">9</td>
            
              <td class="">12</td>
            
              <td class="">10</td>
            
              <td class="">10</td>
            
              <td class="highlight-cell">12</td>
            
              <td class="">13</td>
            
              <td class="">11</td>
            
              <td class="">15</td>
            
              <td class="highlight-cell">10</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="highlight-cell">430</td>
            
              <td class="">467</td>
            
              <td class="">438</td>
            
              <td class="">462</td>
            
              <td class="highlight-cell">447</td>
            
              <td class="">461</td>
            
              <td class="">442</td>
            
              <td class="">453</td>
            
              <td class="highlight-cell">384</td>
            
              <td class="">461</td>
            
              <td class="">499</td>
            
              <td class="">416</td>
            
              <td class="highlight-cell">416</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="highlight-cell">5,580</td>
            
              <td class="">5,620</td>
            
              <td class="">5,950</td>
            
              <td class="">6,239</td>
            
              <td class="highlight-cell">6,844</td>
            
              <td class="">6,833</td>
            
              <td class="">6,940</td>
            
              <td class="">6,665</td>
            
              <td class="highlight-cell">6,635</td>
            
              <td class="">6,834</td>
            
              <td class="">6,938</td>
            
              <td class="">6,811</td>
            
              <td class="highlight-cell">6,740</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="highlight-cell">26%</td>
            
              <td class="">24%</td>
            
              <td class="">25%</td>
            
              <td class="">25%</td>
            
              <td class="highlight-cell">26%</td>
            
              <td class="">24%</td>
            
              <td class="">25%</td>
            
              <td class="">26%</td>
            
              <td class="highlight-cell">19%</td>
            
              <td class="">24%</td>
            
              <td class="">25%</td>
            
              <td class="">26%</td>
            
              <td class="highlight-cell">26%</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'quarters', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">4,127</td>
            
              <td class="">4,266</td>
            
              <td class="">4,472</td>
            
              <td class="">4,682</td>
            
              <td class="highlight-cell">5,080</td>
            
              <td class="">5,243</td>
            
              <td class="">5,190</td>
            
              <td class="">4,965</td>
            
              <td class="highlight-cell">5,407</td>
            
              <td class="">5,191</td>
            
              <td class="">5,177</td>
            
              <td class="">5,054</td>
            
              <td class="highlight-cell">5,013</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="highlight-cell">3.29</td>
            
              <td class="">3.40</td>
            
              <td class="">3.56</td>
            
              <td class="">3.73</td>
            
              <td class="highlight-cell">4.03</td>
            
              <td class="">4.16</td>
            
              <td class="">4.10</td>
            
              <td class="">3.93</td>
            
              <td class="highlight-cell">4.28</td>
            
              <td class="">4.10</td>
            
              <td class="">4.08</td>
            
              <td class="">3.99</td>
            
              <td class="highlight-cell">3.94</td>
            
          </tr>
        
      

      
        <tr class="font-size-14 ink-600">
          <td class="text ink-600">Raw PDF</td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/1552/12/2021/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1552/3/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1552/6/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1552/9/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/1552/12/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1552/3/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1552/6/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1552/9/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/1552/12/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1552/3/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1552/6/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1552/9/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/1552/12/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
        </tr>
      
    </tbody>
  </table>
</div>

      

      
        <div>
          <span class="badge">
            Upcoming result date: <strong>22 May 2025</strong>
          </span>
        </div>
      
    </section>

    <section id="profit-loss" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Profit & Loss</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/ITC/#profit-loss"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    
      <button data-url="/results/rpt/1552/consolidated/"
              data-title="Related Party Transactions"
              onclick="Modal.openInModal(event)"
              class="button-small button-secondary plausible-event-name=Related+party plausible-event-user=unregistered">
        <i class="small icon-users"></i>
        Related Party
      </button>
    

    
      <button data-segment-button-close
              onclick="Segment.closeSegments('profit-loss')"
              class="button-small hidden">
        <i class="icon-cancel"></i>
        Close Segments
      </button>

      
        <button data-segment-button="1"
                onclick="Segment.showSegment('profit-loss', '1')"
                class="button-small button-secondary plausible-event-name=Segment+Results plausible-event-user=unregistered">
          <i class="icon-chart-pie"></i>
          Product Segments
        </button>
      
    

    
  </div>
</div>

<div class="responsive-holder hidden" data-segment-table></div>

<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2013
            
          </th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            TTM
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Sales', 'profit-loss', this)">
                  Sales&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">31,618</td>
            
              <td class="">35,306</td>
            
              <td class="">38,817</td>
            
              <td class="">39,192</td>
            
              <td class="">42,768</td>
            
              <td class="">43,449</td>
            
              <td class="">48,340</td>
            
              <td class="">49,388</td>
            
              <td class="">49,257</td>
            
              <td class="">60,645</td>
            
              <td class="">70,919</td>
            
              <td class="">70,866</td>
            
              <td class="">75,161</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'profit-loss', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">20,398</td>
            
              <td class="">22,227</td>
            
              <td class="">24,566</td>
            
              <td class="">24,661</td>
            
              <td class="">27,298</td>
            
              <td class="">26,928</td>
            
              <td class="">29,802</td>
            
              <td class="">30,044</td>
            
              <td class="">32,193</td>
            
              <td class="">40,021</td>
            
              <td class="">45,215</td>
            
              <td class="">44,634</td>
            
              <td class="">48,872</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Operating Profit
              
            </td>
            
              <td class="">11,221</td>
            
              <td class="">13,080</td>
            
              <td class="">14,252</td>
            
              <td class="">14,531</td>
            
              <td class="">15,470</td>
            
              <td class="">16,521</td>
            
              <td class="">18,537</td>
            
              <td class="">19,344</td>
            
              <td class="">17,065</td>
            
              <td class="">20,623</td>
            
              <td class="">25,704</td>
            
              <td class="">26,233</td>
            
              <td class="">26,289</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                OPM %
              
            </td>
            
              <td class="">35%</td>
            
              <td class="">37%</td>
            
              <td class="">37%</td>
            
              <td class="">37%</td>
            
              <td class="">36%</td>
            
              <td class="">38%</td>
            
              <td class="">38%</td>
            
              <td class="">39%</td>
            
              <td class="">35%</td>
            
              <td class="">34%</td>
            
              <td class="">36%</td>
            
              <td class="">37%</td>
            
              <td class="">35%</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'profit-loss', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">852</td>
            
              <td class="">966</td>
            
              <td class="">1,229</td>
            
              <td class="">1,483</td>
            
              <td class="">1,759</td>
            
              <td class="">2,240</td>
            
              <td class="">2,080</td>
            
              <td class="">2,417</td>
            
              <td class="">2,577</td>
            
              <td class="">1,910</td>
            
              <td class="">2,098</td>
            
              <td class="">2,804</td>
            
              <td class="">2,875</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="">108</td>
            
              <td class="">29</td>
            
              <td class="">91</td>
            
              <td class="">78</td>
            
              <td class="">49</td>
            
              <td class="">115</td>
            
              <td class="">71</td>
            
              <td class="">81</td>
            
              <td class="">58</td>
            
              <td class="">60</td>
            
              <td class="">78</td>
            
              <td class="">80</td>
            
              <td class="">48</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="">859</td>
            
              <td class="">965</td>
            
              <td class="">1,028</td>
            
              <td class="">1,077</td>
            
              <td class="">1,153</td>
            
              <td class="">1,236</td>
            
              <td class="">1,397</td>
            
              <td class="">1,645</td>
            
              <td class="">1,646</td>
            
              <td class="">1,732</td>
            
              <td class="">1,809</td>
            
              <td class="">1,816</td>
            
              <td class="">1,792</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="">11,106</td>
            
              <td class="">13,052</td>
            
              <td class="">14,362</td>
            
              <td class="">14,859</td>
            
              <td class="">16,026</td>
            
              <td class="">17,409</td>
            
              <td class="">19,150</td>
            
              <td class="">20,035</td>
            
              <td class="">17,938</td>
            
              <td class="">20,740</td>
            
              <td class="">25,915</td>
            
              <td class="">27,140</td>
            
              <td class="">27,323</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="">31%</td>
            
              <td class="">31%</td>
            
              <td class="">32%</td>
            
              <td class="">36%</td>
            
              <td class="">35%</td>
            
              <td class="">34%</td>
            
              <td class="">33%</td>
            
              <td class="">22%</td>
            
              <td class="">25%</td>
            
              <td class="">25%</td>
            
              <td class="">25%</td>
            
              <td class="">24%</td>
            
              <td class=""></td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'profit-loss', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">7,704</td>
            
              <td class="">9,001</td>
            
              <td class="">9,779</td>
            
              <td class="">9,501</td>
            
              <td class="">10,477</td>
            
              <td class="">11,493</td>
            
              <td class="">12,836</td>
            
              <td class="">15,593</td>
            
              <td class="">13,383</td>
            
              <td class="">15,503</td>
            
              <td class="">19,477</td>
            
              <td class="">20,751</td>
            
              <td class="">20,435</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="">6.42</td>
            
              <td class="">7.45</td>
            
              <td class="">8.04</td>
            
              <td class="">7.74</td>
            
              <td class="">8.47</td>
            
              <td class="">9.24</td>
            
              <td class="">10.27</td>
            
              <td class="">12.45</td>
            
              <td class="">10.69</td>
            
              <td class="">12.37</td>
            
              <td class="">15.44</td>
            
              <td class="">16.39</td>
            
              <td class="">16.11</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Dividend Payout %
              
            </td>
            
              <td class="">55%</td>
            
              <td class="">54%</td>
            
              <td class="">52%</td>
            
              <td class="">73%</td>
            
              <td class="">56%</td>
            
              <td class="">56%</td>
            
              <td class="">56%</td>
            
              <td class="">82%</td>
            
              <td class="">101%</td>
            
              <td class="">93%</td>
            
              <td class="">100%</td>
            
              <td class="">84%</td>
            
              <td class=""></td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>


      <div style="display: grid;
                  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                  gap: 2%">
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Sales Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>7%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>8%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>13%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>8%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Profit Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>9%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>10%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>16%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>-2%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Stock Price CAGR</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>8%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>23%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>21%</td>
          </tr>
          <tr>
            <td>1 Year:</td>
            <td>8%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Return on Equity</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>25%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>26%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>28%</td>
          </tr>
          <tr>
            <td>Last Year:</td>
            <td>28%</td>
          </tr>
        </table>
      </div>
    </section>

    <section id="balance-sheet" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Balance Sheet</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/ITC/#balance-sheet"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
      <button type="button"
              class="button-small button-secondary plausible-event-name=Corporate+Actions plausible-event-user=unregistered"
              onclick="Modal.openInModal(event)"
              data-title="Corporate actions"
              data-url="/company/actions/1552/">Corporate actions</button>
    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2013
            
          </th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Sep 2024
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                Equity Capital
              
            </td>
            
              <td class="">790</td>
            
              <td class="">795</td>
            
              <td class="">802</td>
            
              <td class="">805</td>
            
              <td class="">1,215</td>
            
              <td class="">1,220</td>
            
              <td class="">1,226</td>
            
              <td class="">1,229</td>
            
              <td class="">1,231</td>
            
              <td class="">1,232</td>
            
              <td class="">1,243</td>
            
              <td class="">1,248</td>
            
              <td class="">1,251</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Reserves
              
            </td>
            
              <td class="">22,368</td>
            
              <td class="">26,442</td>
            
              <td class="">30,934</td>
            
              <td class="">41,875</td>
            
              <td class="">45,198</td>
            
              <td class="">51,290</td>
            
              <td class="">57,915</td>
            
              <td class="">64,044</td>
            
              <td class="">59,116</td>
            
              <td class="">61,223</td>
            
              <td class="">67,912</td>
            
              <td class="">73,259</td>
            
              <td class="">74,015</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Borrowings', 'balance-sheet', this)">
                  Borrowings&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">102</td>
            
              <td class="">242</td>
            
              <td class="">269</td>
            
              <td class="">84</td>
            
              <td class="">46</td>
            
              <td class="">36</td>
            
              <td class="">13</td>
            
              <td class="">277</td>
            
              <td class="">271</td>
            
              <td class="">249</td>
            
              <td class="">306</td>
            
              <td class="">303</td>
            
              <td class="">304</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Liabilities', 'balance-sheet', this)">
                  Other Liabilities&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">12,069</td>
            
              <td class="">13,369</td>
            
              <td class="">13,948</td>
            
              <td class="">8,888</td>
            
              <td class="">9,440</td>
            
              <td class="">11,695</td>
            
              <td class="">12,585</td>
            
              <td class="">11,760</td>
            
              <td class="">13,143</td>
            
              <td class="">14,491</td>
            
              <td class="">16,370</td>
            
              <td class="">16,944</td>
            
              <td class="">18,501</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Total Liabilities
              
            </td>
            
              <td class="">35,329</td>
            
              <td class="">40,848</td>
            
              <td class="">45,952</td>
            
              <td class="">51,651</td>
            
              <td class="">55,898</td>
            
              <td class="">64,241</td>
            
              <td class="">71,739</td>
            
              <td class="">77,311</td>
            
              <td class="">73,761</td>
            
              <td class="">77,196</td>
            
              <td class="">85,831</td>
            
              <td class="">91,754</td>
            
              <td class="">94,071</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Fixed Assets', 'balance-sheet', this)">
                  Fixed Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">12,140</td>
            
              <td class="">12,921</td>
            
              <td class="">15,303</td>
            
              <td class="">15,107</td>
            
              <td class="">15,893</td>
            
              <td class="">16,524</td>
            
              <td class="">19,374</td>
            
              <td class="">21,713</td>
            
              <td class="">23,298</td>
            
              <td class="">24,232</td>
            
              <td class="">25,851</td>
            
              <td class="">27,820</td>
            
              <td class="">29,492</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                CWIP
              
            </td>
            
              <td class="">2,062</td>
            
              <td class="">3,117</td>
            
              <td class="">2,700</td>
            
              <td class="">2,560</td>
            
              <td class="">3,730</td>
            
              <td class="">5,508</td>
            
              <td class="">4,136</td>
            
              <td class="">3,256</td>
            
              <td class="">4,011</td>
            
              <td class="">3,226</td>
            
              <td class="">3,003</td>
            
              <td class="">2,861</td>
            
              <td class="">1,387</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Investments
              
            </td>
            
              <td class="">5,981</td>
            
              <td class="">7,284</td>
            
              <td class="">6,943</td>
            
              <td class="">11,748</td>
            
              <td class="">17,581</td>
            
              <td class="">22,053</td>
            
              <td class="">25,043</td>
            
              <td class="">28,663</td>
            
              <td class="">24,871</td>
            
              <td class="">24,841</td>
            
              <td class="">29,415</td>
            
              <td class="">31,114</td>
            
              <td class="">28,409</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Assets', 'balance-sheet', this)">
                  Other Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">15,146</td>
            
              <td class="">17,526</td>
            
              <td class="">21,006</td>
            
              <td class="">22,237</td>
            
              <td class="">18,694</td>
            
              <td class="">20,156</td>
            
              <td class="">23,185</td>
            
              <td class="">23,678</td>
            
              <td class="">21,580</td>
            
              <td class="">24,898</td>
            
              <td class="">27,561</td>
            
              <td class="">29,959</td>
            
              <td class="">34,784</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Total Assets
              
            </td>
            
              <td class="">35,329</td>
            
              <td class="">40,848</td>
            
              <td class="">45,952</td>
            
              <td class="">51,651</td>
            
              <td class="">55,898</td>
            
              <td class="">64,241</td>
            
              <td class="">71,739</td>
            
              <td class="">77,311</td>
            
              <td class="">73,761</td>
            
              <td class="">77,196</td>
            
              <td class="">85,831</td>
            
              <td class="">91,754</td>
            
              <td class="">94,071</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="cash-flow" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Cash Flows</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/ITC/#cash-flow"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2013
            
          </th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Operating Activity', 'cash-flow', this)">
                  Cash from Operating Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">7,102</td>
            
              <td class="">7,344</td>
            
              <td class="">9,843</td>
            
              <td class="">9,799</td>
            
              <td class="">10,627</td>
            
              <td class="">13,169</td>
            
              <td class="">12,583</td>
            
              <td class="">14,690</td>
            
              <td class="">12,527</td>
            
              <td class="">15,776</td>
            
              <td class="">18,878</td>
            
              <td class="">17,179</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Investing Activity', 'cash-flow', this)">
                  Cash from Investing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">-3,881</td>
            
              <td class="">-3,254</td>
            
              <td class="">-5,275</td>
            
              <td class="">-3,921</td>
            
              <td class="">-3,251</td>
            
              <td class="">-7,114</td>
            
              <td class="">-5,546</td>
            
              <td class="">-6,174</td>
            
              <td class="">5,740</td>
            
              <td class="">-2,238</td>
            
              <td class="">-5,732</td>
            
              <td class="">1,563</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Financing Activity', 'cash-flow', this)">
                  Cash from Financing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">-3,310</td>
            
              <td class="">-4,122</td>
            
              <td class="">-4,661</td>
            
              <td class="">-5,613</td>
            
              <td class="">-7,301</td>
            
              <td class="">-6,221</td>
            
              <td class="">-6,869</td>
            
              <td class="">-8,181</td>
            
              <td class="">-18,634</td>
            
              <td class="">-13,580</td>
            
              <td class="">-13,006</td>
            
              <td class="">-18,551</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Net Cash Flow
              
            </td>
            
              <td class="">-90</td>
            
              <td class="">-32</td>
            
              <td class="">-93</td>
            
              <td class="">266</td>
            
              <td class="">75</td>
            
              <td class="">-166</td>
            
              <td class="">169</td>
            
              <td class="">334</td>
            
              <td class="">-367</td>
            
              <td class="">-43</td>
            
              <td class="">139</td>
            
              <td class="">191</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="ratios" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Ratios</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/ITC/#ratios"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2013
            
          </th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                Debtor Days
              
            </td>
            
              <td class="">16</td>
            
              <td class="">25</td>
            
              <td class="">19</td>
            
              <td class="">18</td>
            
              <td class="">21</td>
            
              <td class="">23</td>
            
              <td class="">30</td>
            
              <td class="">19</td>
            
              <td class="">19</td>
            
              <td class="">15</td>
            
              <td class="">15</td>
            
              <td class="">21</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Inventory Days
              
            </td>
            
              <td class="">227</td>
            
              <td class="">228</td>
            
              <td class="">212</td>
            
              <td class="">244</td>
            
              <td class="">185</td>
            
              <td class="">173</td>
            
              <td class="">165</td>
            
              <td class="">187</td>
            
              <td class="">189</td>
            
              <td class="">150</td>
            
              <td class="">148</td>
            
              <td class="">190</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Days Payable
              
            </td>
            
              <td class="">53</td>
            
              <td class="">58</td>
            
              <td class="">50</td>
            
              <td class="">63</td>
            
              <td class="">60</td>
            
              <td class="">80</td>
            
              <td class="">74</td>
            
              <td class="">76</td>
            
              <td class="">78</td>
            
              <td class="">61</td>
            
              <td class="">59</td>
            
              <td class="">64</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Cash Conversion Cycle
              
            </td>
            
              <td class="">189</td>
            
              <td class="">195</td>
            
              <td class="">181</td>
            
              <td class="">199</td>
            
              <td class="">145</td>
            
              <td class="">115</td>
            
              <td class="">122</td>
            
              <td class="">129</td>
            
              <td class="">129</td>
            
              <td class="">104</td>
            
              <td class="">105</td>
            
              <td class="">146</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Working Capital Days
              
            </td>
            
              <td class="">-5</td>
            
              <td class="">9</td>
            
              <td class="">-4</td>
            
              <td class="">52</td>
            
              <td class="">45</td>
            
              <td class="">31</td>
            
              <td class="">32</td>
            
              <td class="">35</td>
            
              <td class="">36</td>
            
              <td class="">31</td>
            
              <td class="">20</td>
            
              <td class="">38</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                ROCE %
              
            </td>
            
              <td class="">51%</td>
            
              <td class="">50%</td>
            
              <td class="">47%</td>
            
              <td class="">40%</td>
            
              <td class="">36%</td>
            
              <td class="">34%</td>
            
              <td class="">34%</td>
            
              <td class="">32%</td>
            
              <td class="">28%</td>
            
              <td class="">33%</td>
            
              <td class="">39%</td>
            
              <td class="">37%</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="shareholding" class="card card-large">
      <div class="flex flex-space-between flex-wrap margin-bottom-8 flex-align-center">
  <div>
    <h2 class="margin-0">Shareholding Pattern</h2>
    <p class="sub">Numbers in percentages</p>
  </div>
  <div class="flex">
    <div class="options small margin-0">
      <button
        onclick="Utils.setActiveTab(event)"
        class="active"
        data-tab-id="quarterly-shp"
        type="button">
        Quarterly
      </button>
      <button
        onclick="Utils.setActiveTab(event)"
        data-tab-id="yearly-shp"
        type="button">
        Yearly
      </button>
    </div>
    
    <div class="text-align-center margin-left-12">
      <button
        type="button"
        class="button-secondary button-small"
        onclick="Modal.openInModal(event)"
        data-title="ITC trades"
        data-url="/trades/company-1552/"
      >
        Trades
      </button>
      
    </div>
    
  </div>
</div>

<div id="quarterly-shp">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Jun 2022</th><th>Sep 2022</th><th>Dec 2022</th><th>Mar 2023</th><th>Jun 2023</th><th>Sep 2023</th><th>Dec 2023</th><th>Mar 2024</th><th>Jun 2024</th><th>Sep 2024</th><th>Dec 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=foreign_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('foreign_institutions', 'quarterly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>12.68%</td>
            
              <td>42.68%</td>
            
              <td>42.99%</td>
            
              <td>43.35%</td>
            
              <td>43.62%</td>
            
              <td>43.34%</td>
            
              <td>43.26%</td>
            
              <td>40.95%</td>
            
              <td>40.47%</td>
            
              <td>40.53%</td>
            
              <td>40.17%</td>
            
              <td>39.87%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=domestic_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('domestic_institutions', 'quarterly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>42.82%</td>
            
              <td>42.38%</td>
            
              <td>42.19%</td>
            
              <td>42.08%</td>
            
              <td>41.92%</td>
            
              <td>41.94%</td>
            
              <td>41.98%</td>
            
              <td>43.76%</td>
            
              <td>44.02%</td>
            
              <td>44.59%</td>
            
              <td>44.91%</td>
            
              <td>45.19%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=government plausible-event-period=quarterly"
                      onclick="Company.showShareholders('government', 'quarterly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.00%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=public plausible-event-period=quarterly"
                      onclick="Company.showShareholders('public', 'quarterly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>44.50%</td>
            
              <td>14.87%</td>
            
              <td>14.78%</td>
            
              <td>14.52%</td>
            
              <td>14.41%</td>
            
              <td>14.68%</td>
            
              <td>14.71%</td>
            
              <td>15.23%</td>
            
              <td>15.47%</td>
            
              <td>14.83%</td>
            
              <td>14.88%</td>
            
              <td>14.90%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>28,38,036</td><td>28,96,358</td><td>29,36,692</td><td>29,30,527</td><td>30,13,793</td><td>32,74,360</td><td>33,35,815</td><td>36,48,537</td><td>37,56,541</td><td>35,68,560</td><td>37,10,169</td><td>36,47,886</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>

<div id="yearly-shp" class="hidden">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Mar 2017</th><th>Mar 2018</th><th>Mar 2019</th><th>Mar 2020</th><th>Mar 2021</th><th>Mar 2022</th><th>Mar 2023</th><th>Mar 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=foreign_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('foreign_institutions', 'yearly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>20.08%</td>
            
              <td>18.00%</td>
            
              <td>17.04%</td>
            
              <td>14.65%</td>
            
              <td>12.79%</td>
            
              <td>11.99%</td>
            
              <td>43.35%</td>
            
              <td>40.95%</td>
            
              <td>39.87%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=domestic_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('domestic_institutions', 'yearly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>35.79%</td>
            
              <td>37.10%</td>
            
              <td>38.20%</td>
            
              <td>42.46%</td>
            
              <td>42.50%</td>
            
              <td>42.77%</td>
            
              <td>42.08%</td>
            
              <td>43.76%</td>
            
              <td>45.19%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=government plausible-event-period=yearly"
                      onclick="Company.showShareholders('government', 'yearly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
              <td>0.04%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=public plausible-event-period=yearly"
                      onclick="Company.showShareholders('public', 'yearly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>44.13%</td>
            
              <td>44.90%</td>
            
              <td>44.76%</td>
            
              <td>42.89%</td>
            
              <td>44.71%</td>
            
              <td>45.24%</td>
            
              <td>14.52%</td>
            
              <td>15.23%</td>
            
              <td>14.90%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>5,47,642</td><td>7,95,843</td><td>8,57,729</td><td>13,02,214</td><td>21,96,475</td><td>28,40,964</td><td>29,30,527</td><td>36,48,537</td><td>36,47,886</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>


<p class="sub small">
  * The classifications might have changed from Sep'2022 onwards.
  <span class="has-tooltip">
    <i class="icon-info"></i>
    <span class="tooltip" style="width: 300px">
      The new XBRL format added more details from Sep'22 onwards.
      <br>
      <br>
      Classifications such as banks and foreign portfolio investors were not available earlier. The sudden changes in FII or DII can be because of these changes.
      <br>
      <br>
      Click on the line-items to see the names of individual entities.
    </span>
  </span>
</p>


    </section>

    <section id="documents" class="card card-large">
      <div class="flex flex-space-between margin-bottom-16">
        <h2 class="margin-0">Documents</h2>
      </div>

      <div class="flex-row flex-gap-small">
        
          <div class="documents flex-column" style="flex: 2 1 250px;">
            <h3 class="margin-bottom-8">Announcements</h3>

            <div class="show-more-box" style="flex-basis: 300px">
              <div id="company-announcements-tab">
                <div class="flex">
  <div class="options small" style="margin-bottom: 0">
    <button
      type="button"
      class="active"
      
      onclick="Utils.ajaxLoad(event, '/announcements/recent/1552/')"
      data-swap="#company-announcements-tab"
    >
      Recent
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/important/1552/')"
      data-swap="#company-announcements-tab"
    >
      Important
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/search/1552/')"
      data-swap="#company-announcements-tab"
    >
      Search
    </button>
    <a class="button" href="https://www.bseindia.com/stock-share-price/itc-ltd/itc/500875/corp-announcements/" target="_blank" rel="noopener noreferrer">
      All
      <i class="icon-link-ext smaller"></i>
    </a>
  </div>
</div>


  
  <ul class="list-links">
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=b87f7739-5519-444f-abcd-76bbf34c288c.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Newspaper Publication

          
            <div class="ink-600 smaller">4h - Publication of Notice regarding Transfer of unclaimed Dividend and Ordinary Shares to the Investor Education and Protection Fund.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=2f438bb1-bf4a-461d-b12a-01867db3cca8.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Credit Rating

          
            <div class="ink-600 smaller">1d - ITC receives strong ESG rating of 72 and excellent disclosure score from Crisil on May 15, 2025.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=4ace68dd-07b8-4221-90d9-f4fe76f5e5ca.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Divestment : Delectable Technologies Private Limited, Associate Company

          
            <div class="ink-600 smaller">2d - ITC divested entire 39.32% stake in Delectable Technologies for ₹1.01 lakh on 13 May 2025.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=83cbfc85-bbfe-48e5-80f4-f3539e6633ce.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Compliances-Reg.24(A)-Annual Secretarial Compliance

          
            <div class="ink-600 smaller">12 May - ITC&#x27;s Annual Secretarial Compliance Report confirms full SEBI compliance for FY 2024-25, no non-compliances.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=4b05db99-6b66-471f-8270-1e13776d369f.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Board Meeting Intimation for Board Meeting On 22Nd May, 2025

          
            <div class="ink-600 smaller">8 May - Board meeting on 22 May to approve FY25 audited results and consider final dividend.</div>
          
        </a>
      </li>
    
  </ul>




              </div>
            </div>
          </div>
        

        <div class="documents annual-reports flex-column"
             style="flex: 1 1 200px;
                    max-width: 210px">
          <h3>Annual reports</h3>
          <div class="show-more-box">
            
<ul class="list-links">

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=f0d1e906-3c13-4993-ad06-e04ad1d3edf9.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2024
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=\139aec7a-7a7c-458f-ac5a-29f6dbe67c93.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2023
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500875/***********.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2022
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500875/68921500875.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2021
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500875/5008750320.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2020
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500875/5008750319.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2019
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500875/5008750318.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2018
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500875/5008750317.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2017
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500875/5008750316.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2016
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500875/5008750315.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2015
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500875/5008750314.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2014
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://archives.nseindia.com/annual_reports/AR_78_ITC_2012_2013_14062013150845.zip" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2013
      <div class="ink-600 smaller">
        from nse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500875/5008750313.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2013
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500875/5008750312.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2012
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://archives.nseindia.com/annual_reports/AR_ITC_2011_2012_19062012102526.zip" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2012
      <div class="ink-600 smaller">
        from nse
      </div>
    </a>
  </li>

</ul>


          </div>
          
        </div>

        
          <div class="documents credit-ratings flex-column"
               style="flex: 1 1 200px;
                      max-width: 220px">
            <h3>Credit ratings</h3>
            <div class="show-more-box">
              
<ul class="list-links">

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/ITCLimited_April 09_ 2025_RR_366545.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        9 Apr from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=131524" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        29 Nov 2024 from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/ITCLimited_September 26_ 2024_RR_353346.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        26 Sep 2024 from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=123920" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        30 Nov 2023 from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/ITCLimited_August 25, 2023_RR_326644.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        25 Aug 2023 from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/ITCLimited_May 30, 2022_RR_285350.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        30 May 2022 from crisil
      </div>
    </a>
  </li>

</ul>


            </div>
          </div>
        

        
          <div class="documents concalls flex-column"
               style="flex: 2 1 250px;
                      max-width: fit-content">
            <div class="flex flex-space-between flex-align-center margin-bottom-8">
              <h3 class="margin-0">Concalls</h3>
              <button class="a font-size-12 font-weight-500 sub"
                      onclick="Modal.openInModal(event)"
                      data-url="/concalls/add-1552/"
                      data-title="ITC - Add Link">
                <i class="icon-flag"></i>
                <span class="underline-link">Add Missing</span>
              </button>
            </div>
            <div class="show-more-box"><ul class="list-links">

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2025
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=134d99b1-1215-47db-975d-039499f7de17.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Dec 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/xml-data/corpfiling/AttachHis/e143ba8a-59aa-4e63-a006-959b3ce87ff4.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d69065ea-9ed8-4c10-8c73-8a808e67b8af.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.itcportal.com/investor/pdf/ITC-Quarterly-Result-Presentation-Q1-FY2025.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      May 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=454583fe-e8a2-460f-ba6c-a1a1a18cb212.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.itcportal.com/investor/pdf/ITC-Quarterly-Result-Presentation-Q3-FY2024.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=15fd926d-385d-48cb-b633-2881957ea931.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=48d23504-9689-4992-84d0-3fa3d87f1292.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=cd6cc39f-c7ae-4992-a507-cf03c161331c.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=dd8c529d-569d-48b3-a6a5-20775e32469a.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      May 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d3fdc92d-c38c-47e8-970f-3a3cd3029ad3.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=fcf710b0-8cea-4618-b926-529b0b507173.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Dec 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=b85d79fe-a90f-4658-a40f-694c2129b575.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=130deaf2-2b61-45d8-a8ef-f32fc5101725.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=e5729d2a-7bba-4fa9-aee5-e54e02af782a.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      May 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=9acadc1b-6a34-4f24-9a60-beae911c443f.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=ce674fb0-b609-40ba-ba6d-a83a52ecb99f.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Dec 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=cadd3c07-476f-48d0-a4d2-4191b0526c6f.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=7e129185-3494-413a-bf72-82dafec0466b.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=86bb35bf-feea-4dde-81b0-3d76fd7b8ac1.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=28198f4c-275e-41f7-86ae-7762089286b1.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jun 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=8708013f-f5bf-43a2-b5ac-5697dcd7b1d5.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jun 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=0e7c9402-6b1a-44fd-8f94-0c40d68a9f73.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=00d9271e-d28e-41af-8e44-c261de2e56a8.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=20533893-3b74-46d2-a05b-f4056bc43fa7.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Dec 2020
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=2603809a-76e9-4152-ac56-8272825a9ad7.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Nov 2020
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=1c378d93-a1bd-4728-b6e1-5c876a6c8b4e.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Sep 2020
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=582a7a17-ead9-45cd-9eb2-2524f42b99e3.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jun 2019
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=83ea3cbd-781f-4e97-b0d5-f2065db99dec.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

</ul>
</div>
          </div>
        
      </div>
    </section>
    <!-- cache time 2025-05-16 18:56:41 -->
    
  

      </main>
    

    
      

<footer id="large-footer">
  <div class="flex-row flex-space-between flex-column-tablet container no-print">
    <div class="hide-from-tablet" style="margin-bottom: 16px;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
    </div>

    <div class="show-from-tablet" style="padding: 0 64px 0 0;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
      <p class="font-size-19" style="font-weight: 500;">Stock analysis and screening tool</p>
      <p class="sub">
        Mittal Analytics Private Ltd &copy; 2009-2025
        <br>
        Made with <i class="icon-heart red"></i> in India.
      </p>
      <p class="sub font-size-13">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="font-size-13 sub">
        <a href="/guides/terms/" class="underline-link">Terms</a> & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>

    <div class="flex flex-end flex-space-between flex-grow"
         style="max-width: 600px">
      <div class="flex-grow">
        <div class="title">Product</div>
        <ul class="items">
          <li>
            <a href="/premium/?driver=footer">Premium</a>
          </li>
          <li>
            <a href="/docs/changelog/">What's new?</a>
          </li>
          <li>
            <a href="https://bit.ly/learnscreener">Learn</a>
          </li>
          <li>
            <button class="a2hs button-secondary button-small">
              <i class="icon-flash"></i>
              Install
            </button>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Team</div>
        <ul class="items">
          <li>
            <a href="/guides/about-us/">About us</a>
          </li>
          <li>
            <a href="/support/">Support</a>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Theme</div>
        <ul class="items">
          <li>
            <button onclick="SetTheme('light')"
                    aria-label="Set light theme"
                    class="button-plain">
              <i class="icon-sun"></i>
              Light
            </button>
          </li>
          <li>
            <button onclick="SetTheme('dark')"
                    aria-label="Set dark theme"
                    class="button-plain">
              <i class="icon-moon"></i>
              Dark
            </button>
          </li>
          <li>
            <button onclick="SetTheme('auto')"
                    aria-label="Set auto theme"
                    class="button-plain">
              <i class="icon-monitor"></i>
              Auto
            </button>
          </li>
        </ul>
      </div>
    </div>

    <div class="hide-from-tablet">
      <hr>
      <p class="sub">Mittal Analytics Private Ltd &copy; 2009-2024</p>
      <p class="sub">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="sub">
        <a href="/guides/terms/" class="underline-link">Terms</a>
        & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>
  </div>
</footer>

    

    <!-- Important JS (which provide global objects)- check below too -->
    <script src="https://cdn-static.screener.in/js/dialog-polyfill.min.2b5bf9a032d2.js"></script>
    <script src="https://cdn-static.screener.in/js/utils.02a9ce812e49.js"></script>
    <script src="https://cdn-static.screener.in/js/modal.4f872e091355.js"></script>
    <script src="https://cdn-static.screener.in/js/typeahead.8dcc9ac93685.js"></script>
    <script src="https://cdn-static.screener.in/js/filter.component.5add9ef3b7a9.js"></script>

    <!-- Independent JS scripts for page interactions -->
    <script src="https://cdn-static.screener.in/js/navbar.3fe7f0f4a630.js"></script>
    <script src="https://cdn-static.screener.in/js/company.search.e48f94642999.js"></script>
    
  <script src="https://cdn-static.screener.in/js/scroll-aid.5ed2d38a133e.js"></script>
  <script src="https://cdn-static.screener.in/js/company.customisation.d7c717b0de15.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.min.10a29a29621c.js"></script>
  <script src="https://cdn-static.screener.in/js/chartjs-adapter-date-fns.bundle.min.ebae23730a6e.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.e20c5f42613a.js"></script>
  <script src="https://cdn-static.screener.in/js/segment-result.b6af4191fd6f.js"></script>


    <!-- Service worker installer for PWA -->
    <script src="https://cdn-static.screener.in/js/pwa.933118758337.js"></script>
  </body>
</html>
