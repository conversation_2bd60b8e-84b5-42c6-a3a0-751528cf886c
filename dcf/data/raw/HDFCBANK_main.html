
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    
    <title>HDFC Bank Ltd share price | About HDFC Bank | Key Insights - Screener</title>
    

    <!-- plausible -->
    <script defer
            event-user="unregistered"
            
            
            data-domain="screener.in"
            src="https://plausible.screener.in/js/script.pageview-props.tagged-events.js"></script>
    <script>window.plausible = window.plausible || function () { (window.plausible.q = window.plausible.q || []).push(arguments) }</script>

    <!-- FONT -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/inter/inter.43d4904a3137.css" />
    <link rel="stylesheet" href="https://cdn-static.screener.in/fontello/css/fontello.bc317748a65f.css" />

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/normalize.a2444750a947.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/skeleton.9aa9c32992ef.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/custom.39beec5e86b6.css" media="all">

    
    
    
  

  
  <meta name="description" content="HDFC Bank · Mkt Cap: 14,81,166 Crore (up 32.5% in 1 year) · Revenue: 3,36,367 Cr · Profit: 73,440 Cr · Stock is trading at 2.86 times its book value · Company has low interest coverage ratio.">
  

  <style>
  tr[data-row-company-id="1298"] {
    font-weight: 500;
  }

  tr[data-row-company-id="1298"] a {
    color: var(--ink-900);
  }

  :target {
    scroll-margin-top: 90px;
  }

  @supports not (scroll-margin-top: 90px) {
    :target {
      padding-top: 108px;
    }
  }

  @media (min-width: 992px) {
    nav {
      margin-bottom: 0;
    }

    .company-nav {
      top: 50px;
      margin-bottom: 24px;
    }
  }
  </style>


    <meta name="author" content="Mittal Analytics Private Limited">

    <!-- PWA manifest -->
    <meta name="theme-color"
          content="#fff"
          media="(prefers-color-scheme: light)">
    <meta name="theme-color"
          content="#222530"
          media="(prefers-color-scheme: dark)">
    <link rel="manifest" href="/static/manifest.json">

    <!-- favicons -->
    <link rel="apple-touch-icon"
          sizes="180x180"
          href="https://cdn-static.screener.in/favicon/apple-touch-icon.b6e5f24bf7d3.png">
    <link rel="icon"
          type="image/png"
          sizes="32x32"
          href="https://cdn-static.screener.in/favicon/favicon-32x32.00205914303a.png">
    <link rel="icon"
          type="image/png"
          sizes="16x16"
          href="https://cdn-static.screener.in/favicon/favicon-16x16.2b35594b2f33.png">
    <link rel="mask-icon"
          href="https://cdn-static.screener.in/favicon/safari-pinned-tab.adbe3ed3c5ad.svg"
          color="#8ED443">
    <meta name="msapplication-TileColor" content="#da532c">
  </head>

  <body class="light flex-column">
    
      <nav class="u-full-width no-print">
        <div id="mobile-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

<ul class="bottom-menu-bar hide-from-tablet-landscape">
  
    <li class="">
      <a href="/">
        <i class="icon-home"></i>
        <br>
        Home
      </a>
    </li>
  

  <li class="">
    <a href="/explore/">
      <i class="icon-screens"></i>
      <br>
      Screens
    </a>
  </li>

  <li>
    <button type="button"
            onclick="MobileMenu.toggleSearch(this)"
            class="search-button"
            title="Search">
      <i class="icon-search" style="font-size: 18px"></i>
    </button>
  </li>

  <li>
    <button class="button-plain" onclick="Modal.showInModal('#nav-tools-menu')">
      <i class="icon-tools"></i>
      <br>
      Tools
    </button>
  </li>

  
    <li class="">
      <a href="/register/?">
        <i class="icon-user-plus"></i>
        <br>
        Login
      </a>
    </li>
  
</ul>

        <div class="top-nav-holder">
          <div class="container">



<div class="layer-holder top-navigation">
  <div class="layer flex flex-space-between"
       style="align-items: center;
              height: 50px">
    <div class="flex" style="align-items: center">
      <a href="/" class="logo-holder">
        <img alt="Screener Logo"
             class="logo"
             src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg" />
      </a>

      <div class="desktop-links" style="margin-left: 48px">
        <a href="/">
          
            Home
          
        </a>
        <a href="/explore/">Screens</a>
        <div class="dropdown-menu">
          <button class="button-plain"
                  type="button"
                  onclick="Utils.toggleDropdown(event)">
            Tools
            <i class="icon-down"></i>
          </button>
          <ul class="dropdown-content flex-column tools-menu"
              style="width: 350px"
              id="nav-tools-menu">
            <li>
              <a href="/screen/new/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/screens.e960a5daedf9.svg" alt="Create a stock screen">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Create a stock screen</div>
                  <div class="sub font-size-13">Run queries on 10 years of financial data</div>
                </div>
              </a>
            </li>
            <li class="flex flex-align-center no-hover" style="padding: 4px 0;">
              <div class="button button-primary premium-indicator flex flex-align-center">
                <img src="https://cdn-static.screener.in/icons/crown.fb0d2413ee43.svg"
                     alt="Premium icon"
                     style="margin-right: 8px">
                <span>Premium features</span>
              </div>
              <hr class="flex-grow" style="padding: 0; margin: 0;">
            </li>
            <li>
              <a href="/hs/" class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/export.d623f0029933.svg" alt="Commodity Prices">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Commodity Prices</div>
                  <div class="sub font-size-13">See prices and trends of over 10,000 commodities</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/people/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/shares.10acc20cb936.svg"
                       alt="Search shareholders by name">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Search shareholders</div>
                  <div class="sub font-size-13">See companies where a person holds over 1% of the shares</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/announcements/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/loudspeaker.88769927f6eb.svg" alt="Latest Announcements">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Latest Announcements</div>
                  <div class="sub font-size-13">Browse, filter and set alerts for announcements.</div>
                </div>
              </a>
            </li>
            
              <li style="padding: 16px;">
                <a href="/premium/?driver=top-nav-tools"
                   class="button button-primary text-align-center"
                   style="color: var(--white);
                          padding: 10px 24px">Upgrade to premium</a>
              </li>
            
          </ul>
        </div>
      </div>
    </div>

    <div class="show-from-tablet-landscape flex flex-gap-16"
         style="justify-content: flex-end;
                flex: 1 1 400px">
      <div class="search" id="desktop-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

      
        <div class="flex flex-gap-8" style="margin: 4px">
          <a class="button account"
             href="/login/?">
            <i class="icon-user-line blue-icon"></i>
            Login
          </a>
          <a class="button account button-secondary"
             href="/register/?">Get free account</a>
        </div>
      
    </div>
  </div>
</div>
</div>
        </div>
      </nav>
    

    
  <div class="bg-base sticky company-nav">
    <div class="flex flex-space-between container hide-from-tablet-landscape">
      <h1 class="h2 shrink-text" style="margin: 0.5em 0">HDFC Bank Ltd</h1>
      <div class="flex flex-align-center gap-8">
        <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/1298/"
        data-title="HDFC Bank Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

        
      </div>
    </div>

    <div class="sub-nav-holder">
      <div class="sub-nav">
        <a href="#top">
          <span class="shrink-text show-from-tablet-landscape">HDFC Bank</span>
          <span class="hide-from-tablet-landscape">Summary</span>
        </a>
        <a href="#chart">Chart</a>
        <a href="#analysis">Analysis</a>
        <a href="#peers">Peers</a>
        <a href="#quarters">Quarters</a>
        <a href="#profit-loss">Profit &amp; Loss</a>
        <a href="#balance-sheet">Balance Sheet</a>
        <a href="#cash-flow">Cash Flow</a>
        <a href="#ratios">Ratios</a>
        <a href="#shareholding">Investors</a>
        <a href="#documents">Documents</a>
        <div class="flex-grow flex flex-align-center gap-8 show-from-tablet-landscape">
          <div class="flex-grow"></div>
          <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/1298/"
        data-title="HDFC Bank Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

          
        </div>
      </div>
    </div>
  </div>


    
      <main class="flex-grow container">
        



  






        
        <div class="breadcrumb hidden-if-empty"></div>
        

        
  <div data-company-id="1298"
       data-warehouse-id="6596237"
       
       data-consolidated="true"
       id="company-info"></div>

  <div class="card card-large" id="top">
    <div class="flex flex-space-between flex-gap-8">
      <div class="flex-row flex-wrap flex-align-center flex-grow"
           style="flex-basis: 300px">
        <h1 class="margin-0 show-from-tablet-landscape">HDFC Bank Ltd</h1>
        <div class="ink-600 show-from-tablet-landscape" style="opacity: 0.5;">
          <i class="icon-dot"></i>
        </div>
        <div class="font-size-18 strong line-height-14">
          <div class="flex flex-align-center">
            <span>₹ 1,935</span>
            
              <span class="font-size-12 up margin-left-4">
                <i class="icon-circle-up"></i>
                0.05%
              </span>
            
          </div>
          <div class="ink-600 font-size-11 font-weight-500">
            
              16 May
              
                - close price
              
            
          </div>
        </div>
      </div>

      <form method="post" class="flex margin-0" style="align-items: center">
        <input type="hidden" name="csrfmiddlewaretoken" value="Lhk75WDipnaoINkZdFUlPZYCDKQ3GIufVoUFCQYZQ03KSDF8THKHXzfM4Vc0qhpd">
        <input type="hidden" name="next" value="/company/HDFCBANK/consolidated/">

        
          <button class="margin-right-8 plausible-event-name=Excel+Company plausible-event-user=unregistered"
                  formaction="/user/company/export/6596237/"
                  aria-label="Export to Excel">
            <i class="icon-download"></i>
            <span class="hide-on-mobile">Export to Excel</span>
          </button>
        

        <div class="flex dropdown-filter">
          
            <button class="button-primary "
                    formaction="/api/company/1298/add/">
              <i class="icon-plus"></i> Follow
            </button>
          

          
        </div>
      </form>
    </div>

    
    <div class="company-links show-from-tablet-landscape"
         style="font-weight: 500;
                margin-top: 8px">
      
        <a href="http://www.hdfcbank.com"
           target="_blank"
           rel="noopener noreferrer">
          <i class="icon-link"></i>
          <span style="font-size: 1.8rem;">hdfcbank.com</span>
        </a>
      

      
        <a href="https://www.bseindia.com/stock-share-price/hdfc-bank-ltd/HDFCBANK/500180/" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            BSE:
            500180
          </span>
        </a>
      

      
        <a href="https://www.nseindia.com/get-quotes/equity?symbol=HDFCBANK" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            NSE:
            HDFCBANK
          </span>
        </a>
      
    </div>
    

    <div class="company-info">
      
      <div class="company-profile">
        <div class="flex flex-column" style="flex: 1 1;">
          <div class="title">About</div>
          <div class="sub show-more-box about" style="flex-basis: 100px"><p>HDFC Bank Limited (also known as HDFC) is an Indian banking and financial services company headquartered in Mumbai. It is India's largest private sector bank by assets and the world's tenth-largest bank by market capitalization as of May 2024.<br>
As of April 2024, HDFC Bank has a market capitalization of $145 billion, making it the third-largest company on the Indian stock exchanges. <sup><a href="https://en.wikipedia.org/wiki/HDFC_Bank" target="_blank" rel="noopener noreferrer">[1]</a></sup></p></div>

          
            <div class="title">Key Points</div>
            <div class="sub commentary always-show-more-box">
              <p><strong>Market Position</strong><br>
The company is India's one of 3 systemically important banks with a <strong>15% market share</strong> in the banking sector’s advances and a <strong>37% market share</strong> in the private sector banks’ advances as of FY24. It is also the <strong>second-largest bank in India</strong>. <sup><a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=128559#page=2" target="_blank" rel="noopener noreferrer">[1]</a></sup> It is among the top 3 Government Banks collecting direct and indirect taxes for the Government of India. <sup><a href="https://www.bseindia.com/xml-data/corpfiling/AttachHis/2160dea5-d49e-4409-83a4-c2b111a3bd12.pdf#page=82" target="_blank" rel="noopener noreferrer">[2]</a></sup> <strong>It is a market leader in almost every asset category</strong>. <sup><a href="https://www.bseindia.com/xml-data/corpfiling/AttachHis/2160dea5-d49e-4409-83a4-c2b111a3bd12.pdf#page=55" target="_blank" rel="noopener noreferrer">[3]</a></sup></p>
              <div class="show-more-button" style="height: 20px"></div>
            </div>

            <button class="a upper font-weight-500 letter-spacing plausible-event-name=Key+Insight plausible-event-user=unregistered"
                    style="font-size: 1.4rem"
                    type="button"
                    onclick="Modal.openInModal(event)"
                    data-title="HDFC Bank Ltd"
                    data-url="/wiki/company/1298/commentary/v2/"
                    data-is-right-modal="true">
              Read More <i class="icon-right"></i>
            </button>
          
        </div>

        <div class="company-links hide-from-tablet-landscape margin-top-20"
             style="font-weight: 500">
          
            <a href="http://www.hdfcbank.com"
               target="_blank"
               rel="noopener noreferrer">
              <i class="icon-link"></i>
              <span style="font-size: 1.6rem;">Website</span>
            </a>
          

          
            <a href="https://www.bseindia.com/stock-share-price/hdfc-bank-ltd/HDFCBANK/500180/" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">BSE</span>
            </a>
          

          
            <a href="https://www.nseindia.com/get-quotes/equity?symbol=HDFCBANK" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">
                NSE
                
              </span>
            </a>
          
        </div>
      </div>
      

      <div class="company-ratios">
        <ul id="top-ratios">
          


  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Market Cap
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">14,81,166</span>
        
          Cr.
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Current Price
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">1,935</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        High / Low
      
    </span>

    
      <span class="nowrap value">₹ <span class="number">1,979</span> / <span class="number">1,446</span></span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Stock P/E
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">20.9</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Book Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">677</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Dividend Yield
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">1.01</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROCE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">7.51</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">14.5</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Face Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">1.00</span>
        
      </span>
    
  </li>






        </ul>

        <div style="margin-top: 32px;">
          <label for="quick-ratio-search">Add ratio to table</label>

          <div class="flex-row flex-space-between flex-baseline flex-gap-16">
            <div class="dropdown-typeahead flex-grow" style="max-width: 480px;">
              <input id="quick-ratio-search"
                     type="text"
                     placeholder="eg. Promoter holding"
                     style="width: 100%">
            </div>

            
            <a href="/user/quick_ratios/?next=/company/HDFCBANK/consolidated/"
               class="smaller">
              <i class="icon-pencil"></i>
              <span class="upper font-weight-500 letter-spacing">Edit ratios</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  
    <section id="chart" class="card card-large" style="margin-bottom: 2.5rem">
      <div id="chart-menu"
     class="flex flex-space-between flex-wrap"
     style="margin-bottom: 16px">
  <div class="options" id="company-chart-days">
    <button name="days"
            value="30"
            onclick="CompanyChart.setActive(event)"
            type="button">1M</button>
    <button name="days"
            value="180"
            onclick="CompanyChart.setActive(event)"
            type="button">6M</button>
    <button name="days"
            value="365"
            onclick="CompanyChart.setActive(event)"
            type="button">1Yr</button>
    <button name="days"
            value="1095"
            onclick="CompanyChart.setActive(event)"
            type="button">3Yr</button>
    <button name="days"
            value="1825"
            onclick="CompanyChart.setActive(event)"
            type="button">5Yr</button>
    <button name="days"
            value="3652"
            onclick="CompanyChart.setActive(event)"
            type="button">10Yr</button>
    <button name="days"
            value="10000"
            onclick="CompanyChart.setActive(event)"
            type="button">Max</button>
  </div>

  <div class="flex margin-bottom-24">
    <div class="options margin-right-8"
         id="company-chart-metrics"
         style="margin-bottom: 0">
      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Price"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price-DMA50-DMA200-Volume"
                  type="button">Price</button>
        

      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PE"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price to Earning-Median PE-EPS"
                  type="button">PE Ratio</button>
        

      

        

      

        

      

        

      

        

      
      <button class="hidden"
              id="company-chart-metrics-more-active"
              onclick="CompanyChart.setActive(event)"
              name="metrics"
              value=""
              type="button">Hidden</button>
      <div class="dropdown-menu" id="metrics-dropdown">
        <button type="button"
                class="button-plain"
                onclick="Utils.toggleDropdown(event)">
          More <i class="icon-down"></i>
        </button>
        <ul class="dropdown-content" style="max-width: 160px; right: 0">
          

            

          

            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="GPM-OPM-NPM-Quarter Sales"
                        type="button">Sales &amp; Margin</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=EV-EBITDA"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="EV Multiple-Median EV Multiple-EBITDA"
                        type="button">EV / EBITDA</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PBV"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Price to book value-Median PBV-Book value"
                        type="button">Price to Book</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Market+Cap+to+Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Market Cap to Sales-Median Market Cap to Sales-Sales"
                        type="button">Market Cap / Sales</button>
              </li>
            

          
        </ul>
      </div>
    </div>
    
      <button type="button"
              class="button-small button-secondary flex flex-align-center plausible-event-name=Stock+Alert"
              style="border-color: var(--button-border);
                     border-radius: 3px"
              data-title="Alerts for HDFC Bank Ltd"
              onclick="Modal.openInModal(event)"
              data-url="/alerts/stock-6596237/"
              aria-label="Add alerts for company">
        <i class="icon-bell"></i>
        
        <span class="margin-left-4 normal-case letter-spacing-0">Alerts</span>
      </button>
    
  </div>
</div>

<div class="flex no-select">
  <div class="hide-on-mobile chart-label">
    <div id="chart-label-left"></div>
  </div>

  <div id="chart-area" style="flex-grow: 1">
    <div id="chart-info" class="invisible">
      <div id="chart-crosshair"></div>
      <div id="chart-tooltip" class="font-size-14">
        <div id="chart-tooltip-meta" class="ink-700 font-size-12"></div>
        <div class="font-weight-500 font-size-15" id="chart-tooltip-title"></div>
        <div id="chart-tooltip-info"></div>
      </div>
    </div>
    <div id="canvas-chart-holder" style="height: 375px"></div>
  </div>

  <div class="hide-on-mobile chart-label">
    <div id="chart-label-right"></div>
  </div>
</div>
<div id="chart-legend"></div>

    </section>

    
    <section id="analysis" class="card card-large">
      <div class="flex flex-column-mobile flex-gap-32">
        <div class="pros" style="flex-basis: 50%;">
          <p class="title">Pros</p>
          <ul>
            <li>Company has delivered good profit growth of 21.0% CAGR over last 5 years</li><li>Company has been maintaining a healthy dividend payout of 23.3%</li><li>Company&#x27;s median sales growth is 16.4% of last 10 years</li>
          </ul>
        </div>
        <div class="cons" style="flex-basis: 50%;">
          <p class="title">Cons</p>
          <ul>
            <li>Stock is trading at 2.86 times its book value</li><li>Company has low interest coverage ratio.</li><li>Contingent liabilities of Rs.24,09,821 Cr.</li><li>Earnings include an other income of Rs.1,34,548 Cr.</li>
          </ul>
        </div>
      </div>
      <p class="sub font-size-14 margin-top-16 margin-bottom-0">
        <span style="vertical-align: sub;">*</span>
        <span class="font-weight-500">The pros and cons are machine generated.</span>
        <span class="has-tooltip">
          <i class="icon-info"></i>
          <span class="tooltip" style="width: 300px; left: -100px;">
            Pros / cons are based on a checklist to highlight important points. Please exercise caution and do your own analysis.
          </span>
        </span>
      </p>
    </section>

    <section id="peers" class="card card-large">
      <div class="flex flex-space-between" style="align-items: center;">
        <div>
          <h2>Peer comparison</h2>

          
            <p class="sub">
              Sector:
              <a href="/company/compare/********/"
                 target="_blank">Banks</a>
              <span style="margin: 16px"></span>
              Industry:
              <a href="/company/compare/********/********/"
                 target="_blank">Banks - Private Sector</a>
            </p>
          

          
            <p class="flex flex-wrap gap-8 flex-align-center line-height-1 sub"
               id="benchmarks">
              Part of
              
                <a href="/company/1001/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Sensex"
                   target="_blank">BSE Sensex</a>
              
                <a href="/company/NIFTY/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50"
                   target="_blank">Nifty 50</a>
              
                <a href="/company/1005/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+500"
                   target="_blank">BSE 500</a>
              
                <a href="/company/1002/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100"
                   target="_blank">BSE 100</a>
              
                <a href="/company/1003/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+200"
                   target="_blank">BSE 200</a>
              
                <a href="/company/1004/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dollex+200"
                   target="_blank">BSE Dollex 200</a>
              
                <a href="/company/CNX500/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500"
                   target="_blank">Nifty 500</a>
              
                <a href="/company/1015/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+BANKEX"
                   target="_blank">BSE BANKEX</a>
              
                <a href="/company/BANKNIFTY/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Bank"
                   target="_blank">Nifty Bank</a>
              
                <a href="/company/CNX100/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100"
                   target="_blank">Nifty 100</a>
              
                <a href="/company/CNXSERVICE/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Services+Sector"
                   target="_blank">Nifty Services Sector</a>
              
                <a href="/company/CNX200INDE/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+200"
                   target="_blank">Nifty 200</a>
              
                <a href="/company/CNXFINANCE/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Financial+Services"
                   target="_blank">Nifty Financial Services</a>
              
                <a href="/company/NFT100EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Equal+Weight"
                   target="_blank">Nifty 100 Equal Weight</a>
              
                <a href="/company/1153/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Allcap"
                   target="_blank">BSE Allcap</a>
              
                <a href="/company/1155/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+LargeCap"
                   target="_blank">BSE LargeCap</a>
              
                <a href="/company/1162/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Financial+Services"
                   target="_blank">BSE Financial Services</a>
              
                <a href="/company/1125/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+SENSEX+50"
                   target="_blank">BSE SENSEX 50</a>
              
                <a href="/company/NIFTYQLY30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty100+Quality+30"
                   target="_blank">Nifty100 Quality 30</a>
              
                <a href="/company/NIFPVTBANK/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Private+Bank"
                   target="_blank">Nifty Private Bank</a>
              
                <a href="/company/1017/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100+ESG+Index+(INR)"
                   target="_blank">BSE 100 ESG Index (INR)</a>
              
                <a href="/company/1181/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+250+LargeMidCap+Index"
                   target="_blank">BSE 250 LargeMidCap Index</a>
              
                <a href="/company/1188/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Diversified+Financials+Revenue+Growth+Index+(I"
                   target="_blank">BSE Diversified Financials Revenue Growth Index (I</a>
              
                <a href="/company/1018/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Private+Banks+Index"
                   target="_blank">BSE Private Banks Index</a>
              
                <a href="/company/1183/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dividend+Stability+Index"
                   target="_blank">BSE Dividend Stability Index</a>
              
                <a href="/company/1185/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Low+Volatility+Index"
                   target="_blank">BSE Low Volatility Index</a>
              
                <a href="/company/NFT50EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50+Equal+Weight"
                   target="_blank">Nifty 50 Equal Weight</a>
              
                <a href="/company/NFT100LV30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Low+Volatility+30"
                   target="_blank">Nifty 100 Low Volatility 30</a>
              
                <a href="/company/1130/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100+LargeCap+TMC+Index"
                   target="_blank">BSE 100 LargeCap TMC Index</a>
              
                <a href="/company/LMIDCAP250/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+LargeMidcap+250"
                   target="_blank">Nifty LargeMidcap 250</a>
              
                <a href="/company/NFT500MULT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500+Multicap+50:25:25"
                   target="_blank">Nifty 500 Multicap 50:25:25</a>
              
                <a href="/company/NFINSERV25/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Financial+Services+25/50"
                   target="_blank">Nifty Financial Services 25/50</a>
              
                <a href="/company/NIFT100ESG/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty100+ESG"
                   target="_blank">Nifty100 ESG</a>
              
                <a href="/company/NFTYTOTMKT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Total+Market"
                   target="_blank">Nifty Total Market</a>
              
              
                <button type="button"
                        class="a small font-weight-500 plausible-event-name=Show+More+Benchmarks"
                        onclick="Utils.toggleClass('#benchmarks', 'show-hidden')">show all</button>
              
            </p>
          
        </div>

        <div class="flex-reverse">
          <a href="/user/columns/?next=/company/HDFCBANK/consolidated/#peers"
             class="button button-small button-secondary">
            <i class="icon-cog"></i>
            <span class="hide-on-mobile">Edit</span>
            Columns
          </a>
        </div>
      </div>

      <div id="peers-table-placeholder">Loading peers table ...</div>

      <div class="flex-row flex-baseline">
        <label for="search-peer-comparison" style="padding-right: 1rem">Detailed Comparison with:</label>
        <div class="dropdown-typeahead">
          <input type="text"
                 id="search-peer-comparison"
                 placeholder="eg. Infosys"
                 class="small">
        </div>
      </div>
    </section>

    <section id="quarters" class="card card-large">
      
        


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Quarterly Results</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/HDFCBANK/#quarters"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    
      <button data-segment-button-close
              onclick="Segment.closeSegments('quarters')"
              class="button-small hidden">
        <i class="icon-cancel"></i>
        Close Segments
      </button>

      
        <button data-segment-button="1"
                onclick="Segment.showSegment('quarters', '1')"
                class="button-small button-secondary plausible-event-name=Segment+Results plausible-event-user=unregistered">
          <i class="icon-chart-pie"></i>
          Product Segments
        </button>
      
    

    
  </div>
</div>

<div class="responsive-holder hidden" data-segment-table></div>

<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="highlight-cell">
            Mar 2022
            
          </th>
        
          <th class="">
            Jun 2022
            
          </th>
        
          <th class="">
            Sep 2022
            
          </th>
        
          <th class="">
            Dec 2022
            
          </th>
        
          <th class="highlight-cell">
            Mar 2023
            
          </th>
        
          <th class="">
            Jun 2023
            
          </th>
        
          <th class="">
            Sep 2023
            
          </th>
        
          <th class="">
            Dec 2023
            
          </th>
        
          <th class="highlight-cell">
            Mar 2024
            
          </th>
        
          <th class="">
            Jun 2024
            
          </th>
        
          <th class="">
            Sep 2024
            
          </th>
        
          <th class="">
            Dec 2024
            
          </th>
        
          <th class="highlight-cell">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Revenue', 'quarters', this)">
                  Revenue&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">35,574</td>
            
              <td class="">37,274</td>
            
              <td class="">40,930</td>
            
              <td class="">45,002</td>
            
              <td class="highlight-cell">47,548</td>
            
              <td class="">51,168</td>
            
              <td class="">75,039</td>
            
              <td class="">78,008</td>
            
              <td class="highlight-cell">79,434</td>
            
              <td class="">81,546</td>
            
              <td class="">83,002</td>
            
              <td class="">85,040</td>
            
              <td class="highlight-cell">86,779</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="highlight-cell">15,227</td>
            
              <td class="">16,358</td>
            
              <td class="">18,311</td>
            
              <td class="">20,505</td>
            
              <td class="highlight-cell">22,606</td>
            
              <td class="">25,955</td>
            
              <td class="">41,250</td>
            
              <td class="">43,242</td>
            
              <td class="highlight-cell">43,692</td>
            
              <td class="">44,580</td>
            
              <td class="">45,414</td>
            
              <td class="">46,914</td>
            
              <td class="highlight-cell">46,986</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'quarters', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">15,044</td>
            
              <td class="">15,021</td>
            
              <td class="">15,915</td>
            
              <td class="">16,682</td>
            
              <td class="highlight-cell">17,770</td>
            
              <td class="">18,470</td>
            
              <td class="">45,349</td>
            
              <td class="">50,530</td>
            
              <td class="highlight-cell">62,938</td>
            
              <td class="">49,690</td>
            
              <td class="">52,074</td>
            
              <td class="">41,307</td>
            
              <td class="highlight-cell">47,709</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Financing Profit
              
            </td>
            
              <td class="highlight-cell">5,304</td>
            
              <td class="">5,895</td>
            
              <td class="">6,704</td>
            
              <td class="">7,815</td>
            
              <td class="highlight-cell">7,172</td>
            
              <td class="">6,744</td>
            
              <td class="">-11,560</td>
            
              <td class="">-15,764</td>
            
              <td class="highlight-cell">-27,196</td>
            
              <td class="">-12,723</td>
            
              <td class="">-14,487</td>
            
              <td class="">-3,181</td>
            
              <td class="highlight-cell">-7,916</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Financing Margin %
              
            </td>
            
              <td class="highlight-cell">15%</td>
            
              <td class="">16%</td>
            
              <td class="">16%</td>
            
              <td class="">17%</td>
            
              <td class="highlight-cell">15%</td>
            
              <td class="">13%</td>
            
              <td class="">-15%</td>
            
              <td class="">-20%</td>
            
              <td class="highlight-cell">-34%</td>
            
              <td class="">-16%</td>
            
              <td class="">-17%</td>
            
              <td class="">-4%</td>
            
              <td class="highlight-cell">-9%</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'quarters', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">8,386</td>
            
              <td class="">6,929</td>
            
              <td class="">8,252</td>
            
              <td class="">9,121</td>
            
              <td class="highlight-cell">9,610</td>
            
              <td class="">9,853</td>
            
              <td class="">32,528</td>
            
              <td class="">37,007</td>
            
              <td class="highlight-cell">44,958</td>
            
              <td class="">35,450</td>
            
              <td class="">38,455</td>
            
              <td class="">27,154</td>
            
              <td class="highlight-cell">33,489</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="highlight-cell">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="highlight-cell">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="highlight-cell">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="highlight-cell">0</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="highlight-cell">13,690</td>
            
              <td class="">12,823</td>
            
              <td class="">14,956</td>
            
              <td class="">16,936</td>
            
              <td class="highlight-cell">16,783</td>
            
              <td class="">16,597</td>
            
              <td class="">20,967</td>
            
              <td class="">21,243</td>
            
              <td class="highlight-cell">17,761</td>
            
              <td class="">22,727</td>
            
              <td class="">23,968</td>
            
              <td class="">23,973</td>
            
              <td class="highlight-cell">25,573</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="highlight-cell">23%</td>
            
              <td class="">25%</td>
            
              <td class="">25%</td>
            
              <td class="">25%</td>
            
              <td class="highlight-cell">25%</td>
            
              <td class="">25%</td>
            
              <td class="">17%</td>
            
              <td class="">17%</td>
            
              <td class="highlight-cell">-1%</td>
            
              <td class="">24%</td>
            
              <td class="">22%</td>
            
              <td class="">24%</td>
            
              <td class="highlight-cell">25%</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'quarters', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">10,475</td>
            
              <td class="">9,617</td>
            
              <td class="">11,163</td>
            
              <td class="">12,735</td>
            
              <td class="highlight-cell">12,634</td>
            
              <td class="">12,403</td>
            
              <td class="">17,312</td>
            
              <td class="">17,718</td>
            
              <td class="highlight-cell">18,013</td>
            
              <td class="">17,188</td>
            
              <td class="">18,627</td>
            
              <td class="">18,340</td>
            
              <td class="highlight-cell">19,285</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="highlight-cell">18.83</td>
            
              <td class="">17.25</td>
            
              <td class="">19.97</td>
            
              <td class="">22.77</td>
            
              <td class="highlight-cell">22.57</td>
            
              <td class="">22.12</td>
            
              <td class="">22.17</td>
            
              <td class="">22.73</td>
            
              <td class="highlight-cell">23.20</td>
            
              <td class="">21.65</td>
            
              <td class="">23.36</td>
            
              <td class="">23.09</td>
            
              <td class="highlight-cell">24.61</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Gross NPA %
              
            </td>
            
              <td class="highlight-cell"></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class="highlight-cell"></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class="highlight-cell"></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class="highlight-cell"></td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Net NPA %
              
            </td>
            
              <td class="highlight-cell"></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class="highlight-cell"></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class="highlight-cell"></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class=""></td>
            
              <td class="highlight-cell"></td>
            
          </tr>
        
      

      
        <tr class="font-size-14 ink-600">
          <td class="text ink-600">Raw PDF</td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/1298/3/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1298/6/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1298/9/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1298/12/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/1298/3/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1298/6/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1298/9/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1298/12/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/1298/3/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1298/6/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1298/9/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/1298/12/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/1298/3/2025/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
        </tr>
      
    </tbody>
  </table>
</div>

      

      
    </section>

    <section id="profit-loss" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Profit & Loss</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/HDFCBANK/#profit-loss"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    
      <button data-url="/results/rpt/1298/consolidated/"
              data-title="Related Party Transactions"
              onclick="Modal.openInModal(event)"
              class="button-small button-secondary plausible-event-name=Related+party plausible-event-user=unregistered">
        <i class="small icon-users"></i>
        Related Party
      </button>
    

    
      <button data-segment-button-close
              onclick="Segment.closeSegments('profit-loss')"
              class="button-small hidden">
        <i class="icon-cancel"></i>
        Close Segments
      </button>

      
        <button data-segment-button="1"
                onclick="Segment.showSegment('profit-loss', '1')"
                class="button-small button-secondary plausible-event-name=Segment+Results plausible-event-user=unregistered">
          <i class="icon-chart-pie"></i>
          Product Segments
        </button>
      
    

    
  </div>
</div>

<div class="responsive-holder hidden" data-segment-table></div>

<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Revenue', 'profit-loss', this)">
                  Revenue&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">42,555</td>
            
              <td class="">50,666</td>
            
              <td class="">63,162</td>
            
              <td class="">73,271</td>
            
              <td class="">85,288</td>
            
              <td class="">105,161</td>
            
              <td class="">122,189</td>
            
              <td class="">128,552</td>
            
              <td class="">135,936</td>
            
              <td class="">170,754</td>
            
              <td class="">283,649</td>
            
              <td class="">336,367</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="">23,445</td>
            
              <td class="">27,288</td>
            
              <td class="">34,070</td>
            
              <td class="">38,042</td>
            
              <td class="">42,381</td>
            
              <td class="">53,713</td>
            
              <td class="">62,137</td>
            
              <td class="">59,248</td>
            
              <td class="">58,584</td>
            
              <td class="">77,780</td>
            
              <td class="">154,139</td>
            
              <td class="">183,894</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'profit-loss', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">13,508</td>
            
              <td class="">16,164</td>
            
              <td class="">20,055</td>
            
              <td class="">23,856</td>
            
              <td class="">29,532</td>
            
              <td class="">34,856</td>
            
              <td class="">45,459</td>
            
              <td class="">52,457</td>
            
              <td class="">56,557</td>
            
              <td class="">63,042</td>
            
              <td class="">174,196</td>
            
              <td class="">190,780</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Financing Profit
              
            </td>
            
              <td class="">5,602</td>
            
              <td class="">7,214</td>
            
              <td class="">9,037</td>
            
              <td class="">11,374</td>
            
              <td class="">13,374</td>
            
              <td class="">16,592</td>
            
              <td class="">14,593</td>
            
              <td class="">16,848</td>
            
              <td class="">20,795</td>
            
              <td class="">29,932</td>
            
              <td class="">-44,685</td>
            
              <td class="">-38,306</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Financing Margin %
              
            </td>
            
              <td class="">13%</td>
            
              <td class="">14%</td>
            
              <td class="">14%</td>
            
              <td class="">16%</td>
            
              <td class="">16%</td>
            
              <td class="">16%</td>
            
              <td class="">12%</td>
            
              <td class="">13%</td>
            
              <td class="">15%</td>
            
              <td class="">18%</td>
            
              <td class="">-16%</td>
            
              <td class="">-11%</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'profit-loss', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">8,298</td>
            
              <td class="">9,546</td>
            
              <td class="">11,212</td>
            
              <td class="">12,905</td>
            
              <td class="">16,057</td>
            
              <td class="">18,947</td>
            
              <td class="">24,879</td>
            
              <td class="">27,333</td>
            
              <td class="">31,759</td>
            
              <td class="">33,912</td>
            
              <td class="">124,346</td>
            
              <td class="">134,548</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="">689</td>
            
              <td class="">680</td>
            
              <td class="">738</td>
            
              <td class="">886</td>
            
              <td class="">967</td>
            
              <td class="">1,221</td>
            
              <td class="">1,277</td>
            
              <td class="">1,385</td>
            
              <td class="">1,681</td>
            
              <td class="">2,345</td>
            
              <td class="">3,092</td>
            
              <td class="">0</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="">13,211</td>
            
              <td class="">16,079</td>
            
              <td class="">19,511</td>
            
              <td class="">23,393</td>
            
              <td class="">28,464</td>
            
              <td class="">34,318</td>
            
              <td class="">38,195</td>
            
              <td class="">42,796</td>
            
              <td class="">50,873</td>
            
              <td class="">61,498</td>
            
              <td class="">76,569</td>
            
              <td class="">96,242</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="">34%</td>
            
              <td class="">33%</td>
            
              <td class="">34%</td>
            
              <td class="">35%</td>
            
              <td class="">35%</td>
            
              <td class="">35%</td>
            
              <td class="">29%</td>
            
              <td class="">26%</td>
            
              <td class="">25%</td>
            
              <td class="">25%</td>
            
              <td class="">15%</td>
            
              <td class="">24%</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'profit-loss', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">8,768</td>
            
              <td class="">10,703</td>
            
              <td class="">12,821</td>
            
              <td class="">15,317</td>
            
              <td class="">18,561</td>
            
              <td class="">22,446</td>
            
              <td class="">27,296</td>
            
              <td class="">31,857</td>
            
              <td class="">38,151</td>
            
              <td class="">46,149</td>
            
              <td class="">65,446</td>
            
              <td class="">73,440</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="">18.22</td>
            
              <td class="">21.32</td>
            
              <td class="">25.32</td>
            
              <td class="">29.81</td>
            
              <td class="">35.66</td>
            
              <td class="">41.00</td>
            
              <td class="">49.70</td>
            
              <td class="">57.74</td>
            
              <td class="">68.62</td>
            
              <td class="">82.44</td>
            
              <td class="">84.33</td>
            
              <td class="">92.51</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Dividend Payout %
              
            </td>
            
              <td class="">19%</td>
            
              <td class="">19%</td>
            
              <td class="">19%</td>
            
              <td class="">18%</td>
            
              <td class="">18%</td>
            
              <td class="">18%</td>
            
              <td class="">5%</td>
            
              <td class="">11%</td>
            
              <td class="">23%</td>
            
              <td class="">23%</td>
            
              <td class="">23%</td>
            
              <td class="">24%</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>


      <div style="display: grid;
                  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                  gap: 2%">
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Sales Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>21%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>22%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>35%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>19%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Profit Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>21%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>21%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>23%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>11%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Stock Price CAGR</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>14%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>17%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>14%</td>
          </tr>
          <tr>
            <td>1 Year:</td>
            <td>32%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Return on Equity</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>17%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>16%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>16%</td>
          </tr>
          <tr>
            <td>Last Year:</td>
            <td>15%</td>
          </tr>
        </table>
      </div>
    </section>

    <section id="balance-sheet" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Balance Sheet</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/HDFCBANK/#balance-sheet"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
      <button type="button"
              class="button-small button-secondary plausible-event-name=Corporate+Actions plausible-event-user=unregistered"
              onclick="Modal.openInModal(event)"
              data-title="Corporate actions"
              data-url="/company/actions/1298/">Corporate actions</button>
    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                Equity Capital
              
            </td>
            
              <td class="">480</td>
            
              <td class="">501</td>
            
              <td class="">506</td>
            
              <td class="">513</td>
            
              <td class="">519</td>
            
              <td class="">545</td>
            
              <td class="">548</td>
            
              <td class="">551</td>
            
              <td class="">555</td>
            
              <td class="">558</td>
            
              <td class="">760</td>
            
              <td class="">765</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Reserves
              
            </td>
            
              <td class="">43,687</td>
            
              <td class="">62,653</td>
            
              <td class="">73,798</td>
            
              <td class="">91,281</td>
            
              <td class="">109,080</td>
            
              <td class="">153,128</td>
            
              <td class="">175,810</td>
            
              <td class="">209,259</td>
            
              <td class="">246,772</td>
            
              <td class="">288,880</td>
            
              <td class="">455,636</td>
            
              <td class="">517,219</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Deposits
              
            </td>
            
              <td class="">367,080</td>
            
              <td class="">450,284</td>
            
              <td class="">545,873</td>
            
              <td class="">643,134</td>
            
              <td class="">788,375</td>
            
              <td class="">922,503</td>
            
              <td class="">1,146,207</td>
            
              <td class="">1,333,721</td>
            
              <td class="">1,558,003</td>
            
              <td class="">1,882,663</td>
            
              <td class="">2,376,887</td>
            
              <td class="">2,710,898</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Borrowing
              
            </td>
            
              <td class="">49,597</td>
            
              <td class="">59,478</td>
            
              <td class="">103,714</td>
            
              <td class="">98,416</td>
            
              <td class="">156,442</td>
            
              <td class="">157,733</td>
            
              <td class="">186,834</td>
            
              <td class="">177,697</td>
            
              <td class="">226,966</td>
            
              <td class="">256,549</td>
            
              <td class="">730,615</td>
            
              <td class="">634,606</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Liabilities', 'balance-sheet', this)">
                  Other Liabilities&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">42,776</td>
            
              <td class="">34,181</td>
            
              <td class="">38,321</td>
            
              <td class="">59,000</td>
            
              <td class="">48,770</td>
            
              <td class="">58,898</td>
            
              <td class="">71,430</td>
            
              <td class="">78,279</td>
            
              <td class="">90,639</td>
            
              <td class="">101,783</td>
            
              <td class="">466,296</td>
            
              <td class="">528,929</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Total Liabilities
              
            </td>
            
              <td class="">503,620</td>
            
              <td class="">607,097</td>
            
              <td class="">762,212</td>
            
              <td class="">892,344</td>
            
              <td class="">1,103,186</td>
            
              <td class="">1,292,806</td>
            
              <td class="">1,580,830</td>
            
              <td class="">1,799,507</td>
            
              <td class="">2,122,934</td>
            
              <td class="">2,530,432</td>
            
              <td class="">4,030,194</td>
            
              <td class="">4,392,417</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Fixed Assets', 'balance-sheet', this)">
                  Fixed Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">3,026</td>
            
              <td class="">3,225</td>
            
              <td class="">3,667</td>
            
              <td class="">4,000</td>
            
              <td class="">4,008</td>
            
              <td class="">4,369</td>
            
              <td class="">4,776</td>
            
              <td class="">5,248</td>
            
              <td class="">6,432</td>
            
              <td class="">8,431</td>
            
              <td class="">12,604</td>
            
              <td class="">15,258</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                CWIP
              
            </td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
              <td class="">0</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Investments
              
            </td>
            
              <td class="">119,571</td>
            
              <td class="">149,454</td>
            
              <td class="">193,634</td>
            
              <td class="">210,777</td>
            
              <td class="">238,461</td>
            
              <td class="">289,446</td>
            
              <td class="">389,305</td>
            
              <td class="">438,823</td>
            
              <td class="">449,264</td>
            
              <td class="">511,582</td>
            
              <td class="">1,005,682</td>
            
              <td class="">1,186,473</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Assets', 'balance-sheet', this)">
                  Other Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">381,023</td>
            
              <td class="">454,417</td>
            
              <td class="">564,912</td>
            
              <td class="">677,567</td>
            
              <td class="">860,717</td>
            
              <td class="">998,991</td>
            
              <td class="">1,186,750</td>
            
              <td class="">1,355,435</td>
            
              <td class="">1,667,238</td>
            
              <td class="">2,010,419</td>
            
              <td class="">3,011,909</td>
            
              <td class="">3,190,687</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Total Assets
              
            </td>
            
              <td class="">503,620</td>
            
              <td class="">607,097</td>
            
              <td class="">762,212</td>
            
              <td class="">892,344</td>
            
              <td class="">1,103,186</td>
            
              <td class="">1,292,806</td>
            
              <td class="">1,580,830</td>
            
              <td class="">1,799,507</td>
            
              <td class="">2,122,934</td>
            
              <td class="">2,530,432</td>
            
              <td class="">4,030,194</td>
            
              <td class="">4,392,417</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="cash-flow" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Cash Flows</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/HDFCBANK/#cash-flow"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Operating Activity', 'cash-flow', this)">
                  Cash from Operating Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">4,211</td>
            
              <td class="">-21,281</td>
            
              <td class="">-34,435</td>
            
              <td class="">17,282</td>
            
              <td class="">17,214</td>
            
              <td class="">-62,872</td>
            
              <td class="">-16,869</td>
            
              <td class="">42,476</td>
            
              <td class="">-11,960</td>
            
              <td class="">20,814</td>
            
              <td class="">19,069</td>
            
              <td class="">127,242</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Investing Activity', 'cash-flow', this)">
                  Cash from Investing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">-1,099</td>
            
              <td class="">-800</td>
            
              <td class="">-837</td>
            
              <td class="">-1,146</td>
            
              <td class="">-842</td>
            
              <td class="">-1,503</td>
            
              <td class="">-1,403</td>
            
              <td class="">-1,823</td>
            
              <td class="">-2,051</td>
            
              <td class="">-2,992</td>
            
              <td class="">16,600</td>
            
              <td class="">-3,851</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Financing Activity', 'cash-flow', this)">
                  Cash from Financing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">9,270</td>
            
              <td class="">18,694</td>
            
              <td class="">37,815</td>
            
              <td class="">-5,893</td>
            
              <td class="">57,378</td>
            
              <td class="">23,131</td>
            
              <td class="">24,394</td>
            
              <td class="">-7,321</td>
            
              <td class="">48,124</td>
            
              <td class="">23,941</td>
            
              <td class="">-3,983</td>
            
              <td class="">-102,478</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Net Cash Flow
              
            </td>
            
              <td class="">12,382</td>
            
              <td class="">-3,387</td>
            
              <td class="">2,542</td>
            
              <td class="">10,242</td>
            
              <td class="">73,750</td>
            
              <td class="">-41,244</td>
            
              <td class="">6,122</td>
            
              <td class="">33,332</td>
            
              <td class="">34,113</td>
            
              <td class="">41,762</td>
            
              <td class="">31,687</td>
            
              <td class="">20,914</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="ratios" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Ratios</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/HDFCBANK/#ratios"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                ROE %
              
            </td>
            
              <td class="">22%</td>
            
              <td class="">20%</td>
            
              <td class="">19%</td>
            
              <td class="">18%</td>
            
              <td class="">18%</td>
            
              <td class="">17%</td>
            
              <td class="">16%</td>
            
              <td class="">16%</td>
            
              <td class="">17%</td>
            
              <td class="">17%</td>
            
              <td class="">17%</td>
            
              <td class="">15%</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="shareholding" class="card card-large">
      <div class="flex flex-space-between flex-wrap margin-bottom-8 flex-align-center">
  <div>
    <h2 class="margin-0">Shareholding Pattern</h2>
    <p class="sub">Numbers in percentages</p>
  </div>
  <div class="flex">
    <div class="options small margin-0">
      <button
        onclick="Utils.setActiveTab(event)"
        class="active"
        data-tab-id="quarterly-shp"
        type="button">
        Quarterly
      </button>
      <button
        onclick="Utils.setActiveTab(event)"
        data-tab-id="yearly-shp"
        type="button">
        Yearly
      </button>
    </div>
    
    <div class="text-align-center margin-left-12">
      <button
        type="button"
        class="button-secondary button-small"
        onclick="Modal.openInModal(event)"
        data-title="HDFC Bank trades"
        data-url="/trades/company-1298/"
      >
        Trades
      </button>
      
        <div class="ink-700 font-size-12" style="position: absolute; padding-left: 15px;">
          23 Recently
        </div>
      
    </div>
    
  </div>
</div>

<div id="quarterly-shp">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Jun 2022</th><th>Sep 2022</th><th>Dec 2022</th><th>Mar 2023</th><th>Jun 2023</th><th>Sep 2023</th><th>Dec 2023</th><th>Mar 2024</th><th>Jun 2024</th><th>Sep 2024</th><th>Dec 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=promoters plausible-event-period=quarterly"
                      onclick="Company.showShareholders('promoters', 'quarterly', this)">
                Promoters&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>25.73%</td>
            
              <td>25.64%</td>
            
              <td>25.60%</td>
            
              <td>25.59%</td>
            
              <td>25.52%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=foreign_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('foreign_institutions', 'quarterly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>32.31%</td>
            
              <td>32.14%</td>
            
              <td>32.10%</td>
            
              <td>32.24%</td>
            
              <td>33.38%</td>
            
              <td>52.13%</td>
            
              <td>52.31%</td>
            
              <td>47.83%</td>
            
              <td>47.17%</td>
            
              <td>48.02%</td>
            
              <td>49.21%</td>
            
              <td>48.30%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=domestic_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('domestic_institutions', 'quarterly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>27.11%</td>
            
              <td>27.53%</td>
            
              <td>28.13%</td>
            
              <td>28.09%</td>
            
              <td>26.75%</td>
            
              <td>30.39%</td>
            
              <td>30.54%</td>
            
              <td>33.33%</td>
            
              <td>35.20%</td>
            
              <td>35.05%</td>
            
              <td>34.37%</td>
            
              <td>35.68%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=government plausible-event-period=quarterly"
                      onclick="Company.showShareholders('government', 'quarterly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.16%</td>
            
              <td>0.16%</td>
            
              <td>0.16%</td>
            
              <td>0.16%</td>
            
              <td>0.16%</td>
            
              <td>0.17%</td>
            
              <td>0.17%</td>
            
              <td>0.18%</td>
            
              <td>0.18%</td>
            
              <td>0.18%</td>
            
              <td>0.18%</td>
            
              <td>0.18%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=public plausible-event-period=quarterly"
                      onclick="Company.showShareholders('public', 'quarterly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>14.69%</td>
            
              <td>14.51%</td>
            
              <td>13.99%</td>
            
              <td>13.91%</td>
            
              <td>14.19%</td>
            
              <td>17.30%</td>
            
              <td>16.98%</td>
            
              <td>18.64%</td>
            
              <td>17.44%</td>
            
              <td>16.75%</td>
            
              <td>16.23%</td>
            
              <td>15.84%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>26,60,577</td><td>25,20,911</td><td>23,03,291</td><td>22,90,092</td><td>23,45,672</td><td>30,58,659</td><td>30,18,247</td><td>41,21,815</td><td>36,64,325</td><td>39,21,910</td><td>39,24,442</td><td>38,29,146</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>

<div id="yearly-shp" class="hidden">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Mar 2017</th><th>Mar 2018</th><th>Mar 2019</th><th>Mar 2020</th><th>Mar 2021</th><th>Mar 2022</th><th>Mar 2023</th><th>Mar 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=promoters plausible-event-period=yearly"
                      onclick="Company.showShareholders('promoters', 'yearly', this)">
                Promoters&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>26.00%</td>
            
              <td>25.60%</td>
            
              <td>26.50%</td>
            
              <td>26.14%</td>
            
              <td>25.97%</td>
            
              <td>25.78%</td>
            
              <td>25.59%</td>
            
              <td>0.00%</td>
            
              <td>0.00%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=foreign_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('foreign_institutions', 'yearly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>42.13%</td>
            
              <td>40.43%</td>
            
              <td>38.71%</td>
            
              <td>36.68%</td>
            
              <td>39.79%</td>
            
              <td>35.62%</td>
            
              <td>32.24%</td>
            
              <td>47.83%</td>
            
              <td>48.30%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=domestic_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('domestic_institutions', 'yearly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>12.94%</td>
            
              <td>14.97%</td>
            
              <td>16.41%</td>
            
              <td>21.74%</td>
            
              <td>20.99%</td>
            
              <td>24.55%</td>
            
              <td>28.09%</td>
            
              <td>33.33%</td>
            
              <td>35.68%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=government plausible-event-period=yearly"
                      onclick="Company.showShareholders('government', 'yearly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.13%</td>
            
              <td>0.13%</td>
            
              <td>0.20%</td>
            
              <td>0.23%</td>
            
              <td>0.24%</td>
            
              <td>0.16%</td>
            
              <td>0.16%</td>
            
              <td>0.18%</td>
            
              <td>0.18%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=public plausible-event-period=yearly"
                      onclick="Company.showShareholders('public', 'yearly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>18.80%</td>
            
              <td>18.87%</td>
            
              <td>18.19%</td>
            
              <td>15.21%</td>
            
              <td>13.01%</td>
            
              <td>13.89%</td>
            
              <td>13.91%</td>
            
              <td>18.64%</td>
            
              <td>15.84%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>4,63,568</td><td>5,10,377</td><td>5,89,930</td><td>12,86,083</td><td>13,75,294</td><td>21,51,630</td><td>22,90,092</td><td>41,21,815</td><td>38,29,146</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>


<p class="sub small">
  * The classifications might have changed from Sep'2022 onwards.
  <span class="has-tooltip">
    <i class="icon-info"></i>
    <span class="tooltip" style="width: 300px">
      The new XBRL format added more details from Sep'22 onwards.
      <br>
      <br>
      Classifications such as banks and foreign portfolio investors were not available earlier. The sudden changes in FII or DII can be because of these changes.
      <br>
      <br>
      Click on the line-items to see the names of individual entities.
    </span>
  </span>
</p>


    </section>

    <section id="documents" class="card card-large">
      <div class="flex flex-space-between margin-bottom-16">
        <h2 class="margin-0">Documents</h2>
      </div>

      <div class="flex-row flex-gap-small">
        
          <div class="documents flex-column" style="flex: 2 1 250px;">
            <h3 class="margin-bottom-8">Announcements</h3>

            <div class="show-more-box" style="flex-basis: 300px">
              <div id="company-announcements-tab">
                <div class="flex">
  <div class="options small" style="margin-bottom: 0">
    <button
      type="button"
      class="active"
      
      onclick="Utils.ajaxLoad(event, '/announcements/recent/1298/')"
      data-swap="#company-announcements-tab"
    >
      Recent
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/important/1298/')"
      data-swap="#company-announcements-tab"
    >
      Important
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/search/1298/')"
      data-swap="#company-announcements-tab"
    >
      Search
    </button>
    <a class="button" href="https://www.bseindia.com/stock-share-price/hdfc-bank-ltd/hdfcbank/500180/corp-announcements/" target="_blank" rel="noopener noreferrer">
      All
      <i class="icon-link-ext smaller"></i>
    </a>
  </div>
</div>


  
  <ul class="list-links">
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=fd8e850a-01f3-4e71-b383-ade909b5098b.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Allotment of ESOP / ESPS

          
            <span class="ink-600 smaller">22h</span>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=759872bb-da0d-4149-96df-45089ec49add.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Analyst / Investor Meet - Intimation

          
            <div class="ink-600 smaller">1d - Of Schedule of Analyst / Institutional Investor Meetings under the SEBI (LODR) Regulations, 2015</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=32f69472-037f-45a8-9747-55e360db2210.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Grant Of Stock Options Under Employee Stock Option Scheme - ESOS 60 And Restricted Stock Units Under Employees Stock Incentive Master Scheme - 2022 RSU 012

          
            <div class="ink-600 smaller">10 May - Granted 130,206 stock options and 1,714 RSUs under ESOS Plan H-2024 and RSU 012 schemes.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=a122a0d7-f35d-45fb-adfd-5c4f7a5de42d.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Allotment of ESOP / ESPS

          
            <span class="ink-600 smaller">25 Apr</span>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=ff4ae1b4-cf07-4f6d-b097-3bd8139fb570.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Analyst / Investor Meet - Outcome

          
            <div class="ink-600 smaller">24 Apr - HDFC Bank Q4 FY25 earnings call transcript covering financials, outlook, and management reorganization.</div>
          
        </a>
      </li>
    
  </ul>




              </div>
            </div>
          </div>
        

        <div class="documents annual-reports flex-column"
             style="flex: 1 1 200px;
                    max-width: 210px">
          <h3>Annual reports</h3>
          <div class="show-more-box">
            
<ul class="list-links">

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=2160dea5-d49e-4409-83a4-c2b111a3bd12.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2024
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=\0fb0e6bd-7110-44c3-a750-76933ddf105c.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2023
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/73256500180.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2022
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/68609500180.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2021
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/5001800320.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2020
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/5001800319.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2019
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/5001800318.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2018
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/5001800317.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2017
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/5001800316.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2016
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/5001800315.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2015
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/5001800314.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2014
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/5001800313.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2013
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/**********.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2012
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://archives.nseindia.com/annual_reports/AR_HDFCBANK_2011_2012_20062012100212.zip" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2012
      <div class="ink-600 smaller">
        from nse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500180/**********.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2011
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

</ul>


          </div>
          
        </div>

        
          <div class="documents credit-ratings flex-column"
               style="flex: 1 1 200px;
                      max-width: 220px">
            <h3>Credit ratings</h3>
            <div class="show-more-box">
              
<ul class="list-links">

  <li>
    <a href="https://www.careratings.com/upload/CompanyFiles/PR/202503140341_HDFC_Bank_Limited.pdf" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        6 Mar from care
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.indiaratings.co.in/pressrelease/73732" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        4 Dec 2024 from fitch
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/IndiaUniversalTrustAL2_November 29_ 2024_RR_357524.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        29 Nov 2024 from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/IndiaUniversalTrustAL2_November 22_ 2024_RR_356936.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        22 Nov 2024 from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=128559" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        1 Jul 2024 from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/HDFCBankLimited_June 28_ 2024_RR_346791.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        28 Jun 2024 from crisil
      </div>
    </a>
  </li>

</ul>


            </div>
          </div>
        

        
          <div class="documents concalls flex-column"
               style="flex: 2 1 250px;
                      max-width: fit-content">
            <div class="flex flex-space-between flex-align-center margin-bottom-8">
              <h3 class="margin-0">Concalls</h3>
              <button class="a font-size-12 font-weight-500 sub"
                      onclick="Modal.openInModal(event)"
                      data-url="/concalls/add-1298/"
                      data-title="HDFC Bank - Add Link">
                <i class="icon-flag"></i>
                <span class="underline-link">Add Missing</span>
              </button>
            </div>
            <div class="show-more-box"><ul class="list-links">

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2025
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=ff4ae1b4-cf07-4f6d-b097-3bd8139fb570.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/********/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2025"
    >
      Notes
    </button>
    

    
    <a href="https://nsearchives.nseindia.com/corporate/HDFCBANK_19042025160318_SEpresentation19042025.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.youtube.com/watch?v=SnI9s6l69v4" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2025
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=a7e4ad26-5c9e-4ffe-a610-28241b35c3b0.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/********/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2025"
    >
      Notes
    </button>
    

    
    <a href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/Investor%20Relation/Detail%20PAges/financial%20results/PDFs/2024/dec/22/Q3FY25-Earnings-Presentation.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=c7affe75-673f-4507-b421-a7d5bab9d3e6.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/********/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/Investor%20Relation/Detail%20PAges/financial%20results/PDFs/2024/oct/Q2FY25-Earnings-Presentation.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=94d03650-9410-4870-b9ba-d4c553d2740b.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/********/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/About%20Investor%20Relations/pdf/2024/july/Q1FY25_Earnings_Presentation.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.youtube.com/watch?v=bi_jiwKLKnQ" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/About%20Investor%20Relations/pdf/2024/july/Q1FY25_Earnings_Presentation.pdfhttps://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/About%20Investor%20Relations/pdf/2024/july/Q1FY25_Earnings_Presentation.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=880ee466-e764-4e05-9ef3-3d9bb8e485f4.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/********/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/Investor%20Relation/Detail%20PAges/financial%20results/PDFs/2024/20April/Q4FY24-Earnings-Presentation.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://youtu.be/xh0oa926udk?si=WQGaj1nPMlGJcHpr" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Mar 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=12bab14e-f504-46e9-90cd-0881a76dd4a6.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d34bd589-124e-40f2-80b5-82f360a98639.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/18627149/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=163829d1-3b5f-41f7-bd9e-9f3509869245.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.youtube.com/watch?v=j6_jnIErxXE" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/Investor%20Relation/Detail%20PAges/financial%20results/PDFs/2024/23/Earnings-Call-Transcript-Jan16-2024.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/********/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/Investor%20Relation/Detail%20PAges/financial%20results/PDFs/2024/16/Q3FY24_Earnings_Presentation.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/Investor%20Relation/Detail%20PAges/Financial%20Information/PDF/HDFC-Bank-Day/1.%20HDFCB_Day_30Oct23_vF.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.youtube.com/watch?v=APkBOH4Y2Jw" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d420b35c-488c-4ea9-84ca-6183de7e29f4.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/********/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=7931b031-359d-4da8-8425-b4f26c2d2547.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/Investor%20Relation/Detail%20PAges/financial%20results/PDFs/2023/oct/HDFC-EarningsCall-Oct16-2023_CallTranscript.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/********/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/Investor%20Relation/Detail%20PAges/financial%20results/PDFs/2023/oct/Q2FY24%20Earnings%20Presentation.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.youtube.com/watch?v=sDUS9GSyRqc&amp;t=1s" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Sep 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d28f086c-3d7a-4a6d-a138-a0c2123a963a.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.youtube.com/watch?v=eFTe9K7T9Pk" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=989cf3a5-9a81-458e-b0ec-7ea60cc482a7.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/********/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/Investor%20Relation/Detail%20PAges/financial%20results/PDFs/2023/july/HDFCB_Q1FY24_Earnings_Presentation.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.youtube.com/watch?v=KfKI0xSaKxw" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=57e720ab-83b3-4b34-bb81-0c364e88c938.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/9350353/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.hdfcbank.com/content/bbp/repositories/723fb80a-2dde-42a3-9793-7ae1be57c87f/?path=/Footer/About%20Us/Investor%20Relation/Detail%20PAges/financial%20results/PDFs/2023/Q4FY23-Earnings-Presentation.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=42e79ecc-a641-4f9d-8fdc-a4dde6cefb16.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/6883827/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2023"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2022
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=e9327050-8f62-42f3-a539-bd91f156647d.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/2306405/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2022"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2022
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=f5ad442e-6916-47c7-ac65-c42c31df289f.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/1503229/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2022"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=25b7a31b-fd86-456f-8a27-51b4119fe7ce.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=27bf8aa3-**************-7f384e76f3c2.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      May 2018
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=b3087c6b-5b89-43bd-bea9-4250ab026f85.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

</ul>
</div>
          </div>
        
      </div>
    </section>
    <!-- cache time 2025-05-16 18:56:52 -->
    
  

      </main>
    

    
      

<footer id="large-footer">
  <div class="flex-row flex-space-between flex-column-tablet container no-print">
    <div class="hide-from-tablet" style="margin-bottom: 16px;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
    </div>

    <div class="show-from-tablet" style="padding: 0 64px 0 0;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
      <p class="font-size-19" style="font-weight: 500;">Stock analysis and screening tool</p>
      <p class="sub">
        Mittal Analytics Private Ltd &copy; 2009-2025
        <br>
        Made with <i class="icon-heart red"></i> in India.
      </p>
      <p class="sub font-size-13">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="font-size-13 sub">
        <a href="/guides/terms/" class="underline-link">Terms</a> & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>

    <div class="flex flex-end flex-space-between flex-grow"
         style="max-width: 600px">
      <div class="flex-grow">
        <div class="title">Product</div>
        <ul class="items">
          <li>
            <a href="/premium/?driver=footer">Premium</a>
          </li>
          <li>
            <a href="/docs/changelog/">What's new?</a>
          </li>
          <li>
            <a href="https://bit.ly/learnscreener">Learn</a>
          </li>
          <li>
            <button class="a2hs button-secondary button-small">
              <i class="icon-flash"></i>
              Install
            </button>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Team</div>
        <ul class="items">
          <li>
            <a href="/guides/about-us/">About us</a>
          </li>
          <li>
            <a href="/support/">Support</a>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Theme</div>
        <ul class="items">
          <li>
            <button onclick="SetTheme('light')"
                    aria-label="Set light theme"
                    class="button-plain">
              <i class="icon-sun"></i>
              Light
            </button>
          </li>
          <li>
            <button onclick="SetTheme('dark')"
                    aria-label="Set dark theme"
                    class="button-plain">
              <i class="icon-moon"></i>
              Dark
            </button>
          </li>
          <li>
            <button onclick="SetTheme('auto')"
                    aria-label="Set auto theme"
                    class="button-plain">
              <i class="icon-monitor"></i>
              Auto
            </button>
          </li>
        </ul>
      </div>
    </div>

    <div class="hide-from-tablet">
      <hr>
      <p class="sub">Mittal Analytics Private Ltd &copy; 2009-2024</p>
      <p class="sub">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="sub">
        <a href="/guides/terms/" class="underline-link">Terms</a>
        & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>
  </div>
</footer>

    

    <!-- Important JS (which provide global objects)- check below too -->
    <script src="https://cdn-static.screener.in/js/dialog-polyfill.min.2b5bf9a032d2.js"></script>
    <script src="https://cdn-static.screener.in/js/utils.02a9ce812e49.js"></script>
    <script src="https://cdn-static.screener.in/js/modal.4f872e091355.js"></script>
    <script src="https://cdn-static.screener.in/js/typeahead.8dcc9ac93685.js"></script>
    <script src="https://cdn-static.screener.in/js/filter.component.5add9ef3b7a9.js"></script>

    <!-- Independent JS scripts for page interactions -->
    <script src="https://cdn-static.screener.in/js/navbar.3fe7f0f4a630.js"></script>
    <script src="https://cdn-static.screener.in/js/company.search.e48f94642999.js"></script>
    
  <script src="https://cdn-static.screener.in/js/scroll-aid.5ed2d38a133e.js"></script>
  <script src="https://cdn-static.screener.in/js/company.customisation.d7c717b0de15.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.min.10a29a29621c.js"></script>
  <script src="https://cdn-static.screener.in/js/chartjs-adapter-date-fns.bundle.min.ebae23730a6e.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.e20c5f42613a.js"></script>
  <script src="https://cdn-static.screener.in/js/segment-result.b6af4191fd6f.js"></script>


    <!-- Service worker installer for PWA -->
    <script src="https://cdn-static.screener.in/js/pwa.933118758337.js"></script>
  </body>
</html>
