
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    
    <title>Reliance Industries Ltd share price | About Reliance Industr | Key Insights - Screener</title>
    

    <!-- plausible -->
    <script defer
            event-user="unregistered"
            
            
            data-domain="screener.in"
            src="https://plausible.screener.in/js/script.pageview-props.tagged-events.js"></script>
    <script>window.plausible = window.plausible || function () { (window.plausible.q = window.plausible.q || []).push(arguments) }</script>

    <!-- FONT -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/inter/inter.43d4904a3137.css" />
    <link rel="stylesheet" href="https://cdn-static.screener.in/fontello/css/fontello.bc317748a65f.css" />

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/normalize.a2444750a947.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/skeleton.9aa9c32992ef.css" media="all">
    <link rel="stylesheet" href="https://cdn-static.screener.in/css/custom.39beec5e86b6.css" media="all">

    
    
    
  

  
  <meta name="description" content="Reliance Industr · Mkt Cap: 19,71,411 Crore (up 2.18% in 1 year) · Revenue: 9,64,693 Cr · Profit: 81,309 Cr · The company has delivered a poor sales growth of 10.1% over past five years. · Company has a low return on equity of 8.89% over last 3 years. · Promoter Holding: 50.1%">
  

  <style>
  tr[data-row-company-id="2726"] {
    font-weight: 500;
  }

  tr[data-row-company-id="2726"] a {
    color: var(--ink-900);
  }

  :target {
    scroll-margin-top: 90px;
  }

  @supports not (scroll-margin-top: 90px) {
    :target {
      padding-top: 108px;
    }
  }

  @media (min-width: 992px) {
    nav {
      margin-bottom: 0;
    }

    .company-nav {
      top: 50px;
      margin-bottom: 24px;
    }
  }
  </style>


    <meta name="author" content="Mittal Analytics Private Limited">

    <!-- PWA manifest -->
    <meta name="theme-color"
          content="#fff"
          media="(prefers-color-scheme: light)">
    <meta name="theme-color"
          content="#222530"
          media="(prefers-color-scheme: dark)">
    <link rel="manifest" href="/static/manifest.json">

    <!-- favicons -->
    <link rel="apple-touch-icon"
          sizes="180x180"
          href="https://cdn-static.screener.in/favicon/apple-touch-icon.b6e5f24bf7d3.png">
    <link rel="icon"
          type="image/png"
          sizes="32x32"
          href="https://cdn-static.screener.in/favicon/favicon-32x32.00205914303a.png">
    <link rel="icon"
          type="image/png"
          sizes="16x16"
          href="https://cdn-static.screener.in/favicon/favicon-16x16.2b35594b2f33.png">
    <link rel="mask-icon"
          href="https://cdn-static.screener.in/favicon/safari-pinned-tab.adbe3ed3c5ad.svg"
          color="#8ED443">
    <meta name="msapplication-TileColor" content="#da532c">
  </head>

  <body class="light flex-column">
    
      <nav class="u-full-width no-print">
        <div id="mobile-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

<ul class="bottom-menu-bar hide-from-tablet-landscape">
  
    <li class="">
      <a href="/">
        <i class="icon-home"></i>
        <br>
        Home
      </a>
    </li>
  

  <li class="">
    <a href="/explore/">
      <i class="icon-screens"></i>
      <br>
      Screens
    </a>
  </li>

  <li>
    <button type="button"
            onclick="MobileMenu.toggleSearch(this)"
            class="search-button"
            title="Search">
      <i class="icon-search" style="font-size: 18px"></i>
    </button>
  </li>

  <li>
    <button class="button-plain" onclick="Modal.showInModal('#nav-tools-menu')">
      <i class="icon-tools"></i>
      <br>
      Tools
    </button>
  </li>

  
    <li class="">
      <a href="/register/?">
        <i class="icon-user-plus"></i>
        <br>
        Login
      </a>
    </li>
  
</ul>

        <div class="top-nav-holder">
          <div class="container">



<div class="layer-holder top-navigation">
  <div class="layer flex flex-space-between"
       style="align-items: center;
              height: 50px">
    <div class="flex" style="align-items: center">
      <a href="/" class="logo-holder">
        <img alt="Screener Logo"
             class="logo"
             src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg" />
      </a>

      <div class="desktop-links" style="margin-left: 48px">
        <a href="/">
          
            Home
          
        </a>
        <a href="/explore/">Screens</a>
        <div class="dropdown-menu">
          <button class="button-plain"
                  type="button"
                  onclick="Utils.toggleDropdown(event)">
            Tools
            <i class="icon-down"></i>
          </button>
          <ul class="dropdown-content flex-column tools-menu"
              style="width: 350px"
              id="nav-tools-menu">
            <li>
              <a href="/screen/new/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/screens.e960a5daedf9.svg" alt="Create a stock screen">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Create a stock screen</div>
                  <div class="sub font-size-13">Run queries on 10 years of financial data</div>
                </div>
              </a>
            </li>
            <li class="flex flex-align-center no-hover" style="padding: 4px 0;">
              <div class="button button-primary premium-indicator flex flex-align-center">
                <img src="https://cdn-static.screener.in/icons/crown.fb0d2413ee43.svg"
                     alt="Premium icon"
                     style="margin-right: 8px">
                <span>Premium features</span>
              </div>
              <hr class="flex-grow" style="padding: 0; margin: 0;">
            </li>
            <li>
              <a href="/hs/" class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/export.d623f0029933.svg" alt="Commodity Prices">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Commodity Prices</div>
                  <div class="sub font-size-13">See prices and trends of over 10,000 commodities</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/people/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/shares.10acc20cb936.svg"
                       alt="Search shareholders by name">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Search shareholders</div>
                  <div class="sub font-size-13">See companies where a person holds over 1% of the shares</div>
                </div>
              </a>
            </li>
            <li>
              <a href="/announcements/"
                 class="flex flex-align-center flex-gap-16">
                <div class="bg-stripes square-56">
                  <img src="https://cdn-static.screener.in/icons/loudspeaker.88769927f6eb.svg" alt="Latest Announcements">
                </div>
                <div>
                  <div class="font-weight-500 font-size-14">Latest Announcements</div>
                  <div class="sub font-size-13">Browse, filter and set alerts for announcements.</div>
                </div>
              </a>
            </li>
            
              <li style="padding: 16px;">
                <a href="/premium/?driver=top-nav-tools"
                   class="button button-primary text-align-center"
                   style="color: var(--white);
                          padding: 10px 24px">Upgrade to premium</a>
              </li>
            
          </ul>
        </div>
      </div>
    </div>

    <div class="show-from-tablet-landscape flex flex-gap-16"
         style="justify-content: flex-end;
                flex: 1 1 400px">
      <div class="search" id="desktop-search"><div class="has-addon left-addon dropdown-typeahead">
  <i class="addon icon-search"></i>
  <input
    aria-label="Search for a company"
    type="search"
    autocomplete="off"
    spellcheck="false"
    placeholder="Search for a company"
    class="u-full-width"
    
    data-company-search="true">
</div>
</div>

      
        <div class="flex flex-gap-8" style="margin: 4px">
          <a class="button account"
             href="/login/?">
            <i class="icon-user-line blue-icon"></i>
            Login
          </a>
          <a class="button account button-secondary"
             href="/register/?">Get free account</a>
        </div>
      
    </div>
  </div>
</div>
</div>
        </div>
      </nav>
    

    
  <div class="bg-base sticky company-nav">
    <div class="flex flex-space-between container hide-from-tablet-landscape">
      <h1 class="h2 shrink-text" style="margin: 0.5em 0">Reliance Industries Ltd</h1>
      <div class="flex flex-align-center gap-8">
        <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/2726/"
        data-title="Reliance Industries Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

        
      </div>
    </div>

    <div class="sub-nav-holder">
      <div class="sub-nav">
        <a href="#top">
          <span class="shrink-text show-from-tablet-landscape">Reliance Industr</span>
          <span class="hide-from-tablet-landscape">Summary</span>
        </a>
        <a href="#chart">Chart</a>
        <a href="#analysis">Analysis</a>
        <a href="#peers">Peers</a>
        <a href="#quarters">Quarters</a>
        <a href="#profit-loss">Profit &amp; Loss</a>
        <a href="#balance-sheet">Balance Sheet</a>
        <a href="#cash-flow">Cash Flow</a>
        <a href="#ratios">Ratios</a>
        <a href="#shareholding">Investors</a>
        <a href="#documents">Documents</a>
        <div class="flex-grow flex flex-align-center gap-8 show-from-tablet-landscape">
          <div class="flex-grow"></div>
          <button type="button"
        class="a sub plausible-event-name=Notebook plausible-event-user=unregistered"
        onclick="Modal.openInModal(event)"
        data-url="/notebook/2726/"
        data-title="Reliance Industries Ltd"
        data-is-right-modal="true"
        title="View Notebook">
  
    <i class="icon-book-open"></i>
  
  <span class="show-from-tablet-landscape">Notebook</span>
</button>

          
        </div>
      </div>
    </div>
  </div>


    
      <main class="flex-grow container">
        



  






        
        <div class="breadcrumb hidden-if-empty"></div>
        

        
  <div data-company-id="2726"
       data-warehouse-id="6598251"
       
       data-consolidated="true"
       id="company-info"></div>

  <div class="card card-large" id="top">
    <div class="flex flex-space-between flex-gap-8">
      <div class="flex-row flex-wrap flex-align-center flex-grow"
           style="flex-basis: 300px">
        <h1 class="margin-0 show-from-tablet-landscape">Reliance Industries Ltd</h1>
        <div class="ink-600 show-from-tablet-landscape" style="opacity: 0.5;">
          <i class="icon-dot"></i>
        </div>
        <div class="font-size-18 strong line-height-14">
          <div class="flex flex-align-center">
            <span>₹ 1,456</span>
            
              <span class="font-size-12 up margin-left-4">
                <i class="icon-circle-up"></i>
                0.15%
              </span>
            
          </div>
          <div class="ink-600 font-size-11 font-weight-500">
            
              16 May
              
                - close price
              
            
          </div>
        </div>
      </div>

      <form method="post" class="flex margin-0" style="align-items: center">
        <input type="hidden" name="csrfmiddlewaretoken" value="Yub30ccKkAqAdpqwi9Uihrk7yHxisgreLjelvvbDbuOh1boKFGEKkqF75msQIAhm">
        <input type="hidden" name="next" value="/company/RELIANCE/consolidated/">

        
          <button class="margin-right-8 plausible-event-name=Excel+Company plausible-event-user=unregistered"
                  formaction="/user/company/export/6598251/"
                  aria-label="Export to Excel">
            <i class="icon-download"></i>
            <span class="hide-on-mobile">Export to Excel</span>
          </button>
        

        <div class="flex dropdown-filter">
          
            <button class="button-primary "
                    formaction="/api/company/2726/add/">
              <i class="icon-plus"></i> Follow
            </button>
          

          
        </div>
      </form>
    </div>

    
    <div class="company-links show-from-tablet-landscape"
         style="font-weight: 500;
                margin-top: 8px">
      
        <a href="http://www.ril.com"
           target="_blank"
           rel="noopener noreferrer">
          <i class="icon-link"></i>
          <span style="font-size: 1.8rem;">ril.com</span>
        </a>
      

      
        <a href="https://www.bseindia.com/stock-share-price/reliance-industries-ltd/RELIANCE/500325/" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            BSE:
            500325
          </span>
        </a>
      

      
        <a href="https://www.nseindia.com/get-quotes/equity?symbol=RELIANCE" target="_blank" rel="noopener noreferrer">
          <i class="icon-link-ext"></i>
          <span class="ink-700 upper" style="letter-spacing: 0.05px;">
            NSE:
            RELIANCE
          </span>
        </a>
      
    </div>
    

    <div class="company-info">
      
      <div class="company-profile">
        <div class="flex flex-column" style="flex: 1 1;">
          <div class="title">About</div>
          <div class="sub show-more-box about" style="flex-basis: 100px"><p>Reliance was founded by Dhirubhai Ambani and is now promoted and managed by his elder son, Mukesh Dhirubhai Ambani. Ambani's family has about 50% shareholding in the conglomerate.</p></div>

          
            <div class="title">Key Points</div>
            <div class="sub commentary always-show-more-box">
              <p><strong>OIL-TO-CHEMICALS SEGMENT (~57% of revenues)</strong><sup><a href="https://www.bseindia.com/bseplus/AnnualReport/500325/74185500325.pdf#page=76" target="_blank" rel="noopener noreferrer">[1]</a></sup><br>
Under the segment, the company primarily refines crude oil to manufacture/ extract transportation fuels, polymers and elastomers, intermediates and polyesters. It has plants and manufacturing assets located across India in Jamnagar, Hazira, Dahej, Nagothane, Vadodara and others.  <strong>It has a crude refining capacity of 1.4 million barrels per day. It also has the largest single site refinery complex globally. </strong><sup><a href="https://www.bseindia.com/bseplus/AnnualReport/500325/74185500325.pdf#page=101" target="_blank" rel="noopener noreferrer">[2]</a></sup> It had a total throughput of ~77 million metric tonnes in FY22 out of which ~89% was meant for sale.<sup><a href="https://www.bseindia.com/bseplus/AnnualReport/500325/74185500325.pdf#page=102" target="_blank" rel="noopener noreferrer">[3]</a></sup></p>
              <div class="show-more-button" style="height: 20px"></div>
            </div>

            <button class="a upper font-weight-500 letter-spacing plausible-event-name=Key+Insight plausible-event-user=unregistered"
                    style="font-size: 1.4rem"
                    type="button"
                    onclick="Modal.openInModal(event)"
                    data-title="Reliance Industries Ltd"
                    data-url="/wiki/company/2726/commentary/v2/"
                    data-is-right-modal="true">
              Read More <i class="icon-right"></i>
            </button>
          
        </div>

        <div class="company-links hide-from-tablet-landscape margin-top-20"
             style="font-weight: 500">
          
            <a href="http://www.ril.com"
               target="_blank"
               rel="noopener noreferrer">
              <i class="icon-link"></i>
              <span style="font-size: 1.6rem;">Website</span>
            </a>
          

          
            <a href="https://www.bseindia.com/stock-share-price/reliance-industries-ltd/RELIANCE/500325/" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">BSE</span>
            </a>
          

          
            <a href="https://www.nseindia.com/get-quotes/equity?symbol=RELIANCE" target="_blank" rel="noopener noreferrer">
              <i class="icon-link-ext"></i>
              <span class="ink-700 upper" style="letter-spacing: 0.05px;">
                NSE
                
              </span>
            </a>
          
        </div>
      </div>
      

      <div class="company-ratios">
        <ul id="top-ratios">
          


  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Market Cap
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">19,71,411</span>
        
          Cr.
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Current Price
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">1,456</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        High / Low
      
    </span>

    
      <span class="nowrap value">₹ <span class="number">1,609</span> / <span class="number">1,115</span></span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Stock P/E
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">28.3</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Book Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">623</span>
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Dividend Yield
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">0.34</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROCE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">9.43</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        ROE
      
    </span>

    
      <span class="nowrap value">
        
        <span class="number">8.51</span>
        
          %
        
      </span>
    
  </li>

  <li class="flex flex-space-between" data-source="default">
    <span class="name">
      
        Face Value
      
    </span>

    
      <span class="nowrap value">
        ₹
        <span class="number">10.0</span>
        
      </span>
    
  </li>






        </ul>

        <div style="margin-top: 32px;">
          <label for="quick-ratio-search">Add ratio to table</label>

          <div class="flex-row flex-space-between flex-baseline flex-gap-16">
            <div class="dropdown-typeahead flex-grow" style="max-width: 480px;">
              <input id="quick-ratio-search"
                     type="text"
                     placeholder="eg. Promoter holding"
                     style="width: 100%">
            </div>

            
            <a href="/user/quick_ratios/?next=/company/RELIANCE/consolidated/"
               class="smaller">
              <i class="icon-pencil"></i>
              <span class="upper font-weight-500 letter-spacing">Edit ratios</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  
    <section id="chart" class="card card-large" style="margin-bottom: 2.5rem">
      <div id="chart-menu"
     class="flex flex-space-between flex-wrap"
     style="margin-bottom: 16px">
  <div class="options" id="company-chart-days">
    <button name="days"
            value="30"
            onclick="CompanyChart.setActive(event)"
            type="button">1M</button>
    <button name="days"
            value="180"
            onclick="CompanyChart.setActive(event)"
            type="button">6M</button>
    <button name="days"
            value="365"
            onclick="CompanyChart.setActive(event)"
            type="button">1Yr</button>
    <button name="days"
            value="1095"
            onclick="CompanyChart.setActive(event)"
            type="button">3Yr</button>
    <button name="days"
            value="1825"
            onclick="CompanyChart.setActive(event)"
            type="button">5Yr</button>
    <button name="days"
            value="3652"
            onclick="CompanyChart.setActive(event)"
            type="button">10Yr</button>
    <button name="days"
            value="10000"
            onclick="CompanyChart.setActive(event)"
            type="button">Max</button>
  </div>

  <div class="flex margin-bottom-24">
    <div class="options margin-right-8"
         id="company-chart-metrics"
         style="margin-bottom: 0">
      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Price"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price-DMA50-DMA200-Volume"
                  type="button">Price</button>
        

      

        
          <button class="plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PE"
                  onclick="CompanyChart.setActive(event)"
                  name="metrics"
                  value="Price to Earning-Median PE-EPS"
                  type="button">PE Ratio</button>
        

      

        

      

        

      

        

      

        

      
      <button class="hidden"
              id="company-chart-metrics-more-active"
              onclick="CompanyChart.setActive(event)"
              name="metrics"
              value=""
              type="button">Hidden</button>
      <div class="dropdown-menu" id="metrics-dropdown">
        <button type="button"
                class="button-plain"
                onclick="Utils.toggleDropdown(event)">
          More <i class="icon-down"></i>
        </button>
        <ul class="dropdown-content" style="max-width: 160px; right: 0">
          

            

          

            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="GPM-OPM-NPM-Quarter Sales"
                        type="button">Sales &amp; Margin</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=EV-EBITDA"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="EV Multiple-Median EV Multiple-EBITDA"
                        type="button">EV / EBITDA</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=PBV"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Price to book value-Median PBV-Book value"
                        type="button">Price to Book</button>
              </li>
            

          

            
              <li>
                <button class="a u-full-width plausible-event-name=Chart+Metric plausible-event-user=unregistered plausible-event-metric=Market+Cap+to+Sales"
                        style="padding: 4px 12px"
                        onclick="CompanyChart.setActive(event)"
                        name="metrics"
                        value="Market Cap to Sales-Median Market Cap to Sales-Sales"
                        type="button">Market Cap / Sales</button>
              </li>
            

          
        </ul>
      </div>
    </div>
    
      <button type="button"
              class="button-small button-secondary flex flex-align-center plausible-event-name=Stock+Alert"
              style="border-color: var(--button-border);
                     border-radius: 3px"
              data-title="Alerts for Reliance Industries Ltd"
              onclick="Modal.openInModal(event)"
              data-url="/alerts/stock-6598251/"
              aria-label="Add alerts for company">
        <i class="icon-bell"></i>
        
        <span class="margin-left-4 normal-case letter-spacing-0">Alerts</span>
      </button>
    
  </div>
</div>

<div class="flex no-select">
  <div class="hide-on-mobile chart-label">
    <div id="chart-label-left"></div>
  </div>

  <div id="chart-area" style="flex-grow: 1">
    <div id="chart-info" class="invisible">
      <div id="chart-crosshair"></div>
      <div id="chart-tooltip" class="font-size-14">
        <div id="chart-tooltip-meta" class="ink-700 font-size-12"></div>
        <div class="font-weight-500 font-size-15" id="chart-tooltip-title"></div>
        <div id="chart-tooltip-info"></div>
      </div>
    </div>
    <div id="canvas-chart-holder" style="height: 375px"></div>
  </div>

  <div class="hide-on-mobile chart-label">
    <div id="chart-label-right"></div>
  </div>
</div>
<div id="chart-legend"></div>

    </section>

    
    <section id="analysis" class="card card-large">
      <div class="flex flex-column-mobile flex-gap-32">
        <div class="pros" style="flex-basis: 50%;">
          <p class="title">Pros</p>
          <ul>
            
          </ul>
        </div>
        <div class="cons" style="flex-basis: 50%;">
          <p class="title">Cons</p>
          <ul>
            <li>The company has delivered a poor sales growth of 10.1% over past five years.</li><li>Company has a low return on equity of 8.89% over last 3 years.</li><li>Dividend payout has been low at 9.84% of profits over last 3 years</li>
          </ul>
        </div>
      </div>
      <p class="sub font-size-14 margin-top-16 margin-bottom-0">
        <span style="vertical-align: sub;">*</span>
        <span class="font-weight-500">The pros and cons are machine generated.</span>
        <span class="has-tooltip">
          <i class="icon-info"></i>
          <span class="tooltip" style="width: 300px; left: -100px;">
            Pros / cons are based on a checklist to highlight important points. Please exercise caution and do your own analysis.
          </span>
        </span>
      </p>
    </section>

    <section id="peers" class="card card-large">
      <div class="flex flex-space-between" style="align-items: center;">
        <div>
          <h2>Peer comparison</h2>

          
            <p class="sub">
              Sector:
              <a href="/company/compare/00000052/"
                 target="_blank">Refineries</a>
              <span style="margin: 16px"></span>
              Industry:
              <a href="/company/compare/00000052/00000080/"
                 target="_blank">Refineries</a>
            </p>
          

          
            <p class="flex flex-wrap gap-8 flex-align-center line-height-1 sub"
               id="benchmarks">
              Part of
              
                <a href="/company/1001/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Sensex"
                   target="_blank">BSE Sensex</a>
              
                <a href="/company/NIFTY/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50"
                   target="_blank">Nifty 50</a>
              
                <a href="/company/1005/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+500"
                   target="_blank">BSE 500</a>
              
                <a href="/company/1002/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100"
                   target="_blank">BSE 100</a>
              
                <a href="/company/1003/"
                   class="tag plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+200"
                   target="_blank">BSE 200</a>
              
                <a href="/company/1004/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dollex+200"
                   target="_blank">BSE Dollex 200</a>
              
                <a href="/company/CNX500/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500"
                   target="_blank">Nifty 500</a>
              
                <a href="/company/1126/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Oil+&amp;+Gas"
                   target="_blank">BSE Oil &amp; Gas</a>
              
                <a href="/company/CNXENERY/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Energy"
                   target="_blank">Nifty Energy</a>
              
                <a href="/company/CNX100/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100"
                   target="_blank">Nifty 100</a>
              
                <a href="/company/CNXINFRAST/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Infrastructure"
                   target="_blank">Nifty Infrastructure</a>
              
                <a href="/company/CNX200INDE/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+200"
                   target="_blank">Nifty 200</a>
              
                <a href="/company/CNXCOMMODI/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Commodities"
                   target="_blank">Nifty Commodities</a>
              
                <a href="/company/LIX15/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Liquid+15"
                   target="_blank">Nifty 100 Liquid 15</a>
              
                <a href="/company/NFT100EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Equal+Weight"
                   target="_blank">Nifty 100 Equal Weight</a>
              
                <a href="/company/1153/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Allcap"
                   target="_blank">BSE Allcap</a>
              
                <a href="/company/1155/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+LargeCap"
                   target="_blank">BSE LargeCap</a>
              
                <a href="/company/1161/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Energy"
                   target="_blank">BSE Energy</a>
              
                <a href="/company/1166/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+India+Manufacturing+Index"
                   target="_blank">BSE India Manufacturing Index</a>
              
                <a href="/company/1125/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+SENSEX+50"
                   target="_blank">BSE SENSEX 50</a>
              
                <a href="/company/1017/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100+ESG+Index+(INR)"
                   target="_blank">BSE 100 ESG Index (INR)</a>
              
                <a href="/company/1181/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+250+LargeMidCap+Index"
                   target="_blank">BSE 250 LargeMidCap Index</a>
              
                <a href="/company/1183/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Dividend+Stability+Index"
                   target="_blank">BSE Dividend Stability Index</a>
              
                <a href="/company/1185/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+Low+Volatility+Index"
                   target="_blank">BSE Low Volatility Index</a>
              
                <a href="/company/NFT50EQWT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+50+Equal+Weight"
                   target="_blank">Nifty 50 Equal Weight</a>
              
                <a href="/company/NFT100LV30/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+100+Low+Volatility+30"
                   target="_blank">Nifty 100 Low Volatility 30</a>
              
                <a href="/company/1130/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=BSE+100+LargeCap+TMC+Index"
                   target="_blank">BSE 100 LargeCap TMC Index</a>
              
                <a href="/company/LMIDCAP250/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+LargeMidcap+250"
                   target="_blank">Nifty LargeMidcap 250</a>
              
                <a href="/company/NFT500MULT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+500+Multicap+50:25:25"
                   target="_blank">Nifty 500 Multicap 50:25:25</a>
              
                <a href="/company/NIFTOILGAS/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Oil+&amp;+Gas"
                   target="_blank">Nifty Oil &amp; Gas</a>
              
                <a href="/company/NIFT100ESG/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty100+ESG"
                   target="_blank">Nifty100 ESG</a>
              
                <a href="/company/NFINDIAMFG/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+India+Manufacturing"
                   target="_blank">Nifty India Manufacturing</a>
              
                <a href="/company/NFTYTOTMKT/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty+Total+Market"
                   target="_blank">Nifty Total Market</a>
              
                <a href="/company/NMIM503020/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty500+Multicap+India+Manufacturing+50:30:20"
                   target="_blank">Nifty500 Multicap India Manufacturing 50:30:20</a>
              
                <a href="/company/NMIF503020/"
                   class="tag hidden plausible-event-name=Click+Benchmark plausible-event-benchmark=Nifty500+Multicap+Infrastructure+50:30:20"
                   target="_blank">Nifty500 Multicap Infrastructure 50:30:20</a>
              
              
                <button type="button"
                        class="a small font-weight-500 plausible-event-name=Show+More+Benchmarks"
                        onclick="Utils.toggleClass('#benchmarks', 'show-hidden')">show all</button>
              
            </p>
          
        </div>

        <div class="flex-reverse">
          <a href="/user/columns/?next=/company/RELIANCE/consolidated/#peers"
             class="button button-small button-secondary">
            <i class="icon-cog"></i>
            <span class="hide-on-mobile">Edit</span>
            Columns
          </a>
        </div>
      </div>

      <div id="peers-table-placeholder">Loading peers table ...</div>

      <div class="flex-row flex-baseline">
        <label for="search-peer-comparison" style="padding-right: 1rem">Detailed Comparison with:</label>
        <div class="dropdown-typeahead">
          <input type="text"
                 id="search-peer-comparison"
                 placeholder="eg. Infosys"
                 class="small">
        </div>
      </div>
    </section>

    <section id="quarters" class="card card-large">
      
        


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Quarterly Results</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/RELIANCE/#quarters"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    
      <button data-segment-button-close
              onclick="Segment.closeSegments('quarters')"
              class="button-small hidden">
        <i class="icon-cancel"></i>
        Close Segments
      </button>

      
        <button data-segment-button="1"
                onclick="Segment.showSegment('quarters', '1')"
                class="button-small button-secondary plausible-event-name=Segment+Results plausible-event-user=unregistered">
          <i class="icon-chart-pie"></i>
          Product Segments
        </button>
      
    

    
  </div>
</div>

<div class="responsive-holder hidden" data-segment-table></div>

<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="highlight-cell">
            Mar 2022
            
          </th>
        
          <th class="">
            Jun 2022
            
          </th>
        
          <th class="">
            Sep 2022
            
          </th>
        
          <th class="">
            Dec 2022
            
          </th>
        
          <th class="highlight-cell">
            Mar 2023
            
          </th>
        
          <th class="">
            Jun 2023
            
          </th>
        
          <th class="">
            Sep 2023
            
          </th>
        
          <th class="">
            Dec 2023
            
          </th>
        
          <th class="highlight-cell">
            Mar 2024
            
          </th>
        
          <th class="">
            Jun 2024
            
          </th>
        
          <th class="">
            Sep 2024
            
          </th>
        
          <th class="">
            Dec 2024
            
          </th>
        
          <th class="highlight-cell">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Sales', 'quarters', this)">
                  Sales&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">207,375</td>
            
              <td class="">218,855</td>
            
              <td class="">229,409</td>
            
              <td class="">216,737</td>
            
              <td class="highlight-cell">212,834</td>
            
              <td class="">207,559</td>
            
              <td class="">231,886</td>
            
              <td class="">225,086</td>
            
              <td class="highlight-cell">236,533</td>
            
              <td class="">231,784</td>
            
              <td class="">231,535</td>
            
              <td class="">239,986</td>
            
              <td class="highlight-cell">261,388</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'quarters', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">176,009</td>
            
              <td class="">181,157</td>
            
              <td class="">198,438</td>
            
              <td class="">181,728</td>
            
              <td class="highlight-cell">174,478</td>
            
              <td class="">169,466</td>
            
              <td class="">190,918</td>
            
              <td class="">184,430</td>
            
              <td class="highlight-cell">194,017</td>
            
              <td class="">193,019</td>
            
              <td class="">192,477</td>
            
              <td class="">196,197</td>
            
              <td class="highlight-cell">217,556</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Operating Profit
              
            </td>
            
              <td class="highlight-cell">31,366</td>
            
              <td class="">37,698</td>
            
              <td class="">30,971</td>
            
              <td class="">35,009</td>
            
              <td class="highlight-cell">38,356</td>
            
              <td class="">38,093</td>
            
              <td class="">40,968</td>
            
              <td class="">40,656</td>
            
              <td class="highlight-cell">42,516</td>
            
              <td class="">38,765</td>
            
              <td class="">39,058</td>
            
              <td class="">43,789</td>
            
              <td class="highlight-cell">43,832</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                OPM %
              
            </td>
            
              <td class="highlight-cell">15%</td>
            
              <td class="">17%</td>
            
              <td class="">14%</td>
            
              <td class="">16%</td>
            
              <td class="highlight-cell">18%</td>
            
              <td class="">18%</td>
            
              <td class="">18%</td>
            
              <td class="">18%</td>
            
              <td class="highlight-cell">18%</td>
            
              <td class="">17%</td>
            
              <td class="">17%</td>
            
              <td class="">18%</td>
            
              <td class="highlight-cell">17%</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'quarters', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">2,602</td>
            
              <td class="">2,275</td>
            
              <td class="">3,656</td>
            
              <td class="">3,377</td>
            
              <td class="highlight-cell">2,996</td>
            
              <td class="">3,813</td>
            
              <td class="">3,841</td>
            
              <td class="">3,869</td>
            
              <td class="highlight-cell">4,534</td>
            
              <td class="">3,983</td>
            
              <td class="">4,876</td>
            
              <td class="">4,214</td>
            
              <td class="highlight-cell">4,905</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="highlight-cell">3,556</td>
            
              <td class="">3,997</td>
            
              <td class="">4,554</td>
            
              <td class="">5,201</td>
            
              <td class="highlight-cell">5,819</td>
            
              <td class="">5,837</td>
            
              <td class="">5,731</td>
            
              <td class="">5,789</td>
            
              <td class="highlight-cell">5,761</td>
            
              <td class="">5,918</td>
            
              <td class="">6,017</td>
            
              <td class="">6,179</td>
            
              <td class="highlight-cell">6,155</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="highlight-cell">8,001</td>
            
              <td class="">8,942</td>
            
              <td class="">9,726</td>
            
              <td class="">10,183</td>
            
              <td class="highlight-cell">11,452</td>
            
              <td class="">11,775</td>
            
              <td class="">12,585</td>
            
              <td class="">12,903</td>
            
              <td class="highlight-cell">13,569</td>
            
              <td class="">13,596</td>
            
              <td class="">12,880</td>
            
              <td class="">13,181</td>
            
              <td class="highlight-cell">13,479</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="highlight-cell">22,411</td>
            
              <td class="">27,034</td>
            
              <td class="">20,347</td>
            
              <td class="">23,002</td>
            
              <td class="highlight-cell">24,081</td>
            
              <td class="">24,294</td>
            
              <td class="">26,493</td>
            
              <td class="">25,833</td>
            
              <td class="highlight-cell">27,720</td>
            
              <td class="">23,234</td>
            
              <td class="">25,037</td>
            
              <td class="">28,643</td>
            
              <td class="highlight-cell">29,103</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="highlight-cell">20%</td>
            
              <td class="">28%</td>
            
              <td class="">24%</td>
            
              <td class="">23%</td>
            
              <td class="highlight-cell">11%</td>
            
              <td class="">25%</td>
            
              <td class="">25%</td>
            
              <td class="">25%</td>
            
              <td class="highlight-cell">24%</td>
            
              <td class="">25%</td>
            
              <td class="">24%</td>
            
              <td class="">24%</td>
            
              <td class="highlight-cell">23%</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'quarters', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="highlight-cell">18,021</td>
            
              <td class="">19,443</td>
            
              <td class="">15,512</td>
            
              <td class="">17,806</td>
            
              <td class="highlight-cell">21,327</td>
            
              <td class="">18,258</td>
            
              <td class="">19,878</td>
            
              <td class="">19,641</td>
            
              <td class="highlight-cell">21,243</td>
            
              <td class="">17,445</td>
            
              <td class="">19,323</td>
            
              <td class="">21,930</td>
            
              <td class="highlight-cell">22,611</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="highlight-cell">11.98</td>
            
              <td class="">13.27</td>
            
              <td class="">10.09</td>
            
              <td class="">11.67</td>
            
              <td class="highlight-cell">14.26</td>
            
              <td class="">11.83</td>
            
              <td class="">12.85</td>
            
              <td class="">12.76</td>
            
              <td class="highlight-cell">14.01</td>
            
              <td class="">11.19</td>
            
              <td class="">12.24</td>
            
              <td class="">13.70</td>
            
              <td class="highlight-cell">14.34</td>
            
          </tr>
        
      

      
        <tr class="font-size-14 ink-600">
          <td class="text ink-600">Raw PDF</td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/2726/3/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2726/6/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2726/9/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2726/12/2022/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/2726/3/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2726/6/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2726/9/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2726/12/2023/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/2726/3/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2726/6/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2726/9/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link ">
              <a href="/company/source/quarter/2726/12/2024/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
            <td class="hover-link highlight-cell">
              <a href="/company/source/quarter/2726/3/2025/"
                 target="_blank"
                 rel="noopener noreferrer"
                 aria-label="Raw PDF"
                 class="plausible-event-name=Quarterly+PDF plausible-event-user=unregistered">
                <i class="icon-file-pdf ink-600-link"></i>
              </a>
            </td>
          
        </tr>
      
    </tbody>
  </table>
</div>

      

      
    </section>

    <section id="profit-loss" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Profit & Loss</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/RELIANCE/#profit-loss"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    
      <button data-url="/results/rpt/2726/consolidated/"
              data-title="Related Party Transactions"
              onclick="Modal.openInModal(event)"
              class="button-small button-secondary plausible-event-name=Related+party plausible-event-user=unregistered">
        <i class="small icon-users"></i>
        Related Party
      </button>
    

    
      <button data-segment-button-close
              onclick="Segment.closeSegments('profit-loss')"
              class="button-small hidden">
        <i class="icon-cancel"></i>
        Close Segments
      </button>

      
        <button data-segment-button="1"
                onclick="Segment.showSegment('profit-loss', '1')"
                class="button-small button-secondary plausible-event-name=Segment+Results plausible-event-user=unregistered">
          <i class="icon-chart-pie"></i>
          Product Segments
        </button>
      
    

    
  </div>
</div>

<div class="responsive-holder hidden" data-segment-table></div>

<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Sales', 'profit-loss', this)">
                  Sales&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">433,521</td>
            
              <td class="">374,372</td>
            
              <td class="">272,583</td>
            
              <td class="">303,954</td>
            
              <td class="">390,823</td>
            
              <td class="">568,337</td>
            
              <td class="">596,679</td>
            
              <td class="">466,307</td>
            
              <td class="">694,673</td>
            
              <td class="">876,396</td>
            
              <td class="">899,041</td>
            
              <td class="">964,693</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Expenses', 'profit-loss', this)">
                  Expenses&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">398,586</td>
            
              <td class="">336,923</td>
            
              <td class="">230,802</td>
            
              <td class="">257,647</td>
            
              <td class="">326,508</td>
            
              <td class="">484,087</td>
            
              <td class="">507,413</td>
            
              <td class="">385,517</td>
            
              <td class="">586,092</td>
            
              <td class="">734,078</td>
            
              <td class="">736,543</td>
            
              <td class="">799,249</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Operating Profit
              
            </td>
            
              <td class="">34,935</td>
            
              <td class="">37,449</td>
            
              <td class="">41,781</td>
            
              <td class="">46,307</td>
            
              <td class="">64,315</td>
            
              <td class="">84,250</td>
            
              <td class="">89,266</td>
            
              <td class="">80,790</td>
            
              <td class="">108,581</td>
            
              <td class="">142,318</td>
            
              <td class="">162,498</td>
            
              <td class="">165,444</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                OPM %
              
            </td>
            
              <td class="">8%</td>
            
              <td class="">10%</td>
            
              <td class="">15%</td>
            
              <td class="">15%</td>
            
              <td class="">16%</td>
            
              <td class="">15%</td>
            
              <td class="">15%</td>
            
              <td class="">17%</td>
            
              <td class="">16%</td>
            
              <td class="">16%</td>
            
              <td class="">18%</td>
            
              <td class="">17%</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Income', 'profit-loss', this)">
                  Other Income&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">8,865</td>
            
              <td class="">8,528</td>
            
              <td class="">12,212</td>
            
              <td class="">9,222</td>
            
              <td class="">9,869</td>
            
              <td class="">8,406</td>
            
              <td class="">8,570</td>
            
              <td class="">22,432</td>
            
              <td class="">19,600</td>
            
              <td class="">12,020</td>
            
              <td class="">16,179</td>
            
              <td class="">17,978</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Interest
              
            </td>
            
              <td class="">3,836</td>
            
              <td class="">3,316</td>
            
              <td class="">3,691</td>
            
              <td class="">3,849</td>
            
              <td class="">8,052</td>
            
              <td class="">16,495</td>
            
              <td class="">22,027</td>
            
              <td class="">21,189</td>
            
              <td class="">14,584</td>
            
              <td class="">19,571</td>
            
              <td class="">23,118</td>
            
              <td class="">24,269</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Depreciation
              
            </td>
            
              <td class="">11,201</td>
            
              <td class="">11,547</td>
            
              <td class="">11,565</td>
            
              <td class="">11,646</td>
            
              <td class="">16,706</td>
            
              <td class="">20,934</td>
            
              <td class="">22,203</td>
            
              <td class="">26,572</td>
            
              <td class="">29,782</td>
            
              <td class="">40,303</td>
            
              <td class="">50,832</td>
            
              <td class="">53,136</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Profit before tax
              
            </td>
            
              <td class="">28,763</td>
            
              <td class="">31,114</td>
            
              <td class="">38,737</td>
            
              <td class="">40,034</td>
            
              <td class="">49,426</td>
            
              <td class="">55,227</td>
            
              <td class="">53,606</td>
            
              <td class="">55,461</td>
            
              <td class="">83,815</td>
            
              <td class="">94,464</td>
            
              <td class="">104,727</td>
            
              <td class="">106,017</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Tax %
              
            </td>
            
              <td class="">22%</td>
            
              <td class="">24%</td>
            
              <td class="">23%</td>
            
              <td class="">25%</td>
            
              <td class="">27%</td>
            
              <td class="">28%</td>
            
              <td class="">26%</td>
            
              <td class="">3%</td>
            
              <td class="">19%</td>
            
              <td class="">22%</td>
            
              <td class="">25%</td>
            
              <td class="">24%</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Net Profit', 'profit-loss', this)">
                  Net Profit&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">22,548</td>
            
              <td class="">23,640</td>
            
              <td class="">29,861</td>
            
              <td class="">29,833</td>
            
              <td class="">36,080</td>
            
              <td class="">39,837</td>
            
              <td class="">39,880</td>
            
              <td class="">53,739</td>
            
              <td class="">67,845</td>
            
              <td class="">74,088</td>
            
              <td class="">79,020</td>
            
              <td class="">81,309</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                EPS in Rs
              
            </td>
            
              <td class="">16.31</td>
            
              <td class="">17.07</td>
            
              <td class="">21.51</td>
            
              <td class="">21.55</td>
            
              <td class="">26.69</td>
            
              <td class="">29.28</td>
            
              <td class="">29.10</td>
            
              <td class="">38.75</td>
            
              <td class="">44.87</td>
            
              <td class="">49.29</td>
            
              <td class="">51.45</td>
            
              <td class="">51.47</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Dividend Payout %
              
            </td>
            
              <td class="">12%</td>
            
              <td class="">12%</td>
            
              <td class="">10%</td>
            
              <td class="">11%</td>
            
              <td class="">10%</td>
            
              <td class="">10%</td>
            
              <td class="">10%</td>
            
              <td class="">9%</td>
            
              <td class="">9%</td>
            
              <td class="">9%</td>
            
              <td class="">10%</td>
            
              <td class="">11%</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>


      <div style="display: grid;
                  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                  gap: 2%">
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Sales Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>10%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>10%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>12%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>7%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Compounded Profit Growth</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>12%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>10%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>6%</td>
          </tr>
          <tr>
            <td>TTM:</td>
            <td>0%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Stock Price CAGR</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>22%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>17%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>8%</td>
          </tr>
          <tr>
            <td>1 Year:</td>
            <td>2%</td>
          </tr>
        </table>
        <table class="ranges-table">
          <tr>
            <th colspan="2">Return on Equity</th>
          </tr>
          <tr>
            <td>10 Years:</td>
            <td>9%</td>
          </tr>
          <tr>
            <td>5 Years:</td>
            <td>9%</td>
          </tr>
          <tr>
            <td>3 Years:</td>
            <td>9%</td>
          </tr>
          <tr>
            <td>Last Year:</td>
            <td>9%</td>
          </tr>
        </table>
      </div>
    </section>

    <section id="balance-sheet" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Balance Sheet</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/RELIANCE/#balance-sheet"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
      <button type="button"
              class="button-small button-secondary plausible-event-name=Corporate+Actions plausible-event-user=unregistered"
              onclick="Modal.openInModal(event)"
              data-title="Corporate actions"
              data-url="/company/actions/2726/">Corporate actions</button>
    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                Equity Capital
              
            </td>
            
              <td class="">2,940</td>
            
              <td class="">2,943</td>
            
              <td class="">2,948</td>
            
              <td class="">2,959</td>
            
              <td class="">5,922</td>
            
              <td class="">5,926</td>
            
              <td class="">6,339</td>
            
              <td class="">6,445</td>
            
              <td class="">6,765</td>
            
              <td class="">6,766</td>
            
              <td class="">6,766</td>
            
              <td class="">13,532</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Reserves
              
            </td>
            
              <td class="">195,747</td>
            
              <td class="">215,556</td>
            
              <td class="">228,608</td>
            
              <td class="">260,750</td>
            
              <td class="">287,584</td>
            
              <td class="">381,186</td>
            
              <td class="">442,827</td>
            
              <td class="">693,727</td>
            
              <td class="">772,720</td>
            
              <td class="">709,106</td>
            
              <td class="">786,715</td>
            
              <td class="">829,668</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Borrowings', 'balance-sheet', this)">
                  Borrowings&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">138,761</td>
            
              <td class="">168,251</td>
            
              <td class="">194,714</td>
            
              <td class="">217,475</td>
            
              <td class="">239,843</td>
            
              <td class="">307,714</td>
            
              <td class="">355,133</td>
            
              <td class="">278,962</td>
            
              <td class="">319,158</td>
            
              <td class="">451,664</td>
            
              <td class="">458,991</td>
            
              <td class="">369,575</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Liabilities', 'balance-sheet', this)">
                  Other Liabilities&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">91,395</td>
            
              <td class="">117,736</td>
            
              <td class="">172,727</td>
            
              <td class="">225,618</td>
            
              <td class="">277,924</td>
            
              <td class="">302,804</td>
            
              <td class="">358,716</td>
            
              <td class="">340,931</td>
            
              <td class="">399,979</td>
            
              <td class="">438,346</td>
            
              <td class="">502,576</td>
            
              <td class="">737,346</td>
            
          </tr>
        
      
        
          <tr class="stripe strong">
            <td class="text">
              
                Total Liabilities
              
            </td>
            
              <td class="">428,843</td>
            
              <td class="">504,486</td>
            
              <td class="">598,997</td>
            
              <td class="">706,802</td>
            
              <td class="">811,273</td>
            
              <td class="">997,630</td>
            
              <td class="">1,163,015</td>
            
              <td class="">1,320,065</td>
            
              <td class="">1,498,622</td>
            
              <td class="">1,605,882</td>
            
              <td class="">1,755,048</td>
            
              <td class="">1,950,121</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Fixed Assets', 'balance-sheet', this)">
                  Fixed Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">141,417</td>
            
              <td class="">156,458</td>
            
              <td class="">184,910</td>
            
              <td class="">198,526</td>
            
              <td class="">403,885</td>
            
              <td class="">398,374</td>
            
              <td class="">532,658</td>
            
              <td class="">541,258</td>
            
              <td class="">627,798</td>
            
              <td class="">724,805</td>
            
              <td class="">779,985</td>
            
              <td class="">1,092,041</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                CWIP
              
            </td>
            
              <td class="">91,494</td>
            
              <td class="">166,462</td>
            
              <td class="">228,697</td>
            
              <td class="">324,837</td>
            
              <td class="">187,022</td>
            
              <td class="">179,463</td>
            
              <td class="">109,106</td>
            
              <td class="">125,953</td>
            
              <td class="">172,506</td>
            
              <td class="">293,752</td>
            
              <td class="">338,855</td>
            
              <td class="">169,710</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Investments
              
            </td>
            
              <td class="">60,602</td>
            
              <td class="">76,451</td>
            
              <td class="">84,015</td>
            
              <td class="">82,899</td>
            
              <td class="">82,862</td>
            
              <td class="">235,635</td>
            
              <td class="">276,767</td>
            
              <td class="">364,828</td>
            
              <td class="">394,264</td>
            
              <td class="">235,560</td>
            
              <td class="">225,672</td>
            
              <td class="">242,381</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Other Assets', 'balance-sheet', this)">
                  Other Assets&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">135,330</td>
            
              <td class="">105,115</td>
            
              <td class="">101,375</td>
            
              <td class="">100,540</td>
            
              <td class="">137,504</td>
            
              <td class="">184,158</td>
            
              <td class="">244,484</td>
            
              <td class="">288,026</td>
            
              <td class="">304,054</td>
            
              <td class="">351,765</td>
            
              <td class="">410,536</td>
            
              <td class="">445,989</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Total Assets
              
            </td>
            
              <td class="">428,843</td>
            
              <td class="">504,486</td>
            
              <td class="">598,997</td>
            
              <td class="">706,802</td>
            
              <td class="">811,273</td>
            
              <td class="">997,630</td>
            
              <td class="">1,163,015</td>
            
              <td class="">1,320,065</td>
            
              <td class="">1,498,622</td>
            
              <td class="">1,605,882</td>
            
              <td class="">1,755,048</td>
            
              <td class="">1,950,121</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="cash-flow" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Cash Flows</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/RELIANCE/#cash-flow"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Operating Activity', 'cash-flow', this)">
                  Cash from Operating Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">43,261</td>
            
              <td class="">34,374</td>
            
              <td class="">38,134</td>
            
              <td class="">49,550</td>
            
              <td class="">71,459</td>
            
              <td class="">42,346</td>
            
              <td class="">94,877</td>
            
              <td class="">26,958</td>
            
              <td class="">110,654</td>
            
              <td class="">115,032</td>
            
              <td class="">158,788</td>
            
              <td class="">178,703</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Investing Activity', 'cash-flow', this)">
                  Cash from Investing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">-73,070</td>
            
              <td class="">-64,706</td>
            
              <td class="">-36,186</td>
            
              <td class="">-66,201</td>
            
              <td class="">-68,192</td>
            
              <td class="">-94,507</td>
            
              <td class="">-72,497</td>
            
              <td class="">-142,385</td>
            
              <td class="">-109,162</td>
            
              <td class="">-93,001</td>
            
              <td class="">-113,581</td>
            
              <td class="">-137,535</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                <button class="button-plain"
                        onclick="Company.showSchedule('Cash from Financing Activity', 'cash-flow', this)">
                  Cash from Financing Activity&nbsp;<span class="blue-icon">+</span>
                </button>
              
            </td>
            
              <td class="">13,713</td>
            
              <td class="">8,444</td>
            
              <td class="">-3,210</td>
            
              <td class="">8,617</td>
            
              <td class="">-2,001</td>
            
              <td class="">55,906</td>
            
              <td class="">-2,541</td>
            
              <td class="">101,904</td>
            
              <td class="">17,289</td>
            
              <td class="">10,455</td>
            
              <td class="">-16,646</td>
            
              <td class="">-31,891</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Net Cash Flow
              
            </td>
            
              <td class="">-16,096</td>
            
              <td class="">-21,888</td>
            
              <td class="">-1,262</td>
            
              <td class="">-8,034</td>
            
              <td class="">1,266</td>
            
              <td class="">3,745</td>
            
              <td class="">19,839</td>
            
              <td class="">-13,523</td>
            
              <td class="">18,781</td>
            
              <td class="">32,486</td>
            
              <td class="">28,561</td>
            
              <td class="">9,277</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="ratios" class="card card-large">
      


<div class="flex-row flex-space-between flex-gap-16">
  <div>
    <h2>Ratios</h2>

    <p class="sub" style="margin: 0;">
      
        Consolidated Figures in Rs. Crores
        /
        <a href="/company/RELIANCE/#ratios"
           class="">View Standalone</a>
      
    </p>
  </div>

  <div class="flex flex-gap-8"
       style="justify-content: flex-end;
              align-items: center">
    

    

    
  </div>
</div>



<div class="responsive-holder fill-card-width" data-result-table>
  <table class="data-table responsive-text-nowrap">
    <thead>
      <tr>
        <th class="text"></th>
        
          <th class="">
            Mar 2014
            
          </th>
        
          <th class="">
            Mar 2015
            
          </th>
        
          <th class="">
            Mar 2016
            
          </th>
        
          <th class="">
            Mar 2017
            
          </th>
        
          <th class="">
            Mar 2018
            
          </th>
        
          <th class="">
            Mar 2019
            
          </th>
        
          <th class="">
            Mar 2020
            
          </th>
        
          <th class="">
            Mar 2021
            
          </th>
        
          <th class="">
            Mar 2022
            
          </th>
        
          <th class="">
            Mar 2023
            
          </th>
        
          <th class="">
            Mar 2024
            
          </th>
        
          <th class="">
            Mar 2025
            
          </th>
        
      </tr>
    </thead>
    <tbody>
      
        
          <tr class="stripe">
            <td class="text">
              
                Debtor Days
              
            </td>
            
              <td class="">8</td>
            
              <td class="">5</td>
            
              <td class="">6</td>
            
              <td class="">10</td>
            
              <td class="">16</td>
            
              <td class="">19</td>
            
              <td class="">12</td>
            
              <td class="">15</td>
            
              <td class="">12</td>
            
              <td class="">12</td>
            
              <td class="">13</td>
            
              <td class="">16</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                Inventory Days
              
            </td>
            
              <td class="">57</td>
            
              <td class="">66</td>
            
              <td class="">90</td>
            
              <td class="">84</td>
            
              <td class="">83</td>
            
              <td class="">63</td>
            
              <td class="">67</td>
            
              <td class="">102</td>
            
              <td class="">83</td>
            
              <td class="">87</td>
            
              <td class="">95</td>
            
              <td class="">85</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Days Payable
              
            </td>
            
              <td class="">61</td>
            
              <td class="">74</td>
            
              <td class="">117</td>
            
              <td class="">132</td>
            
              <td class="">146</td>
            
              <td class="">100</td>
            
              <td class="">87</td>
            
              <td class="">136</td>
            
              <td class="">123</td>
            
              <td class="">91</td>
            
              <td class="">111</td>
            
              <td class="">108</td>
            
          </tr>
        
      
        
          <tr class=" strong">
            <td class="text">
              
                Cash Conversion Cycle
              
            </td>
            
              <td class="">4</td>
            
              <td class="">-2</td>
            
              <td class="">-21</td>
            
              <td class="">-38</td>
            
              <td class="">-46</td>
            
              <td class="">-18</td>
            
              <td class="">-9</td>
            
              <td class="">-19</td>
            
              <td class="">-27</td>
            
              <td class="">7</td>
            
              <td class="">-3</td>
            
              <td class="">-8</td>
            
          </tr>
        
      
        
          <tr class="stripe">
            <td class="text">
              
                Working Capital Days
              
            </td>
            
              <td class="">-3</td>
            
              <td class="">-37</td>
            
              <td class="">-117</td>
            
              <td class="">-141</td>
            
              <td class="">-144</td>
            
              <td class="">-67</td>
            
              <td class="">-101</td>
            
              <td class="">12</td>
            
              <td class="">-13</td>
            
              <td class="">-9</td>
            
              <td class="">-10</td>
            
              <td class="">21</td>
            
          </tr>
        
      
        
          <tr class="">
            <td class="text">
              
                ROCE %
              
            </td>
            
              <td class="">10%</td>
            
              <td class="">9%</td>
            
              <td class="">10%</td>
            
              <td class="">10%</td>
            
              <td class="">11%</td>
            
              <td class="">12%</td>
            
              <td class="">11%</td>
            
              <td class="">8%</td>
            
              <td class="">8%</td>
            
              <td class="">9%</td>
            
              <td class="">10%</td>
            
              <td class="">9%</td>
            
          </tr>
        
      

      
    </tbody>
  </table>
</div>

    </section>

    <section id="shareholding" class="card card-large">
      <div class="flex flex-space-between flex-wrap margin-bottom-8 flex-align-center">
  <div>
    <h2 class="margin-0">Shareholding Pattern</h2>
    <p class="sub">Numbers in percentages</p>
  </div>
  <div class="flex">
    <div class="options small margin-0">
      <button
        onclick="Utils.setActiveTab(event)"
        class="active"
        data-tab-id="quarterly-shp"
        type="button">
        Quarterly
      </button>
      <button
        onclick="Utils.setActiveTab(event)"
        data-tab-id="yearly-shp"
        type="button">
        Yearly
      </button>
    </div>
    
    <div class="text-align-center margin-left-12">
      <button
        type="button"
        class="button-secondary button-small"
        onclick="Modal.openInModal(event)"
        data-title="Reliance Industr trades"
        data-url="/trades/company-2726/"
      >
        Trades
      </button>
      
        <div class="ink-700 font-size-12" style="position: absolute; padding-left: 15px;">
          2 Recently
        </div>
      
    </div>
    
  </div>
</div>

<div id="quarterly-shp">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Jun 2022</th><th>Sep 2022</th><th>Dec 2022</th><th>Mar 2023</th><th>Jun 2023</th><th>Sep 2023</th><th>Dec 2023</th><th>Mar 2024</th><th>Jun 2024</th><th>Sep 2024</th><th>Dec 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=promoters plausible-event-period=quarterly"
                      onclick="Company.showShareholders('promoters', 'quarterly', this)">
                Promoters&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>50.62%</td>
            
              <td>50.56%</td>
            
              <td>50.49%</td>
            
              <td>50.41%</td>
            
              <td>50.39%</td>
            
              <td>50.27%</td>
            
              <td>50.30%</td>
            
              <td>50.31%</td>
            
              <td>50.33%</td>
            
              <td>50.24%</td>
            
              <td>50.13%</td>
            
              <td>50.10%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=foreign_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('foreign_institutions', 'quarterly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>23.90%</td>
            
              <td>23.58%</td>
            
              <td>23.48%</td>
            
              <td>22.49%</td>
            
              <td>22.55%</td>
            
              <td>22.60%</td>
            
              <td>22.13%</td>
            
              <td>22.06%</td>
            
              <td>21.75%</td>
            
              <td>21.30%</td>
            
              <td>19.16%</td>
            
              <td>19.07%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=domestic_institutions plausible-event-period=quarterly"
                      onclick="Company.showShareholders('domestic_institutions', 'quarterly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>14.67%</td>
            
              <td>14.91%</td>
            
              <td>15.26%</td>
            
              <td>16.06%</td>
            
              <td>16.13%</td>
            
              <td>15.99%</td>
            
              <td>16.59%</td>
            
              <td>16.98%</td>
            
              <td>17.30%</td>
            
              <td>17.61%</td>
            
              <td>19.02%</td>
            
              <td>19.36%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=government plausible-event-period=quarterly"
                      onclick="Company.showShareholders('government', 'quarterly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.17%</td>
            
              <td>0.16%</td>
            
              <td>0.16%</td>
            
              <td>0.16%</td>
            
              <td>0.17%</td>
            
              <td>0.17%</td>
            
              <td>0.18%</td>
            
              <td>0.19%</td>
            
              <td>0.19%</td>
            
              <td>0.19%</td>
            
              <td>0.18%</td>
            
              <td>0.17%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=public plausible-event-period=quarterly"
                      onclick="Company.showShareholders('public', 'quarterly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>10.64%</td>
            
              <td>10.78%</td>
            
              <td>10.59%</td>
            
              <td>10.89%</td>
            
              <td>10.76%</td>
            
              <td>10.98%</td>
            
              <td>10.80%</td>
            
              <td>10.46%</td>
            
              <td>10.43%</td>
            
              <td>10.67%</td>
            
              <td>11.52%</td>
            
              <td>11.29%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>33,06,732</td><td>34,85,825</td><td>33,62,915</td><td>36,39,396</td><td>35,06,867</td><td>36,98,648</td><td>36,13,814</td><td>34,63,276</td><td>34,93,125</td><td>38,34,968</td><td>47,14,959</td><td>47,65,728</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>

<div id="yearly-shp" class="hidden">
  


  <div class="responsive-holder fill-card-width">
    <table class="data-table">
      <thead>
        <tr>
          <th class="text"></th>
          <th>Mar 2017</th><th>Mar 2018</th><th>Mar 2019</th><th>Mar 2020</th><th>Mar 2021</th><th>Mar 2022</th><th>Mar 2023</th><th>Mar 2024</th><th>Mar 2025</th>
        </tr>
      </thead>
      <tbody>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=promoters plausible-event-period=yearly"
                      onclick="Company.showShareholders('promoters', 'yearly', this)">
                Promoters&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>46.32%</td>
            
              <td>47.45%</td>
            
              <td>47.27%</td>
            
              <td>50.07%</td>
            
              <td>50.58%</td>
            
              <td>50.66%</td>
            
              <td>50.41%</td>
            
              <td>50.31%</td>
            
              <td>50.10%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=foreign_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('foreign_institutions', 'yearly', this)">
                FIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>22.58%</td>
            
              <td>24.46%</td>
            
              <td>24.39%</td>
            
              <td>24.08%</td>
            
              <td>25.66%</td>
            
              <td>24.23%</td>
            
              <td>22.49%</td>
            
              <td>22.06%</td>
            
              <td>19.07%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=domestic_institutions plausible-event-period=yearly"
                      onclick="Company.showShareholders('domestic_institutions', 'yearly', this)">
                DIIs&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>11.85%</td>
            
              <td>11.23%</td>
            
              <td>11.86%</td>
            
              <td>13.78%</td>
            
              <td>12.62%</td>
            
              <td>14.23%</td>
            
              <td>16.06%</td>
            
              <td>16.98%</td>
            
              <td>19.36%</td>
            
          </tr>
        
          <tr class="">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=government plausible-event-period=yearly"
                      onclick="Company.showShareholders('government', 'yearly', this)">
                Government&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>0.14%</td>
            
              <td>0.15%</td>
            
              <td>0.18%</td>
            
              <td>0.20%</td>
            
              <td>0.20%</td>
            
              <td>0.17%</td>
            
              <td>0.16%</td>
            
              <td>0.19%</td>
            
              <td>0.17%</td>
            
          </tr>
        
          <tr class="stripe">
            <td class="text">
              <button class="button-plain plausible-event-name=View+Shareholders plausible-event-user=unregistered plausible-event-classification=public plausible-event-period=yearly"
                      onclick="Company.showShareholders('public', 'yearly', this)">
                Public&nbsp;<span class="blue-icon">+</span>
              </button>
            </td>
            
              <td>19.12%</td>
            
              <td>16.72%</td>
            
              <td>16.29%</td>
            
              <td>11.87%</td>
            
              <td>10.94%</td>
            
              <td>10.71%</td>
            
              <td>10.89%</td>
            
              <td>10.46%</td>
            
              <td>11.29%</td>
            
          </tr>
        
        <tr class="sub">
          <td class="text">No. of Shareholders</td>
          <td>25,01,302</td><td>22,66,000</td><td>22,11,231</td><td>26,32,168</td><td>30,31,272</td><td>33,27,847</td><td>36,39,396</td><td>34,63,276</td><td>47,65,728</td>
        </tr>
      </tbody>
    </table>
  </div>


</div>


<p class="sub small">
  * The classifications might have changed from Sep'2022 onwards.
  <span class="has-tooltip">
    <i class="icon-info"></i>
    <span class="tooltip" style="width: 300px">
      The new XBRL format added more details from Sep'22 onwards.
      <br>
      <br>
      Classifications such as banks and foreign portfolio investors were not available earlier. The sudden changes in FII or DII can be because of these changes.
      <br>
      <br>
      Click on the line-items to see the names of individual entities.
    </span>
  </span>
</p>


    </section>

    <section id="documents" class="card card-large">
      <div class="flex flex-space-between margin-bottom-16">
        <h2 class="margin-0">Documents</h2>
      </div>

      <div class="flex-row flex-gap-small">
        
          <div class="documents flex-column" style="flex: 2 1 250px;">
            <h3 class="margin-bottom-8">Announcements</h3>

            <div class="show-more-box" style="flex-basis: 300px">
              <div id="company-announcements-tab">
                <div class="flex">
  <div class="options small" style="margin-bottom: 0">
    <button
      type="button"
      class="active"
      
      onclick="Utils.ajaxLoad(event, '/announcements/recent/2726/')"
      data-swap="#company-announcements-tab"
    >
      Recent
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/important/2726/')"
      data-swap="#company-announcements-tab"
    >
      Important
    </button>
    <button
      type="button"
      
      
      onclick="Utils.ajaxLoad(event, '/announcements/search/2726/')"
      data-swap="#company-announcements-tab"
    >
      Search
    </button>
    <a class="button" href="https://www.bseindia.com/stock-share-price/reliance-industries-ltd/reliance/500325/corp-announcements/" target="_blank" rel="noopener noreferrer">
      All
      <i class="icon-link-ext smaller"></i>
    </a>
  </div>
</div>


  
  <ul class="list-links">
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d84f2226-ca48-44a0-b9f1-dd7de4e2e1cb.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Dissolution Of Skytran Inc.

          
            <div class="ink-600 smaller">8 May - skyTran Inc., non-operating subsidiary, voluntarily dissolved effective May 7, 2025, no financial impact.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=b485ed5f-6e0e-43ca-b27d-3a563fe79ad3.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Media Release

          
            <div class="ink-600 smaller">5 May - Completed India&#x27;s first offshore decommissioning project at Tapti fields, enhancing local capabilities and regulatory framework.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=b40cf8de-d19d-4acd-9e48-a7d54c222f30.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Update To Disclosure Dated June 21, 2022

          
            <div class="ink-600 smaller">3 May - SEBI penalty of Rs. 30 lakh upheld by SAT for Jio-Facebook deal disclosure violation.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d08d0adf-ab4a-4d6d-bb0d-d49671eddb8c.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Trading Plan under SEBI (PIT) Regulations, 2015

          
            <div class="ink-600 smaller">2 May - Notification of trading plan for sale of 125,000 shares by senior advisor&#x27;s relative in Sept 2025.</div>
          
        </a>
      </li>
    
      <li class="overflow-wrap-anywhere">
        <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=87d1669d-c93c-486c-8474-24b5e38a8825.pdf"
           target="_blank"
           rel="noopener noreferrer">
          Announcement under Regulation 30 (LODR)-Allotment of ESOP / ESPS

          
            <div class="ink-600 smaller">30 Apr - The Company has today allotted, 99,736 equity shares of Rs. 10/- each pursuant to Reliance Industries Limited Employees&#x27;&#x27; Stock Option Scheme 2017.</div>
          
        </a>
      </li>
    
  </ul>




              </div>
            </div>
          </div>
        

        <div class="documents annual-reports flex-column"
             style="flex: 1 1 200px;
                    max-width: 210px">
          <h3>Annual reports</h3>
          <div class="show-more-box">
            
<ul class="list-links">

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d473280d-1c2b-4037-8ff9-dfbb82aa2c7e.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2024
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=\b55b5dfc-a3bf-4f24-9d7f-ca09774a1dd9.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2023
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500325/74185500325.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2022
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500325/68509500325.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2021
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500325/5003250320.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2020
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500325/5003250319.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2019
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500325/5003250318.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2018
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500325/5003250317.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2017
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500325/5003250316.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2016
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500325/5003250315.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2015
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500325/5003250314.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2014
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://archives.nseindia.com/annual_reports/AR_19_RELIANCE_2012_2013_08052013171218.zip" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2013
      <div class="ink-600 smaller">
        from nse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500325/5003250313.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2013
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.bseindia.com/bseplus/AnnualReport/500325/5003250312.pdf" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2012
      <div class="ink-600 smaller">
        from bse
      </div>
    </a>
  </li>

  <li>
    <a href="https://archives.nseindia.com/annual_reports/AR_RELIANCE_2011_2012_29062012101059.zip" class="plausible-event-name=Annual+Report plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Financial Year 2012
      <div class="ink-600 smaller">
        from nse
      </div>
    </a>
  </li>

</ul>


          </div>
          
        </div>

        
          <div class="documents credit-ratings flex-column"
               style="flex: 1 1 200px;
                      max-width: 220px">
            <h3>Credit ratings</h3>
            <div class="show-more-box">
              
<ul class="list-links">

  <li>
    <a href="https://www.icra.in/Rationale/ShowRationaleReport/?Id=132755" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        30 Jan from icra
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/FirstBusinessReceivablesTrust_January 23_ 2025_RR_360185.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        23 Jan from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/RelianceIndustriesLimited_January 13_ 2025_RR_360983.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        13 Jan from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/RelianceIndustriesLimited_October 30_ 2024_RR_354933.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        30 Oct 2024 from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/RelianceIndustriesLimited_September 19_ 2024_RR_353035.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        19 Sep 2024 from crisil
      </div>
    </a>
  </li>

  <li>
    <a href="https://www.crisil.com/mnt/winshare/Ratings/RatingList/RatingDocs/FirstBusinessReceivablesTrust_July 17_ 2024_RR_348259.html" class="plausible-event-name=Credit+Rating plausible-event-user=unregistered" target="_blank" rel="noopener noreferrer">
      Rating update
      <div class="ink-600 smaller">
        17 Jul 2024 from crisil
      </div>
    </a>
  </li>

</ul>


            </div>
          </div>
        

        
          <div class="documents concalls flex-column"
               style="flex: 2 1 250px;
                      max-width: fit-content">
            <div class="flex flex-space-between flex-align-center margin-bottom-8">
              <h3 class="margin-0">Concalls</h3>
              <button class="a font-size-12 font-weight-500 sub"
                      onclick="Modal.openInModal(event)"
                      data-url="/concalls/add-2726/"
                      data-title="Reliance Industr - Add Link">
                <i class="icon-flag"></i>
                <span class="underline-link">Add Missing</span>
              </button>
            </div>
            <div class="show-more-box"><ul class="list-links">

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2025
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=b6203612-be21-4d28-ace9-6496cd3fb8b2.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22993912/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2025"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2025
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=3e36b485-0a6c-4f84-bf74-7d60b0e26d9d.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22993911/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2025"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=ac3d5e8f-9a84-4510-99e8-71eabcf49a01.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.youtube.com/watch?v=Tj8c9UfTdXw" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2025
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=aa0547ba-09ef-404d-b57d-c216d1513f17.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22972026/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2025"
    >
      Notes
    </button>
    

    
    <div class="concall-link">PPT</div>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2025
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=89c33a3e-5683-4a1d-b8f9-1952ff4df63f.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22972025/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2025"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=cb5da39b-b46a-45f7-b2b6-932a4f2ecf91.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=0e6309fa-450f-4db6-88b2-d76d9a3ff756.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22949407/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=fd1fe004-0eb0-4a2b-9859-7b62a89e451c.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2024
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=9364b18a-7921-4029-bcfa-78921cd7807e.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=191e6ab9-4751-4017-ba5f-bb63daf2d871.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22929497/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=24faf885-ece1-4852-b0fe-91fc8643dc1c.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=06b05d06-8c38-4aee-aa5b-5e7108ae3871.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/22010117/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=0501df4e-7d64-455d-bbb0-4b2118067e51.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
    <a href="https://www.ril.com/investors/events-presentations#webcast-sec" class="concall-link" target="_blank" rel="noopener noreferrer">
      REC
    </a>
    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2024
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=de3e8ff3-efca-4266-9bc8-7509f48574a4.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/18520636/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2024"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=1cb66ca2-dfd5-4f46-986f-03b0661bd486.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=ef9baa21-923a-48ad-9fb7-2157af59ae16.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/15579504/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=f9c9eba2-4b08-430f-a3a0-0dd5419b7f8f.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Aug 2023
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=7cf2df9a-51bb-45d4-9004-3cad20b5920b.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=f2fa506f-675b-46db-9f7b-26303a05d6b9.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/12406467/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jul 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=dc6d4a06-e2bc-4df4-8b8a-5db0f50b1918.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=9c8c3f07-883a-476b-8fb2-8fe74078e9f5.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/9464587/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Apr 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=7c228ccc-99ad-4bd9-be72-0a746c30ebee.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2023
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=dfb275bd-fd47-4a38-964e-21a67e26b3c2.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/6910417/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Jan 2023"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=1ef9c86c-8f99-4791-a677-ea8a65e4ef28.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2022
    </div>

    
    <a
      class="concall-link"
      href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=aa7a60c6-791d-4e59-94e6-9f94c3a506fc.pdf"
      target="_blank"
      rel="noopener noreferrer"
      title="Raw Transcript"
    >
      Transcript
    </a>
    <button
      class="concall-link plausible-event-name=Concall+Notes plausible-event-user=unregistered"
      type="button"
      data-url="/concalls/summary/4539079/"
      onclick="Modal.openInModal(event)"
      data-is-right-modal="true"
      data-title="Concall Notes - Oct 2022"
    >
      Notes
    </button>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d6182ad1-6f8e-4112-b865-984ea1035107.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=9e81c058-6b85-4f18-ba5a-bbdd72156edf.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      May 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=bf7646f7-940a-429b-b3eb-0625715f6aeb.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=7d172a7c-9056-4588-ad0c-d5c114ff902a.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2022
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=6047c516-bdab-4a88-9103-81d6ddad23d5.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=1008c763-0d31-4432-a031-6dd4afda0c72.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=33e997e6-025e-478e-b71f-fb59dca2a4ef.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=b9139079-b211-4486-a604-ef5b85a7985e.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=caff4f40-fd8f-49ae-929c-f5dae4c98ec3.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Feb 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=4c079f28-425e-4fb4-a830-ae9f4fde355d.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=c44095c1-55cb-4048-a235-6f776dd054e9.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2021
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=ee845952-2c47-4abc-b15a-ab759c068efe.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2020
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=ef91573e-ebb9-4257-8209-b684d9380a3e.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jul 2020
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=c9c7754a-ab5a-4463-a5cb-3baafbef135d.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      May 2020
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=8841c2b4-6659-4ea6-aefd-9874fee15705.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2020
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=a738dab1-1698-4a4f-b863-148b5c5023e0.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2019
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=5f6d2f75-5b1f-4c8b-87eb-34e3c8246fb1.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2019
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=bcf1b986-0d58-4126-b3fa-cf67e7b44e14.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2019
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=05c527f3-c7ff-4e83-a2b3-d7d92b20969d.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2019
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=040c976b-6d40-4903-905e-27820f21eb05.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2018
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=d71a237f-6917-4594-969d-cfd62d1849d9.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Apr 2018
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=e0357498-61d2-405d-98c9-02a1e83b556b.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jan 2018
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=1a49913c-4798-4f48-ab39-e187ea89caf2.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Oct 2017
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=421cf002-f53b-4c1c-a8be-de2fdda6fd05.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

  <li class="flex flex-gap-8 flex-wrap">
    <div class="ink-600 font-size-15 font-weight-500 nowrap" style="width: 74px">
      Jun 2016
    </div>

    
    <div class="concall-link">Transcript</div>
    <div class="concall-link">Notes</div>
    

    
    <a href="https://www.bseindia.com/stockinfo/AnnPdfOpen.aspx?Pname=6669EE6F_088E_4C15_9839_E2DBB861749D_110427.pdf" class="concall-link" target="_blank" rel="noopener noreferrer">
      PPT
    </a>
    

    
  </li>

</ul>
</div>
          </div>
        
      </div>
    </section>
    <!-- cache time 2025-05-16 18:56:54 -->
    
  

      </main>
    

    
      

<footer id="large-footer">
  <div class="flex-row flex-space-between flex-column-tablet container no-print">
    <div class="hide-from-tablet" style="margin-bottom: 16px;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
    </div>

    <div class="show-from-tablet" style="padding: 0 64px 0 0;">
      <img src="https://cdn-static.screener.in/img/logo-black.f44abb4998d1.svg"
           alt="Screener Logo"
           style="max-width: 120px"
           class="logo">
      <p class="font-size-19" style="font-weight: 500;">Stock analysis and screening tool</p>
      <p class="sub">
        Mittal Analytics Private Ltd &copy; 2009-2025
        <br>
        Made with <i class="icon-heart red"></i> in India.
      </p>
      <p class="sub font-size-13">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="font-size-13 sub">
        <a href="/guides/terms/" class="underline-link">Terms</a> & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>

    <div class="flex flex-end flex-space-between flex-grow"
         style="max-width: 600px">
      <div class="flex-grow">
        <div class="title">Product</div>
        <ul class="items">
          <li>
            <a href="/premium/?driver=footer">Premium</a>
          </li>
          <li>
            <a href="/docs/changelog/">What's new?</a>
          </li>
          <li>
            <a href="https://bit.ly/learnscreener">Learn</a>
          </li>
          <li>
            <button class="a2hs button-secondary button-small">
              <i class="icon-flash"></i>
              Install
            </button>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Team</div>
        <ul class="items">
          <li>
            <a href="/guides/about-us/">About us</a>
          </li>
          <li>
            <a href="/support/">Support</a>
          </li>
        </ul>
      </div>

      <div class="flex-grow">
        <div class="title">Theme</div>
        <ul class="items">
          <li>
            <button onclick="SetTheme('light')"
                    aria-label="Set light theme"
                    class="button-plain">
              <i class="icon-sun"></i>
              Light
            </button>
          </li>
          <li>
            <button onclick="SetTheme('dark')"
                    aria-label="Set dark theme"
                    class="button-plain">
              <i class="icon-moon"></i>
              Dark
            </button>
          </li>
          <li>
            <button onclick="SetTheme('auto')"
                    aria-label="Set auto theme"
                    class="button-plain">
              <i class="icon-monitor"></i>
              Auto
            </button>
          </li>
        </ul>
      </div>
    </div>

    <div class="hide-from-tablet">
      <hr>
      <p class="sub">Mittal Analytics Private Ltd &copy; 2009-2024</p>
      <p class="sub">Data provided by C-MOTS Internet Technologies Pvt Ltd</p>
      <p class="sub">
        <a href="/guides/terms/" class="underline-link">Terms</a>
        & <a href="/guides/privacy/" class="underline-link">Privacy</a>.
      </p>
    </div>
  </div>
</footer>

    

    <!-- Important JS (which provide global objects)- check below too -->
    <script src="https://cdn-static.screener.in/js/dialog-polyfill.min.2b5bf9a032d2.js"></script>
    <script src="https://cdn-static.screener.in/js/utils.02a9ce812e49.js"></script>
    <script src="https://cdn-static.screener.in/js/modal.4f872e091355.js"></script>
    <script src="https://cdn-static.screener.in/js/typeahead.8dcc9ac93685.js"></script>
    <script src="https://cdn-static.screener.in/js/filter.component.5add9ef3b7a9.js"></script>

    <!-- Independent JS scripts for page interactions -->
    <script src="https://cdn-static.screener.in/js/navbar.3fe7f0f4a630.js"></script>
    <script src="https://cdn-static.screener.in/js/company.search.e48f94642999.js"></script>
    
  <script src="https://cdn-static.screener.in/js/scroll-aid.5ed2d38a133e.js"></script>
  <script src="https://cdn-static.screener.in/js/company.customisation.d7c717b0de15.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.min.10a29a29621c.js"></script>
  <script src="https://cdn-static.screener.in/js/chartjs-adapter-date-fns.bundle.min.ebae23730a6e.js"></script>
  <script src="https://cdn-static.screener.in/js/chart.e20c5f42613a.js"></script>
  <script src="https://cdn-static.screener.in/js/segment-result.b6af4191fd6f.js"></script>


    <!-- Service worker installer for PWA -->
    <script src="https://cdn-static.screener.in/js/pwa.933118758337.js"></script>
  </body>
</html>
