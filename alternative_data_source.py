#!/usr/bin/env python3
"""
Alternative Data Source for Portfolio Optimization
Creates realistic synthetic data when real data sources are not accessible
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

class AlternativeDataSource:
    """
    Creates realistic synthetic stock data for portfolio optimization testing
    when real data sources are not accessible
    """
    
    def __init__(self, data_dir='synthetic_data'):
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        # Indian stock symbols and their characteristics
        self.stock_info = {
            'RELIANCE': {'sector': 'Energy', 'volatility': 0.25, 'trend': 0.08},
            'TCS': {'sector': 'IT', 'volatility': 0.20, 'trend': 0.12},
            'HDFCBANK': {'sector': 'Banking', 'volatility': 0.30, 'trend': 0.10},
            'INFY': {'sector': 'IT', 'volatility': 0.22, 'trend': 0.11},
            'HINDUNILVR': {'sector': 'FMCG', 'volatility': 0.18, 'trend': 0.09},
            'ICICIBANK': {'sector': 'Banking', 'volatility': 0.32, 'trend': 0.09},
            'SBIN': {'sector': 'Banking', 'volatility': 0.35, 'trend': 0.07},
            'BHARTIARTL': {'sector': 'Telecom', 'volatility': 0.28, 'trend': 0.06},
            'BAJFINANCE': {'sector': 'Financial', 'volatility': 0.40, 'trend': 0.15},
            'KOTAKBANK': {'sector': 'Banking', 'volatility': 0.28, 'trend': 0.11},
            'HCLTECH': {'sector': 'IT', 'volatility': 0.24, 'trend': 0.10},
            'ASIANPAINT': {'sector': 'Paints', 'volatility': 0.26, 'trend': 0.12},
            'AXISBANK': {'sector': 'Banking', 'volatility': 0.33, 'trend': 0.08},
            'MARUTI': {'sector': 'Auto', 'volatility': 0.30, 'trend': 0.07},
            'SUNPHARMA': {'sector': 'Pharma', 'volatility': 0.25, 'trend': 0.08},
            'TATAMOTORS': {'sector': 'Auto', 'volatility': 0.45, 'trend': 0.05},
            'TITAN': {'sector': 'Jewelry', 'volatility': 0.35, 'trend': 0.13},
            'BAJAJFINSV': {'sector': 'Financial', 'volatility': 0.38, 'trend': 0.14},
            'WIPRO': {'sector': 'IT', 'volatility': 0.23, 'trend': 0.09},
            'ADANIPORTS': {'sector': 'Infrastructure', 'volatility': 0.42, 'trend': 0.10}
        }
    
    def generate_stock_data(self, symbol, start_date, end_date, initial_price=None):
        """
        Generate realistic stock data using geometric Brownian motion
        
        Parameters:
        -----------
        symbol : str
            Stock symbol
        start_date : datetime
            Start date for data generation
        end_date : datetime
            End date for data generation
        initial_price : float, optional
            Starting price. If None, uses realistic values based on symbol
        
        Returns:
        --------
        pandas.DataFrame with OHLCV data
        """
        
        if symbol not in self.stock_info:
            # Default parameters for unknown symbols
            volatility = 0.25
            trend = 0.08
        else:
            volatility = self.stock_info[symbol]['volatility']
            trend = self.stock_info[symbol]['trend']
        
        # Set realistic initial prices if not provided
        if initial_price is None:
            price_ranges = {
                'RELIANCE': 2500, 'TCS': 3500, 'HDFCBANK': 1600, 'INFY': 1800,
                'HINDUNILVR': 2400, 'ICICIBANK': 900, 'SBIN': 500, 'BHARTIARTL': 800,
                'BAJFINANCE': 7000, 'KOTAKBANK': 1800, 'HCLTECH': 1200, 'ASIANPAINT': 3200,
                'AXISBANK': 800, 'MARUTI': 9000, 'SUNPHARMA': 1100, 'TATAMOTORS': 450,
                'TITAN': 2800, 'BAJAJFINSV': 1500, 'WIPRO': 450, 'ADANIPORTS': 750
            }
            initial_price = price_ranges.get(symbol, 1000)
        
        # Generate date range
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        # Filter to business days only
        business_days = date_range[date_range.weekday < 5]
        
        n_days = len(business_days)
        dt = 1/252  # Daily time step (252 trading days per year)
        
        # Generate random returns using geometric Brownian motion
        np.random.seed(42)  # For reproducible results
        random_returns = np.random.normal(
            (trend - 0.5 * volatility**2) * dt,
            volatility * np.sqrt(dt),
            n_days
        )
        
        # Add some market regime changes and volatility clustering
        regime_changes = np.random.choice([0, 1], size=n_days, p=[0.98, 0.02])
        random_returns[regime_changes == 1] *= 2  # Increase volatility during regime changes
        
        # Add some autocorrelation to make it more realistic
        for i in range(1, len(random_returns)):
            random_returns[i] += 0.1 * random_returns[i-1]
        
        # Calculate cumulative returns and prices
        cumulative_returns = np.cumsum(random_returns)
        prices = initial_price * np.exp(cumulative_returns)
        
        # Generate OHLC data
        data = []
        for i, (date, close_price) in enumerate(zip(business_days, prices)):
            # Generate intraday volatility
            daily_vol = volatility / np.sqrt(252) * np.random.uniform(0.5, 1.5)
            
            # Generate OHLC based on close price
            if i == 0:
                open_price = initial_price
            else:
                open_price = data[i-1]['Close'] * (1 + np.random.normal(0, daily_vol/4))
            
            high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, daily_vol/2)))
            low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, daily_vol/2)))
            
            # Ensure OHLC relationships are maintained
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            # Generate volume (correlated with price movements)
            price_change = abs(close_price - open_price) / open_price
            base_volume = 1000000  # Base volume
            volume = int(base_volume * (1 + price_change * 5) * np.random.uniform(0.5, 2.0))
            
            data.append({
                'Date': date,
                'Open': round(open_price, 2),
                'High': round(high_price, 2),
                'Low': round(low_price, 2),
                'Close': round(close_price, 2),
                'Volume': volume
            })
        
        # Create DataFrame
        df = pd.DataFrame(data)
        df.set_index('Date', inplace=True)
        
        return df
    
    def generate_portfolio_data(self, symbols=None, period_years=3):
        """
        Generate data for a portfolio of stocks
        
        Parameters:
        -----------
        symbols : list, optional
            List of symbols. If None, uses default Indian stocks
        period_years : int, optional
            Number of years of data to generate
        
        Returns:
        --------
        Dictionary of DataFrames with stock data
        """
        
        if symbols is None:
            symbols = list(self.stock_info.keys())[:10]  # Top 10 stocks
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=period_years * 365)
        
        portfolio_data = {}
        
        print(f"Generating synthetic data for {len(symbols)} stocks...")
        print(f"Period: {start_date.date()} to {end_date.date()}")
        
        for symbol in symbols:
            print(f"Generating data for {symbol}...")
            df = self.generate_stock_data(symbol, start_date, end_date)
            portfolio_data[symbol] = df
            
            # Save individual stock data
            filename = f"{self.data_dir}/{symbol}_synthetic.csv"
            df.to_csv(filename)
            print(f"  Saved {len(df)} days of data to {filename}")
        
        # Save combined data
        self.save_combined_data(portfolio_data)
        
        return portfolio_data
    
    def save_combined_data(self, portfolio_data):
        """Save combined portfolio data"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create returns matrix
        returns_data = {}
        for symbol, df in portfolio_data.items():
            returns = df['Close'].pct_change().dropna()
            returns_data[symbol] = returns
        
        returns_df = pd.DataFrame(returns_data)
        returns_filename = f"{self.data_dir}/returns_matrix_{timestamp}.csv"
        returns_df.to_csv(returns_filename)
        
        # Save metadata
        metadata = {
            'generation_time': datetime.now().isoformat(),
            'data_type': 'synthetic',
            'symbols': list(portfolio_data.keys()),
            'total_symbols': len(portfolio_data),
            'data_summary': {
                symbol: {
                    'rows': len(df),
                    'start_date': df.index.min().strftime('%Y-%m-%d'),
                    'end_date': df.index.max().strftime('%Y-%m-%d'),
                    'avg_price': df['Close'].mean(),
                    'volatility': df['Close'].pct_change().std() * np.sqrt(252)
                }
                for symbol, df in portfolio_data.items()
            }
        }
        
        metadata_filename = f"{self.data_dir}/metadata_{timestamp}.json"
        with open(metadata_filename, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        print(f"Saved returns matrix to {returns_filename}")
        print(f"Saved metadata to {metadata_filename}")
    
    def load_data_for_optimization(self):
        """
        Load the most recent synthetic data in format suitable for portfolio optimization
        
        Returns:
        --------
        Dictionary of DataFrames ready for portfolio optimization
        """
        # Find the most recent data files
        csv_files = [f for f in os.listdir(self.data_dir) if f.endswith('_synthetic.csv')]
        
        if not csv_files:
            print("No synthetic data found. Generating new data...")
            return self.generate_portfolio_data()
        
        # Load existing data
        portfolio_data = {}
        for filename in csv_files:
            symbol = filename.replace('_synthetic.csv', '')
            filepath = os.path.join(self.data_dir, filename)
            df = pd.read_csv(filepath, index_col=0, parse_dates=True)
            portfolio_data[symbol] = df
        
        print(f"Loaded existing synthetic data for {len(portfolio_data)} symbols")
        return portfolio_data

def main():
    """Main function for testing"""
    data_source = AlternativeDataSource()
    
    # Generate data for portfolio optimization
    portfolio_data = data_source.generate_portfolio_data()
    
    print(f"\nGenerated synthetic data summary:")
    for symbol, df in portfolio_data.items():
        returns = df['Close'].pct_change().dropna()
        print(f"{symbol}: {len(df)} days, "
              f"Avg return: {returns.mean()*252:.1%}, "
              f"Volatility: {returns.std()*np.sqrt(252):.1%}")

if __name__ == "__main__":
    main()
