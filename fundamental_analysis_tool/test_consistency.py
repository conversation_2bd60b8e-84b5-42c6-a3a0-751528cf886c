#!/usr/bin/env python3
"""
Test Script for Consistency Analyzer

This script tests the consistency analyzer with real company data
to ensure it's working correctly and provides meaningful results.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules
from utils.data_loader import ScreenerDataLoader
from models.consistency_analyzer import ConsistencyAnalyzer
from models.screener import FundamentalScreener

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_consistency')

def test_consistency_analyzer():
    """
    Test the consistency analyzer with real company data
    """
    print("=" * 60)
    print("TESTING CONSISTENCY ANALYZER")
    print("=" * 60)
    
    # Initialize data loader
    try:
        data_loader = ScreenerDataLoader('../screener_data_collector/data')
        print(f"✓ Data loader initialized successfully")
        print(f"  Found {len(data_loader.get_all_tickers())} tickers")
    except Exception as e:
        print(f"✗ Error initializing data loader: {e}")
        return
    
    # Initialize consistency analyzer
    consistency_analyzer = ConsistencyAnalyzer()
    print(f"✓ Consistency analyzer initialized")
    
    # Get a sample of tickers to test
    all_tickers = data_loader.get_all_tickers()
    test_tickers = all_tickers[:10]  # Test with first 10 companies
    
    print(f"\nTesting with {len(test_tickers)} companies:")
    print(f"Tickers: {', '.join(test_tickers)}")
    
    results = {}
    
    for i, ticker in enumerate(test_tickers, 1):
        print(f"\n[{i}/{len(test_tickers)}] Testing {ticker}...")
        
        try:
            # Load company data
            company_data = data_loader.load_company_data(ticker)
            
            if not company_data:
                print(f"  ✗ No data found for {ticker}")
                continue
            
            print(f"  ✓ Data loaded for {ticker}")
            print(f"    Available sections: {list(company_data.keys())}")
            
            # Analyze consistency
            consistency_results = consistency_analyzer.analyze_company_consistency(company_data)
            results[ticker] = consistency_results
            
            # Print summary results
            overall_score = consistency_results.get('overall_consistency_score')
            if overall_score is not None:
                print(f"  ✓ Overall Consistency Score: {overall_score:.2f}")
            else:
                print(f"  ⚠ No overall consistency score calculated")
            
            # Print individual scores
            growth_score = consistency_results.get('growth_consistency', {}).get('overall_score')
            if growth_score is not None:
                print(f"    Growth Consistency: {growth_score:.2f}")
            
            prof_score = consistency_results.get('profitability_consistency', {}).get('overall_score')
            if prof_score is not None:
                print(f"    Profitability Consistency: {prof_score:.2f}")
            
            leverage_score = consistency_results.get('leverage_consistency', {}).get('overall_score')
            if leverage_score is not None:
                print(f"    Leverage Consistency: {leverage_score:.2f}")
            
            quarterly_score = consistency_results.get('quarterly_consistency', {}).get('overall_score')
            if quarterly_score is not None:
                print(f"    Quarterly Consistency: {quarterly_score:.2f}")
            
        except Exception as e:
            print(f"  ✗ Error analyzing {ticker}: {e}")
            logger.error(f"Error analyzing {ticker}: {e}")
    
    # Print summary
    print(f"\n" + "=" * 60)
    print("SUMMARY OF CONSISTENCY ANALYSIS")
    print("=" * 60)
    
    successful_analyses = len([r for r in results.values() if r.get('overall_consistency_score') is not None])
    print(f"Successfully analyzed: {successful_analyses}/{len(test_tickers)} companies")
    
    if successful_analyses > 0:
        # Calculate average scores
        overall_scores = [r['overall_consistency_score'] for r in results.values() 
                         if r.get('overall_consistency_score') is not None]
        
        if overall_scores:
            avg_overall = sum(overall_scores) / len(overall_scores)
            max_overall = max(overall_scores)
            min_overall = min(overall_scores)
            
            print(f"\nOverall Consistency Scores:")
            print(f"  Average: {avg_overall:.2f}")
            print(f"  Maximum: {max_overall:.2f}")
            print(f"  Minimum: {min_overall:.2f}")
        
        # Find best and worst performers
        best_company = max(results.items(), 
                          key=lambda x: x[1].get('overall_consistency_score', 0))
        worst_company = min(results.items(), 
                           key=lambda x: x[1].get('overall_consistency_score', 100))
        
        print(f"\nBest Performer: {best_company[0]} (Score: {best_company[1].get('overall_consistency_score', 0):.2f})")
        print(f"Worst Performer: {worst_company[0]} (Score: {worst_company[1].get('overall_consistency_score', 100):.2f})")
    
    return results

def test_screening_with_consistency():
    """
    Test the screening system with consistency criteria
    """
    print(f"\n" + "=" * 60)
    print("TESTING SCREENING WITH CONSISTENCY")
    print("=" * 60)
    
    try:
        # Initialize data loader and screener
        data_loader = ScreenerDataLoader('../screener_data_collector/data')
        screener = FundamentalScreener(data_loader)
        
        print(f"✓ Screener initialized successfully")
        
        # Test with a small sample
        all_tickers = data_loader.get_all_tickers()
        test_tickers = all_tickers[:20]  # Test with first 20 companies
        
        print(f"Testing screening with {len(test_tickers)} companies")
        
        # Use the consistency investing criteria
        criteria_file = 'criteria/consistency_investing.json'
        if os.path.exists(criteria_file):
            with open(criteria_file, 'r') as f:
                criteria = json.load(f)
            print(f"✓ Loaded criteria from {criteria_file}")
        else:
            # Use default criteria
            criteria = screener.default_criteria
            print(f"⚠ Using default criteria (criteria file not found)")
        
        # Run screening
        print(f"\nRunning screening...")
        screening_results = screener.screen_companies(
            tickers=test_tickers,
            criteria=criteria,
            max_workers=2  # Use fewer workers for testing
        )
        
        # Print results
        summary = screening_results.get('summary', {})
        print(f"\nScreening Results:")
        print(f"  Total companies: {summary.get('total', 0)}")
        print(f"  Pre-filtered: {summary.get('pre_filtered', 0)}")
        print(f"  Passed Tier 1: {summary.get('tier1_pass', 0)}")
        print(f"  Passed Tier 2: {summary.get('tier2_pass', 0)}")
        print(f"  Passed Tier 3: {summary.get('tier3_pass', 0)}")
        print(f"  Passed Tier 4: {summary.get('tier4_pass', 0)}")
        print(f"  Passed Tier 5: {summary.get('tier5_pass', 0)}")
        print(f"  Passed All Tiers: {summary.get('all_pass', 0)}")
        
        # Show companies that passed all tiers
        passed_all = screening_results.get('passed_companies', {}).get('all', [])
        if passed_all:
            print(f"\nCompanies that passed all tiers:")
            for ticker in passed_all:
                result = screening_results['results'][ticker]
                overall_score = result.get('metrics', {}).get('overall_consistency_score', 'N/A')
                print(f"  {ticker}: Consistency Score = {overall_score}")
        else:
            print(f"\nNo companies passed all tiers")
        
        return screening_results
        
    except Exception as e:
        print(f"✗ Error in screening test: {e}")
        logger.error(f"Error in screening test: {e}")
        return None

def analyze_data_availability():
    """
    Analyze what historical data is available in the dataset
    """
    print(f"\n" + "=" * 60)
    print("ANALYZING DATA AVAILABILITY")
    print("=" * 60)
    
    try:
        data_loader = ScreenerDataLoader('../screener_data_collector/data')
        all_tickers = data_loader.get_all_tickers()
        
        # Sample a few companies to check data structure
        sample_tickers = all_tickers[:5]
        
        data_availability = {
            'profit_loss': 0,
            'quarters': 0,
            'cash_flow': 0,
            'balance_sheet': 0,
            'ratios': 0
        }
        
        historical_data_points = {
            'profit_loss': [],
            'quarters': [],
            'cash_flow': []
        }
        
        for ticker in sample_tickers:
            print(f"\nAnalyzing {ticker}...")
            
            company_data = data_loader.load_company_data(ticker)
            
            for section in data_availability.keys():
                if section in company_data:
                    data_availability[section] += 1
                    print(f"  ✓ Has {section} data")
                    
                    # Count historical data points
                    if section in historical_data_points:
                        section_data = company_data[section]
                        if isinstance(section_data, dict):
                            # Remove units and notes
                            data_keys = [k for k in section_data.keys() 
                                       if k not in ['units', 'notes']]
                            
                            if data_keys:
                                # Get the first metric to count time periods
                                first_metric = section_data[data_keys[0]]
                                if isinstance(first_metric, dict):
                                    time_periods = len(first_metric)
                                    historical_data_points[section].append(time_periods)
                                    print(f"    {time_periods} time periods in {section}")
                else:
                    print(f"  ✗ Missing {section} data")
        
        print(f"\nData Availability Summary (out of {len(sample_tickers)} companies):")
        for section, count in data_availability.items():
            percentage = (count / len(sample_tickers)) * 100
            print(f"  {section}: {count}/{len(sample_tickers)} ({percentage:.1f}%)")
        
        print(f"\nHistorical Data Points:")
        for section, points_list in historical_data_points.items():
            if points_list:
                avg_points = sum(points_list) / len(points_list)
                max_points = max(points_list)
                min_points = min(points_list)
                print(f"  {section}: Avg={avg_points:.1f}, Max={max_points}, Min={min_points}")
        
    except Exception as e:
        print(f"✗ Error analyzing data availability: {e}")
        logger.error(f"Error analyzing data availability: {e}")

def main():
    """
    Main function to run all tests
    """
    print("CONSISTENCY ANALYZER TEST SUITE")
    print("=" * 60)
    
    # Test 1: Analyze data availability
    analyze_data_availability()
    
    # Test 2: Test consistency analyzer
    consistency_results = test_consistency_analyzer()
    
    # Test 3: Test screening with consistency
    screening_results = test_screening_with_consistency()
    
    print(f"\n" + "=" * 60)
    print("TEST SUITE COMPLETED")
    print("=" * 60)
    
    # Save results for further analysis
    if consistency_results or screening_results:
        output_file = 'output/consistency_test_results.json'
        os.makedirs('output', exist_ok=True)
        
        test_results = {
            'consistency_results': consistency_results,
            'screening_results': screening_results
        }
        
        with open(output_file, 'w') as f:
            json.dump(test_results, f, indent=2, default=str)
        
        print(f"✓ Test results saved to {output_file}")

if __name__ == "__main__":
    main()
