#!/bin/bash

# Quick start script for Fundamental Analysis Dashboard

echo "🚀 Starting Fundamental Analysis Dashboard..."
echo "================================================"

# Check if we're in the right directory
if [ ! -f "dashboard.py" ]; then
    echo "❌ Error: dashboard.py not found. Please run this script from the fundamental_analysis_tool directory."
    exit 1
fi

# Check if streamlit is installed
if ! command -v streamlit &> /dev/null; then
    echo "📦 Streamlit not found. Installing..."
    pip install streamlit
fi

# Check if data directory exists
if [ ! -d "../screener_data_collector/data" ]; then
    echo "⚠️  Warning: Data directory not found at ../screener_data_collector/data"
    echo "   Some features may not work properly."
fi

echo "🌐 Launching dashboard at http://localhost:8501"
echo "📊 Dashboard features:"
echo "   • System overview and health checks"
echo "   • Analysis progress tracking"
echo "   • Investment recommendations"
echo "   • Sector analysis and leaders"
echo "   • System controls and testing"
echo ""
echo "💡 Tip: Keep this terminal open while using the dashboard"
echo "================================================"

# Launch the dashboard
streamlit run dashboard.py
