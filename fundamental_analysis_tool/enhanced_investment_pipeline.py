#!/usr/bin/env python3
"""
Enhanced Investment Pipeline

This script implements the complete investment pipeline:
1. Cash Flow Pre-screening (10 years) - Filter weak companies
2. DCF Analysis - Intrinsic value calculation for qualified companies
3. Fundamental Analysis - Consistency, sector, qualitative analysis
4. Portfolio Optimization - Optimal portfolio construction
5. Investment Recommendations - Final actionable recommendations

This is for REAL MONEY - every step is transparent and traceable.
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Add paths for DCF and Portfolio modules
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'dcf'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'Portfolio_optimization'))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import fundamental analysis modules
from utils.data_loader import ScreenerDataLoader
from models.cashflow_prescreener import CashFlowPreScreener
from models.consistency_analyzer import ConsistencyAnalyzer
from models.sector_analyzer import SectorAnalyzer
from models.qualitative_analyzer import QualitativeAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('enhanced_investment_pipeline')

class EnhancedInvestmentPipeline:
    """
    Complete investment pipeline integrating all analysis modules
    """

    def __init__(self, data_path: str = '../screener_data_collector/data'):
        """
        Initialize the enhanced investment pipeline
        """
        self.data_loader = ScreenerDataLoader(data_path)
        self.prescreener = CashFlowPreScreener()
        self.consistency_analyzer = ConsistencyAnalyzer()
        self.sector_analyzer = SectorAnalyzer()
        self.qualitative_analyzer = QualitativeAnalyzer()

        # Initialize DCF and Portfolio modules (will be imported dynamically)
        self.dcf_analyzer = None
        self.portfolio_optimizer = None

        # Load all tickers
        self.all_tickers = self.data_loader.get_all_tickers()
        logger.info(f"Loaded {len(self.all_tickers)} companies for analysis")

        # Pipeline results storage
        self.pipeline_results = {
            'stage_1_cashflow_prescreen': {},
            'stage_2_dcf_analysis': {},
            'stage_3_fundamental_analysis': {},
            'stage_4_portfolio_optimization': {},
            'stage_5_final_recommendations': {}
        }

    def run_complete_pipeline(self,
                            max_companies: int = None,
                            dcf_required: bool = True,
                            portfolio_optimization: bool = True) -> Dict[str, Any]:
        """
        Run the complete investment pipeline

        Parameters:
        -----------
        max_companies : int, optional
            Maximum number of companies to analyze (for testing)
        dcf_required : bool
            Whether DCF analysis is required
        portfolio_optimization : bool
            Whether to run portfolio optimization

        Returns:
        --------
        Complete pipeline results
        """
        logger.info("Starting Enhanced Investment Pipeline")
        print("=" * 100)
        print("🚀 ENHANCED INVESTMENT PIPELINE - REAL MONEY ANALYSIS")
        print("=" * 100)
        print("📊 Pipeline: Cash Flow → DCF → Fundamental → Portfolio → Recommendations")
        print("=" * 100)

        # Stage 1: Cash Flow Pre-screening
        print("\n🔍 STAGE 1: CASH FLOW PRE-SCREENING (10 YEARS)")
        print("-" * 80)

        companies_to_analyze = self.all_tickers[:max_companies] if max_companies else self.all_tickers
        cashflow_results = self._run_cashflow_prescreening(companies_to_analyze)

        # Get companies that passed cash flow screening
        qualified_companies = [
            ticker for ticker, result in cashflow_results['results'].items()
            if result.get('passed_prescreen', False)
        ]

        print(f"✅ Cash Flow Pre-screening Complete:")
        print(f"   📊 Companies Analyzed: {len(companies_to_analyze):,}")
        print(f"   ✅ Companies Passed: {len(qualified_companies):,}")
        print(f"   📈 Pass Rate: {(len(qualified_companies)/len(companies_to_analyze)*100):.1f}%")

        if not qualified_companies:
            print("❌ No companies passed cash flow pre-screening!")
            return self.pipeline_results

        # Stage 2: DCF Analysis (if required)
        if dcf_required and qualified_companies:
            print(f"\n💰 STAGE 2: DCF ANALYSIS FOR {len(qualified_companies)} QUALIFIED COMPANIES")
            print("-" * 80)
            dcf_results = self._run_dcf_analysis(qualified_companies)

            # Filter companies based on DCF results
            dcf_qualified = [
                ticker for ticker, result in dcf_results['results'].items()
                if result.get('dcf_passed', False)
            ]

            print(f"✅ DCF Analysis Complete:")
            print(f"   📊 Companies Analyzed: {len(qualified_companies):,}")
            print(f"   ✅ DCF Qualified: {len(dcf_qualified):,}")
            print(f"   📈 DCF Pass Rate: {(len(dcf_qualified)/len(qualified_companies)*100):.1f}%")

            qualified_companies = dcf_qualified

        # Stage 3: Comprehensive Fundamental Analysis
        if qualified_companies:
            print(f"\n📈 STAGE 3: FUNDAMENTAL ANALYSIS FOR {len(qualified_companies)} COMPANIES")
            print("-" * 80)
            fundamental_results = self._run_fundamental_analysis(qualified_companies)

            print(f"✅ Fundamental Analysis Complete:")
            print(f"   📊 Companies Analyzed: {len(qualified_companies):,}")
            print(f"   📈 Analysis Completed: {fundamental_results['companies_analyzed']}")

        # Stage 4: Portfolio Optimization (if required)
        if portfolio_optimization and qualified_companies:
            print(f"\n🎯 STAGE 4: PORTFOLIO OPTIMIZATION")
            print("-" * 80)
            portfolio_results = self._run_portfolio_optimization(qualified_companies)

            print(f"✅ Portfolio Optimization Complete")

        # Stage 5: Final Investment Recommendations
        print(f"\n🏆 STAGE 5: FINAL INVESTMENT RECOMMENDATIONS")
        print("-" * 80)
        final_recommendations = self._generate_final_recommendations()

        # Save complete results
        self._save_pipeline_results()

        print(f"\n" + "=" * 100)
        print("✅ ENHANCED INVESTMENT PIPELINE COMPLETED!")
        print("=" * 100)

        return self.pipeline_results

    def _run_cashflow_prescreening(self, companies: List[str]) -> Dict[str, Any]:
        """
        Run cash flow pre-screening on companies
        """
        results = {
            'total_companies': len(companies),
            'companies_passed': 0,
            'results': {}
        }

        for i, ticker in enumerate(companies):
            if i % 100 == 0:
                print(f"  Progress: {i}/{len(companies)} ({(i/len(companies)*100):.1f}%)")

            try:
                company_data = self.data_loader.load_company_data(ticker)
                if company_data:
                    prescreen_result = self.prescreener.prescreen_company(company_data)
                    results['results'][ticker] = prescreen_result

                    if prescreen_result.get('passed_prescreen', False):
                        results['companies_passed'] += 1

            except Exception as e:
                logger.error(f"Error pre-screening {ticker}: {e}")
                results['results'][ticker] = {'error': str(e), 'passed_prescreen': False}

        self.pipeline_results['stage_1_cashflow_prescreen'] = results
        return results

    def _run_dcf_analysis(self, qualified_companies: List[str]) -> Dict[str, Any]:
        """
        Run DCF analysis on qualified companies
        """
        print("  🔄 Initializing DCF analysis module...")

        # Try to import DCF modules
        try:
            from dcf_model import DCFModel
            from financial_analyzer import FinancialAnalyzer
            self.dcf_analyzer = DCFModel()
            print("  ✅ DCF modules loaded successfully")
        except ImportError as e:
            print(f"  ⚠️  DCF modules not available: {e}")
            print("  📝 Using simplified DCF estimation...")
            return self._run_simplified_dcf_analysis(qualified_companies)

        results = {
            'total_companies': len(qualified_companies),
            'companies_analyzed': 0,
            'dcf_qualified': 0,
            'results': {}
        }

        for i, ticker in enumerate(qualified_companies):
            if i % 10 == 0:
                print(f"  DCF Progress: {i}/{len(qualified_companies)} ({(i/len(qualified_companies)*100):.1f}%)")

            try:
                company_data = self.data_loader.load_company_data(ticker)
                if company_data:
                    dcf_result = self._analyze_company_dcf(ticker, company_data)
                    results['results'][ticker] = dcf_result
                    results['companies_analyzed'] += 1

                    if dcf_result.get('dcf_passed', False):
                        results['dcf_qualified'] += 1

            except Exception as e:
                logger.error(f"Error in DCF analysis for {ticker}: {e}")
                results['results'][ticker] = {'error': str(e), 'dcf_passed': False}

        self.pipeline_results['stage_2_dcf_analysis'] = results
        return results

    def _run_simplified_dcf_analysis(self, qualified_companies: List[str]) -> Dict[str, Any]:
        """
        Run simplified DCF analysis when full DCF module is not available
        """
        results = {
            'total_companies': len(qualified_companies),
            'companies_analyzed': 0,
            'dcf_qualified': 0,
            'results': {}
        }

        for i, ticker in enumerate(qualified_companies):
            if i % 10 == 0:
                print(f"  Simplified DCF Progress: {i}/{len(qualified_companies)} ({(i/len(qualified_companies)*100):.1f}%)")

            try:
                company_data = self.data_loader.load_company_data(ticker)
                if company_data:
                    dcf_result = self._simplified_dcf_valuation(ticker, company_data)
                    results['results'][ticker] = dcf_result
                    results['companies_analyzed'] += 1

                    if dcf_result.get('dcf_passed', False):
                        results['dcf_qualified'] += 1

            except Exception as e:
                logger.error(f"Error in simplified DCF for {ticker}: {e}")
                results['results'][ticker] = {'error': str(e), 'dcf_passed': False}

        self.pipeline_results['stage_2_dcf_analysis'] = results
        return results

    def _simplified_dcf_valuation(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Simplified DCF valuation using basic financial metrics
        """
        try:
            # Extract financial data
            ratios = company_data.get('ratios', {}).get('ratios', {})
            overview = company_data.get('overview', {})

            # Get key metrics
            market_cap = overview.get('market_cap')
            pe_ratio = ratios.get('pe_ratio')
            roe = ratios.get('roe_%')
            roce = ratios.get('roce_%')
            debt_to_equity = ratios.get('debt_to_equity', 0)

            # Simple valuation criteria
            dcf_score = 50  # Base score
            valuation_signals = []

            # ROE/ROCE analysis
            if roe and isinstance(roe, (int, float)):
                if roe > 15:
                    dcf_score += 15
                    valuation_signals.append(f"Strong ROE: {roe}%")
                elif roe > 10:
                    dcf_score += 10
                elif roe < 5:
                    dcf_score -= 15
                    valuation_signals.append(f"Weak ROE: {roe}%")

            if roce and isinstance(roce, (int, float)):
                if roce > 15:
                    dcf_score += 10
                    valuation_signals.append(f"Strong ROCE: {roce}%")
                elif roce < 5:
                    dcf_score -= 10

            # Debt analysis
            if debt_to_equity and isinstance(debt_to_equity, (int, float)):
                if debt_to_equity < 0.5:
                    dcf_score += 10
                    valuation_signals.append("Conservative debt levels")
                elif debt_to_equity > 2.0:
                    dcf_score -= 15
                    valuation_signals.append("High debt concern")

            # PE ratio analysis
            if pe_ratio and isinstance(pe_ratio, (int, float)):
                if 10 <= pe_ratio <= 25:
                    dcf_score += 10
                    valuation_signals.append("Reasonable valuation")
                elif pe_ratio > 40:
                    dcf_score -= 10
                    valuation_signals.append("High valuation concern")

            # Determine if DCF passed
            dcf_passed = dcf_score >= 60

            return {
                'ticker': ticker,
                'dcf_method': 'simplified',
                'dcf_score': dcf_score,
                'dcf_passed': dcf_passed,
                'valuation_signals': valuation_signals,
                'key_metrics': {
                    'roe': roe,
                    'roce': roce,
                    'pe_ratio': pe_ratio,
                    'debt_to_equity': debt_to_equity
                },
                'recommendation': 'QUALIFIED' if dcf_passed else 'REJECTED'
            }

        except Exception as e:
            return {
                'ticker': ticker,
                'error': str(e),
                'dcf_passed': False
            }

    def _run_fundamental_analysis(self, qualified_companies: List[str]) -> Dict[str, Any]:
        """
        Run comprehensive fundamental analysis
        """
        results = {
            'total_companies': len(qualified_companies),
            'companies_analyzed': 0,
            'results': {}
        }

        for i, ticker in enumerate(qualified_companies):
            if i % 10 == 0:
                print(f"  Fundamental Progress: {i}/{len(qualified_companies)} ({(i/len(qualified_companies)*100):.1f}%)")

            try:
                company_data = self.data_loader.load_company_data(ticker)
                if company_data:
                    # Sector classification
                    sector = self.sector_analyzer.classify_sector(company_data)

                    # Consistency analysis
                    consistency_result = self.consistency_analyzer.analyze_company_consistency(company_data)

                    # Qualitative analysis
                    qualitative_result = self.qualitative_analyzer.analyze_company_qualitative_factors(
                        company_data, sector=sector
                    )

                    # Combine results
                    fundamental_result = {
                        'ticker': ticker,
                        'sector': sector,
                        'consistency': consistency_result,
                        'qualitative': qualitative_result,
                        'consistency_score': consistency_result.get('overall_consistency_score', 0),
                        'qualitative_score': qualitative_result.get('qualitative_score', 0),
                        'overall_fundamental_score': (
                            consistency_result.get('overall_consistency_score', 0) * 0.6 +
                            qualitative_result.get('qualitative_score', 0) * 0.4
                        )
                    }

                    results['results'][ticker] = fundamental_result
                    results['companies_analyzed'] += 1

            except Exception as e:
                logger.error(f"Error in fundamental analysis for {ticker}: {e}")
                results['results'][ticker] = {'error': str(e)}

        self.pipeline_results['stage_3_fundamental_analysis'] = results
        return results

    def _run_portfolio_optimization(self, qualified_companies: List[str]) -> Dict[str, Any]:
        """
        Run portfolio optimization on qualified companies
        """
        print("  🔄 Initializing portfolio optimization...")

        # Try to import Portfolio optimization modules
        try:
            from integrated_portfolio_optimization import IntegratedPortfolioOptimization
            print("  ✅ Portfolio optimization modules loaded")
        except ImportError as e:
            print(f"  ⚠️  Portfolio modules not available: {e}")
            print("  📝 Using simplified portfolio allocation...")
            return self._simplified_portfolio_allocation(qualified_companies)

        # Implementation will be added based on available modules
        return {'status': 'portfolio_optimization_placeholder'}

    def _simplified_portfolio_allocation(self, qualified_companies: List[str]) -> Dict[str, Any]:
        """
        Simplified portfolio allocation when full optimization is not available
        """
        # Get fundamental scores for allocation
        fundamental_results = self.pipeline_results.get('stage_3_fundamental_analysis', {}).get('results', {})

        # Create simple allocation based on scores
        allocations = {}
        total_score = 0

        for ticker in qualified_companies:
            if ticker in fundamental_results:
                score = fundamental_results[ticker].get('overall_fundamental_score', 0)
                if score > 50:  # Only include companies with decent scores
                    allocations[ticker] = score
                    total_score += score

        # Normalize to percentages
        if total_score > 0:
            for ticker in allocations:
                allocations[ticker] = (allocations[ticker] / total_score) * 100

        return {
            'method': 'simplified_score_based',
            'total_companies': len(allocations),
            'allocations': allocations,
            'total_allocation': sum(allocations.values())
        }

    def _generate_final_recommendations(self) -> Dict[str, Any]:
        """
        Generate final investment recommendations
        """
        # Combine all analysis results
        cashflow_results = self.pipeline_results.get('stage_1_cashflow_prescreen', {}).get('results', {})
        dcf_results = self.pipeline_results.get('stage_2_dcf_analysis', {}).get('results', {})
        fundamental_results = self.pipeline_results.get('stage_3_fundamental_analysis', {}).get('results', {})

        recommendations = {
            'strong_buy': [],
            'buy': [],
            'hold': [],
            'detailed_analysis': {}
        }

        for ticker in fundamental_results:
            try:
                # Get all scores
                cashflow_score = cashflow_results.get(ticker, {}).get('cash_flow_score', 0)
                dcf_score = dcf_results.get(ticker, {}).get('dcf_score', 0)
                fundamental_score = fundamental_results[ticker].get('overall_fundamental_score', 0)

                # Calculate combined score
                combined_score = (
                    cashflow_score * 0.3 +
                    dcf_score * 0.4 +
                    fundamental_score * 0.3
                )

                # Generate recommendation
                if combined_score >= 70:
                    recommendation = 'STRONG_BUY'
                    recommendations['strong_buy'].append(ticker)
                elif combined_score >= 60:
                    recommendation = 'BUY'
                    recommendations['buy'].append(ticker)
                elif combined_score >= 50:
                    recommendation = 'HOLD'
                    recommendations['hold'].append(ticker)
                else:
                    recommendation = 'AVOID'

                # Store detailed analysis
                recommendations['detailed_analysis'][ticker] = {
                    'ticker': ticker,
                    'sector': fundamental_results[ticker].get('sector', 'unknown'),
                    'cashflow_score': cashflow_score,
                    'dcf_score': dcf_score,
                    'fundamental_score': fundamental_score,
                    'combined_score': combined_score,
                    'recommendation': recommendation
                }

            except Exception as e:
                logger.error(f"Error generating recommendation for {ticker}: {e}")

        self.pipeline_results['stage_5_final_recommendations'] = recommendations
        return recommendations

    def _save_pipeline_results(self):
        """
        Save complete pipeline results
        """
        os.makedirs('output/enhanced_pipeline', exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'output/enhanced_pipeline/enhanced_pipeline_results_{timestamp}.json'

        # Add metadata
        self.pipeline_results['metadata'] = {
            'timestamp': datetime.now().isoformat(),
            'total_companies_analyzed': len(self.all_tickers),
            'pipeline_version': '1.0',
            'modules_used': ['cashflow_prescreener', 'dcf_analysis', 'fundamental_analysis', 'portfolio_optimization']
        }

        with open(output_file, 'w') as f:
            json.dump(self.pipeline_results, f, indent=2, default=str)

        print(f"📁 Complete pipeline results saved to: {output_file}")
        return output_file

def main():
    """
    Main function to run the enhanced investment pipeline
    """
    print("🚀 ENHANCED INVESTMENT PIPELINE")
    print("=" * 80)
    print("Real Money Analysis - Every Step Transparent and Traceable")
    print("=" * 80)

    # Initialize pipeline
    pipeline = EnhancedInvestmentPipeline()

    # Run complete pipeline
    results = pipeline.run_complete_pipeline(
        max_companies=100,  # Test with 100 companies first
        dcf_required=True,
        portfolio_optimization=True
    )

    # Display summary
    print(f"\n🏆 PIPELINE SUMMARY:")
    print(f"=" * 80)

    # Show final recommendations
    final_recs = results.get('stage_5_final_recommendations', {})

    print(f"💎 STRONG BUY: {len(final_recs.get('strong_buy', []))} companies")
    print(f"📈 BUY: {len(final_recs.get('buy', []))} companies")
    print(f"📊 HOLD: {len(final_recs.get('hold', []))} companies")

    # Show top recommendations
    detailed = final_recs.get('detailed_analysis', {})
    if detailed:
        sorted_companies = sorted(detailed.items(), key=lambda x: x[1].get('combined_score', 0), reverse=True)

        print(f"\n🏆 TOP 10 RECOMMENDATIONS:")
        for i, (ticker, data) in enumerate(sorted_companies[:10], 1):
            score = data.get('combined_score', 0)
            recommendation = data.get('recommendation', 'UNKNOWN')
            sector = data.get('sector', 'unknown')
            print(f"  {i:2d}. {ticker:12s} ({sector:15s}) - Score: {score:.1f} - {recommendation}")

    print(f"\n✅ Enhanced Investment Pipeline Complete!")

if __name__ == "__main__":
    main()
