#!/usr/bin/env python3
"""
🚀 PRODUCTION SCRIPT: Full 4950 Company Warren Buffett DCF Analysis

This script runs the complete investment analysis pipeline on all 4950 companies:
1. Cash Flow Pre-screening (10-year analysis)
2. <PERSON> Style DCF Analysis
3. Portfolio Optimization
4. Investment Recommendations

Usage:
    python run_full_4950_analysis.py                    # Full analysis
    python run_full_4950_analysis.py --resume           # Resume from interruption
    python run_full_4950_analysis.py --phase dcf_only   # Run specific phase
    python run_full_4950_analysis.py --batch_size 100   # Custom batch size
"""

import os
import sys
import json
import time
import argparse
import psutil
import gc
from datetime import datetime
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.data_loader import ScreenerDataLoader
from models.cashflow_prescreener import CashFlowPreScreener
from warren_buffett_dcf_analysis import WarrenBuffettDCFAnalysis
from real_portfolio_integration import RealPortfolioIntegration

class Full4950Analysis:
    """
    Production-ready analysis system for all 4950 companies
    """

    def __init__(self, batch_size=50, max_workers=8, resume=False):
        # Auto-adjust for system capabilities
        memory_gb = psutil.virtual_memory().total / (1024**3)
        cpu_count = psutil.cpu_count()

        # Conservative adjustments for lower-spec systems
        if memory_gb < 16:
            batch_size = min(batch_size, 25)  # Reduce batch size
            print(f"🔧 Adjusted batch size to {batch_size} for {memory_gb:.1f}GB RAM")

        if cpu_count < 8:
            max_workers = min(max_workers, cpu_count)  # Limit workers to CPU count
            print(f"🔧 Adjusted max workers to {max_workers} for {cpu_count} CPU cores")

        self.batch_size = batch_size
        self.max_workers = max_workers
        self.resume = resume
        self.start_time = datetime.now()

        # Create output directories
        self.output_dir = Path('output/full_4950_analysis')
        self.intermediate_dir = Path('output/intermediate_results')
        self.reports_dir = Path('output/reports')

        for dir_path in [self.output_dir, self.intermediate_dir, self.reports_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)

        # Initialize components
        self.data_loader = ScreenerDataLoader('../screener_data_collector/data')
        self.prescreener = CashFlowPreScreener()
        self.warren_buffett_analyzer = WarrenBuffettDCFAnalysis()
        self.portfolio_integration = RealPortfolioIntegration()

        # Progress tracking
        self.progress = {
            'phase': 'initialization',
            'total_companies': 0,
            'processed_companies': 0,
            'qualified_companies': 0,
            'dcf_analyzed': 0,
            'buffett_approved': 0,
            'start_time': self.start_time.isoformat(),
            'current_time': datetime.now().isoformat(),
            'estimated_completion': None
        }

        print("🚀 PRODUCTION ANALYSIS: 4950 Company Warren Buffett DCF System")
        print("=" * 80)
        print(f"📊 Batch Size: {self.batch_size}")
        print(f"🔧 Max Workers: {self.max_workers}")
        print(f"🔄 Resume Mode: {self.resume}")
        print(f"📁 Output Directory: {self.output_dir}")
        print("=" * 80)

    def check_system_requirements(self):
        """Check if system meets requirements for 4950 company analysis"""
        print("\n🔧 SYSTEM REQUIREMENTS CHECK")
        print("-" * 50)

        # Memory check
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"💾 Total RAM: {memory_gb:.1f} GB")

        if memory_gb < 16:
            print("⚠️  WARNING: Less than 16GB RAM. Consider reducing batch size.")
        elif memory_gb >= 32:
            print("✅ Excellent: 32GB+ RAM available")
        else:
            print("✅ Good: Sufficient RAM for analysis")

        # CPU check
        cpu_count = psutil.cpu_count()
        print(f"🖥️  CPU Cores: {cpu_count}")

        if cpu_count < 8:
            print("⚠️  WARNING: Less than 8 cores. Consider reducing max_workers.")
        else:
            print("✅ Good: Sufficient CPU cores")

        # Disk space check
        disk = psutil.disk_usage('.')
        disk_free_gb = disk.free / (1024**3)
        print(f"💽 Free Disk Space: {disk_free_gb:.1f} GB")

        if disk_free_gb < 10:
            print("❌ ERROR: Less than 10GB free space. Analysis may fail.")
            return False
        elif disk_free_gb >= 50:
            print("✅ Excellent: Plenty of disk space")
        else:
            print("✅ Good: Sufficient disk space")

        return True

    def save_progress(self):
        """Save current progress to file"""
        self.progress['current_time'] = datetime.now().isoformat()

        # Calculate estimated completion
        if self.progress['processed_companies'] > 0:
            elapsed_time = (datetime.now() - self.start_time).total_seconds()
            rate = self.progress['processed_companies'] / elapsed_time
            remaining = self.progress['total_companies'] - self.progress['processed_companies']
            eta_seconds = remaining / rate if rate > 0 else 0
            eta = datetime.now().timestamp() + eta_seconds
            self.progress['estimated_completion'] = datetime.fromtimestamp(eta).isoformat()

        progress_file = self.intermediate_dir / 'progress_tracking.json'
        with open(progress_file, 'w') as f:
            json.dump(self.progress, f, indent=2)

    def optimize_memory(self):
        """Optimize memory usage during processing"""
        gc.collect()
        memory_percent = psutil.virtual_memory().percent

        if memory_percent > 85:
            print(f"⚠️  High memory usage: {memory_percent:.1f}%")
            # Reduce batch size if memory is high
            self.batch_size = max(10, self.batch_size // 2)
            print(f"🔧 Reduced batch size to: {self.batch_size}")

        return memory_percent

    def phase_1_data_loading(self):
        """Phase 1: Load and validate all company data"""
        print("\n📊 PHASE 1: DATA LOADING & VALIDATION")
        print("-" * 50)

        self.progress['phase'] = 'data_loading'

        # Load all available tickers
        all_tickers = self.data_loader.get_all_tickers()
        self.progress['total_companies'] = len(all_tickers)

        print(f"📊 Total companies loaded: {len(all_tickers)}")

        if len(all_tickers) < 4900:
            print(f"⚠️  Warning: Only {len(all_tickers)} companies available, target is 4950")

        # Validate data coverage
        print("🔍 Validating data coverage...")
        coverage = self.data_loader.get_data_coverage()

        qualified_tickers = []
        for ticker in all_tickers:
            ticker_coverage = coverage.get(ticker, {})
            if ticker_coverage.get('has_sufficient_data', False):
                qualified_tickers.append(ticker)

        print(f"✅ Companies with sufficient data: {len(qualified_tickers)}")

        self.save_progress()
        return qualified_tickers

    def phase_2_cash_flow_prescreening(self, qualified_tickers):
        """Phase 2: Cash flow pre-screening with 10-year analysis"""
        print("\n🔍 PHASE 2: CASH FLOW PRE-SCREENING")
        print("-" * 50)

        self.progress['phase'] = 'cash_flow_prescreening'

        # Check if we can resume from previous run
        prescreen_file = self.output_dir / 'prescreen_results.json'
        if self.resume and prescreen_file.exists():
            print("🔄 Resuming from previous cash flow screening...")
            with open(prescreen_file, 'r') as f:
                prescreen_results = json.load(f)
        else:
            print(f"🚀 Starting cash flow analysis for {len(qualified_tickers)} companies...")

            # Run batch pre-screening
            prescreen_results = self.run_batch_prescreening(qualified_tickers)

            # Save results
            with open(prescreen_file, 'w') as f:
                json.dump(prescreen_results, f, indent=2)

        # Extract qualified companies
        cash_flow_qualified = []
        for ticker, result in prescreen_results.get('company_results', {}).items():
            if result.get('qualified', False) and result.get('cashflow_score', 0) >= 40:
                cash_flow_qualified.append(ticker)

        self.progress['qualified_companies'] = len(cash_flow_qualified)
        print(f"💰 Cash flow qualified companies: {len(cash_flow_qualified)}")

        self.save_progress()
        return cash_flow_qualified, prescreen_results

    def phase_3_warren_buffett_dcf(self, cash_flow_qualified):
        """Phase 3: Warren Buffett style DCF analysis"""
        print("\n🎯 PHASE 3: WARREN BUFFETT DCF ANALYSIS")
        print("-" * 50)

        self.progress['phase'] = 'warren_buffett_dcf'

        # Check if we can resume from previous run
        dcf_file = self.output_dir / 'dcf_results.json'
        if self.resume and dcf_file.exists():
            print("🔄 Resuming from previous DCF analysis...")
            with open(dcf_file, 'r') as f:
                dcf_results = json.load(f)
        else:
            print(f"🚀 Starting DCF analysis for {len(cash_flow_qualified)} companies...")

            # Run batch DCF analysis
            dcf_results = self.warren_buffett_analyzer.batch_analyze_companies(
                tickers=cash_flow_qualified,
                batch_size=self.batch_size,
                max_workers=self.max_workers,
                save_intermediate=True,
                progress_callback=self.update_dcf_progress
            )

            # Save results
            with open(dcf_file, 'w') as f:
                json.dump(dcf_results, f, indent=2)

        self.progress['dcf_analyzed'] = len(dcf_results.get('company_results', {}))

        # Count Buffett approved companies
        buffett_approved = 0
        for result in dcf_results.get('company_results', {}).values():
            if result.get('recommendation', {}).get('buffett_approved', False):
                buffett_approved += 1

        self.progress['buffett_approved'] = buffett_approved
        print(f"⭐ Buffett approved companies: {buffett_approved}")

        self.save_progress()
        return dcf_results

    def phase_4_portfolio_optimization(self, dcf_results):
        """Phase 4: Portfolio optimization"""
        print("\n💼 PHASE 4: PORTFOLIO OPTIMIZATION")
        print("-" * 50)

        self.progress['phase'] = 'portfolio_optimization'

        # Extract investment candidates
        investment_candidates = []
        for ticker, result in dcf_results.get('company_results', {}).items():
            recommendation = result.get('recommendation', {})
            if recommendation.get('buffett_approved', False):
                investment_candidates.append({
                    'ticker': ticker,
                    'buffett_score': result.get('buffett_score', {}).get('buffett_score', 0),
                    'margin_of_safety': result.get('margin_of_safety_analysis', {}).get('conservative_margin_of_safety', 0),
                    'recommendation': recommendation.get('recommendation', 'UNKNOWN')
                })

        print(f"💎 Investment candidates: {len(investment_candidates)}")

        # Run portfolio optimization if available
        portfolio_results = {}
        if self.portfolio_integration.portfolio_available and len(investment_candidates) > 0:
            print("🚀 Running portfolio optimization...")

            candidate_tickers = [candidate['ticker'] for candidate in investment_candidates]

            try:
                portfolio_results = self.portfolio_integration.build_optimal_portfolio(
                    tickers=candidate_tickers,
                    investment_amount=1000000,  # 10 Lakh investment
                    risk_tolerance='moderate'
                )
                print(f"✅ Optimal portfolio created with {len(portfolio_results.get('holdings', []))} positions")

            except Exception as e:
                print(f"⚠️  Portfolio optimization failed: {e}")
                portfolio_results = {'error': str(e)}

        else:
            print("⚠️  Portfolio optimization not available or no candidates")

        # Save portfolio results
        portfolio_file = self.output_dir / 'portfolio_recommendations.json'
        with open(portfolio_file, 'w') as f:
            json.dump({
                'investment_candidates': investment_candidates,
                'portfolio_results': portfolio_results
            }, f, indent=2)

        self.save_progress()
        return investment_candidates, portfolio_results

    def phase_5_generate_reports(self, prescreen_results, dcf_results, investment_candidates, portfolio_results):
        """Phase 5: Generate comprehensive reports"""
        print("\n📋 PHASE 5: GENERATING REPORTS")
        print("-" * 50)

        self.progress['phase'] = 'generating_reports'

        # Create comprehensive analysis summary
        analysis_summary = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'total_runtime_minutes': (datetime.now() - self.start_time).total_seconds() / 60,
                'system_configuration': {
                    'batch_size': self.batch_size,
                    'max_workers': self.max_workers,
                    'resume_mode': self.resume
                }
            },
            'analysis_statistics': {
                'total_companies_available': self.progress['total_companies'],
                'companies_with_sufficient_data': len(prescreen_results.get('company_results', {})),
                'cash_flow_qualified': self.progress['qualified_companies'],
                'dcf_analyzed': self.progress['dcf_analyzed'],
                'buffett_approved': self.progress['buffett_approved'],
                'investment_candidates': len(investment_candidates)
            },
            'performance_metrics': {
                'prescreen_pass_rate': (self.progress['qualified_companies'] / self.progress['total_companies']) * 100,
                'dcf_success_rate': (self.progress['dcf_analyzed'] / self.progress['qualified_companies']) * 100 if self.progress['qualified_companies'] > 0 else 0,
                'buffett_approval_rate': (self.progress['buffett_approved'] / self.progress['dcf_analyzed']) * 100 if self.progress['dcf_analyzed'] > 0 else 0
            },
            'top_investment_candidates': sorted(investment_candidates, key=lambda x: x['buffett_score'], reverse=True)[:20],
            'portfolio_summary': portfolio_results
        }

        # Save comprehensive results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Main analysis file
        analysis_file = self.output_dir / f'analysis_{timestamp}.json'
        with open(analysis_file, 'w') as f:
            json.dump({
                'prescreen_results': prescreen_results,
                'dcf_results': dcf_results,
                'investment_candidates': investment_candidates,
                'portfolio_results': portfolio_results,
                'analysis_summary': analysis_summary
            }, f, indent=2)

        # Summary file
        summary_file = self.output_dir / 'analysis_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(analysis_summary, f, indent=2)

        print(f"✅ Comprehensive analysis saved to: {analysis_file}")
        print(f"📊 Summary saved to: {summary_file}")

        self.save_progress()
        return analysis_summary

    def run_batch_prescreening(self, tickers):
        """Run batch cash flow pre-screening"""
        print(f"🔍 Processing {len(tickers)} companies for cash flow analysis...")

        results = {
            'company_results': {},
            'summary_statistics': {
                'total_companies': len(tickers),
                'processed_companies': 0,
                'qualified_companies': 0,
                'failed_companies': 0
            }
        }

        qualified_count = 0
        failed_count = 0

        for i, ticker in enumerate(tickers, 1):
            try:
                # Load company data
                company_data = self.data_loader.load_company_data(ticker)

                if company_data:
                    # Run pre-screening
                    prescreen_result = self.prescreener.prescreen_company(company_data)

                    # Store result
                    results['company_results'][ticker] = {
                        'qualified': prescreen_result.get('passed_prescreen', False),
                        'cashflow_score': prescreen_result.get('cash_flow_score', 0),
                        'reasons_passed': prescreen_result.get('reasons_passed', []),
                        'reasons_failed': prescreen_result.get('reasons_failed', []),
                        'analysis': prescreen_result.get('analysis', {})
                    }

                    if prescreen_result.get('passed_prescreen', False):
                        qualified_count += 1
                    else:
                        failed_count += 1
                else:
                    # No data available
                    results['company_results'][ticker] = {
                        'qualified': False,
                        'cashflow_score': 0,
                        'reasons_failed': ['No company data available'],
                        'analysis': {}
                    }
                    failed_count += 1

                # Update progress
                self.progress['processed_companies'] = i
                if i % 100 == 0:
                    print(f"📊 Processed {i}/{len(tickers)} companies ({qualified_count} qualified)")
                    self.save_progress()
                    self.optimize_memory()

            except Exception as e:
                print(f"❌ Error processing {ticker}: {e}")
                results['company_results'][ticker] = {
                    'qualified': False,
                    'cashflow_score': 0,
                    'reasons_failed': [f'Processing error: {str(e)}'],
                    'analysis': {}
                }
                failed_count += 1

        # Update final statistics
        results['summary_statistics'].update({
            'processed_companies': len(tickers),
            'qualified_companies': qualified_count,
            'failed_companies': failed_count,
            'qualification_rate': (qualified_count / len(tickers)) * 100 if len(tickers) > 0 else 0
        })

        print(f"✅ Pre-screening complete: {qualified_count} qualified, {failed_count} failed")
        return results

    def update_prescreen_progress(self, processed, total):
        """Update progress during pre-screening"""
        self.progress['processed_companies'] = processed
        if processed % 100 == 0:  # Update every 100 companies
            self.save_progress()
            self.optimize_memory()

    def update_dcf_progress(self, processed, total):
        """Update progress during DCF analysis"""
        self.progress['dcf_analyzed'] = processed
        if processed % 20 == 0:  # Update every 20 companies
            self.save_progress()
            self.optimize_memory()

    def run_full_analysis(self, phase=None):
        """Run the complete 4950 company analysis pipeline"""

        # System requirements check
        if not self.check_system_requirements():
            print("❌ System requirements not met. Exiting.")
            return None

        try:
            # Phase 1: Data Loading
            if phase is None or phase == 'data_loading':
                qualified_tickers = self.phase_1_data_loading()
            else:
                # Load from previous run
                qualified_tickers = self.data_loader.get_all_tickers()

            # Phase 2: Cash Flow Pre-screening
            if phase is None or phase == 'prescreen':
                cash_flow_qualified, prescreen_results = self.phase_2_cash_flow_prescreening(qualified_tickers)
            elif phase == 'data_loading':
                # Only data loading requested, exit here
                print("✅ Data loading phase completed successfully!")
                return {'phase': 'data_loading', 'qualified_tickers': len(qualified_tickers)}
            else:
                # Load from previous run
                prescreen_file = self.output_dir / 'prescreen_results.json'
                if not prescreen_file.exists():
                    print("❌ Prescreen results not found. Run prescreen phase first.")
                    return None
                with open(prescreen_file, 'r') as f:
                    prescreen_results = json.load(f)
                cash_flow_qualified = [t for t, r in prescreen_results.get('company_results', {}).items() if r.get('qualified', False)]

            # Phase 3: Warren Buffett DCF Analysis
            if phase is None or phase == 'dcf_only':
                dcf_results = self.phase_3_warren_buffett_dcf(cash_flow_qualified)
            else:
                # Load from previous run
                dcf_file = self.output_dir / 'dcf_results.json'
                with open(dcf_file, 'r') as f:
                    dcf_results = json.load(f)

            # Phase 4: Portfolio Optimization
            if phase is None or phase == 'portfolio':
                investment_candidates, portfolio_results = self.phase_4_portfolio_optimization(dcf_results)
            else:
                # Load from previous run
                portfolio_file = self.output_dir / 'portfolio_recommendations.json'
                with open(portfolio_file, 'r') as f:
                    portfolio_data = json.load(f)
                investment_candidates = portfolio_data['investment_candidates']
                portfolio_results = portfolio_data['portfolio_results']

            # Phase 5: Generate Reports
            if phase is None or phase == 'reports':
                analysis_summary = self.phase_5_generate_reports(prescreen_results, dcf_results, investment_candidates, portfolio_results)

            # Final summary
            total_time = (datetime.now() - self.start_time).total_seconds() / 60

            print("\n" + "=" * 80)
            print("🎉 ANALYSIS COMPLETE!")
            print("=" * 80)
            print(f"⏱️  Total Runtime: {total_time:.1f} minutes")
            print(f"📊 Companies Analyzed: {self.progress['total_companies']}")
            print(f"💰 Cash Flow Qualified: {self.progress['qualified_companies']}")
            print(f"🎯 DCF Analyzed: {self.progress['dcf_analyzed']}")
            print(f"⭐ Buffett Approved: {self.progress['buffett_approved']}")
            print(f"💎 Investment Candidates: {len(investment_candidates)}")
            print(f"📁 Results saved to: {self.output_dir}")
            print("=" * 80)

            return analysis_summary

        except Exception as e:
            print(f"\n❌ ANALYSIS FAILED: {e}")
            print("🔄 You can resume using: python run_full_4950_analysis.py --resume")
            raise

def main():
    """Main function with command line argument parsing"""
    parser = argparse.ArgumentParser(description='Run full 4950 company Warren Buffett DCF analysis')
    parser.add_argument('--batch_size', type=int, default=50, help='Batch size for processing')
    parser.add_argument('--max_workers', type=int, default=8, help='Maximum worker processes')
    parser.add_argument('--resume', action='store_true', help='Resume from previous run')
    parser.add_argument('--phase', choices=['data_loading', 'prescreen', 'dcf_only', 'portfolio', 'reports'],
                       help='Run specific phase only')

    args = parser.parse_args()

    # Initialize and run analysis
    analyzer = Full4950Analysis(
        batch_size=args.batch_size,
        max_workers=args.max_workers,
        resume=args.resume
    )

    result = analyzer.run_full_analysis(phase=args.phase)

    if result:
        print("\n🚀 Analysis completed successfully!")
        print("📊 View results with: python show_analysis_summary.py")
        print("📈 Export to Excel with: python export_results_to_excel.py")
    else:
        print("\n❌ Analysis failed. Check logs for details.")

if __name__ == "__main__":
    main()
