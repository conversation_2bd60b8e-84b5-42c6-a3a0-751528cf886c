#!/usr/bin/env python3
"""
DCF Integration Module

This module integrates the existing DCF analysis from the dcf/ folder
with the fundamental analysis pipeline. It ensures that companies
are valued using proper DCF methodology after passing cash flow screening.

This is for REAL MONEY - DCF valuation is critical for investment decisions.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any, Optional

# Add DCF module path
dcf_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'dcf')
sys.path.insert(0, dcf_path)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('dcf_integration')

class DCFIntegration:
    """
    Integration layer for DCF analysis with fundamental analysis pipeline
    """
    
    def __init__(self):
        """
        Initialize DCF integration
        """
        self.dcf_available = False
        self.dcf_model = None
        self.financial_analyzer = None
        
        # Try to import DCF modules
        self._initialize_dcf_modules()
    
    def _initialize_dcf_modules(self):
        """
        Initialize DCF modules from the dcf/ folder
        """
        try:
            # Import DCF modules
            from dcf_model import DCFModel
            from financial_analyzer import FinancialAnalyzer
            from wacc_calculator import WACCCalculator
            
            self.dcf_model = DCFModel()
            self.financial_analyzer = FinancialAnalyzer()
            self.wacc_calculator = WACCCalculator()
            self.dcf_available = True
            
            logger.info("✅ DCF modules loaded successfully")
            print("✅ DCF Analysis Module: LOADED")
            
        except ImportError as e:
            logger.warning(f"DCF modules not available: {e}")
            print(f"⚠️  DCF Analysis Module: NOT AVAILABLE ({e})")
            print("📝 Will use simplified valuation methodology")
            self.dcf_available = False
    
    def analyze_company_dcf(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform DCF analysis on a company
        
        Parameters:
        -----------
        ticker : str
            Company ticker
        company_data : Dict[str, Any]
            Company financial data
            
        Returns:
        --------
        DCF analysis results
        """
        if self.dcf_available:
            return self._full_dcf_analysis(ticker, company_data)
        else:
            return self._simplified_dcf_analysis(ticker, company_data)
    
    def _full_dcf_analysis(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Full DCF analysis using the dcf/ module
        """
        try:
            logger.info(f"Running full DCF analysis for {ticker}")
            
            # Prepare data for DCF model
            dcf_inputs = self._prepare_dcf_inputs(ticker, company_data)
            
            if not dcf_inputs:
                return self._simplified_dcf_analysis(ticker, company_data)
            
            # Run DCF model
            dcf_result = self.dcf_model.calculate_dcf(dcf_inputs)
            
            # Calculate WACC
            wacc = self.wacc_calculator.calculate_wacc(dcf_inputs)
            
            # Analyze results
            analysis_result = self._analyze_dcf_results(ticker, dcf_result, wacc, company_data)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error in full DCF analysis for {ticker}: {e}")
            return self._simplified_dcf_analysis(ticker, company_data)
    
    def _prepare_dcf_inputs(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare inputs for DCF model from company data
        """
        try:
            # Extract financial data
            profit_loss = company_data.get('profit_loss', {})
            balance_sheet = company_data.get('balance_sheet', {})
            cash_flow = company_data.get('cash_flow', {})
            ratios = company_data.get('ratios', {}).get('ratios', {})
            overview = company_data.get('overview', {})
            
            # Prepare DCF inputs
            dcf_inputs = {
                'ticker': ticker,
                'financial_data': {
                    'profit_loss': profit_loss,
                    'balance_sheet': balance_sheet,
                    'cash_flow': cash_flow,
                    'ratios': ratios
                },
                'market_data': {
                    'market_cap': overview.get('market_cap'),
                    'current_price': overview.get('current_price'),
                    'shares_outstanding': overview.get('shares_outstanding')
                },
                'assumptions': {
                    'growth_rate': self._estimate_growth_rate(profit_loss),
                    'terminal_growth_rate': 3.0,  # Conservative 3%
                    'discount_rate': self._estimate_discount_rate(ratios),
                    'projection_years': 10
                }
            }
            
            return dcf_inputs
            
        except Exception as e:
            logger.error(f"Error preparing DCF inputs for {ticker}: {e}")
            return None
    
    def _estimate_growth_rate(self, profit_loss: Dict[str, Any]) -> float:
        """
        Estimate growth rate from historical data
        """
        try:
            # Extract revenue data
            revenues = []
            years = sorted([year for year in profit_loss.keys() 
                          if year not in ['TTM', 'units', 'notes']])
            
            for year in years:
                if isinstance(profit_loss[year], dict):
                    revenue = profit_loss[year].get('revenue', profit_loss[year].get('sales'))
                    if revenue and isinstance(revenue, (int, float)):
                        revenues.append(float(revenue))
            
            if len(revenues) >= 3:
                # Calculate CAGR
                years_span = len(revenues) - 1
                cagr = ((revenues[-1] / revenues[0]) ** (1/years_span) - 1) * 100
                
                # Cap growth rate at reasonable levels
                return min(max(cagr, 0), 25)  # Between 0% and 25%
            
            return 10.0  # Default 10% growth
            
        except Exception as e:
            logger.error(f"Error estimating growth rate: {e}")
            return 10.0
    
    def _estimate_discount_rate(self, ratios: Dict[str, Any]) -> float:
        """
        Estimate discount rate (WACC) from company ratios
        """
        try:
            # Base risk-free rate (approximate)
            risk_free_rate = 6.5  # Indian 10-year bond yield
            
            # Market risk premium
            market_risk_premium = 6.0
            
            # Estimate beta based on sector and financial metrics
            debt_to_equity = ratios.get('debt_to_equity', 0)
            
            # Simple beta estimation
            if debt_to_equity < 0.5:
                beta = 0.8  # Low risk
            elif debt_to_equity < 1.0:
                beta = 1.0  # Market risk
            else:
                beta = 1.2  # High risk
            
            # Calculate cost of equity using CAPM
            cost_of_equity = risk_free_rate + (beta * market_risk_premium)
            
            # For simplicity, use cost of equity as discount rate
            # In full DCF, this would be WACC calculation
            return min(max(cost_of_equity, 8.0), 18.0)  # Between 8% and 18%
            
        except Exception as e:
            logger.error(f"Error estimating discount rate: {e}")
            return 12.0  # Default 12%
    
    def _analyze_dcf_results(self, ticker: str, dcf_result: Dict[str, Any], 
                           wacc: float, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze DCF results and generate investment recommendation
        """
        try:
            intrinsic_value = dcf_result.get('intrinsic_value_per_share', 0)
            current_price = company_data.get('overview', {}).get('current_price', 0)
            
            # Calculate margin of safety
            if current_price and current_price > 0:
                margin_of_safety = ((intrinsic_value - current_price) / current_price) * 100
            else:
                margin_of_safety = 0
            
            # DCF scoring
            dcf_score = 50  # Base score
            
            # Margin of safety scoring
            if margin_of_safety > 20:
                dcf_score += 30
                valuation_signal = "Significantly Undervalued"
            elif margin_of_safety > 10:
                dcf_score += 20
                valuation_signal = "Undervalued"
            elif margin_of_safety > 0:
                dcf_score += 10
                valuation_signal = "Fairly Valued"
            elif margin_of_safety > -10:
                dcf_score -= 10
                valuation_signal = "Slightly Overvalued"
            else:
                dcf_score -= 20
                valuation_signal = "Overvalued"
            
            # Quality adjustments
            ratios = company_data.get('ratios', {}).get('ratios', {})
            roe = ratios.get('roe_%', 0)
            
            if roe and roe > 15:
                dcf_score += 10
            elif roe and roe < 10:
                dcf_score -= 10
            
            # DCF pass/fail
            dcf_passed = dcf_score >= 60 and margin_of_safety > -10
            
            return {
                'ticker': ticker,
                'dcf_method': 'full_dcf',
                'intrinsic_value': intrinsic_value,
                'current_price': current_price,
                'margin_of_safety': margin_of_safety,
                'wacc': wacc,
                'dcf_score': dcf_score,
                'dcf_passed': dcf_passed,
                'valuation_signal': valuation_signal,
                'recommendation': 'QUALIFIED' if dcf_passed else 'REJECTED',
                'dcf_details': dcf_result
            }
            
        except Exception as e:
            logger.error(f"Error analyzing DCF results for {ticker}: {e}")
            return {'ticker': ticker, 'error': str(e), 'dcf_passed': False}
    
    def _simplified_dcf_analysis(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Simplified DCF analysis when full DCF module is not available
        """
        try:
            # Extract key metrics
            ratios = company_data.get('ratios', {}).get('ratios', {})
            overview = company_data.get('overview', {})
            
            # Key valuation metrics
            pe_ratio = ratios.get('pe_ratio')
            pb_ratio = ratios.get('pb_ratio')
            roe = ratios.get('roe_%')
            roce = ratios.get('roce_%')
            debt_to_equity = ratios.get('debt_to_equity', 0)
            
            # Simplified valuation score
            dcf_score = 50  # Base score
            valuation_signals = []
            
            # ROE analysis
            if roe and isinstance(roe, (int, float)):
                if roe > 20:
                    dcf_score += 20
                    valuation_signals.append(f"Excellent ROE: {roe}%")
                elif roe > 15:
                    dcf_score += 15
                    valuation_signals.append(f"Strong ROE: {roe}%")
                elif roe > 10:
                    dcf_score += 10
                elif roe < 5:
                    dcf_score -= 15
                    valuation_signals.append(f"Weak ROE: {roe}%")
            
            # ROCE analysis
            if roce and isinstance(roce, (int, float)):
                if roce > 20:
                    dcf_score += 15
                    valuation_signals.append(f"Excellent ROCE: {roce}%")
                elif roce > 15:
                    dcf_score += 10
                elif roce < 5:
                    dcf_score -= 10
            
            # Debt analysis
            if debt_to_equity and isinstance(debt_to_equity, (int, float)):
                if debt_to_equity < 0.3:
                    dcf_score += 15
                    valuation_signals.append("Very conservative debt")
                elif debt_to_equity < 0.5:
                    dcf_score += 10
                    valuation_signals.append("Conservative debt")
                elif debt_to_equity > 2.0:
                    dcf_score -= 20
                    valuation_signals.append("High debt concern")
                elif debt_to_equity > 1.0:
                    dcf_score -= 10
            
            # PE ratio analysis
            if pe_ratio and isinstance(pe_ratio, (int, float)):
                if 8 <= pe_ratio <= 15:
                    dcf_score += 15
                    valuation_signals.append("Attractive valuation")
                elif 15 < pe_ratio <= 25:
                    dcf_score += 10
                    valuation_signals.append("Reasonable valuation")
                elif pe_ratio > 40:
                    dcf_score -= 15
                    valuation_signals.append("High valuation concern")
                elif pe_ratio > 30:
                    dcf_score -= 10
            
            # PB ratio analysis
            if pb_ratio and isinstance(pb_ratio, (int, float)):
                if pb_ratio < 1.5:
                    dcf_score += 10
                    valuation_signals.append("Low price-to-book")
                elif pb_ratio > 5:
                    dcf_score -= 10
                    valuation_signals.append("High price-to-book")
            
            # Determine if DCF passed
            dcf_passed = dcf_score >= 60
            
            # Generate recommendation
            if dcf_score >= 80:
                recommendation = "STRONG_QUALIFIED"
            elif dcf_score >= 60:
                recommendation = "QUALIFIED"
            else:
                recommendation = "REJECTED"
            
            return {
                'ticker': ticker,
                'dcf_method': 'simplified',
                'dcf_score': dcf_score,
                'dcf_passed': dcf_passed,
                'valuation_signals': valuation_signals,
                'key_metrics': {
                    'roe': roe,
                    'roce': roce,
                    'pe_ratio': pe_ratio,
                    'pb_ratio': pb_ratio,
                    'debt_to_equity': debt_to_equity
                },
                'recommendation': recommendation
            }
            
        except Exception as e:
            logger.error(f"Error in simplified DCF for {ticker}: {e}")
            return {
                'ticker': ticker,
                'error': str(e),
                'dcf_passed': False
            }
    
    def batch_dcf_analysis(self, companies: List[str], company_data_loader) -> Dict[str, Any]:
        """
        Run DCF analysis on multiple companies
        
        Parameters:
        -----------
        companies : List[str]
            List of company tickers
        company_data_loader : function
            Function to load company data
            
        Returns:
        --------
        Batch DCF results
        """
        results = {
            'total_companies': len(companies),
            'companies_analyzed': 0,
            'dcf_qualified': 0,
            'results': {}
        }
        
        print(f"🔄 Running DCF analysis on {len(companies)} companies...")
        
        for i, ticker in enumerate(companies):
            if i % 10 == 0:
                print(f"  DCF Progress: {i}/{len(companies)} ({(i/len(companies)*100):.1f}%)")
            
            try:
                company_data = company_data_loader(ticker)
                if company_data:
                    dcf_result = self.analyze_company_dcf(ticker, company_data)
                    results['results'][ticker] = dcf_result
                    results['companies_analyzed'] += 1
                    
                    if dcf_result.get('dcf_passed', False):
                        results['dcf_qualified'] += 1
                        
            except Exception as e:
                logger.error(f"Error in DCF analysis for {ticker}: {e}")
                results['results'][ticker] = {'error': str(e), 'dcf_passed': False}
        
        print(f"✅ DCF Analysis Complete:")
        print(f"   📊 Companies Analyzed: {results['companies_analyzed']}")
        print(f"   ✅ DCF Qualified: {results['dcf_qualified']}")
        print(f"   📈 DCF Pass Rate: {(results['dcf_qualified']/results['companies_analyzed']*100):.1f}%")
        
        return results

def test_dcf_integration():
    """
    Test the DCF integration with sample companies
    """
    print("🧪 TESTING DCF INTEGRATION")
    print("=" * 60)
    
    # Initialize DCF integration
    dcf_integration = DCFIntegration()
    
    # Test with sample companies
    test_tickers = ['TCS', 'HDFCBANK', 'RELIANCE', 'INFY']
    
    # Import data loader
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    from utils.data_loader import ScreenerDataLoader
    
    data_loader = ScreenerDataLoader('../screener_data_collector/data')
    
    for ticker in test_tickers:
        print(f"\n📊 Testing DCF for {ticker}...")
        
        company_data = data_loader.load_company_data(ticker)
        if company_data:
            dcf_result = dcf_integration.analyze_company_dcf(ticker, company_data)
            
            print(f"  DCF Method: {dcf_result.get('dcf_method', 'unknown')}")
            print(f"  DCF Score: {dcf_result.get('dcf_score', 0):.1f}")
            print(f"  DCF Passed: {'✅ YES' if dcf_result.get('dcf_passed', False) else '❌ NO'}")
            print(f"  Recommendation: {dcf_result.get('recommendation', 'Unknown')}")
            
            if 'valuation_signals' in dcf_result:
                signals = dcf_result['valuation_signals']
                if signals:
                    print(f"  Signals: {', '.join(signals[:2])}")
        else:
            print(f"  ❌ No data available for {ticker}")
    
    print(f"\n✅ DCF Integration Test Complete!")

if __name__ == "__main__":
    test_dcf_integration()
