#!/usr/bin/env python3
"""
Quick Sector Lookup Utility

Simple command-line tool to lookup sectors and companies.
"""

import os
import sys
import json
import argparse

def load_latest_sector_mapping():
    """Load the latest sector mapping file"""
    sector_dir = 'output/sector_analysis'
    
    if not os.path.exists(sector_dir):
        print("❌ No sector analysis directory found")
        print("💡 Run: python test_sector_system.py")
        return None
    
    # Find all sector mapping files
    mapping_files = [f for f in os.listdir(sector_dir) if f.endswith('.json')]
    
    if not mapping_files:
        print("❌ No sector mapping files found")
        print("💡 Run: python test_sector_system.py")
        return None
    
    # Get the latest file
    latest_file = sorted(mapping_files)[-1]
    mapping_path = os.path.join(sector_dir, latest_file)
    
    try:
        with open(mapping_path, 'r') as f:
            mapping = json.load(f)
        
        print(f"📁 Loaded sector mapping: {latest_file}")
        return mapping
        
    except Exception as e:
        print(f"❌ Error loading sector mapping: {e}")
        return None

def find_sector_for_ticker(mapping, ticker):
    """Find sector for a specific ticker"""
    if not mapping:
        return None
    
    sectors = mapping.get('sectors', {})
    ticker = ticker.upper()
    
    for sector, companies in sectors.items():
        if ticker in companies:
            return sector
    
    return None

def get_companies_in_sector(mapping, sector):
    """Get all companies in a sector"""
    if not mapping:
        return []
    
    sectors = mapping.get('sectors', {})
    return sectors.get(sector.lower(), [])

def search_companies(mapping, search_term):
    """Search for companies by partial name"""
    if not mapping:
        return []
    
    results = []
    sectors = mapping.get('sectors', {})
    search_term = search_term.upper()
    
    for sector, companies in sectors.items():
        for company in companies:
            if search_term in company.upper():
                results.append({
                    'ticker': company,
                    'sector': sector,
                    'sector_display': sector.replace('_', ' ').title()
                })
    
    return sorted(results, key=lambda x: x['ticker'])

def show_all_sectors(mapping):
    """Show all sectors and their company counts"""
    if not mapping:
        return
    
    sectors = mapping.get('sectors', {})
    summary = mapping.get('summary', {})
    
    print("📊 ALL SECTORS")
    print("=" * 50)
    
    # Show summary
    print(f"Total Sectors: {summary.get('total_sectors', len(sectors))}")
    print(f"Total Companies: {summary.get('total_companies', 0)}")
    print(f"Last Updated: {mapping.get('timestamp', 'Unknown')}")
    print()
    
    # Sort sectors by company count
    sorted_sectors = sorted(sectors.items(), key=lambda x: len(x[1]), reverse=True)
    
    for i, (sector, companies) in enumerate(sorted_sectors, 1):
        sector_display = sector.replace('_', ' ').title()
        print(f"{i:2d}. {sector_display:20s}: {len(companies):3d} companies")

def show_sector_details(mapping, sector):
    """Show detailed information about a specific sector"""
    if not mapping:
        return
    
    sectors = mapping.get('sectors', {})
    sector_stats = mapping.get('sector_statistics', {})
    
    if sector not in sectors:
        print(f"❌ Sector '{sector}' not found")
        available_sectors = list(sectors.keys())
        print(f"Available sectors: {', '.join(available_sectors)}")
        return
    
    companies = sectors[sector]
    stats = sector_stats.get(sector, {})
    
    sector_display = sector.replace('_', ' ').title()
    
    print(f"🏭 SECTOR DETAILS: {sector_display}")
    print("=" * 50)
    
    print(f"Company Count: {len(companies)}")
    print(f"Companies: {', '.join(companies)}")
    
    # Show financial metrics if available
    financial_metrics = stats.get('financial_metrics', {})
    if financial_metrics:
        print(f"\n📊 Financial Metrics (Average):")
        for metric, data in financial_metrics.items():
            metric_display = metric.replace('_', ' ').title()
            mean_value = data.get('mean', 0)
            print(f"  {metric_display}: {mean_value:.2f}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Sector Lookup Utility")
    parser.add_argument('command', choices=['ticker', 'sector', 'search', 'list', 'details'],
                       help='Command to execute')
    parser.add_argument('value', nargs='?', help='Ticker, sector name, or search term')
    
    args = parser.parse_args()
    
    # Load sector mapping
    mapping = load_latest_sector_mapping()
    if not mapping:
        return
    
    print()
    
    if args.command == 'ticker':
        if not args.value:
            print("❌ Please provide a ticker symbol")
            return
        
        sector = find_sector_for_ticker(mapping, args.value)
        if sector:
            sector_display = sector.replace('_', ' ').title()
            print(f"🔍 {args.value.upper()} → {sector_display}")
        else:
            print(f"❌ {args.value.upper()} not found in sector mapping")
    
    elif args.command == 'sector':
        if not args.value:
            print("❌ Please provide a sector name")
            return
        
        companies = get_companies_in_sector(mapping, args.value)
        if companies:
            sector_display = args.value.replace('_', ' ').title()
            print(f"🏭 {sector_display} ({len(companies)} companies):")
            print(f"   {', '.join(companies)}")
        else:
            print(f"❌ Sector '{args.value}' not found")
    
    elif args.command == 'search':
        if not args.value:
            print("❌ Please provide a search term")
            return
        
        results = search_companies(mapping, args.value)
        if results:
            print(f"🔍 Search results for '{args.value}' ({len(results)} found):")
            for result in results:
                print(f"   {result['ticker']:12s} → {result['sector_display']}")
        else:
            print(f"❌ No companies found matching '{args.value}'")
    
    elif args.command == 'list':
        show_all_sectors(mapping)
    
    elif args.command == 'details':
        if not args.value:
            print("❌ Please provide a sector name")
            return
        
        show_sector_details(mapping, args.value)

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("🔍 SECTOR LOOKUP UTILITY")
        print("=" * 40)
        print("Usage examples:")
        print("  python sector_lookup.py ticker TCS")
        print("  python sector_lookup.py sector banking")
        print("  python sector_lookup.py search HDFC")
        print("  python sector_lookup.py list")
        print("  python sector_lookup.py details banking")
        print()
        print("Commands:")
        print("  ticker   - Find sector for a ticker")
        print("  sector   - List companies in a sector")
        print("  search   - Search for companies")
        print("  list     - Show all sectors")
        print("  details  - Show sector details")
    else:
        main()
