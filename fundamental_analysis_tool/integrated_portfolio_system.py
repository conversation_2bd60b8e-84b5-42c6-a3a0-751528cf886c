#!/usr/bin/env python3
"""
Integrated Portfolio System - Master Integration Module
Combines Fundamental Analysis + DCF + Cash Flow + Portfolio Optimization

This module creates a comprehensive investment management system that:
1. Uses fundamental analysis to screen companies (5-tier system)
2. Applies DCF valuation for intrinsic value assessment
3. Verifies cash flow quality over 10 years
4. Optimizes portfolio allocation using 22+ optimization methods
5. Generates risk-appropriate investment recommendations
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import logging

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, '..'))

# Import fundamental analysis system
from professional_modular_system import ProfessionalModularSystem

# Import portfolio optimization system
try:
    from Portfolio_optimization.optimal_portfolio_builder import OptimalPortfolioBuilder
    from Portfolio_optimization.integrated_portfolio_optimization import IntegratedPortfolioOptimization
    PORTFOLIO_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Portfolio optimization not available: {e}")
    PORTFOLIO_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class IntegratedPortfolioSystem:
    """
    Master system integrating fundamental analysis with portfolio optimization

    This class provides a unified interface to:
    - Extract qualified companies from fundamental analysis
    - Collect daily price data for portfolio optimization
    - Run comprehensive portfolio optimization matrix
    - Generate investment recommendations for different risk profiles
    """

    def __init__(self, data_dir='integrated_portfolio_data'):
        """
        Initialize the integrated portfolio system

        Parameters:
        -----------
        data_dir : str
            Directory to store integrated system data
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # Initialize subsystems
        self.fundamental_system = ProfessionalModularSystem()

        if PORTFOLIO_AVAILABLE:
            self.portfolio_system = OptimalPortfolioBuilder(
                data_dir=str(self.data_dir / 'portfolio_data'),
                results_dir=str(self.data_dir / 'portfolio_results')
            )
        else:
            self.portfolio_system = None
            logger.warning("Portfolio optimization system not available")

        # Define comprehensive ticker sets
        self.ticker_sets = self._define_comprehensive_ticker_sets()

        # Define optimization methods
        self.optimization_methods = self._define_optimization_methods()

        # Define risk profiles
        self.risk_profiles = self._define_risk_profiles()

        logger.info("Integrated Portfolio System initialized successfully")

    def _define_comprehensive_ticker_sets(self) -> Dict[str, Dict[str, Any]]:
        """
        Define all ticker sets for comprehensive analysis
        """
        return {
            # Base fundamental analysis sets
            'ALL_TICKERS': {
                'name': 'Complete Universe',
                'description': 'All 4,903 companies in database',
                'risk_level': 'HIGH',
                'expected_companies': 4903
            },
            'CASHFLOW_QUALIFIED': {
                'name': 'Cash Flow Quality',
                'description': 'Companies with strong 10-year cash flow',
                'risk_level': 'MEDIUM',
                'expected_companies': 1245
            },
            'DCF_SEVERELY_UNDERVALUED': {
                'name': 'Severely Undervalued',
                'description': 'Companies with >50% margin of safety',
                'risk_level': 'MEDIUM',
                'expected_companies': 2699
            },
            'DCF_UNDERVALUED': {
                'name': 'Moderately Undervalued',
                'description': 'Companies with 20-50% margin of safety',
                'risk_level': 'MEDIUM',
                'expected_companies': 211
            },
            'DCF_OVERVALUED': {
                'name': 'Overvalued (Contrarian)',
                'description': 'Overvalued companies for contrarian strategy',
                'risk_level': 'HIGH',
                'expected_companies': 432
            },

            # Cross-analysis sets (highest quality)
            'CASHFLOW_ON_DCF_SEVERELY_UNDERVALUED': {
                'name': 'Triple-Filtered Quality',
                'description': 'Cash flow verified + severely undervalued',
                'risk_level': 'LOW',
                'expected_companies': 342
            },
            'CASHFLOW_ON_DCF_UNDERVALUED': {
                'name': 'Double-Verified Value',
                'description': 'Cash flow verified + moderately undervalued',
                'risk_level': 'LOW',
                'expected_companies': 520
            },
            'DCF_ON_CASHFLOW_QUALIFIED': {
                'name': 'Cash-First Approach',
                'description': 'DCF analysis on cash flow qualified',
                'risk_level': 'MEDIUM',
                'expected_companies': 1245
            },

            # Tier-based sets (to be implemented)
            'TIER_1_QUALIFIED': {
                'name': 'Financial Health Focus',
                'description': 'Companies passing Tier 1 (ROE, ROCE, D/E)',
                'risk_level': 'MEDIUM',
                'expected_companies': 'TBD'
            },
            'TIER_1_2_3_QUALIFIED': {
                'name': 'GARP Strategy',
                'description': 'Growth at Reasonable Price (Tiers 1+2+3)',
                'risk_level': 'MEDIUM',
                'expected_companies': 'TBD'
            },
            'ALL_5_TIERS_QUALIFIED': {
                'name': 'Warren Buffett Quality',
                'description': 'Companies passing all 5 tiers',
                'risk_level': 'LOW',
                'expected_companies': 'TBD'
            }
        }

    def _define_optimization_methods(self) -> Dict[str, Dict[str, Any]]:
        """
        Define all portfolio optimization methods
        """
        return {
            # Traditional methods (7 methods)
            'max_sharpe': {
                'name': 'Maximum Sharpe Ratio',
                'category': 'traditional',
                'risk_level': 'MEDIUM',
                'description': 'Maximize risk-adjusted returns'
            },
            'min_variance': {
                'name': 'Minimum Variance',
                'category': 'traditional',
                'risk_level': 'LOW',
                'description': 'Minimize portfolio volatility'
            },
            'max_sortino': {
                'name': 'Maximum Sortino Ratio',
                'category': 'traditional',
                'risk_level': 'MEDIUM',
                'description': 'Focus on downside risk'
            },
            'risk_parity': {
                'name': 'Risk Parity',
                'category': 'traditional',
                'risk_level': 'MEDIUM',
                'description': 'Equal risk contribution'
            },
            'black_litterman': {
                'name': 'Black-Litterman',
                'category': 'traditional',
                'risk_level': 'MEDIUM',
                'description': 'Market equilibrium + views'
            },

            # Advanced methods (15+ methods)
            'hrp': {
                'name': 'Hierarchical Risk Parity',
                'category': 'advanced',
                'risk_level': 'LOW',
                'description': 'Clustering-based diversification'
            },
            'robust': {
                'name': 'Robust Optimization',
                'category': 'advanced',
                'risk_level': 'MEDIUM',
                'description': 'Uncertainty handling'
            },
            'ml_xgboost': {
                'name': 'Machine Learning (XGBoost)',
                'category': 'advanced',
                'risk_level': 'HIGH',
                'description': 'ML-based return prediction'
            },
            'kelly_copula': {
                'name': 'Kelly Criterion + Copula',
                'category': 'advanced',
                'risk_level': 'HIGH',
                'description': 'Growth maximization'
            },
            'quantum': {
                'name': 'Quantum-Inspired',
                'category': 'advanced',
                'risk_level': 'HIGH',
                'description': 'Simulated annealing optimization'
            }
        }

    def _define_risk_profiles(self) -> Dict[str, Dict[str, Any]]:
        """
        Define risk profiles for investment recommendations
        """
        return {
            'CONSERVATIVE': {
                'target_volatility': 0.08,      # 8% annual volatility
                'target_return': 0.12,          # 12% annual return
                'max_drawdown': 0.05,           # 5% maximum drawdown
                'preferred_ticker_sets': [
                    'ALL_5_TIERS_QUALIFIED',
                    'CASHFLOW_ON_DCF_SEVERELY_UNDERVALUED'
                ],
                'preferred_methods': ['min_variance', 'risk_parity', 'hrp'],
                'max_single_weight': 0.10       # 10% max in any single stock
            },
            'MODERATE': {
                'target_volatility': 0.12,      # 12% annual volatility
                'target_return': 0.16,          # 16% annual return
                'max_drawdown': 0.08,           # 8% maximum drawdown
                'preferred_ticker_sets': [
                    'CASHFLOW_ON_DCF_SEVERELY_UNDERVALUED',
                    'TIER_1_2_3_QUALIFIED'
                ],
                'preferred_methods': ['max_sharpe', 'robust', 'black_litterman'],
                'max_single_weight': 0.15       # 15% max in any single stock
            },
            'AGGRESSIVE': {
                'target_volatility': 0.18,      # 18% annual volatility
                'target_return': 0.22,          # 22% annual return
                'max_drawdown': 0.12,           # 12% maximum drawdown
                'preferred_ticker_sets': [
                    'DCF_SEVERELY_UNDERVALUED',
                    'CASHFLOW_QUALIFIED'
                ],
                'preferred_methods': ['kelly_copula', 'ml_xgboost', 'quantum'],
                'max_single_weight': 0.20       # 20% max in any single stock
            }
        }

    def get_all_qualified_tickers(self) -> Dict[str, List[str]]:
        """
        Get all qualified tickers from all ticker sets

        Returns:
        --------
        Dictionary mapping ticker set names to lists of tickers
        """
        logger.info("🔍 Collecting all qualified tickers from fundamental analysis...")

        all_qualified_tickers = {}

        for set_name, set_config in self.ticker_sets.items():
            try:
                if set_name.startswith('TIER_'):
                    # Skip tier-based sets for now (to be implemented)
                    logger.info(f"⏭️ Skipping {set_name} (tier-based sets not yet implemented)")
                    continue

                logger.info(f"📊 Getting tickers for {set_name}...")
                tickers = self.fundamental_system.get_ticker_set_tickers(set_name)
                all_qualified_tickers[set_name] = tickers

                logger.info(f"✅ {set_name}: {len(tickers)} companies")

            except Exception as e:
                logger.error(f"❌ Error getting tickers for {set_name}: {e}")
                all_qualified_tickers[set_name] = []

        # Calculate unique tickers across all sets
        unique_tickers = set()
        for tickers in all_qualified_tickers.values():
            unique_tickers.update(tickers)

        logger.info(f"🎯 Total unique qualified tickers: {len(unique_tickers)}")

        return all_qualified_tickers

    def collect_price_data_for_qualified_tickers(self, max_tickers_per_set=50,
                                                period='5y', force_refresh=False) -> Dict[str, Any]:
        """
        Collect daily price data for all qualified tickers

        Parameters:
        -----------
        max_tickers_per_set : int
            Maximum number of tickers to collect per set (for testing)
        period : str
            Period of data to collect (e.g., '5y', '3y')
        force_refresh : bool
            Whether to force refresh existing data

        Returns:
        --------
        Dictionary with collection results and statistics
        """
        if not PORTFOLIO_AVAILABLE:
            logger.error("❌ Portfolio optimization system not available")
            return {'error': 'Portfolio system not available'}

        logger.info("🚀 Starting comprehensive price data collection...")

        # Get all qualified tickers
        all_qualified_tickers = self.get_all_qualified_tickers()

        # Collect unique tickers (limit for testing)
        unique_tickers = set()
        for set_name, tickers in all_qualified_tickers.items():
            if tickers:  # Skip empty sets
                limited_tickers = tickers[:max_tickers_per_set]
                unique_tickers.update(limited_tickers)

        unique_tickers_list = list(unique_tickers)
        logger.info(f"📊 Collecting price data for {len(unique_tickers_list)} unique tickers...")

        # Collect price data using portfolio system
        try:
            # Use the correct method from OptimalPortfolioBuilder
            # First collect stock data, then get returns data
            self.portfolio_system.collect_stock_data(
                max_stocks=len(unique_tickers_list),
                period=period
            )

            # Get returns data for the collected stocks
            price_data = self.portfolio_system.get_returns_data(unique_tickers_list)

            logger.info(f"✅ Successfully collected price data for {len(price_data)} companies")

            # Save collection metadata
            collection_metadata = {
                'timestamp': datetime.now().isoformat(),
                'total_unique_tickers': len(unique_tickers_list),
                'successful_collections': len(price_data),
                'success_rate': len(price_data) / len(unique_tickers_list) * 100,
                'period': period,
                'ticker_sets_processed': list(all_qualified_tickers.keys()),
                'failed_tickers': list(set(unique_tickers_list) - set(price_data.keys()))
            }

            # Save metadata
            metadata_file = self.data_dir / 'price_data_collection_metadata.json'
            with open(metadata_file, 'w') as f:
                json.dump(collection_metadata, f, indent=2)

            logger.info(f"📊 Collection success rate: {collection_metadata['success_rate']:.1f}%")

            return {
                'status': 'success',
                'price_data': price_data,
                'metadata': collection_metadata
            }

        except Exception as e:
            logger.error(f"❌ Error collecting price data: {e}")
            return {'status': 'error', 'error': str(e)}


def main():
    """
    Main function to demonstrate the integrated portfolio system
    """
    print("🚀 Integrated Portfolio System - Demo")
    print("=" * 50)

    # Initialize system
    system = IntegratedPortfolioSystem()

    # Step 1: Get all qualified tickers
    print("\n📊 Step 1: Getting all qualified tickers...")
    qualified_tickers = system.get_all_qualified_tickers()

    for set_name, tickers in qualified_tickers.items():
        print(f"  {set_name}: {len(tickers)} companies")

    # Step 2: Collect price data (limited for demo)
    print("\n💰 Step 2: Collecting price data (limited to 20 tickers per set for demo)...")
    collection_result = system.collect_price_data_for_qualified_tickers(
        max_tickers_per_set=20,
        period='3y',
        force_refresh=False
    )

    if collection_result['status'] == 'success':
        metadata = collection_result['metadata']
        print(f"✅ Successfully collected data for {metadata['successful_collections']} companies")
        print(f"📊 Success rate: {metadata['success_rate']:.1f}%")
    else:
        print(f"❌ Error: {collection_result['error']}")

    print("\n🎯 Next steps:")
    print("1. Implement portfolio optimization matrix")
    print("2. Add tier-based ticker sets")
    print("3. Create investment recommendations")
    print("4. Build dashboard integration")


if __name__ == "__main__":
    main()
