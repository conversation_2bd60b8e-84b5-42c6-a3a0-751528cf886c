#!/usr/bin/env python3
"""
🔧 PROFESSIONAL MODULAR ANALYSIS SYSTEM

Clean, professional modular system for running cross-analysis:
- Analysis Type: CASHFLOW, DCF, FUNDAMENTAL, PORTFOLIO
- Ticker Set: ALL_TICKERS, CASHFLOW_QUALIFIED, DCF_UNDERVALUED, etc.
- Smart duplicate checking
- Professional result saving and viewing
- Dashboard integration
"""

import os
import sys
import json
import hashlib
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.data_loader import ScreenerDataLoader

class ProfessionalModularSystem:
    """
    Professional modular analysis system with smart duplicate checking
    """

    def __init__(self):
        self.data_loader = ScreenerDataLoader('../screener_data_collector/data')
        self.results_dir = Path('output/modular_analysis')
        self.results_dir.mkdir(parents=True, exist_ok=True)

        # Analysis configurations
        self.analysis_types = {
            'CASHFLOW': {
                'name': 'Cash Flow Analysis',
                'description': 'Pre-screening based on cash flow quality',
                'script': 'run_cashflow_analysis.py'
            },
            'DCF': {
                'name': 'DCF Valuation Analysis',
                'description': 'Discounted Cash Flow valuation',
                'script': 'run_direct_dcf_analysis.py'
            },
            'FUNDAMENTAL': {
                'name': 'Fundamental Analysis',
                'description': 'Comprehensive fundamental screening with multi-tier analysis',
                'script': 'professional_modular_system.py'
            },
            'PORTFOLIO': {
                'name': 'Portfolio Optimization',
                'description': 'Portfolio construction and optimization',
                'script': None  # To be implemented
            }
        }

        self.ticker_sets = {
            'ALL_TICKERS': {
                'name': 'All Companies',
                'description': 'Complete dataset of all companies'
            },
            'CASHFLOW_QUALIFIED': {
                'name': 'Cash Flow Qualified',
                'description': 'Companies that passed cash flow analysis'
            },
            'DCF_SEVERELY_UNDERVALUED': {
                'name': 'DCF Severely Undervalued (>50%)',
                'description': 'Companies with >50% margin of safety'
            },
            'DCF_UNDERVALUED': {
                'name': 'DCF Undervalued (20-50%)',
                'description': 'Companies with 20-50% margin of safety'
            },
            'DCF_FAIRLY_VALUED': {
                'name': 'DCF Fairly Valued (-20% to +20%)',
                'description': 'Companies with -20% to +20% margin of safety'
            },
            'DCF_OVERVALUED': {
                'name': 'DCF Overvalued (20-50%)',
                'description': 'Companies with -20% to -50% margin of safety'
            },
            'DCF_SEVERELY_OVERVALUED': {
                'name': 'DCF Severely Overvalued (>50%)',
                'description': 'Companies with <-50% margin of safety'
            },
            'DCF_BUFFETT_APPROVED': {
                'name': 'Buffett Approved',
                'description': 'Warren Buffett criteria approved companies'
            },
            'CUSTOM_LIST': {
                'name': 'Custom Ticker List',
                'description': 'User-specified list of tickers'
            },
            # Cross-Analysis Results
            'CASHFLOW_ON_DCF_SEVERELY_UNDERVALUED': {
                'name': 'Cash Flow on DCF Severely Undervalued',
                'description': 'Cash flow analysis on DCF severely undervalued companies'
            },
            'CASHFLOW_ON_DCF_UNDERVALUED': {
                'name': 'Cash Flow on DCF Undervalued',
                'description': 'Cash flow analysis on DCF undervalued companies'
            },
            'DCF_ON_CASHFLOW_QUALIFIED': {
                'name': 'DCF on Cash Flow Qualified',
                'description': 'DCF analysis on cash flow qualified companies'
            },
            'FUNDAMENTAL_ON_DCF_SEVERELY_UNDERVALUED': {
                'name': 'Fundamental on DCF Severely Undervalued',
                'description': 'Fundamental analysis on DCF severely undervalued companies'
            },
            'FUNDAMENTAL_ON_CASHFLOW_QUALIFIED': {
                'name': 'Fundamental on Cash Flow Qualified',
                'description': 'Fundamental analysis on cash flow qualified companies'
            },
            'FUNDAMENTAL_ON_DCF_UNDERVALUED': {
                'name': 'Fundamental on DCF Undervalued',
                'description': 'Fundamental analysis on DCF undervalued companies'
            }
        }

    def generate_analysis_id(self, analysis_type: str, ticker_set: str,
                           custom_tickers: Optional[List[str]] = None,
                           sample_size: Optional[int] = None) -> str:
        """Generate unique analysis ID for duplicate checking"""

        # Create identifier string
        id_components = [analysis_type, ticker_set]

        if custom_tickers:
            id_components.append('_'.join(sorted(custom_tickers)))

        if sample_size:
            id_components.append(f'sample_{sample_size}')

        # Create hash for unique ID
        id_string = '_'.join(id_components)
        hash_suffix = hashlib.md5(id_string.encode()).hexdigest()[:8]

        return f"{analysis_type}_{ticker_set}_{hash_suffix}"

    def check_existing_analysis(self, analysis_id: str) -> Optional[Dict[str, Any]]:
        """Check if analysis already exists"""

        # Look for existing files with this analysis ID
        pattern = f"{analysis_id}_*.json"
        existing_files = list(self.results_dir.glob(pattern))

        if existing_files:
            # Return the most recent file
            latest_file = sorted(existing_files, key=lambda x: x.stat().st_mtime)[-1]
            try:
                with open(latest_file, 'r') as f:
                    data = json.load(f)
                return {
                    'file_path': str(latest_file),
                    'data': data,
                    'timestamp': data.get('metadata', {}).get('timestamp', 'Unknown')
                }
            except Exception:
                return None

        return None

    def get_ticker_set_tickers(self, ticker_set: str,
                              custom_tickers: Optional[List[str]] = None) -> List[str]:
        """Get tickers for specified set"""

        if ticker_set == 'ALL_TICKERS':
            return self.data_loader.get_all_tickers()

        elif ticker_set == 'CASHFLOW_QUALIFIED':
            return self._get_cashflow_qualified_tickers()

        elif ticker_set == 'DCF_SEVERELY_UNDERVALUED':
            return self._get_dcf_filtered_tickers(lambda margin: margin > 50)

        elif ticker_set == 'DCF_UNDERVALUED':
            return self._get_dcf_filtered_tickers(lambda margin: 20 < margin <= 50)

        elif ticker_set == 'DCF_FAIRLY_VALUED':
            return self._get_dcf_filtered_tickers(lambda margin: -20 <= margin <= 20)

        elif ticker_set == 'DCF_OVERVALUED':
            return self._get_dcf_filtered_tickers(lambda margin: -50 <= margin < -20)

        elif ticker_set == 'DCF_SEVERELY_OVERVALUED':
            return self._get_dcf_filtered_tickers(lambda margin: margin < -50)

        elif ticker_set == 'DCF_BUFFETT_APPROVED':
            return self._get_buffett_approved_tickers()

        elif ticker_set == 'CUSTOM_LIST':
            return custom_tickers or []

        # Cross-Analysis Results
        elif ticker_set == 'CASHFLOW_ON_DCF_SEVERELY_UNDERVALUED':
            return self._get_cross_analysis_tickers('CASHFLOW', 'DCF_SEVERELY_UNDERVALUED')

        elif ticker_set == 'CASHFLOW_ON_DCF_UNDERVALUED':
            return self._get_cross_analysis_tickers('CASHFLOW', 'DCF_UNDERVALUED')

        elif ticker_set == 'DCF_ON_CASHFLOW_QUALIFIED':
            return self._get_cross_analysis_tickers('DCF', 'CASHFLOW_QUALIFIED')

        elif ticker_set == 'FUNDAMENTAL_ON_DCF_SEVERELY_UNDERVALUED':
            return self._get_cross_analysis_tickers('FUNDAMENTAL', 'DCF_SEVERELY_UNDERVALUED')

        elif ticker_set == 'FUNDAMENTAL_ON_CASHFLOW_QUALIFIED':
            return self._get_cross_analysis_tickers('FUNDAMENTAL', 'CASHFLOW_QUALIFIED')

        elif ticker_set == 'FUNDAMENTAL_ON_DCF_UNDERVALUED':
            return self._get_cross_analysis_tickers('FUNDAMENTAL', 'DCF_UNDERVALUED')

        else:
            raise ValueError(f"Unknown ticker set: {ticker_set}")

    def _get_cashflow_qualified_tickers(self) -> List[str]:
        """Get tickers that passed cash flow analysis"""
        # Look for latest cash flow analysis
        cashflow_files = list(self.results_dir.glob('CASHFLOW_*.json'))

        # Also check legacy modular results
        legacy_dir = Path('output/modular_results')
        if legacy_dir.exists():
            cashflow_files.extend(list(legacy_dir.glob('Result_1_*.json')))

        if not cashflow_files:
            print("❌ No cash flow results found. Run cash flow analysis first.")
            return []

        # Find the largest analysis (most companies)
        best_file = None
        max_companies = 0

        for file_path in cashflow_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                metadata = data.get('metadata', data.get('analysis_metadata', {}))
                total_companies = metadata.get('total_companies', 0)

                if total_companies > max_companies:
                    max_companies = total_companies
                    best_file = file_path
            except Exception:
                continue

        if not best_file:
            return []

        # Extract qualified tickers
        with open(best_file, 'r') as f:
            data = json.load(f)

        qualified_tickers = []
        company_results = data.get('company_results', {})

        for ticker, result in company_results.items():
            if result.get('qualified', False) or result.get('passed_prescreen', False):
                qualified_tickers.append(ticker)

        print(f"📊 Found {len(qualified_tickers)} cash flow qualified tickers")
        return qualified_tickers

    def _get_dcf_filtered_tickers(self, filter_func) -> List[str]:
        """Get tickers based on DCF filter"""
        # Look for latest DCF analysis
        dcf_files = list(self.results_dir.glob('DCF_*.json'))

        # Also check direct DCF directory
        dcf_dir = Path('output/direct_dcf_analysis')
        if dcf_dir.exists():
            dcf_files.extend(list(dcf_dir.glob('direct_dcf_analysis_*.json')))

        # Also check legacy modular results
        legacy_dir = Path('output/modular_results')
        if legacy_dir.exists():
            dcf_files.extend(list(legacy_dir.glob('Result_2_*.json')))

        if not dcf_files:
            print("❌ No DCF results found. Run DCF analysis first.")
            return []

        # Find the largest analysis
        best_file = None
        max_companies = 0

        for file_path in dcf_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                company_results = data.get('company_results', {})
                if len(company_results) > max_companies:
                    max_companies = len(company_results)
                    best_file = file_path
            except Exception:
                continue

        if not best_file:
            return []

        # Extract filtered tickers
        with open(best_file, 'r') as f:
            data = json.load(f)

        filtered_tickers = []
        company_results = data.get('company_results', {})

        for ticker, result in company_results.items():
            margin = result.get('margin_of_safety', 0)
            if filter_func(margin):
                filtered_tickers.append(ticker)

        print(f"📊 Found {len(filtered_tickers)} DCF filtered tickers")
        return filtered_tickers

    def _get_buffett_approved_tickers(self) -> List[str]:
        """Get Warren Buffett approved tickers"""
        # Similar logic to DCF filtered, but for buffett_approved flag
        dcf_files = list(self.results_dir.glob('DCF_*.json'))

        dcf_dir = Path('output/direct_dcf_analysis')
        if dcf_dir.exists():
            dcf_files.extend(list(dcf_dir.glob('direct_dcf_analysis_*.json')))

        if not dcf_files:
            return []

        # Find largest analysis
        best_file = None
        max_companies = 0

        for file_path in dcf_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                company_results = data.get('company_results', {})
                if len(company_results) > max_companies:
                    max_companies = len(company_results)
                    best_file = file_path
            except Exception:
                continue

        if not best_file:
            return []

        with open(best_file, 'r') as f:
            data = json.load(f)

        buffett_tickers = []
        company_results = data.get('company_results', {})

        for ticker, result in company_results.items():
            if result.get('buffett_approved', False):
                buffett_tickers.append(ticker)

        print(f"📊 Found {len(buffett_tickers)} Buffett approved tickers")
        return buffett_tickers

    def _get_cross_analysis_tickers(self, analysis_type: str, base_ticker_set: str) -> List[str]:
        """Get tickers from cross-analysis results (e.g., CASHFLOW analysis on DCF_SEVERELY_UNDERVALUED)"""

        # Look for cross-analysis results
        pattern = f"{analysis_type}_{base_ticker_set}_*.json"
        cross_files = list(self.results_dir.glob(pattern))

        if not cross_files:
            print(f"❌ No {analysis_type} analysis found on {base_ticker_set}. Run the cross-analysis first:")
            print(f"   python professional_modular_system.py --run {analysis_type}:{base_ticker_set}")
            return []

        # Find the most recent analysis
        latest_file = sorted(cross_files, key=lambda x: x.stat().st_mtime)[-1]

        try:
            with open(latest_file, 'r') as f:
                data = json.load(f)

            qualified_tickers = []

            if analysis_type == 'CASHFLOW':
                # Extract cash flow qualified companies
                company_results = data.get('company_results', {})
                for ticker, result in company_results.items():
                    if result.get('qualified', False) or result.get('passed_prescreen', False):
                        qualified_tickers.append(ticker)

            elif analysis_type == 'DCF':
                # Extract DCF companies (all analyzed companies)
                company_results = data.get('company_results', {})
                qualified_tickers = list(company_results.keys())

            elif analysis_type == 'FUNDAMENTAL':
                # Extract fundamental qualified companies
                summary = data.get('summary', {})
                qualified_companies = summary.get('qualified_companies', [])
                for company in qualified_companies:
                    if isinstance(company, dict):
                        qualified_tickers.append(company.get('ticker'))
                    else:
                        qualified_tickers.append(company)

            print(f"📊 Found {len(qualified_tickers)} tickers from {analysis_type} on {base_ticker_set}")
            return qualified_tickers

        except Exception as e:
            print(f"❌ Error loading cross-analysis results: {e}")
            return []

    def get_ticker_set_info(self, ticker_set: str) -> Dict[str, Any]:
        """Get information about a ticker set"""
        try:
            tickers = self.get_ticker_set_tickers(ticker_set)
            return {
                'ticker_set': ticker_set,
                'name': self.ticker_sets[ticker_set]['name'],
                'description': self.ticker_sets[ticker_set]['description'],
                'ticker_count': len(tickers),
                'sample_tickers': tickers[:5] if tickers else []
            }
        except Exception as e:
            return {
                'ticker_set': ticker_set,
                'name': self.ticker_sets.get(ticker_set, {}).get('name', 'Unknown'),
                'description': self.ticker_sets.get(ticker_set, {}).get('description', 'Unknown'),
                'ticker_count': 0,
                'error': str(e)
            }

    def list_available_analyses(self) -> List[Dict[str, Any]]:
        """List all saved analyses"""
        analyses = []

        for file_path in self.results_dir.glob('*.json'):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                metadata = data.get('metadata', {})
                analyses.append({
                    'file_name': file_path.name,
                    'analysis_id': metadata.get('analysis_id', file_path.stem),
                    'analysis_type': metadata.get('analysis_type', 'Unknown'),
                    'ticker_set': metadata.get('ticker_set', 'Unknown'),
                    'timestamp': metadata.get('timestamp', 'Unknown'),
                    'total_companies': metadata.get('total_companies', 0),
                    'file_path': str(file_path)
                })
            except Exception:
                continue

        # Sort by timestamp (newest first)
        analyses.sort(key=lambda x: x['timestamp'], reverse=True)
        return analyses

    def run_analysis(self, analysis_type: str, ticker_set: str,
                    custom_tickers: Optional[List[str]] = None,
                    sample_size: Optional[int] = None) -> Dict[str, Any]:
        """Run analysis and save results professionally"""

        # Generate analysis ID
        analysis_id = self.generate_analysis_id(analysis_type, ticker_set, custom_tickers, sample_size)

        # Check for existing analysis
        existing = self.check_existing_analysis(analysis_id)
        if existing:
            return {
                'status': 'exists',
                'analysis_id': analysis_id,
                'message': f'Analysis already exists: {existing["timestamp"]}',
                'file_path': existing['file_path']
            }

        # Get tickers
        try:
            tickers = self.get_ticker_set_tickers(ticker_set, custom_tickers)
            if not tickers:
                return {
                    'status': 'error',
                    'error': f'No tickers found for {ticker_set}'
                }

            # Apply sample size
            if sample_size and sample_size < len(tickers):
                tickers = tickers[:sample_size]

            print(f"🚀 Running {analysis_type} on {len(tickers)} companies from {ticker_set}")

            # Run the analysis
            if analysis_type == 'CASHFLOW':
                return self._run_cashflow_analysis(analysis_id, tickers, ticker_set, sample_size)
            elif analysis_type == 'DCF':
                return self._run_dcf_analysis(analysis_id, tickers, ticker_set, sample_size)
            elif analysis_type == 'FUNDAMENTAL':
                return self._run_fundamental_analysis(analysis_id, tickers, ticker_set, sample_size)
            elif analysis_type == 'PORTFOLIO':
                return {'status': 'pending', 'message': 'Portfolio optimization not yet implemented'}
            else:
                return {'status': 'error', 'error': f'Unknown analysis type: {analysis_type}'}

        except Exception as e:
            return {'status': 'error', 'error': str(e)}

    def _run_cashflow_analysis(self, analysis_id: str, tickers: List[str],
                              ticker_set: str, sample_size: Optional[int]) -> Dict[str, Any]:
        """Run cash flow analysis"""
        import subprocess
        import tempfile

        # Create temporary ticker file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(tickers, f)
            temp_file = f.name

        try:
            # Run cash flow analysis
            cmd = ['python', 'run_cashflow_analysis.py', '--ticker-file', temp_file]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)

            if result.returncode == 0:
                # Find the generated result file and rename it
                self._save_professional_result(analysis_id, 'CASHFLOW', ticker_set, len(tickers), sample_size)
                return {
                    'status': 'success',
                    'analysis_id': analysis_id,
                    'message': 'Cash flow analysis completed successfully'
                }
            else:
                return {'status': 'error', 'error': result.stderr}

        finally:
            # Clean up temp file
            os.unlink(temp_file)

    def _run_dcf_analysis(self, analysis_id: str, tickers: List[str],
                         ticker_set: str, sample_size: Optional[int]) -> Dict[str, Any]:
        """Run DCF analysis"""
        import subprocess

        # Create ticker string
        ticker_string = ','.join(tickers)

        # Run DCF analysis
        cmd = ['python', 'run_direct_dcf_analysis.py', '--tickers', ticker_string]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1200)

        if result.returncode == 0:
            # Save professional result
            self._save_professional_result(analysis_id, 'DCF', ticker_set, len(tickers), sample_size)
            return {
                'status': 'success',
                'analysis_id': analysis_id,
                'message': 'DCF analysis completed successfully'
            }
        else:
            return {'status': 'error', 'error': result.stderr}

    def _run_fundamental_analysis(self, analysis_id: str, tickers: List[str],
                                 ticker_set: str, sample_size: Optional[int]) -> Dict[str, Any]:
        """Run comprehensive fundamental analysis on specified tickers"""
        try:
            print(f"🔧 Running comprehensive fundamental analysis on {len(tickers)} companies...")

            # Import the existing fundamental analysis models
            from models.screener import FundamentalScreener
            from models.qualitative_analyzer import QualitativeAnalyzer
            from models.sector_analyzer import SectorAnalyzer

            # Initialize the screener with data loader
            screener = FundamentalScreener(self.data_loader)
            qualitative_analyzer = QualitativeAnalyzer()
            sector_analyzer = SectorAnalyzer()

            # Run comprehensive screening
            print("📊 Running multi-tier fundamental screening...")
            screening_results = screener.screen_companies(
                tickers=tickers,
                criteria=None,  # Use default comprehensive criteria
                max_workers=4
            )

            # Enhance results with qualitative and sector analysis
            print("🔍 Enhancing with qualitative and sector analysis...")
            enhanced_results = self._enhance_fundamental_results(
                screening_results, qualitative_analyzer, sector_analyzer, analysis_id, ticker_set
            )

            # Save the results professionally
            self._save_fundamental_results(enhanced_results, analysis_id, ticker_set, len(tickers), sample_size)

            qualified_count = len(enhanced_results['summary']['qualified_companies'])
            qualification_rate = enhanced_results['summary']['qualification_rate']

            print(f"✅ Fundamental analysis completed!")
            print(f"   📊 Analyzed: {len(tickers)} companies")
            print(f"   ✅ Qualified: {qualified_count} companies")
            print(f"   📈 Qualification Rate: {qualification_rate:.1f}%")

            return {
                'status': 'success',
                'analysis_id': analysis_id,
                'message': f'Comprehensive fundamental analysis completed on {len(tickers)} companies',
                'qualified_companies': qualified_count,
                'qualification_rate': qualification_rate
            }

        except Exception as e:
            print(f"❌ Error in fundamental analysis: {e}")
            return {'status': 'error', 'error': str(e)}

    def _enhance_fundamental_results(self, screening_results: Dict[str, Any],
                                   qualitative_analyzer, sector_analyzer,
                                   analysis_id: str, ticker_set: str) -> Dict[str, Any]:
        """Enhance screening results with qualitative and sector analysis"""

        # Get companies that passed all tiers
        all_pass_companies = screening_results['passed_companies']['all']

        enhanced_results = {
            'metadata': {
                'analysis_id': analysis_id,
                'analysis_type': 'FUNDAMENTAL',
                'ticker_set': ticker_set,
                'total_companies': screening_results['summary']['total'],
                'timestamp': datetime.now().isoformat(),
                'description': f'Comprehensive Fundamental Analysis on {self.ticker_sets[ticker_set]["name"]}'
            },
            'screening_results': screening_results,
            'company_results': {},
            'summary': {
                'total_analyzed': screening_results['summary']['total'],
                'tier1_pass': screening_results['summary']['tier1_pass'],
                'tier2_pass': screening_results['summary']['tier2_pass'],
                'tier3_pass': screening_results['summary']['tier3_pass'],
                'tier4_pass': screening_results['summary']['tier4_pass'],
                'tier5_pass': screening_results['summary']['tier5_pass'],
                'all_tiers_pass': screening_results['summary']['all_pass'],
                'qualified_companies': [],
                'qualification_rate': 0.0
            }
        }

        # Process each company that passed all tiers
        for ticker in all_pass_companies:
            company_result = screening_results['results'].get(ticker, {})

            if 'error' not in company_result:
                # Add qualitative analysis
                try:
                    qualitative_result = qualitative_analyzer.analyze_company(ticker)
                    company_result['qualitative_analysis'] = qualitative_result
                except Exception as e:
                    company_result['qualitative_analysis'] = {'error': str(e)}

                # Add sector analysis
                try:
                    sector_result = sector_analyzer.analyze_company_sector(ticker)
                    company_result['sector_analysis'] = sector_result
                except Exception as e:
                    company_result['sector_analysis'] = {'error': str(e)}

                # Calculate overall fundamental score
                fundamental_score = self._calculate_fundamental_score(company_result)
                company_result['fundamental_score'] = fundamental_score

                # Determine if qualified (score >= 70)
                qualified = fundamental_score >= 70.0
                company_result['qualified'] = qualified

                if qualified:
                    enhanced_results['summary']['qualified_companies'].append({
                        'ticker': ticker,
                        'fundamental_score': fundamental_score,
                        'tier1_pass': company_result.get('tier1_pass', False),
                        'tier2_pass': company_result.get('tier2_pass', False),
                        'tier3_pass': company_result.get('tier3_pass', False),
                        'tier4_pass': company_result.get('tier4_pass', False),
                        'tier5_pass': company_result.get('tier5_pass', False)
                    })

            enhanced_results['company_results'][ticker] = company_result

        # Calculate qualification rate
        total_analyzed = enhanced_results['summary']['total_analyzed']
        qualified_count = len(enhanced_results['summary']['qualified_companies'])

        if total_analyzed > 0:
            enhanced_results['summary']['qualification_rate'] = (qualified_count / total_analyzed) * 100

        # Sort qualified companies by score
        enhanced_results['summary']['qualified_companies'].sort(
            key=lambda x: x['fundamental_score'], reverse=True
        )

        return enhanced_results

    def _calculate_fundamental_score(self, company_result: Dict[str, Any]) -> float:
        """Calculate overall fundamental score"""
        score = 0.0

        # Tier scores (60% weight)
        tier_weights = {'tier1': 0.15, 'tier2': 0.15, 'tier3': 0.10, 'tier4': 0.10, 'tier5': 0.10}

        for tier, weight in tier_weights.items():
            if company_result.get(f'{tier}_pass', False):
                score += weight * 100

        # Qualitative score (25% weight)
        qualitative = company_result.get('qualitative_analysis', {})
        if 'overall_score' in qualitative:
            score += 0.25 * qualitative['overall_score']

        # Sector analysis score (15% weight)
        sector = company_result.get('sector_analysis', {})
        if 'sector_score' in sector:
            score += 0.15 * sector['sector_score']

        return round(score, 1)

    def _save_fundamental_results(self, results: Dict[str, Any], analysis_id: str,
                                ticker_set: str, total_companies: int, sample_size: Optional[int]):
        """Save fundamental analysis results professionally"""

        # Create professional metadata
        professional_metadata = {
            'analysis_id': analysis_id,
            'analysis_type': 'FUNDAMENTAL',
            'ticker_set': ticker_set,
            'total_companies': total_companies,
            'sample_size': sample_size,
            'timestamp': datetime.now().isoformat(),
            'description': f'Comprehensive Fundamental Analysis on {self.ticker_sets[ticker_set]["name"]}'
        }

        # Update metadata in results
        results['metadata'].update(professional_metadata)

        # Save with professional naming
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{analysis_id}_{timestamp}.json"
        output_path = self.results_dir / filename

        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"✅ Professional fundamental analysis saved: {filename}")

    def _save_professional_result(self, analysis_id: str, analysis_type: str,
                                 ticker_set: str, total_companies: int,
                                 sample_size: Optional[int]):
        """Save result with professional metadata"""

        # Find the latest generated result file
        if analysis_type == 'CASHFLOW':
            source_dir = Path('output/modular_results')
            pattern = 'Result_1_*.json'
        else:  # DCF
            source_dir = Path('output/direct_dcf_analysis')
            pattern = 'direct_dcf_analysis_*.json'

        if source_dir.exists():
            source_files = list(source_dir.glob(pattern))
            if source_files:
                latest_file = sorted(source_files, key=lambda x: x.stat().st_mtime)[-1]

                # Load the data
                with open(latest_file, 'r') as f:
                    data = json.load(f)

                # Create professional metadata
                professional_metadata = {
                    'analysis_id': analysis_id,
                    'analysis_type': analysis_type,
                    'ticker_set': ticker_set,
                    'total_companies': total_companies,
                    'sample_size': sample_size,
                    'timestamp': datetime.now().isoformat(),
                    'source_file': str(latest_file),
                    'description': f'{self.analysis_types[analysis_type]["name"]} on {self.ticker_sets[ticker_set]["name"]}'
                }

                # Add professional metadata to data
                data['metadata'] = professional_metadata

                # Save with professional naming
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{analysis_id}_{timestamp}.json"
                output_path = self.results_dir / filename

                with open(output_path, 'w') as f:
                    json.dump(data, f, indent=2, default=str)

                print(f"✅ Professional result saved: {filename}")

def main():
    """Main function for command line usage"""
    import argparse

    parser = argparse.ArgumentParser(description='Professional Modular Analysis System')
    parser.add_argument('--list-sets', action='store_true', help='List available ticker sets')
    parser.add_argument('--list-analyses', action='store_true', help='List saved analyses')
    parser.add_argument('--info', help='Get info about specific ticker set')
    parser.add_argument('--run', help='Run analysis: format ANALYSIS_TYPE:TICKER_SET')
    parser.add_argument('--sample', type=int, help='Sample size for testing')

    args = parser.parse_args()

    system = ProfessionalModularSystem()

    if args.list_sets:
        print("📊 AVAILABLE TICKER SETS:")
        for set_name in system.ticker_sets.keys():
            info = system.get_ticker_set_info(set_name)
            print(f"   {set_name:25} | {info['name']:25} | {info['ticker_count']:,} companies")
        return

    if args.list_analyses:
        print("📁 SAVED ANALYSES:")
        analyses = system.list_available_analyses()
        for analysis in analyses:
            print(f"   {analysis['analysis_id']:30} | {analysis['analysis_type']:10} | {analysis['ticker_set']:20} | {analysis['total_companies']:,} companies")
        return

    if args.info:
        info = system.get_ticker_set_info(args.info)
        print(f"📊 TICKER SET INFO: {args.info}")
        print(f"   Name: {info['name']}")
        print(f"   Description: {info['description']}")
        print(f"   Companies: {info['ticker_count']:,}")
        if info.get('sample_tickers'):
            print(f"   Sample: {', '.join(info['sample_tickers'])}")
        return

    if args.run:
        try:
            analysis_type, ticker_set = args.run.split(':')
            result = system.run_analysis(analysis_type, ticker_set, sample_size=args.sample)
            print(f"🎯 RESULT: {result['status']}")
            if result['status'] == 'success':
                print(f"✅ Analysis ID: {result['analysis_id']}")
            elif result['status'] == 'error':
                print(f"❌ Error: {result['error']}")
        except ValueError:
            print("❌ Invalid format. Use: ANALYSIS_TYPE:TICKER_SET")
        return

if __name__ == "__main__":
    main()
