# 🔗 **FUNCTION DEPENDENCIES & CALL FLOW DIAGRAM**

## 📋 **OVERVIEW**

This document provides a comprehensive mapping of function dependencies, call flows, and inter-component relationships in the financial analysis system.

---

## 🎯 **MAIN SYSTEM ENTRY POINTS**

### **🎛️ Dashboard Entry Point (dashboard.py)**
```python
def main():
    └── create_dashboard()
        ├── display_cash_flow_analysis()
        ├── display_dcf_analysis()
        └── display_professional_modular_section()
            ├── display_cross_analysis_runner()
            ├── display_saved_results()
            └── display_system_info()
```

### **🔧 CLI Entry Point (professional_modular_system.py)**
```python
def main():
    └── ProfessionalModularSystem()
        ├── run_analysis()
        ├── list_available_analyses()
        ├── get_ticker_set_info()
        └── check_existing_analysis()
```

---

## 🔄 **CORE SYSTEM CALL FLOWS**

### **📊 Analysis Execution Flow**
```
User Request
    ↓
professional_modular_system.py::run_analysis()
    ├── generate_analysis_id()
    ├── check_existing_analysis()
    ├── get_ticker_set_tickers()
    │   ├── _get_cashflow_qualified_tickers()
    │   ├── _get_dcf_filtered_tickers()
    │   ├── _get_buffett_approved_tickers()
    │   └── _get_cross_analysis_tickers()
    └── Analysis Type Router:
        ├── _run_cashflow_analysis()
        ├── _run_dcf_analysis()
        ├── _run_fundamental_analysis()
        └── _run_portfolio_analysis()
```

### **🔍 Fundamental Analysis Call Flow**
```
_run_fundamental_analysis()
    ├── FundamentalScreener(data_loader)
    ├── QualitativeAnalyzer()
    ├── SectorAnalyzer()
    └── screener.screen_companies()
        ├── _pre_filter_companies()
        ├── ThreadPoolExecutor()
        └── _screen_company() [parallel]
            ├── data_loader.load_company_data()
            ├── _extract_metrics()
            ├── consistency_analyzer.analyze_company_consistency()
            └── _check_tier() [for each tier 1-5]
```

### **💰 Cash Flow Analysis Call Flow**
```
_run_cashflow_analysis()
    ├── subprocess.run(['python', 'run_cashflow_analysis.py'])
    └── run_cashflow_analysis.py
        ├── data_loader.get_all_tickers()
        ├── CashFlowAnalyzer()
        └── analyze_company() [for each ticker]
            ├── data_loader.get_company_data()
            ├── extract_cash_flow_metrics()
            ├── calculate_cash_flow_score()
            └── determine_qualification()
```

### **📈 DCF Analysis Call Flow**
```
_run_dcf_analysis()
    ├── subprocess.run(['python', 'run_direct_dcf_analysis.py'])
    └── run_direct_dcf_analysis.py
        ├── DCFAnalyzer()
        └── analyze_company() [for each ticker]
            ├── data_loader.get_company_data()
            ├── calculate_free_cash_flow()
            ├── estimate_growth_rate()
            ├── calculate_wacc()
            ├── calculate_terminal_value()
            ├── calculate_present_value()
            └── calculate_margin_of_safety()
```

---

## 🏗️ **COMPONENT DEPENDENCY MATRIX**

### **📊 Data Layer Dependencies**
```
data_loader.py (Core Data Interface)
    ├── Used by: ALL analysis components
    ├── Dependencies: screener_data_collector/
    └── Functions:
        ├── get_all_tickers() → List[str]
        ├── load_company_data(ticker) → Dict
        ├── load_all_companies_summary() → DataFrame
        └── get_company_data(ticker) → Dict
```

### **🔧 Analysis Layer Dependencies**
```
models/screener.py (Fundamental Analysis)
    ├── Dependencies: 
    │   ├── data_loader.py
    │   ├── models/consistency_analyzer.py
    │   ├── models/qualitative_analyzer.py
    │   └── models/sector_analyzer.py
    ├── Used by: professional_modular_system.py
    └── Key Functions:
        ├── screen_companies()
        ├── _screen_company()
        ├── _extract_metrics()
        └── _check_tier()

models/consistency_analyzer.py (Historical Analysis)
    ├── Dependencies: pandas, numpy, scipy
    ├── Used by: models/screener.py
    └── Key Functions:
        ├── analyze_company_consistency()
        ├── _analyze_growth_consistency()
        ├── _analyze_profitability_consistency()
        └── _analyze_quarterly_consistency()

dcf/dcf_analyzer.py (DCF Valuation)
    ├── Dependencies: data_loader.py
    ├── Used by: run_direct_dcf_analysis.py
    └── Key Functions:
        ├── calculate_dcf_value()
        ├── calculate_wacc()
        ├── estimate_growth_rate()
        └── calculate_terminal_value()
```

### **🎛️ Control Layer Dependencies**
```
professional_modular_system.py (Main Controller)
    ├── Dependencies:
    │   ├── data_loader.py
    │   ├── models/screener.py
    │   ├── models/qualitative_analyzer.py
    │   ├── models/sector_analyzer.py
    │   ├── run_cashflow_analysis.py
    │   └── run_direct_dcf_analysis.py
    ├── Used by: dashboard.py, CLI
    └── Key Functions:
        ├── run_analysis()
        ├── get_ticker_set_tickers()
        ├── list_available_analyses()
        └── check_existing_analysis()

dashboard.py (Web Interface)
    ├── Dependencies:
    │   ├── professional_modular_system.py
    │   ├── data_loader.py
    │   └── streamlit, plotly, pandas
    ├── Used by: Web users
    └── Key Functions:
        ├── display_professional_modular_section()
        ├── display_professional_analysis_results()
        ├── get_investment_implications()
        └── create_dashboard()
```

---

## 🔄 **DATA FLOW DEPENDENCIES**

### **📊 Input Data Flow**
```
External Data Sources
    ├── screener.in → screener_data_collector/
    ├── NSE/BSE → exchange_data_collector/
    └── Manual Input → CUSTOM_LIST

Raw Data Storage
    ├── screener_data_collector/data/
    ├── exchange_data_collector/company_data/
    └── Temporary files

Data Access Layer
    └── utils/data_loader.py
        ├── Unified interface for all data access
        ├── Caching and optimization
        └── Error handling and validation
```

### **📈 Analysis Data Flow**
```
Ticker Lists
    ├── ALL_TICKERS (4,903)
    ├── CASHFLOW_QUALIFIED (1,245)
    ├── DCF_FILTERED (various categories)
    └── CROSS_ANALYSIS (combinations)

Analysis Processing
    ├── Parallel processing (ThreadPoolExecutor)
    ├── Sample size limiting
    ├── Error handling per company
    └── Progress tracking

Results Storage
    ├── output/modular_analysis/
    ├── Professional naming convention
    ├── Metadata inclusion
    └── JSON format standardization
```

### **🎯 Output Data Flow**
```
Analysis Results
    ├── Company-level results
    ├── Summary statistics
    ├── Tier performance data
    └── Investment implications

Dashboard Display
    ├── Interactive tables
    ├── Filtering capabilities
    ├── Investment recommendations
    └── Visual charts

Export Capabilities
    ├── JSON download
    ├── CSV export potential
    └── PDF report generation
```

---

## 🔧 **CRITICAL FUNCTION RELATIONSHIPS**

### **🎯 Core Analysis Pipeline**
```
1. Ticker Set Resolution:
   get_ticker_set_tickers() → [ticker list]
   
2. Data Loading:
   data_loader.load_company_data() → {company data}
   
3. Analysis Execution:
   analysis_engine.analyze() → {results}
   
4. Result Enhancement:
   _enhance_results() → {enhanced results}
   
5. Professional Storage:
   _save_professional_result() → {file path}
```

### **📊 Cross-Analysis Dependencies**
```
Base Analysis Results → Cross-Analysis Input
    ├── CASHFLOW results → DCF input tickers
    ├── DCF results → FUNDAMENTAL input tickers
    └── Combined results → Portfolio input tickers

Cross-Analysis Execution:
    ├── _get_cross_analysis_tickers()
    ├── Pattern matching for result files
    ├── Ticker extraction from results
    └── New analysis on filtered tickers
```

### **🎛️ Dashboard Integration Points**
```
System Controller Integration:
    ├── professional_modular_system.list_available_analyses()
    ├── professional_modular_system.run_analysis()
    └── professional_modular_system.get_ticker_set_info()

Result Display Integration:
    ├── display_professional_analysis_results()
    ├── get_investment_implications()
    └── Advanced filtering and search
```

---

## 🚀 **EXTENSION POINTS & INTERFACES**

### **🔧 Adding New Analysis Types**
```
1. Create analysis engine:
   new_analysis/new_analyzer.py

2. Add to professional_modular_system.py:
   elif analysis_type == 'NEW_TYPE':
       return self._run_new_analysis()

3. Implement runner method:
   def _run_new_analysis(self, analysis_id, tickers, ticker_set, sample_size)

4. Add to dashboard display:
   elif analysis['analysis_type'] == 'NEW_TYPE':
       # Display logic
```

### **📊 Adding New Data Sources**
```
1. Create data collector:
   new_data_collector/collect_new_data.py

2. Extend data_loader.py:
   def load_new_data_source(self, ticker)

3. Update analysis engines:
   Use new data through data_loader interface
```

### **🎯 Adding New Ticker Sets**
```
1. Add to ticker_sets dictionary:
   'NEW_TICKER_SET': {
       'name': 'New Set Name',
       'description': 'Description'
   }

2. Add getter method:
   elif ticker_set == 'NEW_TICKER_SET':
       return self._get_new_ticker_set()

3. Implement getter:
   def _get_new_ticker_set(self) -> List[str]
```

---

## 📋 **FUNCTION SIGNATURE REFERENCE**

### **🎛️ Core System Functions**
```python
# Main Controller
ProfessionalModularSystem.run_analysis(
    analysis_type: str, 
    ticker_set: str,
    custom_tickers: Optional[List[str]] = None,
    sample_size: Optional[int] = None
) -> Dict[str, Any]

# Data Access
ScreenerDataLoader.load_company_data(ticker: str) -> Dict[str, Any]
ScreenerDataLoader.get_all_tickers() -> List[str]

# Analysis Engines
FundamentalScreener.screen_companies(
    tickers: List[str] = None,
    criteria: Dict = None,
    max_workers: int = 4
) -> Dict[str, Any]

# Dashboard Display
display_professional_analysis_results(analysis: Dict[str, Any]) -> None
get_investment_implications(
    tier1_pass: bool, tier2_pass: bool, tier3_pass: bool, 
    tier4_pass: bool, tier5_pass: bool
) -> Tuple[str, str, str]
```

**🎯 This comprehensive function dependency mapping provides a complete blueprint for understanding, maintaining, and extending the financial analysis system.**
