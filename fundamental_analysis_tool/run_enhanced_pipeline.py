#!/usr/bin/env python3
"""
Run Enhanced Investment Pipeline

This script runs the complete enhanced pipeline:
1. Load existing cash flow pre-screening results (1245 companies)
2. Run DCF analysis on qualified companies
3. Integrate with existing fundamental analysis
4. Run portfolio optimization
5. Generate final investment recommendations

This is for REAL MONEY - every step is transparent and traceable.
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import modules
from utils.data_loader import ScreenerDataLoader
from dcf_integration import DCFIntegration

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('enhanced_pipeline')

class EnhancedPipelineRunner:
    """
    Enhanced pipeline runner that integrates all analysis components
    """
    
    def __init__(self):
        """
        Initialize the enhanced pipeline runner
        """
        self.data_loader = ScreenerDataLoader('../screener_data_collector/data')
        self.dcf_integration = DCFIntegration()
        
        # Load existing results
        self.existing_results = self._load_existing_results()
        
        # Pipeline results
        self.enhanced_results = {
            'metadata': {
                'timestamp': datetime.now().isoformat(),
                'pipeline_version': '2.0_enhanced',
                'stages': ['cashflow_prescreen', 'dcf_analysis', 'fundamental_analysis', 'portfolio_optimization']
            },
            'stage_1_existing_cashflow': {},
            'stage_2_dcf_analysis': {},
            'stage_3_enhanced_fundamental': {},
            'stage_4_portfolio_optimization': {},
            'stage_5_final_recommendations': {}
        }
    
    def _load_existing_results(self):
        """
        Load existing production analysis results
        """
        results_dir = 'output/production_analysis'
        
        if not os.path.exists(results_dir):
            print("❌ No existing results found")
            return None
        
        # Find latest results
        result_files = [f for f in os.listdir(results_dir) if f.startswith('production_analysis_final_')]
        
        if not result_files:
            print("❌ No production analysis files found")
            return None
        
        latest_file = sorted(result_files)[-1]
        file_path = os.path.join(results_dir, latest_file)
        
        print(f"📁 Loading existing results from: {latest_file}")
        
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error loading results: {e}")
            return None
    
    def run_enhanced_pipeline(self):
        """
        Run the complete enhanced pipeline
        """
        print("=" * 100)
        print("🚀 ENHANCED INVESTMENT PIPELINE - REAL MONEY ANALYSIS")
        print("=" * 100)
        print("📊 Pipeline: Existing Results → DCF → Enhanced Analysis → Portfolio → Final Recommendations")
        print("=" * 100)
        
        if not self.existing_results:
            print("❌ Cannot proceed without existing results")
            return None
        
        # Stage 1: Extract qualified companies from existing results
        print("\n🔍 STAGE 1: EXTRACTING QUALIFIED COMPANIES FROM EXISTING RESULTS")
        print("-" * 80)
        
        qualified_companies = self._extract_qualified_companies()
        
        print(f"✅ Qualified Companies Extracted:")
        print(f"   📊 Total Companies: {len(qualified_companies)}")
        
        # Stage 2: DCF Analysis on qualified companies
        print(f"\n💰 STAGE 2: DCF ANALYSIS ON {len(qualified_companies)} QUALIFIED COMPANIES")
        print("-" * 80)
        
        dcf_results = self._run_dcf_analysis(qualified_companies)
        
        # Stage 3: Enhanced Fundamental Analysis
        print(f"\n📈 STAGE 3: ENHANCED FUNDAMENTAL ANALYSIS")
        print("-" * 80)
        
        enhanced_fundamental = self._enhance_fundamental_analysis(qualified_companies, dcf_results)
        
        # Stage 4: Portfolio Optimization
        print(f"\n🎯 STAGE 4: PORTFOLIO OPTIMIZATION")
        print("-" * 80)
        
        portfolio_results = self._run_portfolio_optimization(qualified_companies, dcf_results)
        
        # Stage 5: Final Recommendations
        print(f"\n🏆 STAGE 5: FINAL INVESTMENT RECOMMENDATIONS")
        print("-" * 80)
        
        final_recommendations = self._generate_final_recommendations(qualified_companies, dcf_results, portfolio_results)
        
        # Save results
        self._save_enhanced_results()
        
        # Display summary
        self._display_final_summary()
        
        return self.enhanced_results
    
    def _extract_qualified_companies(self):
        """
        Extract companies that passed cash flow screening
        """
        cashflow_results = self.existing_results.get('stage_1_cashflow_prescreen', {}).get('results', {})
        
        qualified_companies = []
        for ticker, result in cashflow_results.items():
            if result.get('passed_prescreen', False):
                qualified_companies.append(ticker)
        
        # Store in enhanced results
        self.enhanced_results['stage_1_existing_cashflow'] = {
            'total_qualified': len(qualified_companies),
            'qualified_companies': qualified_companies,
            'source': 'existing_production_analysis'
        }
        
        return qualified_companies
    
    def _run_dcf_analysis(self, qualified_companies: List[str]):
        """
        Run DCF analysis on qualified companies
        """
        print(f"  🔄 Running DCF analysis on {len(qualified_companies)} companies...")
        
        # Use batch DCF analysis
        dcf_results = self.dcf_integration.batch_dcf_analysis(
            qualified_companies, 
            self.data_loader.load_company_data
        )
        
        # Store results
        self.enhanced_results['stage_2_dcf_analysis'] = dcf_results
        
        return dcf_results
    
    def _enhance_fundamental_analysis(self, qualified_companies: List[str], dcf_results: Dict[str, Any]):
        """
        Enhance fundamental analysis by combining existing results with DCF
        """
        print(f"  🔄 Enhancing fundamental analysis...")
        
        # Get existing comprehensive analysis
        existing_comprehensive = self.existing_results.get('stage_2_comprehensive_analysis', {}).get('results', {})
        
        enhanced_analysis = {}
        
        for ticker in qualified_companies:
            try:
                # Get existing analysis
                existing_data = existing_comprehensive.get(ticker, {})
                
                # Get DCF results
                dcf_data = dcf_results.get('results', {}).get(ticker, {})
                
                # Combine scores
                consistency_score = existing_data.get('consistency_score', 0)
                qualitative_score = existing_data.get('qualitative_score', 0)
                dcf_score = dcf_data.get('dcf_score', 0)
                
                # Enhanced combined score
                # Cash Flow (from existing): 20%
                # DCF Analysis: 40% 
                # Consistency: 25%
                # Qualitative: 15%
                
                cashflow_score = 0
                cashflow_data = self.existing_results.get('stage_1_cashflow_prescreen', {}).get('results', {}).get(ticker, {})
                if cashflow_data:
                    cashflow_score = cashflow_data.get('cash_flow_score', 0)
                
                enhanced_score = (
                    cashflow_score * 0.20 +
                    dcf_score * 0.40 +
                    consistency_score * 0.25 +
                    qualitative_score * 0.15
                )
                
                # Generate enhanced recommendation
                if enhanced_score >= 75:
                    recommendation = 'STRONG_BUY'
                elif enhanced_score >= 65:
                    recommendation = 'BUY'
                elif enhanced_score >= 55:
                    recommendation = 'HOLD'
                else:
                    recommendation = 'AVOID'
                
                enhanced_analysis[ticker] = {
                    'ticker': ticker,
                    'sector': existing_data.get('sector', 'unknown'),
                    'cashflow_score': cashflow_score,
                    'dcf_score': dcf_score,
                    'consistency_score': consistency_score,
                    'qualitative_score': qualitative_score,
                    'enhanced_score': enhanced_score,
                    'recommendation': recommendation,
                    'dcf_passed': dcf_data.get('dcf_passed', False),
                    'valuation_signals': dcf_data.get('valuation_signals', [])
                }
                
            except Exception as e:
                logger.error(f"Error enhancing analysis for {ticker}: {e}")
        
        self.enhanced_results['stage_3_enhanced_fundamental'] = {
            'total_companies': len(enhanced_analysis),
            'results': enhanced_analysis
        }
        
        print(f"  ✅ Enhanced analysis completed for {len(enhanced_analysis)} companies")
        
        return enhanced_analysis
    
    def _run_portfolio_optimization(self, qualified_companies: List[str], dcf_results: Dict[str, Any]):
        """
        Run portfolio optimization
        """
        print(f"  🔄 Running portfolio optimization...")
        
        # Try to import portfolio optimization modules
        try:
            portfolio_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'Portfolio_optimization')
            sys.path.insert(0, portfolio_path)
            
            from integrated_portfolio_optimization import IntegratedPortfolioOptimization
            print("  ✅ Portfolio optimization modules loaded")
            
            # For now, create simplified portfolio allocation
            portfolio_results = self._simplified_portfolio_optimization(qualified_companies, dcf_results)
            
        except ImportError as e:
            print(f"  ⚠️  Portfolio modules not available: {e}")
            print("  📝 Using simplified portfolio allocation...")
            portfolio_results = self._simplified_portfolio_optimization(qualified_companies, dcf_results)
        
        self.enhanced_results['stage_4_portfolio_optimization'] = portfolio_results
        
        return portfolio_results
    
    def _simplified_portfolio_optimization(self, qualified_companies: List[str], dcf_results: Dict[str, Any]):
        """
        Simplified portfolio optimization based on enhanced scores
        """
        # Get enhanced fundamental results
        enhanced_fundamental = self.enhanced_results.get('stage_3_enhanced_fundamental', {}).get('results', {})
        
        # Create portfolio allocations
        portfolio_candidates = {}
        
        for ticker in qualified_companies:
            if ticker in enhanced_fundamental:
                analysis = enhanced_fundamental[ticker]
                
                # Only include companies with good scores and DCF pass
                enhanced_score = analysis.get('enhanced_score', 0)
                dcf_passed = analysis.get('dcf_passed', False)
                
                if enhanced_score >= 60 and dcf_passed:
                    portfolio_candidates[ticker] = {
                        'score': enhanced_score,
                        'sector': analysis.get('sector', 'unknown'),
                        'recommendation': analysis.get('recommendation', 'HOLD')
                    }
        
        # Sort by score
        sorted_candidates = sorted(portfolio_candidates.items(), key=lambda x: x[1]['score'], reverse=True)
        
        # Create portfolio allocation (top 20 companies)
        top_companies = sorted_candidates[:20]
        
        # Simple equal-weight allocation with score adjustment
        total_score = sum(data['score'] for _, data in top_companies)
        
        portfolio_allocation = {}
        for ticker, data in top_companies:
            if total_score > 0:
                weight = (data['score'] / total_score) * 100
                portfolio_allocation[ticker] = {
                    'weight': weight,
                    'score': data['score'],
                    'sector': data['sector'],
                    'recommendation': data['recommendation']
                }
        
        return {
            'method': 'score_based_allocation',
            'total_candidates': len(portfolio_candidates),
            'portfolio_size': len(portfolio_allocation),
            'allocation': portfolio_allocation,
            'total_weight': sum(data['weight'] for data in portfolio_allocation.values())
        }
    
    def _generate_final_recommendations(self, qualified_companies: List[str], 
                                      dcf_results: Dict[str, Any], 
                                      portfolio_results: Dict[str, Any]):
        """
        Generate final investment recommendations
        """
        enhanced_fundamental = self.enhanced_results.get('stage_3_enhanced_fundamental', {}).get('results', {})
        
        # Categorize recommendations
        recommendations = {
            'strong_buy': [],
            'buy': [],
            'hold': [],
            'portfolio_allocation': portfolio_results.get('allocation', {}),
            'summary_statistics': {}
        }
        
        for ticker, analysis in enhanced_fundamental.items():
            recommendation = analysis.get('recommendation', 'AVOID')
            
            if recommendation == 'STRONG_BUY':
                recommendations['strong_buy'].append(analysis)
            elif recommendation == 'BUY':
                recommendations['buy'].append(analysis)
            elif recommendation == 'HOLD':
                recommendations['hold'].append(analysis)
        
        # Sort each category by score
        for category in ['strong_buy', 'buy', 'hold']:
            recommendations[category].sort(key=lambda x: x.get('enhanced_score', 0), reverse=True)
        
        # Generate summary statistics
        recommendations['summary_statistics'] = {
            'total_analyzed': len(enhanced_fundamental),
            'strong_buy_count': len(recommendations['strong_buy']),
            'buy_count': len(recommendations['buy']),
            'hold_count': len(recommendations['hold']),
            'portfolio_companies': len(recommendations['portfolio_allocation']),
            'dcf_qualified': len([r for r in enhanced_fundamental.values() if r.get('dcf_passed', False)])
        }
        
        self.enhanced_results['stage_5_final_recommendations'] = recommendations
        
        return recommendations
    
    def _save_enhanced_results(self):
        """
        Save enhanced pipeline results
        """
        os.makedirs('output/enhanced_pipeline', exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'output/enhanced_pipeline/enhanced_pipeline_results_{timestamp}.json'
        
        with open(output_file, 'w') as f:
            json.dump(self.enhanced_results, f, indent=2, default=str)
        
        print(f"📁 Enhanced pipeline results saved to: {output_file}")
        
        # Also save portfolio allocation as CSV
        portfolio_allocation = self.enhanced_results.get('stage_5_final_recommendations', {}).get('portfolio_allocation', {})
        
        if portfolio_allocation:
            portfolio_df = pd.DataFrame.from_dict(portfolio_allocation, orient='index')
            portfolio_df.index.name = 'ticker'
            portfolio_csv = f'output/enhanced_pipeline/portfolio_allocation_{timestamp}.csv'
            portfolio_df.to_csv(portfolio_csv)
            print(f"📁 Portfolio allocation saved to: {portfolio_csv}")
        
        return output_file
    
    def _display_final_summary(self):
        """
        Display final summary of the enhanced pipeline
        """
        final_recs = self.enhanced_results.get('stage_5_final_recommendations', {})
        summary_stats = final_recs.get('summary_statistics', {})
        
        print(f"\n" + "=" * 100)
        print("✅ ENHANCED INVESTMENT PIPELINE COMPLETED!")
        print("=" * 100)
        
        print(f"📊 PIPELINE SUMMARY:")
        print(f"   Total Companies Analyzed: {summary_stats.get('total_analyzed', 0):,}")
        print(f"   DCF Qualified: {summary_stats.get('dcf_qualified', 0):,}")
        print(f"   Portfolio Companies: {summary_stats.get('portfolio_companies', 0)}")
        
        print(f"\n🎯 INVESTMENT RECOMMENDATIONS:")
        print(f"   💎 STRONG BUY: {summary_stats.get('strong_buy_count', 0)} companies")
        print(f"   📈 BUY: {summary_stats.get('buy_count', 0)} companies")
        print(f"   📊 HOLD: {summary_stats.get('hold_count', 0)} companies")
        
        # Show top recommendations
        strong_buy = final_recs.get('strong_buy', [])
        buy = final_recs.get('buy', [])
        
        print(f"\n🏆 TOP 10 INVESTMENT OPPORTUNITIES:")
        all_top = strong_buy + buy
        all_top.sort(key=lambda x: x.get('enhanced_score', 0), reverse=True)
        
        for i, rec in enumerate(all_top[:10], 1):
            ticker = rec.get('ticker', 'Unknown')
            sector = rec.get('sector', 'unknown')
            score = rec.get('enhanced_score', 0)
            recommendation = rec.get('recommendation', 'UNKNOWN')
            dcf_passed = '✅' if rec.get('dcf_passed', False) else '❌'
            
            print(f"   {i:2d}. {ticker:12s} ({sector:15s}) - Score: {score:.1f} - {recommendation} - DCF: {dcf_passed}")
        
        # Show portfolio allocation
        portfolio_allocation = final_recs.get('portfolio_allocation', {})
        if portfolio_allocation:
            print(f"\n💼 PORTFOLIO ALLOCATION (TOP 10):")
            sorted_portfolio = sorted(portfolio_allocation.items(), key=lambda x: x[1]['weight'], reverse=True)
            
            for i, (ticker, data) in enumerate(sorted_portfolio[:10], 1):
                weight = data.get('weight', 0)
                score = data.get('score', 0)
                sector = data.get('sector', 'unknown')
                print(f"   {i:2d}. {ticker:12s} ({sector:15s}) - Weight: {weight:.1f}% - Score: {score:.1f}")
        
        print(f"\n🎉 Ready for Real Money Investment Decisions!")
        print("=" * 100)

def main():
    """
    Main function to run the enhanced pipeline
    """
    print("🚀 ENHANCED INVESTMENT PIPELINE RUNNER")
    print("=" * 80)
    print("Real Money Analysis - DCF + Portfolio Optimization Integration")
    print("=" * 80)
    
    # Initialize and run pipeline
    pipeline = EnhancedPipelineRunner()
    results = pipeline.run_enhanced_pipeline()
    
    if results:
        print(f"\n✅ Enhanced Pipeline Completed Successfully!")
        print(f"📁 Results available in output/enhanced_pipeline/")
    else:
        print(f"\n❌ Pipeline failed to complete")

if __name__ == "__main__":
    main()
