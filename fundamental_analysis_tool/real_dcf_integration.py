#!/usr/bin/env python3
"""
REAL DCF Integration Module

This module ACTUALLY integrates the existing DCF analysis from the dcf/ folder
with the fundamental analysis pipeline. No more simplified analysis - this uses
the real DCF modules you built.

This is for REAL MONEY - proper DCF valuation using your existing implementation.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any, Optional

# Add DCF module path - ACTUAL path to your DCF folder
dcf_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'dcf')
sys.path.insert(0, dcf_path)

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('real_dcf_integration')

class RealDCFIntegration:
    """
    REAL DCF integration using your actual DCF modules
    """

    def __init__(self):
        """
        Initialize REAL DCF integration
        """
        self.dcf_available = False
        self.dcf_model = None
        self.financial_analyzer = None
        self.wacc_calculator = None

        # Try to import REAL DCF modules
        self._initialize_real_dcf_modules()

    def _initialize_real_dcf_modules(self):
        """
        Initialize REAL DCF modules from the dcf/ folder
        """
        try:
            # Import ACTUAL DCF modules from your dcf/ folder
            from dcf_model import DCFModel
            from financial_analyzer import FinancialAnalyzer
            from wacc_calculator import WACCCalculator

            self.dcf_model = DCFModel()
            self.financial_analyzer = FinancialAnalyzer()
            self.wacc_calculator = WACCCalculator()
            self.dcf_available = True

            logger.info("✅ REAL DCF modules loaded successfully from dcf/ folder")
            print("✅ REAL DCF Analysis Module: LOADED")
            print(f"   📁 DCF Path: {dcf_path}")
            print(f"   🔧 DCF Model: {type(self.dcf_model).__name__}")
            print(f"   📊 Financial Analyzer: {type(self.financial_analyzer).__name__}")
            print(f"   💰 WACC Calculator: {type(self.wacc_calculator).__name__}")

        except ImportError as e:
            logger.error(f"REAL DCF modules not available: {e}")
            print(f"❌ REAL DCF Analysis Module: NOT AVAILABLE")
            print(f"   Error: {e}")
            print(f"   DCF Path: {dcf_path}")
            print(f"   Check if dcf/ folder exists and contains the required modules")
            self.dcf_available = False

    def analyze_company_real_dcf(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform REAL DCF analysis on a company using your actual DCF modules

        Parameters:
        -----------
        ticker : str
            Company ticker
        company_data : Dict[str, Any]
            Company financial data from screener

        Returns:
        --------
        REAL DCF analysis results
        """
        if not self.dcf_available:
            return {
                'ticker': ticker,
                'error': 'REAL DCF modules not available',
                'dcf_method': 'unavailable',
                'dcf_passed': False,
                'dcf_score': 0
            }

        try:
            logger.info(f"Running REAL DCF analysis for {ticker}")

            # Step 1: Convert screener data to DCF format
            dcf_data = self._convert_screener_to_dcf_format(ticker, company_data)

            # Step 2: Run financial analysis using REAL financial analyzer
            # Note: Skip financial analyzer if it can't find data - we have screener data
            try:
                financial_analysis = self.financial_analyzer.analyze_financials(ticker)
            except Exception as e:
                logger.warning(f"Financial analyzer failed for {ticker}: {e}")
                financial_analysis = {'error': str(e)}

            # Step 3: Calculate WACC using REAL WACC calculator
            wacc_result = self.wacc_calculator.calculate_wacc(ticker, dcf_data)

            # Step 4: Prepare DCF inputs
            dcf_inputs = self._prepare_real_dcf_inputs(ticker, dcf_data, financial_analysis, wacc_result)

            # Step 5: Run REAL DCF model
            dcf_result = self.dcf_model.run_dcf_valuation(dcf_inputs)

            # Step 6: Analyze and score results
            analysis_result = self._analyze_real_dcf_results(ticker, dcf_result, wacc_result, company_data)

            return analysis_result

        except Exception as e:
            logger.error(f"Error in REAL DCF analysis for {ticker}: {e}")
            return {
                'ticker': ticker,
                'error': str(e),
                'dcf_method': 'real_dcf_failed',
                'dcf_passed': False,
                'dcf_score': 0
            }

    def _convert_screener_to_dcf_format(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert screener data format to DCF module format with enhanced FCF calculation
        """
        # Create DCF-compatible data structure
        dcf_data = {
            'overview': company_data.get('overview', {}),
            'profit_loss': {},
            'balance_sheet': {},
            'cash_flow': {},
            'ratios': company_data.get('ratios', {})
        }

        # Add symbol to overview for DCF module
        dcf_data['overview']['symbol'] = ticker

        # Convert profit & loss data
        pl_data = company_data.get('profit_loss', {})
        if pl_data:
            # Remove units and notes
            pl_clean = {k: v for k, v in pl_data.items() if k not in ['units', 'notes']}
            dcf_data['profit_loss'] = pl_clean

        # Convert balance sheet data
        bs_data = company_data.get('balance_sheet', {})
        if bs_data:
            bs_clean = {k: v for k, v in bs_data.items() if k not in ['units', 'notes']}
            dcf_data['balance_sheet'] = bs_clean

        # Convert cash flow data with enhanced FCF calculation
        cf_data = company_data.get('cash_flow', {})
        if cf_data:
            cf_clean = {k: v for k, v in cf_data.items() if k not in ['units', 'notes']}

            # Calculate proper FCF using our comprehensive cash flow analysis
            enhanced_cf_data = self._enhance_cash_flow_data(ticker, cf_clean, pl_clean)
            dcf_data['cash_flow'] = enhanced_cf_data

        return dcf_data

    def _enhance_cash_flow_data(self, ticker: str, cf_data: Dict[str, Any], pl_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance cash flow data with proper FCF calculations using our comprehensive analysis
        """
        try:
            # Use our comprehensive cash flow analysis
            from models.cashflow_prescreener import CashFlowPreScreener
            prescreener = CashFlowPreScreener()

            # Create company data structure for analysis
            company_data = {
                'cash_flow': cf_data,
                'profit_loss': pl_data
            }

            # Run comprehensive cash flow analysis
            cf_analysis = prescreener.prescreen_company(company_data)

            if cf_analysis and 'analysis' in cf_analysis:
                analysis = cf_analysis['analysis']

                # Extract FCF data from our comprehensive analysis
                if 'free_cash_flow' in analysis:
                    fcf_analysis = analysis['free_cash_flow']
                    fcf_series = fcf_analysis.get('fcf_series', {})

                    # Add calculated FCF to cash flow data
                    if fcf_series:
                        # Create FCF entry in cash flow data
                        cf_data['Free Cash Flow'] = fcf_series
                        logger.info(f"Enhanced FCF data for {ticker}: {len(fcf_series)} years of FCF")

                # Extract OCF data from our comprehensive analysis
                if 'operating_cash_flow' in analysis:
                    ocf_analysis = analysis['operating_cash_flow']
                    ocf_series = ocf_analysis.get('ocf_series', {})

                    # Add calculated OCF to cash flow data
                    if ocf_series:
                        cf_data['Operating Cash Flow'] = ocf_series
                        logger.info(f"Enhanced OCF data for {ticker}: {len(ocf_series)} years of OCF")

            return cf_data

        except Exception as e:
            logger.error(f"Error enhancing cash flow data for {ticker}: {e}")
            return cf_data

    def _prepare_real_dcf_inputs(self, ticker: str, dcf_data: Dict[str, Any],
                                financial_analysis: Dict[str, Any],
                                wacc_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare inputs for REAL DCF model
        """
        # Extract key financial metrics
        try:
            # Get latest revenue
            pl_data = dcf_data.get('profit_loss', {})
            latest_year = None
            latest_revenue = None

            # Find the most recent year with data
            years = [year for year in pl_data.keys() if year != 'TTM']
            if years:
                years.sort(reverse=True)  # Most recent first
                for year in years:
                    if isinstance(pl_data[year], dict):
                        revenue = pl_data[year].get('revenue') or pl_data[year].get('sales')
                        if revenue:
                            latest_year = year
                            latest_revenue = float(str(revenue).replace(',', '')) if revenue else 0
                            break

            # Get cash flow data for FCF margin calculation
            cf_data = dcf_data.get('cash_flow', {})
            latest_fcf = None

            if latest_year and cf_data and latest_year in cf_data:
                if isinstance(cf_data[latest_year], dict):
                    fcf = (cf_data[latest_year].get('free_cash_flow') or
                          cf_data[latest_year].get('fcf') or
                          cf_data[latest_year].get('cash_from_operations', 0))
                    if fcf:
                        latest_fcf = float(str(fcf).replace(',', '')) if fcf else 0

            # Calculate FCF margin
            fcf_margin = 0.1  # Default 10%
            if latest_revenue and latest_fcf and latest_revenue > 0:
                fcf_margin = latest_fcf / latest_revenue
                fcf_margin = max(0.05, min(0.3, fcf_margin))  # Between 5% and 30%

            # Prepare DCF inputs
            dcf_inputs = {
                'ticker': ticker,
                'base_revenue': latest_revenue or 1000,  # Default if not found
                'base_fcf_margin': fcf_margin,
                'discount_rate': wacc_result.get('wacc', 0.12),
                'terminal_growth_rate': 0.03,  # 3% terminal growth
                'terminal_multiple': 15,  # 15x terminal multiple
                'growth_years_1_5': 0.15,  # 15% growth years 1-5
                'growth_years_6_10': 0.08,  # 8% growth years 6-10
                'fcf_margin_years_1_5': fcf_margin,
                'fcf_margin_years_6_10': fcf_margin * 1.1,  # Slight improvement
                'shares_outstanding': self._get_shares_outstanding(dcf_data),
                'net_debt': self._get_net_debt(dcf_data)
            }

            return dcf_inputs

        except Exception as e:
            logger.error(f"Error preparing DCF inputs for {ticker}: {e}")
            # Return default inputs
            return {
                'ticker': ticker,
                'base_revenue': 1000,
                'base_fcf_margin': 0.1,
                'discount_rate': 0.12,
                'terminal_growth_rate': 0.03,
                'terminal_multiple': 15,
                'growth_years_1_5': 0.15,
                'growth_years_6_10': 0.08,
                'fcf_margin_years_1_5': 0.1,
                'fcf_margin_years_6_10': 0.11,
                'shares_outstanding': 100,
                'net_debt': 0
            }

    def _get_shares_outstanding(self, dcf_data: Dict[str, Any]) -> float:
        """
        Extract shares outstanding from data with better extraction logic
        """
        try:
            overview = dcf_data.get('overview', {})

            # Try multiple fields for shares outstanding
            shares_fields = ['shares_outstanding', 'Shares Outstanding', 'shares', 'Shares']
            for field in shares_fields:
                shares = overview.get(field)
                if shares:
                    shares_value = self._safe_float_conversion(shares)
                    if shares_value > 0:
                        # Convert to actual shares (assuming data is in millions/crores)
                        if shares_value < 1000:  # Likely in crores
                            return shares_value * 10_000_000  # Convert crores to actual shares
                        elif shares_value < 100_000:  # Likely in millions
                            return shares_value * 1_000_000  # Convert millions to actual shares
                        else:
                            return shares_value

            # Try to calculate from market cap and current price
            market_cap = self._safe_float_conversion(overview.get('market_cap', '0'))
            current_price = self._safe_float_conversion(overview.get('current_price', '0'))

            if market_cap > 0 and current_price > 0:
                # Market cap is in crores, current price is per share
                # Shares = Market Cap (in crores) * 10,000,000 / Current Price
                shares_calculated = (market_cap * 10_000_000) / current_price

                # Sanity check - if result is too high, market cap might already be in actual rupees
                if shares_calculated > 50_000_000_000:  # More than 50 billion shares is unrealistic
                    shares_calculated = market_cap / current_price

                return shares_calculated

            # Default fallback - use a reasonable estimate based on company size
            if market_cap > 100000:  # Large cap
                return 500_000_000  # 50 crore shares
            elif market_cap > 10000:  # Mid cap
                return 100_000_000  # 10 crore shares
            else:  # Small cap
                return 50_000_000   # 5 crore shares

        except Exception as e:
            logger.error(f"Error extracting shares outstanding: {e}")
            return 100_000_000  # Default 10 crore shares

    def _safe_float_conversion(self, value: Any) -> float:
        """
        Safely convert value to float, handling Indian number formats
        """
        try:
            if value is None:
                return 0.0

            if isinstance(value, (int, float)):
                return float(value)

            if isinstance(value, str):
                # Remove common Indian number formatting
                value = value.replace(',', '').replace('₹', '').replace('%', '').strip()

                # Handle empty strings
                if not value or value == '-' or value == 'N/A':
                    return 0.0

                return float(value)

            return 0.0

        except (ValueError, TypeError):
            return 0.0

    def _get_net_debt(self, dcf_data: Dict[str, Any]) -> float:
        """
        Calculate net debt from balance sheet
        """
        try:
            bs_data = dcf_data.get('balance_sheet', {})

            # Find latest year
            years = [year for year in bs_data.keys() if year != 'TTM']
            if not years:
                return 0

            years.sort(reverse=True)
            latest_year = years[0]

            if latest_year in bs_data and isinstance(bs_data[latest_year], dict):
                bs_latest = bs_data[latest_year]

                # Get total debt
                total_debt = (bs_latest.get('total_debt') or
                             bs_latest.get('Total Debt') or
                             bs_latest.get('borrowings') or 0)

                # Get cash and equivalents
                cash = (bs_latest.get('cash') or
                       bs_latest.get('Cash') or
                       bs_latest.get('cash_and_equivalents') or 0)

                if total_debt:
                    total_debt = float(str(total_debt).replace(',', ''))
                if cash:
                    cash = float(str(cash).replace(',', ''))

                return max(0, total_debt - cash)

            return 0

        except Exception:
            return 0

    def _analyze_real_dcf_results(self, ticker: str, dcf_result: Dict[str, Any],
                                 wacc_result: Dict[str, Any],
                                 company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze REAL DCF results and generate investment recommendation
        """
        try:
            # Extract valuation results
            valuation_summary = dcf_result.get('valuation_summary', {})

            intrinsic_value_per_share = valuation_summary.get('intrinsic_value_per_share', 0)
            enterprise_value = valuation_summary.get('enterprise_value', 0)
            equity_value = valuation_summary.get('equity_value', 0)

            # Get current market price
            overview = company_data.get('overview', {})
            current_price = overview.get('current_price', 0)

            if isinstance(current_price, str):
                current_price = float(current_price.replace(',', '')) if current_price else 0

            # Calculate margin of safety
            margin_of_safety = 0
            if current_price and current_price > 0 and intrinsic_value_per_share > 0:
                margin_of_safety = ((intrinsic_value_per_share - current_price) / current_price) * 100

            # DCF scoring based on REAL DCF results
            dcf_score = 50  # Base score

            # Intrinsic value quality
            if intrinsic_value_per_share > 0:
                dcf_score += 20

            # Margin of safety scoring
            if margin_of_safety > 25:
                dcf_score += 30
                valuation_signal = "Significantly Undervalued"
            elif margin_of_safety > 15:
                dcf_score += 25
                valuation_signal = "Undervalued"
            elif margin_of_safety > 5:
                dcf_score += 15
                valuation_signal = "Fairly Valued"
            elif margin_of_safety > -10:
                dcf_score -= 10
                valuation_signal = "Slightly Overvalued"
            else:
                dcf_score -= 20
                valuation_signal = "Overvalued"

            # WACC quality
            wacc = wacc_result.get('wacc', 0.12)
            if 0.08 <= wacc <= 0.15:
                dcf_score += 10  # Reasonable WACC

            # DCF pass criteria (stricter for real DCF)
            dcf_passed = (dcf_score >= 70 and
                         margin_of_safety > 0 and
                         intrinsic_value_per_share > 0)

            # Generate recommendation
            if dcf_score >= 85 and margin_of_safety > 20:
                recommendation = "STRONG_BUY"
            elif dcf_score >= 70 and margin_of_safety > 10:
                recommendation = "BUY"
            elif dcf_score >= 60:
                recommendation = "HOLD"
            else:
                recommendation = "AVOID"

            return {
                'ticker': ticker,
                'dcf_method': 'real_dcf_analysis',
                'intrinsic_value_per_share': intrinsic_value_per_share,
                'enterprise_value': enterprise_value,
                'equity_value': equity_value,
                'current_price': current_price,
                'margin_of_safety': margin_of_safety,
                'wacc': wacc,
                'dcf_score': dcf_score,
                'dcf_passed': dcf_passed,
                'valuation_signal': valuation_signal,
                'recommendation': recommendation,
                'dcf_details': dcf_result,
                'wacc_details': wacc_result
            }

        except Exception as e:
            logger.error(f"Error analyzing REAL DCF results for {ticker}: {e}")
            return {
                'ticker': ticker,
                'error': str(e),
                'dcf_method': 'real_dcf_analysis_failed',
                'dcf_passed': False,
                'dcf_score': 0
            }

def test_real_dcf_integration():
    """
    Test the REAL DCF integration with sample companies
    """
    print("🧪 TESTING REAL DCF INTEGRATION")
    print("=" * 80)

    # Initialize REAL DCF integration
    real_dcf = RealDCFIntegration()

    if not real_dcf.dcf_available:
        print("❌ REAL DCF modules not available - cannot test")
        return

    # Test with sample companies
    test_tickers = ['TCS', 'HDFCBANK', 'RELIANCE']

    # Import data loader
    from utils.data_loader import ScreenerDataLoader
    data_loader = ScreenerDataLoader('../screener_data_collector/data')

    for ticker in test_tickers:
        print(f"\n📊 Testing REAL DCF for {ticker}...")

        company_data = data_loader.load_company_data(ticker)
        if company_data:
            dcf_result = real_dcf.analyze_company_real_dcf(ticker, company_data)

            print(f"  DCF Method: {dcf_result.get('dcf_method', 'unknown')}")
            print(f"  Intrinsic Value: ₹{dcf_result.get('intrinsic_value_per_share', 0):.2f}")
            print(f"  Current Price: ₹{dcf_result.get('current_price', 0):.2f}")
            print(f"  Margin of Safety: {dcf_result.get('margin_of_safety', 0):.1f}%")
            print(f"  WACC: {dcf_result.get('wacc', 0):.1%}")
            print(f"  DCF Score: {dcf_result.get('dcf_score', 0):.1f}")
            print(f"  DCF Passed: {'✅ YES' if dcf_result.get('dcf_passed', False) else '❌ NO'}")
            print(f"  Recommendation: {dcf_result.get('recommendation', 'Unknown')}")

            if 'error' in dcf_result:
                print(f"  ❌ Error: {dcf_result['error']}")
        else:
            print(f"  ❌ No data available for {ticker}")

    print(f"\n✅ REAL DCF Integration Test Complete!")

if __name__ == "__main__":
    test_real_dcf_integration()
