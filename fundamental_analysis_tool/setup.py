#!/usr/bin/env python3
"""
Setup script for Fundamental Analysis System

This script sets up the environment and installs required dependencies.
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False

def check_data_directory():
    """Check if data directory exists"""
    data_path = "../screener_data_collector/data"
    
    if os.path.exists(data_path):
        print(f"✅ Data directory found: {data_path}")
        return True
    else:
        print(f"⚠️  Data directory not found: {data_path}")
        print("Please ensure the screener_data_collector is set up and has collected data.")
        return False

def create_output_directories():
    """Create output directories"""
    directories = [
        "output",
        "output/production_analysis",
        "output/sector_analysis",
        "output/test_results"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 Created directory: {directory}")

def run_system_test():
    """Run a quick system test"""
    print("\n🧪 Running system test...")
    
    try:
        # Import key modules to test
        from utils.data_loader import ScreenerDataLoader
        from models.cashflow_prescreener import CashFlowPreScreener
        from models.sector_analyzer import SectorAnalyzer
        from models.qualitative_analyzer import QualitativeAnalyzer
        from models.consistency_analyzer import ConsistencyAnalyzer
        
        print("✅ All modules imported successfully!")
        
        # Test data loading
        data_loader = ScreenerDataLoader('../screener_data_collector/data')
        total_tickers = len(data_loader.get_all_tickers())
        print(f"✅ Data loader working: {total_tickers} companies loaded")
        
        return True
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("=" * 60)
    print("📊 FUNDAMENTAL ANALYSIS SYSTEM SETUP")
    print("=" * 60)
    
    # Step 1: Install requirements
    if not install_requirements():
        print("❌ Setup failed at package installation")
        return False
    
    # Step 2: Check data directory
    if not check_data_directory():
        print("⚠️  Warning: Data directory not found. Some features may not work.")
    
    # Step 3: Create output directories
    create_output_directories()
    
    # Step 4: Run system test
    if not run_system_test():
        print("❌ Setup failed at system test")
        return False
    
    print("\n" + "=" * 60)
    print("✅ SETUP COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    
    print("\n🚀 Next Steps:")
    print("1. Launch the dashboard:")
    print("   streamlit run dashboard.py")
    print("\n2. Run a test analysis:")
    print("   python test_enhanced_system.py")
    print("\n3. Run full production analysis:")
    print("   python production_analysis.py")
    
    print("\n📊 Dashboard will be available at: http://localhost:8501")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
