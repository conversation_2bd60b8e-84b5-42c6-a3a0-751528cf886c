#!/usr/bin/env python3
"""
Command Line Interface for Fundamental Analysis System

This script provides easy access to all system functions through a simple CLI.
"""

import sys
import os
import subprocess
import argparse
from datetime import datetime

def print_banner():
    """Print system banner"""
    print("=" * 80)
    print("📊 COMPREHENSIVE FUNDAMENTAL ANALYSIS SYSTEM")
    print("=" * 80)
    print("🎯 Strategic Approach: Cash Flow Pre-screening → Comprehensive Analysis")
    print("📈 Analyzes 4903+ companies for investment opportunities")
    print("=" * 80)

def run_dashboard():
    """Launch the interactive dashboard"""
    print("🚀 Launching Interactive Dashboard...")
    print("📊 Dashboard will be available at: http://localhost:8501")
    print("💡 Keep this terminal open while using the dashboard")
    print("-" * 60)
    
    try:
        subprocess.run(["streamlit", "run", "dashboard.py"])
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped by user")
    except FileNotFoundError:
        print("❌ Streamlit not found. Please install it:")
        print("   pip install streamlit")
    except Exception as e:
        print(f"❌ Error launching dashboard: {e}")

def run_test():
    """Run system test"""
    print("🧪 Running Enhanced System Test...")
    print("📊 Testing with sample companies...")
    print("-" * 60)
    
    try:
        result = subprocess.run(["python", "test_enhanced_system.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Test completed successfully!")
            print("\nTest Results Summary:")
            # Extract key results from output
            lines = result.stdout.split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['✅', '✓', 'PASS', 'Score:', 'Summary:']):
                    print(f"  {line}")
        else:
            print("❌ Test failed!")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Error running test: {e}")

def run_production():
    """Run production analysis"""
    print("🚀 Starting Production Analysis...")
    print("⚠️  This will analyze all 4903 companies and may take 2-4 hours")
    print("📊 Progress will be shown in real-time")
    
    confirm = input("\n❓ Do you want to continue? (y/N): ")
    if confirm.lower() != 'y':
        print("❌ Production analysis cancelled")
        return
    
    print("\n🔄 Starting analysis...")
    print("-" * 60)
    
    try:
        # Run in real-time so user can see progress
        subprocess.run(["python", "production_analysis.py"])
        print("\n✅ Production analysis completed!")
        
    except KeyboardInterrupt:
        print("\n⚠️  Analysis interrupted by user")
        print("💾 Intermediate results may be saved in output/production_analysis/")
        
    except Exception as e:
        print(f"❌ Error in production analysis: {e}")

def show_status():
    """Show system status"""
    print("📊 System Status Check...")
    print("-" * 60)
    
    try:
        # Import and test key modules
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from utils.data_loader import ScreenerDataLoader
        
        # Test data loading
        data_loader = ScreenerDataLoader('../screener_data_collector/data')
        total_companies = len(data_loader.get_all_tickers())
        
        print(f"✅ System Status: Healthy")
        print(f"📊 Companies Available: {total_companies:,}")
        print(f"📁 Data Path: ../screener_data_collector/data")
        
        # Check for recent results
        results_dir = 'output/production_analysis'
        if os.path.exists(results_dir):
            result_files = [f for f in os.listdir(results_dir) 
                          if f.startswith('production_analysis_final_')]
            if result_files:
                latest_file = sorted(result_files)[-1]
                timestamp = latest_file.split('_')[-1].replace('.json', '')
                print(f"📈 Latest Analysis: {timestamp}")
            else:
                print(f"📈 Latest Analysis: None found")
        else:
            print(f"📈 Latest Analysis: No results directory")
            
        # Check modules
        modules = [
            'models.cashflow_prescreener',
            'models.consistency_analyzer', 
            'models.sector_analyzer',
            'models.qualitative_analyzer'
        ]
        
        print(f"\n🔧 Module Status:")
        for module in modules:
            try:
                __import__(module)
                print(f"  ✅ {module}")
            except ImportError as e:
                print(f"  ❌ {module}: {e}")
                
    except Exception as e:
        print(f"❌ System Error: {e}")

def show_help():
    """Show help information"""
    print("📚 Fundamental Analysis System - Help")
    print("-" * 60)
    print("Available Commands:")
    print("")
    print("🚀 Quick Start:")
    print("  python run.py dashboard    # Launch interactive dashboard (RECOMMENDED)")
    print("  python run.py test         # Run system test with sample companies")
    print("")
    print("📊 Analysis:")
    print("  python run.py production   # Analyze all 4903 companies (2-4 hours)")
    print("  python run.py status       # Check system health and data availability")
    print("")
    print("📁 Direct Scripts:")
    print("  python dashboard.py        # Launch dashboard directly")
    print("  python test_enhanced_system.py    # Run comprehensive test")
    print("  python production_analysis.py     # Run full analysis")
    print("")
    print("🎯 Recommended Workflow:")
    print("  1. python run.py status     # Check system health")
    print("  2. python run.py test       # Validate with sample data")
    print("  3. python run.py dashboard  # Use interactive interface")
    print("  4. python run.py production # Run full analysis when ready")
    print("")
    print("📊 Dashboard Features:")
    print("  • Real-time system monitoring")
    print("  • Analysis progress tracking")
    print("  • Investment recommendations")
    print("  • Sector analysis and leaders")
    print("  • One-click test and production runs")

def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(
        description="Comprehensive Fundamental Analysis System",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        'command',
        nargs='?',
        choices=['dashboard', 'test', 'production', 'status', 'help'],
        default='help',
        help='Command to execute'
    )
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.command == 'dashboard':
        run_dashboard()
    elif args.command == 'test':
        run_test()
    elif args.command == 'production':
        run_production()
    elif args.command == 'status':
        show_status()
    elif args.command == 'help':
        show_help()
    else:
        show_help()

if __name__ == "__main__":
    main()
