#!/usr/bin/env python3
"""
📊 ANALYSIS RESULTS SUMMARY

Display comprehensive summary of the 4950 company Warren Buffett DCF analysis results.
Shows investment recommendations, performance metrics, and top picks.

Usage:
    python show_analysis_summary.py              # Show full summary
    python show_analysis_summary.py --top 10     # Show top 10 picks only
    python show_analysis_summary.py --sector     # Show sector breakdown
"""

import os
import sys
import json
import argparse
from datetime import datetime
from pathlib import Path
from collections import defaultdict

def load_analysis_results():
    """Load the latest analysis results"""
    output_dir = Path('output/full_4950_analysis')
    
    # Try to load summary first
    summary_file = output_dir / 'analysis_summary.json'
    if summary_file.exists():
        with open(summary_file, 'r') as f:
            return json.load(f)
    
    # If no summary, look for latest analysis file
    analysis_files = list(output_dir.glob('analysis_*.json'))
    if analysis_files:
        latest_file = max(analysis_files, key=lambda x: x.stat().st_mtime)
        with open(latest_file, 'r') as f:
            data = json.load(f)
            return data.get('analysis_summary', {})
    
    return None

def format_number(num):
    """Format numbers with appropriate suffixes"""
    if num >= 1_000_000:
        return f"{num/1_000_000:.1f}M"
    elif num >= 1_000:
        return f"{num/1_000:.1f}K"
    else:
        return str(int(num))

def display_executive_summary(results):
    """Display executive summary of analysis"""
    print("🎯 EXECUTIVE SUMMARY")
    print("=" * 80)
    
    metadata = results.get('analysis_metadata', {})
    stats = results.get('analysis_statistics', {})
    metrics = results.get('performance_metrics', {})
    
    # Analysis Overview
    print(f"📅 Analysis Date: {metadata.get('timestamp', 'Unknown')}")
    print(f"⏱️  Total Runtime: {metadata.get('total_runtime_minutes', 0):.1f} minutes")
    
    # Key Statistics
    print(f"\n📊 KEY STATISTICS")
    print("-" * 50)
    print(f"📈 Total Companies Analyzed: {format_number(stats.get('total_companies_available', 0))}")
    print(f"💰 Cash Flow Qualified: {format_number(stats.get('cash_flow_qualified', 0))}")
    print(f"🎯 DCF Analyzed: {format_number(stats.get('dcf_analyzed', 0))}")
    print(f"⭐ Buffett Approved: {format_number(stats.get('buffett_approved', 0))}")
    print(f"💎 Investment Candidates: {format_number(stats.get('investment_candidates', 0))}")
    
    # Performance Metrics
    print(f"\n📈 PERFORMANCE METRICS")
    print("-" * 50)
    print(f"🔍 Pre-screen Pass Rate: {metrics.get('prescreen_pass_rate', 0):.1f}%")
    print(f"✅ DCF Success Rate: {metrics.get('dcf_success_rate', 0):.1f}%")
    print(f"⭐ Buffett Approval Rate: {metrics.get('buffett_approval_rate', 0):.1f}%")

def display_top_investment_candidates(results, top_n=20):
    """Display top investment candidates"""
    candidates = results.get('top_investment_candidates', [])
    
    if not candidates:
        print("❌ No investment candidates found")
        return
    
    print(f"\n💎 TOP {min(top_n, len(candidates))} INVESTMENT CANDIDATES")
    print("=" * 80)
    
    # Table header
    print(f"{'Rank':<4} {'Ticker':<12} {'Buffett Score':<13} {'Margin of Safety':<16} {'Recommendation':<15}")
    print("-" * 80)
    
    for i, candidate in enumerate(candidates[:top_n], 1):
        ticker = candidate.get('ticker', 'Unknown')
        score = candidate.get('buffett_score', 0)
        margin = candidate.get('margin_of_safety', 0)
        recommendation = candidate.get('recommendation', 'Unknown')
        
        # Color coding for recommendations
        if recommendation == 'STRONG_BUY':
            rec_display = '🟢 STRONG_BUY'
        elif recommendation == 'BUY':
            rec_display = '🟡 BUY'
        elif recommendation == 'HOLD':
            rec_display = '🟠 HOLD'
        else:
            rec_display = f'⚪ {recommendation}'
        
        print(f"{i:<4} {ticker:<12} {score:<13.1f} {margin:<16.1f}% {rec_display:<15}")

def display_sector_analysis(results):
    """Display sector-wise analysis"""
    candidates = results.get('top_investment_candidates', [])
    
    if not candidates:
        print("❌ No data for sector analysis")
        return
    
    # Group by sectors (this would need sector data from the analysis)
    # For now, we'll show a placeholder
    print(f"\n🏭 SECTOR ANALYSIS")
    print("=" * 80)
    print("📊 Sector breakdown requires sector classification data")
    print("💡 This feature will be enhanced with sector mapping integration")

def display_portfolio_summary(results):
    """Display portfolio optimization summary"""
    portfolio = results.get('portfolio_summary', {})
    
    print(f"\n💼 PORTFOLIO OPTIMIZATION SUMMARY")
    print("=" * 80)
    
    if not portfolio or portfolio.get('error'):
        print("⚠️  Portfolio optimization not available or failed")
        if portfolio.get('error'):
            print(f"❌ Error: {portfolio['error']}")
        return
    
    holdings = portfolio.get('holdings', [])
    if holdings:
        print(f"📊 Optimal Portfolio: {len(holdings)} positions")
        print(f"💰 Total Investment: ₹{portfolio.get('total_investment', 0):,.0f}")
        
        print(f"\n📈 TOP PORTFOLIO HOLDINGS")
        print("-" * 50)
        print(f"{'Ticker':<12} {'Weight':<8} {'Amount':<15}")
        print("-" * 50)
        
        for holding in holdings[:10]:  # Show top 10 holdings
            ticker = holding.get('ticker', 'Unknown')
            weight = holding.get('weight', 0) * 100
            amount = holding.get('amount', 0)
            print(f"{ticker:<12} {weight:<8.1f}% ₹{amount:<15,.0f}")
    else:
        print("❌ No portfolio holdings found")

def display_detailed_statistics(results):
    """Display detailed analysis statistics"""
    print(f"\n📊 DETAILED ANALYSIS STATISTICS")
    print("=" * 80)
    
    stats = results.get('analysis_statistics', {})
    
    # Analysis funnel
    total = stats.get('total_companies_available', 0)
    sufficient_data = stats.get('companies_with_sufficient_data', 0)
    qualified = stats.get('cash_flow_qualified', 0)
    dcf_analyzed = stats.get('dcf_analyzed', 0)
    approved = stats.get('buffett_approved', 0)
    candidates = stats.get('investment_candidates', 0)
    
    print("🔍 ANALYSIS FUNNEL")
    print("-" * 50)
    print(f"📊 Total Companies: {total:,}")
    print(f"    ↓ ({(sufficient_data/total*100) if total > 0 else 0:.1f}% pass)")
    print(f"📈 Sufficient Data: {sufficient_data:,}")
    print(f"    ↓ ({(qualified/sufficient_data*100) if sufficient_data > 0 else 0:.1f}% pass)")
    print(f"💰 Cash Flow Qualified: {qualified:,}")
    print(f"    ↓ ({(dcf_analyzed/qualified*100) if qualified > 0 else 0:.1f}% analyzed)")
    print(f"🎯 DCF Analyzed: {dcf_analyzed:,}")
    print(f"    ↓ ({(approved/dcf_analyzed*100) if dcf_analyzed > 0 else 0:.1f}% approved)")
    print(f"⭐ Buffett Approved: {approved:,}")
    print(f"    ↓ (Final candidates)")
    print(f"💎 Investment Candidates: {candidates:,}")

def display_system_configuration(results):
    """Display system configuration used for analysis"""
    metadata = results.get('analysis_metadata', {})
    config = metadata.get('system_configuration', {})
    
    print(f"\n🔧 SYSTEM CONFIGURATION")
    print("=" * 80)
    print(f"📊 Batch Size: {config.get('batch_size', 'Unknown')}")
    print(f"🔧 Max Workers: {config.get('max_workers', 'Unknown')}")
    print(f"🔄 Resume Mode: {config.get('resume_mode', 'Unknown')}")
    print(f"⏱️  Total Runtime: {metadata.get('total_runtime_minutes', 0):.1f} minutes")

def check_data_freshness():
    """Check how fresh the analysis data is"""
    output_dir = Path('output/full_4950_analysis')
    summary_file = output_dir / 'analysis_summary.json'
    
    if summary_file.exists():
        modified_time = datetime.fromtimestamp(summary_file.stat().st_mtime)
        age = datetime.now() - modified_time
        
        print(f"\n📅 DATA FRESHNESS")
        print("=" * 80)
        print(f"📊 Last Analysis: {modified_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if age.days > 30:
            print(f"⚠️  Data is {age.days} days old - consider running fresh analysis")
        elif age.days > 7:
            print(f"🟡 Data is {age.days} days old - may need update")
        else:
            print(f"✅ Data is fresh ({age.days} days old)")
    else:
        print(f"\n❌ No analysis results found")
        print("💡 Run analysis with: python run_full_4950_analysis.py")

def main():
    """Main function with command line argument parsing"""
    parser = argparse.ArgumentParser(description='Show 4950 company analysis summary')
    parser.add_argument('--top', type=int, default=20, help='Number of top candidates to show')
    parser.add_argument('--sector', action='store_true', help='Show sector breakdown')
    parser.add_argument('--detailed', action='store_true', help='Show detailed statistics')
    parser.add_argument('--config', action='store_true', help='Show system configuration')
    parser.add_argument('--portfolio', action='store_true', help='Show portfolio summary only')
    
    args = parser.parse_args()
    
    # Load analysis results
    results = load_analysis_results()
    
    if not results:
        print("❌ No analysis results found")
        print("💡 Run analysis first: python run_full_4950_analysis.py")
        return
    
    # Check data freshness
    check_data_freshness()
    
    # Display requested information
    if args.portfolio:
        display_portfolio_summary(results)
    elif args.sector:
        display_sector_analysis(results)
    elif args.config:
        display_system_configuration(results)
    elif args.detailed:
        display_detailed_statistics(results)
    else:
        # Default: show comprehensive summary
        display_executive_summary(results)
        display_top_investment_candidates(results, args.top)
        display_portfolio_summary(results)
        
        if args.detailed:
            display_detailed_statistics(results)
            display_system_configuration(results)
    
    print(f"\n💡 NEXT STEPS")
    print("=" * 80)
    print("📈 Export to Excel: python export_results_to_excel.py")
    print("📊 Monitor live analysis: python monitor_analysis_progress.py")
    print("🔄 Run fresh analysis: python run_full_4950_analysis.py")
    print("🎯 View specific sectors: python show_analysis_summary.py --sector")

if __name__ == "__main__":
    main()
