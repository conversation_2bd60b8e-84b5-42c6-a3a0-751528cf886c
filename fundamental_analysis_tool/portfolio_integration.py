#!/usr/bin/env python3
"""
Portfolio Optimization Integration

This module integrates the final investment recommendations with the existing
Portfolio_optimization module for optimal portfolio construction.

This is for REAL MONEY - portfolio optimization is critical for risk management.
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
import logging

# Add Portfolio_optimization path
portfolio_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'Portfolio_optimization')
sys.path.insert(0, portfolio_path)

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('portfolio_integration')

class PortfolioIntegration:
    """
    Integration layer between fundamental analysis and portfolio optimization
    """
    
    def __init__(self):
        """
        Initialize portfolio integration
        """
        self.portfolio_available = False
        self.portfolio_builder = None
        self.integrated_optimizer = None
        
        # Try to import portfolio modules
        self._initialize_portfolio_modules()
        
        # Load final investment results
        self.final_results = self._load_final_results()
        
        # Integration results
        self.integration_results = {
            'metadata': {
                'timestamp': datetime.now().isoformat(),
                'integration_version': '1.0',
                'description': 'Integration of fundamental analysis with portfolio optimization'
            },
            'selected_companies': {},
            'portfolio_optimization': {},
            'final_portfolio': {},
            'performance_metrics': {}
        }
    
    def _initialize_portfolio_modules(self):
        """
        Initialize portfolio optimization modules
        """
        try:
            # Import portfolio optimization modules
            from optimal_portfolio_builder import OptimalPortfolioBuilder
            from integrated_portfolio_optimization import IntegratedPortfolioOptimization
            from stock_data_scraper import StockDataScraper
            
            self.portfolio_available = True
            logger.info("✅ Portfolio optimization modules loaded successfully")
            print("✅ Portfolio Optimization Module: LOADED")
            
        except ImportError as e:
            logger.warning(f"Portfolio modules not available: {e}")
            print(f"⚠️  Portfolio Optimization Module: NOT AVAILABLE ({e})")
            print("📝 Will use simplified portfolio construction")
            self.portfolio_available = False
    
    def _load_final_results(self):
        """
        Load final investment pipeline results
        """
        results_dir = 'output/final_pipeline'
        
        if not os.path.exists(results_dir):
            print("❌ No final pipeline results found")
            return None
        
        # Find latest results
        result_files = [f for f in os.listdir(results_dir) if f.startswith('final_investment_pipeline_')]
        
        if not result_files:
            print("❌ No final pipeline files found")
            return None
        
        latest_file = sorted(result_files)[-1]
        file_path = os.path.join(results_dir, latest_file)
        
        print(f"📁 Loading final results from: {latest_file}")
        
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error loading final results: {e}")
            return None
    
    def run_portfolio_integration(self):
        """
        Run complete portfolio integration
        """
        print("=" * 100)
        print("🎯 PORTFOLIO OPTIMIZATION INTEGRATION")
        print("=" * 100)
        print("📊 Integration: Final Recommendations → Portfolio Optimization → Optimal Portfolio")
        print("=" * 100)
        
        if not self.final_results:
            print("❌ Cannot proceed without final results")
            return None
        
        # Stage 1: Extract selected companies
        print("\n🔍 STAGE 1: EXTRACTING SELECTED COMPANIES")
        print("-" * 80)
        selected_companies = self._extract_selected_companies()
        
        # Stage 2: Fetch market data
        print(f"\n📈 STAGE 2: FETCHING MARKET DATA")
        print("-" * 80)
        market_data = self._fetch_market_data(selected_companies)
        
        # Stage 3: Portfolio optimization
        print(f"\n🎯 STAGE 3: PORTFOLIO OPTIMIZATION")
        print("-" * 80)
        optimization_results = self._run_portfolio_optimization(selected_companies, market_data)
        
        # Stage 4: Final portfolio construction
        print(f"\n🏆 STAGE 4: FINAL PORTFOLIO CONSTRUCTION")
        print("-" * 80)
        final_portfolio = self._construct_final_portfolio(optimization_results)
        
        # Save results
        self._save_integration_results()
        
        # Display summary
        self._display_integration_summary()
        
        return self.integration_results
    
    def _extract_selected_companies(self):
        """
        Extract selected companies from final results
        """
        # Get portfolio allocation from final results
        final_recommendations = self.final_results.get('stage_5_final_recommendations', {})
        portfolio_allocation = final_recommendations.get('portfolio_allocation', {})
        
        # Also get BUY recommendations
        buy_recommendations = final_recommendations.get('buy', [])
        strong_buy_recommendations = final_recommendations.get('strong_buy', [])
        
        # Combine all selected companies
        selected_companies = list(portfolio_allocation.keys())
        
        # Add BUY recommendations not in portfolio
        for rec in buy_recommendations + strong_buy_recommendations:
            ticker = rec.get('ticker')
            if ticker and ticker not in selected_companies:
                selected_companies.append(ticker)
        
        # Limit to top 20 for portfolio optimization
        selected_companies = selected_companies[:20]
        
        print(f"✅ Selected {len(selected_companies)} companies for portfolio optimization")
        print(f"   Companies: {', '.join(selected_companies[:10])}{'...' if len(selected_companies) > 10 else ''}")
        
        self.integration_results['selected_companies'] = {
            'total_selected': len(selected_companies),
            'companies': selected_companies,
            'source': 'final_investment_pipeline'
        }
        
        return selected_companies
    
    def _fetch_market_data(self, selected_companies: List[str]):
        """
        Fetch market data for selected companies
        """
        print(f"  🔄 Fetching market data for {len(selected_companies)} companies...")
        
        if self.portfolio_available:
            return self._fetch_real_market_data(selected_companies)
        else:
            return self._create_synthetic_market_data(selected_companies)
    
    def _fetch_real_market_data(self, selected_companies: List[str]):
        """
        Fetch real market data using portfolio optimization modules
        """
        try:
            from stock_data_scraper import StockDataScraper
            
            # Initialize data scraper
            data_dir = 'market_data'
            os.makedirs(data_dir, exist_ok=True)
            
            scraper = StockDataScraper(data_dir=data_dir)
            
            # Fetch data for selected companies
            print(f"  📊 Fetching real market data...")
            
            market_data = {}
            successful_fetches = 0
            
            for ticker in selected_companies:
                try:
                    # Fetch 2 years of data
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=730)
                    
                    stock_data = scraper.fetch_stock_data(
                        ticker, 
                        start_date.strftime('%Y-%m-%d'),
                        end_date.strftime('%Y-%m-%d')
                    )
                    
                    if stock_data is not None and len(stock_data) > 100:
                        market_data[ticker] = stock_data
                        successful_fetches += 1
                    
                except Exception as e:
                        logger.error(f"Error fetching data for {ticker}: {e}")
            
            print(f"  ✅ Successfully fetched data for {successful_fetches}/{len(selected_companies)} companies")
            
            if successful_fetches < 5:
                print("  ⚠️  Insufficient real data, falling back to synthetic data")
                return self._create_synthetic_market_data(selected_companies)
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error fetching real market data: {e}")
            print(f"  ⚠️  Error fetching real data, using synthetic data")
            return self._create_synthetic_market_data(selected_companies)
    
    def _create_synthetic_market_data(self, selected_companies: List[str]):
        """
        Create synthetic market data for demonstration
        """
        print(f"  📊 Creating synthetic market data for demonstration...")
        
        # Generate synthetic data
        np.random.seed(42)  # For reproducible results
        
        # Create 2 years of daily data
        num_days = 500
        dates = pd.date_range(end=datetime.now(), periods=num_days, freq='D')
        
        market_data = {}
        
        for ticker in selected_companies:
            # Generate realistic stock price movements
            initial_price = np.random.uniform(100, 2000)  # Random starting price
            
            # Generate returns with some correlation to market
            daily_returns = np.random.normal(0.0008, 0.02, num_days)  # ~20% annual volatility
            
            # Add some momentum and mean reversion
            for i in range(1, len(daily_returns)):
                daily_returns[i] += 0.1 * daily_returns[i-1]  # Momentum
                daily_returns[i] -= 0.05 * daily_returns[i-1]  # Mean reversion
            
            # Calculate prices
            prices = [initial_price]
            for ret in daily_returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            # Create DataFrame
            stock_data = pd.DataFrame({
                'Date': dates,
                'Close': prices,
                'Open': [p * np.random.uniform(0.98, 1.02) for p in prices],
                'High': [p * np.random.uniform(1.00, 1.05) for p in prices],
                'Low': [p * np.random.uniform(0.95, 1.00) for p in prices],
                'Volume': np.random.randint(100000, 10000000, num_days)
            })
            
            stock_data.set_index('Date', inplace=True)
            market_data[ticker] = stock_data
        
        print(f"  ✅ Created synthetic data for {len(market_data)} companies")
        
        return market_data
    
    def _run_portfolio_optimization(self, selected_companies: List[str], market_data: Dict[str, pd.DataFrame]):
        """
        Run portfolio optimization
        """
        print(f"  🔄 Running portfolio optimization...")
        
        if self.portfolio_available:
            return self._run_advanced_optimization(selected_companies, market_data)
        else:
            return self._run_simplified_optimization(selected_companies, market_data)
    
    def _run_advanced_optimization(self, selected_companies: List[str], market_data: Dict[str, pd.DataFrame]):
        """
        Run advanced portfolio optimization using existing modules
        """
        try:
            from integrated_portfolio_optimization import IntegratedPortfolioOptimization
            
            # Prepare returns data
            returns_data = {}
            
            for ticker, data in market_data.items():
                if len(data) > 50:  # Ensure sufficient data
                    returns = data['Close'].pct_change().dropna()
                    if len(returns) > 50:
                        returns_data[ticker] = returns
            
            if len(returns_data) < 5:
                print("  ⚠️  Insufficient data for advanced optimization, using simplified method")
                return self._run_simplified_optimization(selected_companies, market_data)
            
            # Convert to DataFrame
            returns_df = pd.DataFrame(returns_data)
            returns_df = returns_df.dropna()
            
            print(f"  📊 Running advanced optimization on {len(returns_df.columns)} assets...")
            
            # Initialize integrated optimizer
            optimizer = IntegratedPortfolioOptimization(
                returns_data=returns_df,
                risk_free_rate=0.06,  # 6% risk-free rate
                factor_data=None,
                option_data=None,
                current_weights=None,
                market_impact_coef=1e-6,
                fixed_cost=0.0001
            )
            
            # Run optimization
            optimization_results = optimizer.run_all_optimizations(
                risk_aversion=2.0,
                option_budget=0.05,
                turnover_constraint=0.3
            )
            
            print(f"  ✅ Advanced optimization completed")
            
            return {
                'method': 'advanced_integrated',
                'returns_data': returns_df,
                'optimization_results': optimization_results,
                'assets_optimized': len(returns_df.columns)
            }
            
        except Exception as e:
            logger.error(f"Error in advanced optimization: {e}")
            print(f"  ⚠️  Advanced optimization failed, using simplified method")
            return self._run_simplified_optimization(selected_companies, market_data)
    
    def _run_simplified_optimization(self, selected_companies: List[str], market_data: Dict[str, pd.DataFrame]):
        """
        Run simplified portfolio optimization
        """
        print(f"  📊 Running simplified portfolio optimization...")
        
        # Calculate returns and risk metrics
        returns_data = {}
        risk_metrics = {}
        
        for ticker, data in market_data.items():
            if len(data) > 50:
                returns = data['Close'].pct_change().dropna()
                
                if len(returns) > 50:
                    returns_data[ticker] = returns
                    
                    # Calculate risk metrics
                    risk_metrics[ticker] = {
                        'mean_return': returns.mean() * 252,  # Annualized
                        'volatility': returns.std() * np.sqrt(252),  # Annualized
                        'sharpe_ratio': (returns.mean() * 252) / (returns.std() * np.sqrt(252)) if returns.std() > 0 else 0
                    }
        
        # Simple mean-variance optimization
        assets = list(returns_data.keys())
        n_assets = len(assets)
        
        if n_assets < 3:
            print(f"  ⚠️  Insufficient assets ({n_assets}) for optimization")
            return None
        
        # Equal weight as baseline
        equal_weights = np.ones(n_assets) / n_assets
        
        # Risk-adjusted weights based on Sharpe ratios
        sharpe_ratios = np.array([risk_metrics[asset]['sharpe_ratio'] for asset in assets])
        sharpe_ratios = np.maximum(sharpe_ratios, 0.1)  # Minimum Sharpe ratio
        
        # Normalize to get weights
        risk_adjusted_weights = sharpe_ratios / sharpe_ratios.sum()
        
        # Create portfolio allocation
        portfolio_weights = {}
        for i, asset in enumerate(assets):
            portfolio_weights[asset] = {
                'weight': risk_adjusted_weights[i],
                'expected_return': risk_metrics[asset]['mean_return'],
                'volatility': risk_metrics[asset]['volatility'],
                'sharpe_ratio': risk_metrics[asset]['sharpe_ratio']
            }
        
        print(f"  ✅ Simplified optimization completed for {n_assets} assets")
        
        return {
            'method': 'simplified_mean_variance',
            'portfolio_weights': portfolio_weights,
            'risk_metrics': risk_metrics,
            'assets_optimized': n_assets
        }
    
    def _construct_final_portfolio(self, optimization_results: Dict[str, Any]):
        """
        Construct final portfolio combining fundamental analysis and optimization
        """
        print(f"  🔄 Constructing final portfolio...")
        
        if not optimization_results:
            print("  ❌ No optimization results available")
            return None
        
        # Get fundamental analysis scores
        final_recommendations = self.final_results.get('stage_5_final_recommendations', {})
        portfolio_allocation = final_recommendations.get('portfolio_allocation', {})
        
        # Combine optimization weights with fundamental scores
        final_portfolio = {}
        
        if optimization_results.get('method') == 'advanced_integrated':
            # Use advanced optimization results
            opt_results = optimization_results.get('optimization_results', {})
            
            # Get the best performing optimization method
            best_method = 'markowitz'  # Default
            if 'markowitz' in opt_results:
                weights = opt_results['markowitz'].get('weights', {})
                
                for asset, weight in weights.items():
                    if asset in portfolio_allocation:
                        fundamental_score = portfolio_allocation[asset].get('enhanced_score', 0)
                        
                        final_portfolio[asset] = {
                            'weight': weight * 100,  # Convert to percentage
                            'fundamental_score': fundamental_score,
                            'optimization_method': 'advanced_markowitz'
                        }
        
        else:
            # Use simplified optimization results
            portfolio_weights = optimization_results.get('portfolio_weights', {})
            
            for asset, opt_data in portfolio_weights.items():
                if asset in portfolio_allocation:
                    fundamental_data = portfolio_allocation[asset]
                    
                    # Combine optimization weight with fundamental score
                    opt_weight = opt_data.get('weight', 0)
                    fundamental_score = fundamental_data.get('enhanced_score', 0)
                    
                    # Adjust weight based on fundamental score
                    adjusted_weight = opt_weight * (fundamental_score / 70)  # Normalize around score 70
                    
                    final_portfolio[asset] = {
                        'weight': adjusted_weight * 100,  # Convert to percentage
                        'fundamental_score': fundamental_score,
                        'expected_return': opt_data.get('expected_return', 0),
                        'volatility': opt_data.get('volatility', 0),
                        'sharpe_ratio': opt_data.get('sharpe_ratio', 0),
                        'optimization_method': 'simplified_mean_variance'
                    }
        
        # Normalize weights to 100%
        total_weight = sum(data.get('weight', 0) for data in final_portfolio.values())
        
        if total_weight > 0:
            for asset in final_portfolio:
                final_portfolio[asset]['weight'] = (
                    final_portfolio[asset]['weight'] / total_weight * 100
                )
        
        print(f"  ✅ Final portfolio constructed with {len(final_portfolio)} assets")
        
        self.integration_results['final_portfolio'] = {
            'total_assets': len(final_portfolio),
            'portfolio': final_portfolio,
            'optimization_method': optimization_results.get('method', 'unknown')
        }
        
        return final_portfolio
    
    def _save_integration_results(self):
        """
        Save integration results
        """
        os.makedirs('output/portfolio_integration', exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'output/portfolio_integration/portfolio_integration_{timestamp}.json'
        
        with open(output_file, 'w') as f:
            json.dump(self.integration_results, f, indent=2, default=str)
        
        print(f"📁 Integration results saved to: {output_file}")
        
        # Save final portfolio as CSV
        final_portfolio = self.integration_results.get('final_portfolio', {}).get('portfolio', {})
        
        if final_portfolio:
            portfolio_df = pd.DataFrame.from_dict(final_portfolio, orient='index')
            portfolio_df.index.name = 'ticker'
            portfolio_csv = f'output/portfolio_integration/final_portfolio_{timestamp}.csv'
            portfolio_df.to_csv(portfolio_csv)
            print(f"📁 Final portfolio saved to: {portfolio_csv}")
        
        return output_file
    
    def _display_integration_summary(self):
        """
        Display integration summary
        """
        final_portfolio = self.integration_results.get('final_portfolio', {}).get('portfolio', {})
        
        print(f"\n" + "=" * 100)
        print("✅ PORTFOLIO OPTIMIZATION INTEGRATION COMPLETED!")
        print("=" * 100)
        
        print(f"📊 INTEGRATION SUMMARY:")
        print(f"   Portfolio Assets: {len(final_portfolio)}")
        print(f"   Optimization Method: {self.integration_results.get('final_portfolio', {}).get('optimization_method', 'Unknown')}")
        
        if final_portfolio:
            print(f"\n💼 FINAL OPTIMIZED PORTFOLIO:")
            sorted_portfolio = sorted(final_portfolio.items(), 
                                    key=lambda x: x[1].get('weight', 0), reverse=True)
            
            print(f"{'Rank':<4} {'Ticker':<12} {'Weight':<8} {'Fund Score':<10} {'Expected Return':<15} {'Volatility':<10}")
            print("-" * 80)
            
            for i, (ticker, data) in enumerate(sorted_portfolio, 1):
                weight = data.get('weight', 0)
                fund_score = data.get('fundamental_score', 0)
                exp_return = data.get('expected_return', 0)
                volatility = data.get('volatility', 0)
                
                print(f"{i:<4} {ticker:<12} {weight:<8.1f}% {fund_score:<10.1f} {exp_return:<15.1%} {volatility:<10.1%}")
        
        print(f"\n🎉 Ready for Real Money Portfolio Implementation!")
        print("=" * 100)

def main():
    """
    Main function to run portfolio integration
    """
    print("🎯 PORTFOLIO OPTIMIZATION INTEGRATION")
    print("=" * 80)
    print("Integrating Fundamental Analysis with Portfolio Optimization")
    print("=" * 80)
    
    # Initialize and run integration
    integration = PortfolioIntegration()
    results = integration.run_portfolio_integration()
    
    if results:
        print(f"\n✅ Portfolio Integration Completed Successfully!")
        print(f"📁 Results available in output/portfolio_integration/")
    else:
        print(f"\n❌ Portfolio integration failed to complete")

if __name__ == "__main__":
    main()
