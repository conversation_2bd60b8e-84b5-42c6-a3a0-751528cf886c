# Fundamental Analysis Tool - Architecture

This document describes the architecture and data flow of the Fundamental Analysis Tool.

## Component Overview

```mermaid
graph TD
    A[Data Loader] --> B[Financial Ratios]
    A --> C[Valuation]
    A --> D[Checklist]
    B --> E[Ranking]
    C --> E
    D --> E
    E --> F[Visualization]
    E --> G[Report Generation]
```

## Module Structure

```
fundamental_analysis_tool/
├── data/                      # For storing processed data
├── models/                    # Core analysis models
│   ├── __init__.py
│   ├── financial_ratios.py    # Financial ratio calculations
│   ├── valuation.py           # DCF and other valuation methods
│   ├── checklist.py           # Due diligence checklists
│   └── ranking.py             # Company ranking algorithms
├── utils/                     # Utility functions
│   ├── __init__.py
│   ├── data_loader.py         # Load data from screener_data_collector
│   └── visualization.py       # Charts and visualizations
├── analysis.py                # Main analysis script
├── README.md                  # Documentation
└── requirements.txt           # Dependencies
```

## Data Flow

```mermaid
sequenceDiagram
    participant User
    participant Analyzer
    participant DataLoader
    participant FinancialRatios
    participant Valuation
    participant Checklist
    participant Ranker
    participant Visualizer
    
    User->>Analyzer: analyze_company(ticker)
    Analyzer->>DataLoader: load_company_data(ticker)
    DataLoader-->>Analyzer: company_data
    
    Analyzer->>FinancialRatios: calculate_all_ratios(company_data)
    FinancialRatios-->>Analyzer: ratios
    
    Analyzer->>FinancialRatios: calculate_growth_rates(company_data)
    FinancialRatios-->>Analyzer: growth_rates
    
    Analyzer->>Valuation: calculate_dcf(company_data)
    Valuation-->>Analyzer: dcf_result
    
    Analyzer->>Valuation: calculate_intrinsic_value_band(company_data)
    Valuation-->>Analyzer: intrinsic_value_band
    
    Analyzer->>Checklist: evaluate_company(company_data)
    Checklist-->>Analyzer: checklist_result
    
    Analyzer-->>User: analysis_results
    
    User->>Analyzer: rank_companies(tickers)
    Analyzer->>Ranker: rank_companies(companies_data)
    Ranker-->>Analyzer: ranking_results
    Analyzer-->>User: ranking_results
    
    User->>Analyzer: generate_report(results)
    Analyzer-->>User: report
    
    User->>Visualizer: plot_financial_trend(data)
    Visualizer-->>User: visualization
```

## Key Classes and Methods

### FundamentalAnalyzer

The main class that orchestrates the analysis process.

- `analyze_company(ticker, analysis_types)`: Analyzes a single company
- `analyze_multiple_companies(tickers, analysis_types)`: Analyzes multiple companies
- `rank_companies(tickers, criteria)`: Ranks companies based on criteria
- `filter_and_rank_companies(filters, criteria)`: Filters and ranks companies
- `generate_report(analysis_results, report_type, output_file)`: Generates a report

### ScreenerDataLoader

Loads financial data from the screener_data_collector.

- `load_company_data(ticker)`: Loads data for a specific company
- `load_multiple_companies(tickers)`: Loads data for multiple companies
- `load_all_companies_summary()`: Loads summary data for all companies

### FinancialRatios

Calculates financial ratios for fundamental analysis.

- `calculate_all_ratios(company_data)`: Calculates all financial ratios
- `calculate_profitability_ratios(company_data)`: Calculates profitability ratios
- `calculate_leverage_ratios(company_data)`: Calculates leverage ratios
- `calculate_valuation_ratios(company_data)`: Calculates valuation ratios
- `calculate_operating_ratios(company_data)`: Calculates operating ratios
- `calculate_growth_rates(company_data, years)`: Calculates growth rates

### Valuation

Performs valuation analysis for companies.

- `calculate_dcf(company_data, growth_rate, terminal_growth_rate, discount_rate)`: Performs DCF analysis
- `perform_sensitivity_analysis(company_data, growth_rates, discount_rates)`: Performs sensitivity analysis
- `calculate_intrinsic_value_band(company_data, growth_rate, discount_rate)`: Calculates intrinsic value band

### Checklist

Evaluates companies against due diligence checklists.

- `evaluate_company(company_data, checklist)`: Evaluates a company against a checklist
- `evaluate_multiple_companies(companies_data, checklist)`: Evaluates multiple companies
- `rank_companies(evaluation_results)`: Ranks companies based on checklist scores

### CompanyRanker

Ranks companies based on fundamental analysis.

- `rank_companies(companies_data, criteria)`: Ranks companies based on criteria
- `filter_companies(companies_data, filters)`: Filters companies based on criteria

### FinancialVisualizer

Creates visualizations for financial data analysis.

- `plot_financial_trend(data, title, ylabel, filename)`: Plots a financial trend
- `plot_financial_comparison(data, metric, title, filename)`: Plots a comparison of a financial metric
- `plot_ratio_heatmap(data, title, filename)`: Plots a heatmap of financial ratios
- `plot_dcf_sensitivity(growth_rates, discount_rates, values, current_price, title, filename)`: Plots a sensitivity analysis
- `plot_checklist_radar(data, title, filename)`: Plots a radar chart for checklist scores
