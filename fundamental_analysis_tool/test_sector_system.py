#!/usr/bin/env python3
"""
Test Sector System

This script demonstrates how the sector classification and saving works
with a sample of companies.
"""

import os
import sys
import json
from datetime import datetime

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.data_loader import ScreenerDataLoader
from models.sector_analyzer import SectorAnalyzer

def test_sector_classification():
    """Test sector classification with sample companies"""
    print("🧪 TESTING SECTOR CLASSIFICATION SYSTEM")
    print("=" * 60)
    
    # Initialize components
    data_loader = ScreenerDataLoader('../screener_data_collector/data')
    sector_analyzer = SectorAnalyzer()
    
    # Test with a diverse sample of companies
    test_tickers = [
        'TCS', 'INFY', 'WIPRO', 'HCLTECH',  # IT Services
        'HDFCBANK', 'ICICIBANK', 'SBIN', 'AXISBANK',  # Banking
        'RELIANCE', 'ONGC', 'IOC', 'BPCL',  # Oil & Gas
        'HINDUNILVR', 'ITC', 'NESTLEIND', 'BRITANNIA',  # FMCG
        'MARUTI', 'TATAMOTORS', 'BAJAJ-AUTO', 'M&M',  # Automotive
        'SUNPHARMA', 'DRREDDY', 'CIPLA', 'LUPIN',  # Pharmaceuticals
        'LT', 'ULTRACEMCO', 'GRASIM', 'ACC',  # Construction/Cement
        'NTPC', 'POWERGRID', 'COALINDIA', 'ADANIPOWER'  # Power/Utilities
    ]
    
    print(f"📊 Testing with {len(test_tickers)} companies across multiple sectors")
    
    # Load company data
    companies_data = {}
    sector_classifications = {}
    
    print("\n🔍 Step 1: Loading company data and classifying sectors...")
    for ticker in test_tickers:
        try:
            company_data = data_loader.load_company_data(ticker)
            if company_data:
                companies_data[ticker] = company_data
                
                # Classify sector
                sector = sector_analyzer.classify_sector(company_data)
                sector_classifications[ticker] = sector
                
                print(f"  {ticker:12s} → {sector}")
            else:
                print(f"  {ticker:12s} → No data available")
        except Exception as e:
            print(f"  {ticker:12s} → Error: {e}")
    
    print(f"\n✅ Successfully loaded and classified {len(companies_data)} companies")
    
    # Discover sectors and relationships
    print("\n🏭 Step 2: Discovering sector relationships and peer networks...")
    sector_discovery = sector_analyzer.discover_all_sectors_and_players(companies_data)
    
    # Display results
    sectors_found = sector_discovery['sectors_found']
    
    print(f"\n📊 SECTOR DISCOVERY RESULTS:")
    print("-" * 60)
    print(f"Total sectors discovered: {len(sectors_found)}")
    
    for sector, companies in sectors_found.items():
        if len(companies) > 0:
            sector_display = sector.replace('_', ' ').title()
            print(f"\n🏭 {sector_display} ({len(companies)} companies):")
            print(f"   Companies: {', '.join(companies)}")
    
    # Save sector mapping
    print(f"\n💾 Step 3: Saving sector mapping...")
    
    # Create output directory
    os.makedirs('output/sector_analysis', exist_ok=True)
    
    # Save with timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f'output/sector_analysis/test_sector_mapping_{timestamp}.json'
    
    # Export the mapping
    saved_file = sector_analyzer.export_sector_mapping(sector_discovery, output_file)
    
    print(f"✅ Sector mapping saved to: {saved_file}")
    
    # Show what's saved in the file
    print(f"\n📄 SAVED FILE STRUCTURE:")
    print("-" * 60)
    
    with open(saved_file, 'r') as f:
        saved_data = json.load(f)
    
    print(f"File contains:")
    print(f"  📅 timestamp: {saved_data.get('timestamp')}")
    print(f"  🏭 sectors: {len(saved_data.get('sectors', {}))}")
    print(f"  📊 sector_statistics: {len(saved_data.get('sector_statistics', {}))}")
    print(f"  🔗 cross_sector_relationships: {len(saved_data.get('cross_sector_relationships', {}))}")
    print(f"  💡 insights: {len(saved_data.get('insights', []))}")
    
    # Show sample sector data
    sectors_data = saved_data.get('sectors', {})
    if sectors_data:
        print(f"\n📋 SAMPLE SECTOR MAPPINGS:")
        print("-" * 60)
        
        for sector, companies in list(sectors_data.items())[:5]:  # Show first 5 sectors
            print(f"  {sector}: {companies}")
    
    # Show insights
    insights = saved_data.get('insights', [])
    if insights:
        print(f"\n💡 SECTOR INSIGHTS:")
        print("-" * 60)
        for insight in insights:
            print(f"  • {insight}")
    
    return saved_file

def demonstrate_sector_usage():
    """Demonstrate how to use saved sector information"""
    print(f"\n🔧 DEMONSTRATING SECTOR USAGE")
    print("=" * 60)
    
    # Check if we have any saved sector mappings
    sector_dir = 'output/sector_analysis'
    if not os.path.exists(sector_dir):
        print("❌ No sector mappings found. Run test first.")
        return
    
    # Find the latest sector mapping file
    mapping_files = [f for f in os.listdir(sector_dir) if f.endswith('.json')]
    if not mapping_files:
        print("❌ No sector mapping files found.")
        return
    
    latest_file = sorted(mapping_files)[-1]
    mapping_path = os.path.join(sector_dir, latest_file)
    
    print(f"📁 Loading sector mapping from: {latest_file}")
    
    # Load the mapping
    with open(mapping_path, 'r') as f:
        sector_mapping = json.load(f)
    
    sectors = sector_mapping.get('sectors', {})
    
    print(f"\n🔍 SECTOR LOOKUP EXAMPLES:")
    print("-" * 60)
    
    # Example 1: Find sector for specific companies
    test_companies = ['TCS', 'HDFCBANK', 'RELIANCE', 'HINDUNILVR']
    
    for ticker in test_companies:
        found_sector = None
        for sector, companies in sectors.items():
            if ticker in companies:
                found_sector = sector
                break
        
        if found_sector:
            print(f"  {ticker:12s} → {found_sector.replace('_', ' ').title()}")
        else:
            print(f"  {ticker:12s} → Not found in mapping")
    
    print(f"\n🏭 SECTOR COMPANIES LOOKUP:")
    print("-" * 60)
    
    # Example 2: Get all companies in specific sectors
    target_sectors = ['banking', 'it_services', 'oil_gas']
    
    for sector in target_sectors:
        if sector in sectors:
            companies = sectors[sector]
            sector_display = sector.replace('_', ' ').title()
            print(f"  {sector_display}: {', '.join(companies)}")
        else:
            print(f"  {sector}: Not found")
    
    print(f"\n📊 SECTOR STATISTICS:")
    print("-" * 60)
    
    total_companies = sum(len(companies) for companies in sectors.values())
    print(f"  Total sectors: {len(sectors)}")
    print(f"  Total companies: {total_companies}")
    print(f"  Average companies per sector: {total_companies/len(sectors):.1f}")
    
    # Show largest sectors
    largest_sectors = sorted(sectors.items(), key=lambda x: len(x[1]), reverse=True)[:5]
    print(f"\n🏆 TOP 5 LARGEST SECTORS:")
    for i, (sector, companies) in enumerate(largest_sectors, 1):
        sector_display = sector.replace('_', ' ').title()
        print(f"  {i}. {sector_display}: {len(companies)} companies")

def main():
    """Main function"""
    print("🧪 SECTOR SYSTEM TEST & DEMONSTRATION")
    print("=" * 80)
    
    try:
        # Test sector classification and saving
        saved_file = test_sector_classification()
        
        # Demonstrate usage
        demonstrate_sector_usage()
        
        print(f"\n" + "=" * 80)
        print("✅ SECTOR SYSTEM TEST COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        
        print(f"\n📁 Files created:")
        print(f"  • {saved_file}")
        
        print(f"\n🔧 How to use sector information:")
        print(f"  1. Load the JSON file to get sector mappings")
        print(f"  2. Use ticker → sector lookup for classification")
        print(f"  3. Use sector → companies lookup for peer analysis")
        print(f"  4. Use sector statistics for benchmarking")
        
        print(f"\n💡 Integration with analysis:")
        print(f"  • Cash flow pre-screening uses sector-specific thresholds")
        print(f"  • Consistency analysis compares within sector peers")
        print(f"  • Qualitative analysis applies sector-specific factors")
        print(f"  • Investment recommendations group by sectors")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
