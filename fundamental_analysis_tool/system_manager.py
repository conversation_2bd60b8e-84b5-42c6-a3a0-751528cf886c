#!/usr/bin/env python3
"""
Investment System Manager

This script provides comprehensive management and monitoring of the investment analysis system.
It includes system status, data health checks, and automated maintenance.

This is for REAL MONEY - system health is critical for investment decisions.
"""

import os
import sys
import json
import argparse
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('system_manager')

class InvestmentSystemManager:
    """
    Comprehensive investment system manager
    """
    
    def __init__(self):
        """
        Initialize system manager
        """
        self.system_status = {
            'timestamp': datetime.now().isoformat(),
            'data_health': {},
            'analysis_health': {},
            'portfolio_health': {},
            'system_recommendations': []
        }
    
    def check_system_status(self):
        """
        Check complete system status
        """
        print("=" * 100)
        print("🔍 INVESTMENT SYSTEM STATUS CHECK")
        print("=" * 100)
        print(f"📊 Status Check Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 100)
        
        # Check data health
        print("\n📊 DATA HEALTH CHECK")
        print("-" * 80)
        data_health = self._check_data_health()
        
        # Check analysis health
        print(f"\n📈 ANALYSIS HEALTH CHECK")
        print("-" * 80)
        analysis_health = self._check_analysis_health()
        
        # Check portfolio health
        print(f"\n🎯 PORTFOLIO HEALTH CHECK")
        print("-" * 80)
        portfolio_health = self._check_portfolio_health()
        
        # Generate system recommendations
        print(f"\n🎯 SYSTEM RECOMMENDATIONS")
        print("-" * 80)
        recommendations = self._generate_system_recommendations()
        
        # Display summary
        self._display_system_summary()
        
        return self.system_status
    
    def _check_data_health(self):
        """
        Check data health and freshness
        """
        data_health = {
            'screener_data': {},
            'market_data': {},
            'data_freshness': {},
            'data_completeness': {}
        }
        
        # Check screener data
        screener_dir = '../screener_data_collector/data'
        if os.path.exists(screener_dir):
            # Count companies
            json_files = [f for f in os.listdir(screener_dir) if f.endswith('.json')]
            companies_count = len(json_files)
            
            # Check data freshness
            if json_files:
                latest_file = max([os.path.join(screener_dir, f) for f in json_files], 
                                key=os.path.getmtime)
                last_modified = datetime.fromtimestamp(os.path.getmtime(latest_file))
                days_old = (datetime.now() - last_modified).days
                
                data_health['screener_data'] = {
                    'companies_count': companies_count,
                    'last_updated': last_modified.isoformat(),
                    'days_old': days_old,
                    'status': 'fresh' if days_old <= 7 else 'stale' if days_old <= 30 else 'very_stale'
                }
                
                print(f"  📊 Screener Data: {companies_count} companies")
                print(f"    Last Updated: {last_modified.strftime('%Y-%m-%d')} ({days_old} days ago)")
                print(f"    Status: {'✅ FRESH' if days_old <= 7 else '⚠️ STALE' if days_old <= 30 else '❌ VERY STALE'}")
            else:
                data_health['screener_data'] = {'status': 'missing', 'companies_count': 0}
                print(f"  ❌ Screener Data: No data found")
        else:
            data_health['screener_data'] = {'status': 'missing', 'companies_count': 0}
            print(f"  ❌ Screener Data: Directory not found")
        
        # Check market data
        market_dir = 'market_data'
        if os.path.exists(market_dir):
            market_files = [f for f in os.listdir(market_dir) if f.endswith('.csv')]
            
            data_health['market_data'] = {
                'files_count': len(market_files),
                'status': 'available' if market_files else 'missing'
            }
            
            print(f"  📈 Market Data: {len(market_files)} files")
            print(f"    Status: {'✅ AVAILABLE' if market_files else '❌ MISSING'}")
        else:
            data_health['market_data'] = {'status': 'missing', 'files_count': 0}
            print(f"  ❌ Market Data: Directory not found")
        
        self.system_status['data_health'] = data_health
        return data_health
    
    def _check_analysis_health(self):
        """
        Check analysis results health
        """
        analysis_health = {
            'fundamental_analysis': {},
            'dcf_analysis': {},
            'final_pipeline': {}
        }
        
        # Check fundamental analysis results
        production_dir = 'output/production_analysis'
        if os.path.exists(production_dir):
            result_files = [f for f in os.listdir(production_dir) 
                          if f.startswith('production_analysis_final_')]
            
            if result_files:
                latest_file = sorted(result_files)[-1]
                file_path = os.path.join(production_dir, latest_file)
                
                try:
                    with open(file_path, 'r') as f:
                        results = json.load(f)
                    
                    # Extract key metrics
                    cashflow_results = results.get('stage_1_cashflow_prescreen', {}).get('results', {})
                    comprehensive_results = results.get('stage_2_comprehensive_analysis', {}).get('results', {})
                    
                    passed_companies = sum(1 for r in cashflow_results.values() 
                                         if r.get('passed_prescreen', False))
                    
                    analysis_health['fundamental_analysis'] = {
                        'latest_file': latest_file,
                        'total_companies': len(cashflow_results),
                        'passed_companies': passed_companies,
                        'pass_rate': (passed_companies / len(cashflow_results) * 100) if cashflow_results else 0,
                        'status': 'healthy'
                    }
                    
                    print(f"  📈 Fundamental Analysis: {len(cashflow_results)} companies analyzed")
                    print(f"    Passed Pre-screening: {passed_companies} ({(passed_companies/len(cashflow_results)*100):.1f}%)")
                    print(f"    Latest Results: {latest_file}")
                    print(f"    Status: ✅ HEALTHY")
                    
                except Exception as e:
                    analysis_health['fundamental_analysis'] = {'status': 'corrupted', 'error': str(e)}
                    print(f"  ❌ Fundamental Analysis: Results corrupted ({e})")
            else:
                analysis_health['fundamental_analysis'] = {'status': 'missing'}
                print(f"  ❌ Fundamental Analysis: No results found")
        else:
            analysis_health['fundamental_analysis'] = {'status': 'missing'}
            print(f"  ❌ Fundamental Analysis: Directory not found")
        
        # Check final pipeline results
        final_dir = 'output/final_pipeline'
        if os.path.exists(final_dir):
            final_files = [f for f in os.listdir(final_dir) 
                          if f.startswith('final_investment_pipeline_')]
            
            if final_files:
                latest_final = sorted(final_files)[-1]
                file_path = os.path.join(final_dir, latest_final)
                
                try:
                    with open(file_path, 'r') as f:
                        final_results = json.load(f)
                    
                    summary_stats = final_results.get('stage_5_final_recommendations', {}).get('summary_statistics', {})
                    
                    analysis_health['final_pipeline'] = {
                        'latest_file': latest_final,
                        'total_analyzed': summary_stats.get('total_analyzed', 0),
                        'dcf_qualified': summary_stats.get('dcf_qualified', 0),
                        'buy_count': summary_stats.get('buy_count', 0),
                        'portfolio_companies': summary_stats.get('portfolio_companies', 0),
                        'status': 'healthy'
                    }
                    
                    print(f"  🎯 Final Pipeline: {summary_stats.get('total_analyzed', 0)} companies")
                    print(f"    DCF Qualified: {summary_stats.get('dcf_qualified', 0)}")
                    print(f"    BUY Recommendations: {summary_stats.get('buy_count', 0)}")
                    print(f"    Portfolio Companies: {summary_stats.get('portfolio_companies', 0)}")
                    print(f"    Status: ✅ HEALTHY")
                    
                except Exception as e:
                    analysis_health['final_pipeline'] = {'status': 'corrupted', 'error': str(e)}
                    print(f"  ❌ Final Pipeline: Results corrupted ({e})")
            else:
                analysis_health['final_pipeline'] = {'status': 'missing'}
                print(f"  ❌ Final Pipeline: No results found")
        else:
            analysis_health['final_pipeline'] = {'status': 'missing'}
            print(f"  ❌ Final Pipeline: Directory not found")
        
        self.system_status['analysis_health'] = analysis_health
        return analysis_health
    
    def _check_portfolio_health(self):
        """
        Check portfolio optimization health
        """
        portfolio_health = {
            'portfolio_integration': {},
            'optimization_status': {}
        }
        
        # Check portfolio integration results
        portfolio_dir = 'output/portfolio_integration'
        if os.path.exists(portfolio_dir):
            portfolio_files = [f for f in os.listdir(portfolio_dir) 
                             if f.startswith('final_portfolio_')]
            
            if portfolio_files:
                latest_portfolio = sorted(portfolio_files)[-1]
                file_path = os.path.join(portfolio_dir, latest_portfolio)
                
                try:
                    portfolio_df = pd.read_csv(file_path)
                    
                    # Calculate portfolio metrics
                    total_weight = portfolio_df['weight'].sum()
                    avg_score = portfolio_df['fundamental_score'].mean()
                    
                    portfolio_health['portfolio_integration'] = {
                        'latest_file': latest_portfolio,
                        'portfolio_assets': len(portfolio_df),
                        'total_weight': total_weight,
                        'avg_fundamental_score': avg_score,
                        'status': 'healthy' if abs(total_weight - 100) < 1 else 'warning'
                    }
                    
                    print(f"  🎯 Portfolio Integration: {len(portfolio_df)} assets")
                    print(f"    Total Weight: {total_weight:.1f}%")
                    print(f"    Avg Fundamental Score: {avg_score:.1f}")
                    print(f"    Latest Portfolio: {latest_portfolio}")
                    print(f"    Status: {'✅ HEALTHY' if abs(total_weight - 100) < 1 else '⚠️ WARNING'}")
                    
                except Exception as e:
                    portfolio_health['portfolio_integration'] = {'status': 'corrupted', 'error': str(e)}
                    print(f"  ❌ Portfolio Integration: Results corrupted ({e})")
            else:
                portfolio_health['portfolio_integration'] = {'status': 'missing'}
                print(f"  ❌ Portfolio Integration: No results found")
        else:
            portfolio_health['portfolio_integration'] = {'status': 'missing'}
            print(f"  ❌ Portfolio Integration: Directory not found")
        
        self.system_status['portfolio_health'] = portfolio_health
        return portfolio_health
    
    def _generate_system_recommendations(self):
        """
        Generate system maintenance recommendations
        """
        recommendations = []
        
        # Data recommendations
        data_health = self.system_status.get('data_health', {})
        screener_data = data_health.get('screener_data', {})
        
        if screener_data.get('status') == 'missing':
            recommendations.append({
                'type': 'critical',
                'category': 'data',
                'message': 'Screener data missing - run data collection immediately',
                'action': 'cd ../screener_data_collector && python screener_data_collector.py'
            })
        elif screener_data.get('days_old', 0) > 7:
            recommendations.append({
                'type': 'warning',
                'category': 'data',
                'message': f'Screener data is {screener_data.get("days_old", 0)} days old - consider updating',
                'action': 'python monthly_update_pipeline.py'
            })
        
        # Analysis recommendations
        analysis_health = self.system_status.get('analysis_health', {})
        
        if analysis_health.get('fundamental_analysis', {}).get('status') == 'missing':
            recommendations.append({
                'type': 'critical',
                'category': 'analysis',
                'message': 'Fundamental analysis results missing - run analysis pipeline',
                'action': 'python production_analysis_pipeline.py'
            })
        
        if analysis_health.get('final_pipeline', {}).get('status') == 'missing':
            recommendations.append({
                'type': 'critical',
                'category': 'analysis',
                'message': 'Final pipeline results missing - run final pipeline',
                'action': 'python final_investment_pipeline.py'
            })
        
        # Portfolio recommendations
        portfolio_health = self.system_status.get('portfolio_health', {})
        
        if portfolio_health.get('portfolio_integration', {}).get('status') == 'missing':
            recommendations.append({
                'type': 'warning',
                'category': 'portfolio',
                'message': 'Portfolio integration missing - run portfolio optimization',
                'action': 'python portfolio_integration.py'
            })
        
        # General maintenance recommendations
        recommendations.append({
            'type': 'info',
            'category': 'maintenance',
            'message': 'Schedule monthly updates for automated system maintenance',
            'action': 'Set up cron job for monthly_update_pipeline.py'
        })
        
        # Display recommendations
        for rec in recommendations:
            type_icon = {'critical': '🔴', 'warning': '⚠️', 'info': 'ℹ️'}.get(rec['type'], 'ℹ️')
            print(f"  {type_icon} {rec['message']}")
            print(f"    Action: {rec['action']}")
        
        self.system_status['system_recommendations'] = recommendations
        return recommendations
    
    def _display_system_summary(self):
        """
        Display system summary
        """
        print(f"\n" + "=" * 100)
        print("📊 SYSTEM HEALTH SUMMARY")
        print("=" * 100)
        
        # Overall health score
        health_scores = []
        
        # Data health score
        data_health = self.system_status.get('data_health', {})
        screener_status = data_health.get('screener_data', {}).get('status', 'missing')
        data_score = {'fresh': 100, 'stale': 70, 'very_stale': 30, 'missing': 0}.get(screener_status, 0)
        health_scores.append(data_score)
        
        # Analysis health score
        analysis_health = self.system_status.get('analysis_health', {})
        fundamental_status = analysis_health.get('fundamental_analysis', {}).get('status', 'missing')
        final_status = analysis_health.get('final_pipeline', {}).get('status', 'missing')
        analysis_score = (
            ({'healthy': 100, 'corrupted': 30, 'missing': 0}.get(fundamental_status, 0) +
             {'healthy': 100, 'corrupted': 30, 'missing': 0}.get(final_status, 0)) / 2
        )
        health_scores.append(analysis_score)
        
        # Portfolio health score
        portfolio_health = self.system_status.get('portfolio_health', {})
        portfolio_status = portfolio_health.get('portfolio_integration', {}).get('status', 'missing')
        portfolio_score = {'healthy': 100, 'warning': 70, 'corrupted': 30, 'missing': 0}.get(portfolio_status, 0)
        health_scores.append(portfolio_score)
        
        # Overall health
        overall_health = sum(health_scores) / len(health_scores) if health_scores else 0
        
        print(f"🏥 OVERALL SYSTEM HEALTH: {overall_health:.0f}/100")
        
        if overall_health >= 90:
            print("   Status: ✅ EXCELLENT - System ready for real money decisions")
        elif overall_health >= 70:
            print("   Status: ⚠️  GOOD - Minor issues, system mostly operational")
        elif overall_health >= 50:
            print("   Status: ⚠️  WARNING - Significant issues, review before investment decisions")
        else:
            print("   Status: ❌ CRITICAL - Major issues, do not use for investment decisions")
        
        print(f"\n📊 Component Health:")
        print(f"   Data Health: {data_score:.0f}/100")
        print(f"   Analysis Health: {analysis_score:.0f}/100")
        print(f"   Portfolio Health: {portfolio_score:.0f}/100")
        
        # Recommendations count
        recommendations = self.system_status.get('system_recommendations', [])
        critical_count = sum(1 for r in recommendations if r.get('type') == 'critical')
        warning_count = sum(1 for r in recommendations if r.get('type') == 'warning')
        
        print(f"\n🎯 Action Items:")
        print(f"   Critical Issues: {critical_count}")
        print(f"   Warnings: {warning_count}")
        print(f"   Total Recommendations: {len(recommendations)}")
        
        print("=" * 100)

def main():
    """
    Main function for system manager
    """
    parser = argparse.ArgumentParser(description='Investment System Manager')
    parser.add_argument('--action', choices=['status', 'health', 'update'], 
                       default='status', help='Action to perform')
    
    args = parser.parse_args()
    
    manager = InvestmentSystemManager()
    
    if args.action == 'status':
        manager.check_system_status()
    elif args.action == 'health':
        manager.check_system_status()
    elif args.action == 'update':
        print("Running monthly update...")
        os.system('python monthly_update_pipeline.py')

if __name__ == "__main__":
    main()
