#!/usr/bin/env python3
"""
Warren Buffett Style DCF Analysis

This module implements Warren Buffett style intrinsic value calculation using:
1. REAL DCF modules from dcf/ folder
2. Enhanced FCF calculations from comprehensive cash flow analysis
3. Conservative assumptions and margin of safety focus
4. Batch analysis capability for all qualified companies

This addresses your request: "make it robust first maybe we can apply on the
whole company list to get an essence of intrinsic the valuation you know
the Warren buffet style"
"""

import os
import sys
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import modules
from utils.data_loader import ScreenerDataLoader
from real_dcf_integration import RealDCFIntegration
from models.cashflow_prescreener import CashFlowPreScreener

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('warren_buffett_dcf')

class WarrenBuffettDCFAnalysis:
    """
    Warren Buffett style DCF analysis with conservative assumptions
    """

    def __init__(self):
        """
        Initialize Warren Buffett style DCF analysis
        """
        self.data_loader = ScreenerDataLoader('../screener_data_collector/data')
        self.real_dcf = RealDCFIntegration()
        self.prescreener = CashFlowPreScreener()

        # Warren Buffett style criteria
        self.buffett_criteria = {
            'min_roe': 15.0,  # Minimum 15% ROE
            'min_roce': 15.0,  # Minimum 15% ROCE
            'max_debt_to_equity': 0.5,  # Maximum 50% debt to equity
            'min_operating_margin': 10.0,  # Minimum 10% operating margin
            'min_cash_flow_years': 7,  # Minimum 7 years of positive cash flow
            'min_fcf_margin': 5.0,  # Minimum 5% FCF margin
            'required_margin_of_safety': 25.0,  # Minimum 25% margin of safety
            'max_pe_ratio': 25.0,  # Maximum P/E ratio of 25
            'min_revenue_growth': 5.0,  # Minimum 5% revenue growth
            'quality_score_threshold': 70.0  # Minimum quality score
        }

        # Results storage
        self.analysis_results = {}

    def analyze_company_buffett_style(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform Warren Buffett style analysis on a company

        Parameters:
        -----------
        ticker : str
            Company ticker
        company_data : Dict[str, Any]
            Company financial data

        Returns:
        --------
        Warren Buffett style analysis results
        """
        try:
            logger.info(f"Running Warren Buffett style analysis for {ticker}")

            # Step 1: Quality Assessment (Buffett's first filter)
            quality_assessment = self._assess_business_quality(ticker, company_data)

            # Step 2: Cash Flow Analysis (Buffett's cash generation focus)
            cash_flow_analysis = self._analyze_cash_generation(ticker, company_data)

            # Step 3: Financial Strength (Buffett's balance sheet focus)
            financial_strength = self._assess_financial_strength(ticker, company_data)

            # Step 4: Management Efficiency (Buffett's ROE/ROCE focus)
            management_efficiency = self._assess_management_efficiency(ticker, company_data)

            # Step 5: Intrinsic Value Calculation (Conservative DCF)
            intrinsic_valuation = self._calculate_conservative_intrinsic_value(ticker, company_data)

            # Step 6: Margin of Safety Analysis
            margin_of_safety_analysis = self._analyze_margin_of_safety(ticker, intrinsic_valuation, company_data)

            # Step 7: Overall Buffett Score
            buffett_score = self._calculate_buffett_score(
                quality_assessment, cash_flow_analysis, financial_strength,
                management_efficiency, margin_of_safety_analysis
            )

            # Step 8: Investment Recommendation
            recommendation = self._generate_buffett_recommendation(buffett_score, margin_of_safety_analysis)

            return {
                'ticker': ticker,
                'analysis_method': 'warren_buffett_style',
                'quality_assessment': quality_assessment,
                'cash_flow_analysis': cash_flow_analysis,
                'financial_strength': financial_strength,
                'management_efficiency': management_efficiency,
                'intrinsic_valuation': intrinsic_valuation,
                'margin_of_safety_analysis': margin_of_safety_analysis,
                'buffett_score': buffett_score,
                'recommendation': recommendation,
                'analysis_timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in Warren Buffett analysis for {ticker}: {e}")
            return {
                'ticker': ticker,
                'error': str(e),
                'analysis_method': 'warren_buffett_style_failed',
                'buffett_score': 0,
                'recommendation': 'AVOID'
            }

    def _assess_business_quality(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Assess business quality using Buffett's criteria
        """
        try:
            overview = company_data.get('overview', {})
            ratios = company_data.get('ratios', {})

            # Extract key quality metrics
            market_cap = self._safe_float(overview.get('market_cap', '0'))
            pe_ratio = self._safe_float(overview.get('pe_ratio', '0'))

            # Business size (Buffett prefers large, stable businesses)
            size_score = 0
            if market_cap > 10000:  # > 10,000 Cr
                size_score = 100
            elif market_cap > 5000:  # > 5,000 Cr
                size_score = 75
            elif market_cap > 1000:  # > 1,000 Cr
                size_score = 50
            else:
                size_score = 25

            # Valuation reasonableness
            valuation_score = 0
            if 0 < pe_ratio <= 15:
                valuation_score = 100
            elif 15 < pe_ratio <= 20:
                valuation_score = 75
            elif 20 < pe_ratio <= 25:
                valuation_score = 50
            elif 25 < pe_ratio <= 30:
                valuation_score = 25
            else:
                valuation_score = 0

            # Overall quality score
            quality_score = (size_score + valuation_score) / 2

            return {
                'market_cap': market_cap,
                'pe_ratio': pe_ratio,
                'size_score': size_score,
                'valuation_score': valuation_score,
                'quality_score': quality_score,
                'quality_grade': self._get_grade(quality_score)
            }

        except Exception as e:
            logger.error(f"Error assessing business quality for {ticker}: {e}")
            return {'error': str(e), 'quality_score': 0}

    def _analyze_cash_generation(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze cash generation capability (Buffett's key focus)
        """
        try:
            # Use our comprehensive cash flow analysis
            cf_result = self.prescreener.prescreen_company(company_data)

            if not cf_result or 'analysis' not in cf_result:
                return {'error': 'No cash flow analysis available', 'cash_score': 0}

            analysis = cf_result['analysis']

            # Operating Cash Flow Analysis
            ocf_analysis = analysis.get('operating_cash_flow', {})
            ocf_score = 0
            if 'error' not in ocf_analysis:
                positive_percentage = ocf_analysis.get('positive_percentage', 0)
                growth_consistency = ocf_analysis.get('growth_consistency', 0)

                # Buffett wants consistent cash generation
                if positive_percentage >= 90:
                    ocf_score = 100
                elif positive_percentage >= 80:
                    ocf_score = 80
                elif positive_percentage >= 70:
                    ocf_score = 60
                else:
                    ocf_score = 40

                # Bonus for growth consistency
                if growth_consistency >= 70:
                    ocf_score = min(100, ocf_score + 20)

            # Free Cash Flow Analysis
            fcf_analysis = analysis.get('free_cash_flow', {})
            fcf_score = 0
            if 'error' not in fcf_analysis:
                fcf_positive_percentage = fcf_analysis.get('positive_percentage', 0)

                # Buffett wants positive FCF generation
                if fcf_positive_percentage >= 80:
                    fcf_score = 100
                elif fcf_positive_percentage >= 70:
                    fcf_score = 80
                elif fcf_positive_percentage >= 60:
                    fcf_score = 60
                else:
                    fcf_score = 40

            # Overall cash generation score
            cash_score = (ocf_score + fcf_score) / 2

            return {
                'ocf_analysis': ocf_analysis,
                'fcf_analysis': fcf_analysis,
                'ocf_score': ocf_score,
                'fcf_score': fcf_score,
                'cash_score': cash_score,
                'cash_grade': self._get_grade(cash_score)
            }

        except Exception as e:
            logger.error(f"Error analyzing cash generation for {ticker}: {e}")
            return {'error': str(e), 'cash_score': 0}

    def _assess_financial_strength(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Assess financial strength (Buffett's balance sheet focus)
        """
        try:
            # Get latest balance sheet data
            balance_sheet = company_data.get('balance_sheet', {})

            if not balance_sheet:
                return {'error': 'No balance sheet data', 'strength_score': 0}

            # Find latest year
            bs_clean = {k: v for k, v in balance_sheet.items() if k not in ['units', 'notes']}
            years = [year for year in bs_clean.keys() if year != 'TTM']

            if not years:
                return {'error': 'No balance sheet years available', 'strength_score': 0}

            years.sort(reverse=True)
            latest_year = years[0]
            latest_bs = bs_clean[latest_year]

            if not isinstance(latest_bs, dict):
                return {'error': 'Invalid balance sheet data', 'strength_score': 0}

            # Extract key financial strength metrics
            total_debt = self._safe_float(latest_bs.get('total_debt', '0'))
            total_equity = self._safe_float(latest_bs.get('shareholders_equity', '0'))
            cash = self._safe_float(latest_bs.get('cash', '0'))

            # Calculate debt-to-equity ratio
            debt_to_equity = 0
            if total_equity > 0:
                debt_to_equity = total_debt / total_equity

            # Score debt levels (Buffett prefers low debt)
            debt_score = 0
            if debt_to_equity <= 0.2:
                debt_score = 100
            elif debt_to_equity <= 0.3:
                debt_score = 80
            elif debt_to_equity <= 0.5:
                debt_score = 60
            elif debt_to_equity <= 0.7:
                debt_score = 40
            else:
                debt_score = 20

            # Cash position score
            cash_score = 0
            if cash > total_debt:
                cash_score = 100  # Net cash positive
            elif cash > total_debt * 0.5:
                cash_score = 80
            elif cash > total_debt * 0.3:
                cash_score = 60
            else:
                cash_score = 40

            # Overall financial strength
            strength_score = (debt_score + cash_score) / 2

            return {
                'total_debt': total_debt,
                'total_equity': total_equity,
                'cash': cash,
                'debt_to_equity': debt_to_equity,
                'debt_score': debt_score,
                'cash_score': cash_score,
                'strength_score': strength_score,
                'strength_grade': self._get_grade(strength_score)
            }

        except Exception as e:
            logger.error(f"Error assessing financial strength for {ticker}: {e}")
            return {'error': str(e), 'strength_score': 0}

    def _assess_management_efficiency(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Assess management efficiency (Buffett's ROE/ROCE focus)
        """
        try:
            overview = company_data.get('overview', {})

            # Extract efficiency metrics
            roe = self._safe_float(overview.get('roe', '0'))
            roce = self._safe_float(overview.get('roce', '0'))
            operating_margin = self._safe_float(overview.get('operating_margin', '0'))

            # ROE scoring (Buffett wants >15%)
            roe_score = 0
            if roe >= 20:
                roe_score = 100
            elif roe >= 15:
                roe_score = 80
            elif roe >= 12:
                roe_score = 60
            elif roe >= 10:
                roe_score = 40
            else:
                roe_score = 20

            # ROCE scoring
            roce_score = 0
            if roce >= 20:
                roce_score = 100
            elif roce >= 15:
                roce_score = 80
            elif roce >= 12:
                roce_score = 60
            elif roce >= 10:
                roce_score = 40
            else:
                roce_score = 20

            # Operating margin scoring
            margin_score = 0
            if operating_margin >= 20:
                margin_score = 100
            elif operating_margin >= 15:
                margin_score = 80
            elif operating_margin >= 10:
                margin_score = 60
            elif operating_margin >= 5:
                margin_score = 40
            else:
                margin_score = 20

            # Overall efficiency score
            efficiency_score = (roe_score + roce_score + margin_score) / 3

            return {
                'roe': roe,
                'roce': roce,
                'operating_margin': operating_margin,
                'roe_score': roe_score,
                'roce_score': roce_score,
                'margin_score': margin_score,
                'efficiency_score': efficiency_score,
                'efficiency_grade': self._get_grade(efficiency_score)
            }

        except Exception as e:
            logger.error(f"Error assessing management efficiency for {ticker}: {e}")
            return {'error': str(e), 'efficiency_score': 0}

    def _calculate_conservative_intrinsic_value(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate conservative intrinsic value using REAL DCF modules
        """
        try:
            if not self.real_dcf.dcf_available:
                return {'error': 'DCF modules not available', 'intrinsic_value': 0}

            # Run REAL DCF analysis with conservative assumptions
            dcf_result = self.real_dcf.analyze_company_real_dcf(ticker, company_data)

            if 'error' in dcf_result:
                return {'error': dcf_result['error'], 'intrinsic_value': 0}

            # Extract intrinsic value
            intrinsic_value = dcf_result.get('intrinsic_value_per_share', 0)
            enterprise_value = dcf_result.get('enterprise_value', 0)
            equity_value = dcf_result.get('equity_value', 0)

            # Apply conservative discount (Buffett's margin of safety)
            conservative_intrinsic_value = intrinsic_value * 0.8  # 20% discount for conservatism

            return {
                'intrinsic_value': intrinsic_value,
                'conservative_intrinsic_value': conservative_intrinsic_value,
                'enterprise_value': enterprise_value,
                'equity_value': equity_value,
                'dcf_details': dcf_result
            }

        except Exception as e:
            logger.error(f"Error calculating intrinsic value for {ticker}: {e}")
            return {'error': str(e), 'intrinsic_value': 0}

    def _analyze_margin_of_safety(self, ticker: str, intrinsic_valuation: Dict[str, Any],
                                 company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze margin of safety (Buffett's key investment criterion)
        """
        try:
            overview = company_data.get('overview', {})
            current_price = self._safe_float(overview.get('current_price', '0'))

            intrinsic_value = intrinsic_valuation.get('intrinsic_value', 0)
            conservative_intrinsic_value = intrinsic_valuation.get('conservative_intrinsic_value', 0)

            if current_price <= 0 or intrinsic_value <= 0:
                return {'error': 'Invalid price or intrinsic value', 'margin_of_safety': 0}

            # Calculate margin of safety
            margin_of_safety = ((intrinsic_value - current_price) / current_price) * 100
            conservative_margin_of_safety = ((conservative_intrinsic_value - current_price) / current_price) * 100

            # Buffett's margin of safety assessment
            safety_grade = "F"
            if conservative_margin_of_safety >= 50:
                safety_grade = "A+"
            elif conservative_margin_of_safety >= 30:
                safety_grade = "A"
            elif conservative_margin_of_safety >= 20:
                safety_grade = "B"
            elif conservative_margin_of_safety >= 10:
                safety_grade = "C"
            elif conservative_margin_of_safety >= 0:
                safety_grade = "D"

            # Investment signal
            if conservative_margin_of_safety >= 25:
                investment_signal = "STRONG_BUY"
            elif conservative_margin_of_safety >= 15:
                investment_signal = "BUY"
            elif conservative_margin_of_safety >= 5:
                investment_signal = "HOLD"
            else:
                investment_signal = "AVOID"

            return {
                'current_price': current_price,
                'intrinsic_value': intrinsic_value,
                'conservative_intrinsic_value': conservative_intrinsic_value,
                'margin_of_safety': margin_of_safety,
                'conservative_margin_of_safety': conservative_margin_of_safety,
                'safety_grade': safety_grade,
                'investment_signal': investment_signal
            }

        except Exception as e:
            logger.error(f"Error analyzing margin of safety for {ticker}: {e}")
            return {'error': str(e), 'margin_of_safety': 0}

    def _calculate_buffett_score(self, quality_assessment: Dict[str, Any],
                               cash_flow_analysis: Dict[str, Any],
                               financial_strength: Dict[str, Any],
                               management_efficiency: Dict[str, Any],
                               margin_of_safety_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate overall Warren Buffett score
        """
        try:
            # Extract individual scores
            quality_score = quality_assessment.get('quality_score', 0)
            cash_score = cash_flow_analysis.get('cash_score', 0)
            strength_score = financial_strength.get('strength_score', 0)
            efficiency_score = management_efficiency.get('efficiency_score', 0)

            # Margin of safety bonus/penalty
            conservative_margin = margin_of_safety_analysis.get('conservative_margin_of_safety', 0)
            safety_bonus = 0
            if conservative_margin >= 30:
                safety_bonus = 20
            elif conservative_margin >= 20:
                safety_bonus = 15
            elif conservative_margin >= 10:
                safety_bonus = 10
            elif conservative_margin < 0:
                safety_bonus = -20

            # Weighted Buffett score
            buffett_score = (
                quality_score * 0.2 +      # 20% weight
                cash_score * 0.3 +         # 30% weight (Buffett's focus)
                strength_score * 0.2 +     # 20% weight
                efficiency_score * 0.3     # 30% weight (Buffett's focus)
            ) + safety_bonus

            # Cap at 100
            buffett_score = min(100, max(0, buffett_score))

            # Overall grade
            buffett_grade = self._get_grade(buffett_score)

            return {
                'quality_score': quality_score,
                'cash_score': cash_score,
                'strength_score': strength_score,
                'efficiency_score': efficiency_score,
                'safety_bonus': safety_bonus,
                'buffett_score': buffett_score,
                'buffett_grade': buffett_grade
            }

        except Exception as e:
            logger.error(f"Error calculating Buffett score: {e}")
            return {'buffett_score': 0, 'buffett_grade': 'F'}

    def _generate_buffett_recommendation(self, buffett_score: Dict[str, Any],
                                       margin_of_safety_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate Warren Buffett style investment recommendation
        """
        try:
            score = buffett_score.get('buffett_score', 0)
            conservative_margin = margin_of_safety_analysis.get('conservative_margin_of_safety', 0)
            investment_signal = margin_of_safety_analysis.get('investment_signal', 'AVOID')

            # Buffett's criteria
            if score >= 80 and conservative_margin >= 25:
                recommendation = "STRONG_BUY"
                reason = "Excellent business with significant margin of safety"
            elif score >= 70 and conservative_margin >= 15:
                recommendation = "BUY"
                reason = "Good business with adequate margin of safety"
            elif score >= 60 and conservative_margin >= 5:
                recommendation = "HOLD"
                reason = "Decent business but limited margin of safety"
            elif score >= 50:
                recommendation = "WATCH"
                reason = "Average business, wait for better price"
            else:
                recommendation = "AVOID"
                reason = "Poor business fundamentals"

            return {
                'recommendation': recommendation,
                'reason': reason,
                'investment_signal': investment_signal,
                'buffett_approved': score >= 70 and conservative_margin >= 15
            }

        except Exception as e:
            logger.error(f"Error generating recommendation: {e}")
            return {'recommendation': 'AVOID', 'reason': 'Analysis error'}

    def _safe_float(self, value: Any) -> float:
        """Safely convert value to float, handling Indian number formats"""
        try:
            if value is None:
                return 0.0

            if isinstance(value, (int, float)):
                return float(value)

            if isinstance(value, str):
                # Remove common Indian number formatting
                value = value.replace(',', '').replace('₹', '').replace('%', '').strip()

                # Handle empty strings and special values
                if not value or value == '-' or value == 'N/A' or value == '':
                    return 0.0

                return float(value)

            return float(value) if value else 0.0

        except (ValueError, TypeError):
            return 0.0

    def _get_grade(self, score: float) -> str:
        """Convert score to letter grade"""
        if score >= 90:
            return "A+"
        elif score >= 80:
            return "A"
        elif score >= 70:
            return "B"
        elif score >= 60:
            return "C"
        elif score >= 50:
            return "D"
        else:
            return "F"

def test_warren_buffett_analysis():
    """
    Test Warren Buffett style analysis
    """
    print("🎯 WARREN BUFFETT STYLE DCF ANALYSIS")
    print("=" * 80)

    analyzer = WarrenBuffettDCFAnalysis()

    # Test with sample companies
    test_companies = ['TCS', 'HDFCBANK', 'RELIANCE']

    for ticker in test_companies:
        print(f"\n📊 Warren Buffett Analysis for {ticker}")
        print("-" * 60)

        company_data = analyzer.data_loader.load_company_data(ticker)
        if company_data:
            result = analyzer.analyze_company_buffett_style(ticker, company_data)

            if 'error' not in result:
                # Display key results
                buffett_score = result.get('buffett_score', {})
                margin_analysis = result.get('margin_of_safety_analysis', {})
                recommendation = result.get('recommendation', {})

                print(f"  Buffett Score: {buffett_score.get('buffett_score', 0):.1f}/100 ({buffett_score.get('buffett_grade', 'F')})")
                print(f"  Intrinsic Value: ₹{margin_analysis.get('intrinsic_value', 0):.2f}")
                print(f"  Conservative Value: ₹{margin_analysis.get('conservative_intrinsic_value', 0):.2f}")
                print(f"  Current Price: ₹{margin_analysis.get('current_price', 0):.2f}")
                print(f"  Margin of Safety: {margin_analysis.get('conservative_margin_of_safety', 0):.1f}%")
                print(f"  Recommendation: {recommendation.get('recommendation', 'Unknown')}")
                print(f"  Buffett Approved: {'✅ YES' if recommendation.get('buffett_approved', False) else '❌ NO'}")
            else:
                print(f"  ❌ Error: {result['error']}")
        else:
            print(f"  ❌ No data available")

if __name__ == "__main__":
    test_warren_buffett_analysis()
