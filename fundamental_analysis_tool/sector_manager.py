#!/usr/bin/env python3
"""
Sector Management System

This script manages sector classifications, saves/loads sector mappings,
and provides utilities for sector-based analysis.
"""

import os
import sys
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.data_loader import ScreenerDataLoader
from models.sector_analyzer import SectorAnalyzer

class SectorManager:
    """
    Manages sector classifications and mappings
    """
    
    def __init__(self, data_path: str = '../screener_data_collector/data'):
        """
        Initialize sector manager
        """
        self.data_loader = ScreenerDataLoader(data_path)
        self.sector_analyzer = SectorAnalyzer()
        self.sector_mapping_file = 'output/sector_analysis/latest_sector_mapping.json'
        self.sector_mapping = None
        
        # Create output directory
        os.makedirs('output/sector_analysis', exist_ok=True)
    
    def discover_and_save_sectors(self, force_refresh: bool = False) -> str:
        """
        Discover all sectors from the complete dataset and save mapping
        
        Parameters:
        -----------
        force_refresh : bool
            Force rediscovery even if mapping exists
            
        Returns:
        --------
        Path to saved sector mapping file
        """
        print("🔍 Discovering sectors from all companies...")
        
        # Check if we already have a recent mapping
        if not force_refresh and os.path.exists(self.sector_mapping_file):
            print(f"✅ Using existing sector mapping: {self.sector_mapping_file}")
            return self.sector_mapping_file
        
        # Load all company data (this might take a while)
        all_tickers = self.data_loader.get_all_tickers()
        print(f"📊 Loading data for {len(all_tickers)} companies...")
        
        companies_data = {}
        loaded_count = 0
        
        for i, ticker in enumerate(all_tickers):
            if i % 500 == 0:
                print(f"  Progress: {i}/{len(all_tickers)} ({(i/len(all_tickers)*100):.1f}%)")
            
            try:
                company_data = self.data_loader.load_company_data(ticker)
                if company_data:
                    companies_data[ticker] = company_data
                    loaded_count += 1
            except Exception as e:
                continue
        
        print(f"✅ Loaded data for {loaded_count} companies")
        
        # Discover sectors
        print("🏭 Analyzing sectors and peer networks...")
        sector_discovery = self.sector_analyzer.discover_all_sectors_and_players(companies_data)
        
        # Save with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        timestamped_file = f'output/sector_analysis/sector_mapping_{timestamp}.json'
        
        # Export timestamped version
        self.sector_analyzer.export_sector_mapping(sector_discovery, timestamped_file)
        
        # Also save as latest
        self.sector_analyzer.export_sector_mapping(sector_discovery, self.sector_mapping_file)
        
        print(f"✅ Sector mapping saved to:")
        print(f"  📁 {timestamped_file}")
        print(f"  📁 {self.sector_mapping_file}")
        
        # Print summary
        sectors_found = sector_discovery['sectors_found']
        print(f"\n📊 Sector Discovery Summary:")
        print(f"  Total sectors: {len(sectors_found)}")
        print(f"  Total companies classified: {sum(len(companies) for companies in sectors_found.values())}")
        
        # Show top sectors
        largest_sectors = sorted(sectors_found.items(), key=lambda x: len(x[1]), reverse=True)[:10]
        print(f"\n🏆 Top 10 Sectors by Company Count:")
        for i, (sector, companies) in enumerate(largest_sectors, 1):
            print(f"  {i:2d}. {sector.replace('_', ' ').title():20s}: {len(companies):3d} companies")
        
        return timestamped_file
    
    def load_sector_mapping(self, mapping_file: str = None) -> Dict[str, Any]:
        """
        Load sector mapping from file
        
        Parameters:
        -----------
        mapping_file : str, optional
            Path to mapping file. If None, uses latest.
            
        Returns:
        --------
        Sector mapping dictionary
        """
        if mapping_file is None:
            mapping_file = self.sector_mapping_file
        
        if not os.path.exists(mapping_file):
            print(f"❌ Sector mapping file not found: {mapping_file}")
            print("💡 Run: python sector_manager.py discover")
            return {}
        
        try:
            with open(mapping_file, 'r') as f:
                self.sector_mapping = json.load(f)
            
            print(f"✅ Loaded sector mapping from: {mapping_file}")
            return self.sector_mapping
            
        except Exception as e:
            print(f"❌ Error loading sector mapping: {e}")
            return {}
    
    def get_sector_for_ticker(self, ticker: str) -> str:
        """
        Get sector for a specific ticker
        
        Parameters:
        -----------
        ticker : str
            Company ticker
            
        Returns:
        --------
        Sector name or 'unknown'
        """
        if self.sector_mapping is None:
            self.load_sector_mapping()
        
        if not self.sector_mapping:
            return 'unknown'
        
        sectors = self.sector_mapping.get('sectors', {})
        
        for sector, companies in sectors.items():
            if ticker in companies:
                return sector
        
        return 'unknown'
    
    def get_companies_in_sector(self, sector: str) -> List[str]:
        """
        Get all companies in a specific sector
        
        Parameters:
        -----------
        sector : str
            Sector name
            
        Returns:
        --------
        List of company tickers
        """
        if self.sector_mapping is None:
            self.load_sector_mapping()
        
        if not self.sector_mapping:
            return []
        
        sectors = self.sector_mapping.get('sectors', {})
        return sectors.get(sector, [])
    
    def get_all_sectors(self) -> Dict[str, List[str]]:
        """
        Get all sectors and their companies
        
        Returns:
        --------
        Dictionary mapping sector names to company lists
        """
        if self.sector_mapping is None:
            self.load_sector_mapping()
        
        if not self.sector_mapping:
            return {}
        
        return self.sector_mapping.get('sectors', {})
    
    def export_sector_csv(self, output_file: str = None) -> str:
        """
        Export sector mapping to CSV format
        
        Parameters:
        -----------
        output_file : str, optional
            Output CSV file path
            
        Returns:
        --------
        Path to exported CSV file
        """
        if self.sector_mapping is None:
            self.load_sector_mapping()
        
        if not self.sector_mapping:
            print("❌ No sector mapping available")
            return ""
        
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'output/sector_analysis/sector_mapping_{timestamp}.csv'
        
        # Create DataFrame
        data = []
        sectors = self.sector_mapping.get('sectors', {})
        
        for sector, companies in sectors.items():
            for company in companies:
                data.append({
                    'ticker': company,
                    'sector': sector,
                    'sector_display': sector.replace('_', ' ').title()
                })
        
        df = pd.DataFrame(data)
        df = df.sort_values(['sector', 'ticker'])
        
        # Save CSV
        df.to_csv(output_file, index=False)
        
        print(f"✅ Sector mapping exported to CSV: {output_file}")
        print(f"📊 Total records: {len(df)}")
        
        return output_file
    
    def show_sector_summary(self):
        """
        Display sector summary information
        """
        if self.sector_mapping is None:
            self.load_sector_mapping()
        
        if not self.sector_mapping:
            print("❌ No sector mapping available")
            return
        
        sectors = self.sector_mapping.get('sectors', {})
        summary = self.sector_mapping.get('summary', {})
        
        print("📊 SECTOR MAPPING SUMMARY")
        print("=" * 60)
        
        # Overall statistics
        print(f"📈 Total Sectors: {summary.get('total_sectors', len(sectors))}")
        print(f"🏢 Total Companies: {summary.get('total_companies', 0)}")
        print(f"📅 Last Updated: {self.sector_mapping.get('timestamp', 'Unknown')}")
        
        # Sector breakdown
        print(f"\n🏭 SECTOR BREAKDOWN:")
        print("-" * 60)
        
        # Sort sectors by company count
        sorted_sectors = sorted(sectors.items(), key=lambda x: len(x[1]), reverse=True)
        
        for i, (sector, companies) in enumerate(sorted_sectors, 1):
            sector_display = sector.replace('_', ' ').title()
            print(f"{i:2d}. {sector_display:25s}: {len(companies):3d} companies")
            
            # Show sample companies for top sectors
            if i <= 5 and len(companies) > 0:
                sample_companies = companies[:5]
                print(f"    Sample: {', '.join(sample_companies)}")
                if len(companies) > 5:
                    print(f"    ... and {len(companies) - 5} more")
                print()
    
    def search_companies(self, search_term: str) -> List[Dict[str, str]]:
        """
        Search for companies by name or ticker
        
        Parameters:
        -----------
        search_term : str
            Search term (ticker or partial name)
            
        Returns:
        --------
        List of matching companies with their sectors
        """
        if self.sector_mapping is None:
            self.load_sector_mapping()
        
        if not self.sector_mapping:
            return []
        
        results = []
        sectors = self.sector_mapping.get('sectors', {})
        search_term = search_term.upper()
        
        for sector, companies in sectors.items():
            for company in companies:
                if search_term in company.upper():
                    results.append({
                        'ticker': company,
                        'sector': sector,
                        'sector_display': sector.replace('_', ' ').title()
                    })
        
        return sorted(results, key=lambda x: x['ticker'])

def main():
    """
    Main function for command-line usage
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="Sector Management System")
    parser.add_argument('command', choices=['discover', 'summary', 'export', 'search'], 
                       help='Command to execute')
    parser.add_argument('--force', action='store_true', 
                       help='Force refresh of sector mapping')
    parser.add_argument('--search-term', type=str, 
                       help='Search term for companies')
    parser.add_argument('--output', type=str, 
                       help='Output file path')
    
    args = parser.parse_args()
    
    # Initialize sector manager
    manager = SectorManager()
    
    if args.command == 'discover':
        print("🔍 SECTOR DISCOVERY")
        print("=" * 60)
        mapping_file = manager.discover_and_save_sectors(force_refresh=args.force)
        print(f"\n✅ Sector discovery completed!")
        print(f"📁 Mapping saved to: {mapping_file}")
        
    elif args.command == 'summary':
        manager.show_sector_summary()
        
    elif args.command == 'export':
        print("📤 EXPORTING SECTOR MAPPING TO CSV")
        print("=" * 60)
        csv_file = manager.export_sector_csv(args.output)
        if csv_file:
            print(f"✅ Export completed: {csv_file}")
        
    elif args.command == 'search':
        if not args.search_term:
            print("❌ Please provide --search-term")
            return
        
        print(f"🔍 SEARCHING FOR: {args.search_term}")
        print("=" * 60)
        
        results = manager.search_companies(args.search_term)
        
        if results:
            print(f"Found {len(results)} matching companies:")
            for result in results:
                print(f"  {result['ticker']:12s} - {result['sector_display']}")
        else:
            print("No matching companies found")

if __name__ == "__main__":
    main()
