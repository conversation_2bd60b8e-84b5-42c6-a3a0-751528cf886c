#!/usr/bin/env python3
"""
Debug script to understand the data structure
"""

import os
import sys
import json

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.data_loader import ScreenerDataLoader

def debug_data_structure():
    """
    Debug the data structure to understand why only quarterly consistency is calculated
    """
    data_loader = ScreenerDataLoader('../screener_data_collector/data')
    
    # Load data for TCS as an example
    company_data = data_loader.load_company_data('TCS')
    
    print("=== TCS DATA STRUCTURE ===")
    print(f"Available sections: {list(company_data.keys())}")
    
    # Check profit_loss structure
    if 'profit_loss' in company_data:
        print(f"\n=== PROFIT_LOSS STRUCTURE ===")
        pl_data = company_data['profit_loss']
        print(f"Keys: {list(pl_data.keys())}")
        
        # Check if it has units and notes
        if 'units' in pl_data:
            print(f"Units: {pl_data['units']}")
        if 'notes' in pl_data:
            print(f"Notes: {pl_data['notes']}")
        
        # Check the first few metrics
        data_keys = [k for k in pl_data.keys() if k not in ['units', 'notes']]
        print(f"Data keys (first 5): {data_keys[:5]}")
        
        if data_keys:
            first_metric = data_keys[0]
            print(f"\nFirst metric '{first_metric}':")
            print(f"Type: {type(pl_data[first_metric])}")
            if isinstance(pl_data[first_metric], dict):
                print(f"Years: {list(pl_data[first_metric].keys())}")
                print(f"Sample values: {dict(list(pl_data[first_metric].items())[:3])}")
    
    # Check quarters structure
    if 'quarters' in company_data:
        print(f"\n=== QUARTERS STRUCTURE ===")
        quarters_data = company_data['quarters']
        print(f"Keys: {list(quarters_data.keys())}")
        
        # Check if it has units and notes
        if 'units' in quarters_data:
            print(f"Units: {quarters_data['units']}")
        if 'notes' in quarters_data:
            print(f"Notes: {quarters_data['notes']}")
        
        # Check the first few metrics
        data_keys = [k for k in quarters_data.keys() if k not in ['units', 'notes']]
        print(f"Data keys (first 5): {data_keys[:5]}")
        
        if data_keys:
            first_metric = data_keys[0]
            print(f"\nFirst metric '{first_metric}':")
            print(f"Type: {type(quarters_data[first_metric])}")
            if isinstance(quarters_data[first_metric], dict):
                print(f"Quarters: {list(quarters_data[first_metric].keys())}")
                print(f"Sample values: {dict(list(quarters_data[first_metric].items())[:3])}")
    
    # Check cash_flow structure
    if 'cash_flow' in company_data:
        print(f"\n=== CASH_FLOW STRUCTURE ===")
        cf_data = company_data['cash_flow']
        print(f"Keys: {list(cf_data.keys())}")
        
        # Check if it has units and notes
        if 'units' in cf_data:
            print(f"Units: {cf_data['units']}")
        if 'notes' in cf_data:
            print(f"Notes: {cf_data['notes']}")
        
        # Check the first few metrics
        data_keys = [k for k in cf_data.keys() if k not in ['units', 'notes']]
        print(f"Data keys (first 5): {data_keys[:5]}")
        
        if data_keys:
            first_metric = data_keys[0]
            print(f"\nFirst metric '{first_metric}':")
            print(f"Type: {type(cf_data[first_metric])}")
            if isinstance(cf_data[first_metric], dict):
                print(f"Years: {list(cf_data[first_metric].keys())}")
                print(f"Sample values: {dict(list(cf_data[first_metric].items())[:3])}")
    
    # Check ratios structure
    if 'ratios' in company_data:
        print(f"\n=== RATIOS STRUCTURE ===")
        ratios_data = company_data['ratios']
        print(f"Keys: {list(ratios_data.keys())}")
        print(f"Type: {type(ratios_data)}")
        
        if isinstance(ratios_data, dict):
            for key, value in ratios_data.items():
                print(f"  {key}: {type(value)} - {value if not isinstance(value, dict) else f'dict with {len(value)} keys'}")

if __name__ == "__main__":
    debug_data_structure()
