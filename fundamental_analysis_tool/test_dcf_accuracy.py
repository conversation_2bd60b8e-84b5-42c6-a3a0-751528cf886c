#!/usr/bin/env python3
"""
🔍 DCF ACCURACY TEST

This script tests DCF calculations on known companies to verify accuracy
and ensure we're not getting all companies as overpriced.

Usage:
    python test_dcf_accuracy.py
"""

import os
import sys
import json
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.data_loader import ScreenerDataLoader
from warren_buffett_dcf_analysis import WarrenBuffettDCFAnalysis

def test_dcf_accuracy():
    """Test DCF accuracy on known companies"""
    
    print("🔍 DCF ACCURACY TEST")
    print("=" * 80)
    print("Testing DCF calculations on known companies to verify accuracy...")
    print("=" * 80)
    
    # Initialize components
    data_loader = ScreenerDataLoader('../screener_data_collector/data')
    warren_buffett_analyzer = WarrenBuffettDCFAnalysis()
    
    # Test companies - mix of different sectors and sizes
    test_companies = [
        'TCS',        # Large IT
        'RELIANCE',   # Large Conglomerate
        'HDFCBANK',   # Large Bank
        'INFY',       # Large IT
        'WIPRO',      # Mid IT
        'SBIN',       # Large Bank
        'ICICIBANK',  # Large Bank
        'MARUTI',     # Large Auto
        'HINDUNILVR', # Large FMCG
        'ITC'         # Large FMCG
    ]
    
    results = []
    
    for ticker in test_companies:
        print(f"\n📊 Testing {ticker}...")
        print("-" * 40)
        
        try:
            # Load company data
            company_data = data_loader.load_company_data(ticker)
            
            if not company_data:
                print(f"   ❌ No data available for {ticker}")
                continue
            
            # Show basic company info
            overview = company_data.get('overview', {})
            market_cap = overview.get('market_cap', 'N/A')
            current_price = overview.get('current_price', 'N/A')
            
            print(f"   📈 Market Cap: {market_cap}")
            print(f"   💰 Current Price: ₹{current_price}")
            
            # Run DCF analysis
            dcf_result = warren_buffett_analyzer.analyze_company_buffett_style(ticker, company_data)
            
            if not dcf_result:
                print(f"   ❌ DCF analysis failed for {ticker}")
                continue
            
            # Extract key metrics
            intrinsic_val = dcf_result.get('intrinsic_valuation', {})
            dcf_details = intrinsic_val.get('dcf_details', {})
            margin_analysis = dcf_result.get('margin_of_safety_analysis', {})
            buffett_score = dcf_result.get('buffett_score', {})
            recommendation = dcf_result.get('recommendation', {})
            
            # Get key values
            enterprise_value = dcf_details.get('enterprise_value', 0)
            equity_value = dcf_details.get('equity_value', 0)
            
            # Calculate per-share value
            per_share_value = 0
            margin_of_safety = 0
            
            try:
                current_price_num = float(str(current_price).replace(',', '')) if current_price != 'N/A' else 0
                market_cap_num = float(str(market_cap).replace(',', '')) if market_cap != 'N/A' else 0
                
                if market_cap_num > 0 and current_price_num > 0 and enterprise_value > 0:
                    shares_outstanding = (market_cap_num * 10_000_000) / current_price_num
                    per_share_value = (enterprise_value * 10_000_000) / shares_outstanding
                    margin_of_safety = ((per_share_value - current_price_num) / current_price_num) * 100
            except:
                pass
            
            # Store results
            result = {
                'ticker': ticker,
                'market_cap': market_cap,
                'current_price': current_price_num,
                'enterprise_value': enterprise_value,
                'equity_value': equity_value,
                'per_share_value': per_share_value,
                'margin_of_safety': margin_of_safety,
                'buffett_score': buffett_score.get('buffett_score', 0),
                'buffett_grade': buffett_score.get('buffett_grade', 'N/A'),
                'buffett_approved': recommendation.get('buffett_approved', False),
                'recommendation': recommendation.get('recommendation', 'UNKNOWN'),
                'dcf_details': dcf_details
            }
            
            results.append(result)
            
            # Display results
            print(f"   🏢 Enterprise Value: ₹{enterprise_value:,.0f} Cr")
            print(f"   💎 Equity Value: ₹{equity_value:,.0f} Cr")
            
            if per_share_value > 0:
                print(f"   📊 DCF Per Share Value: ₹{per_share_value:.2f}")
                print(f"   📈 Current Price: ₹{current_price_num:.2f}")
                print(f"   🎯 Margin of Safety: {margin_of_safety:.1f}%")
                
                if margin_of_safety > 0:
                    print(f"   ✅ Status: UNDERVALUED by {margin_of_safety:.1f}%")
                else:
                    print(f"   ⚠️  Status: OVERVALUED by {abs(margin_of_safety):.1f}%")
            else:
                print(f"   ❌ Per share calculation failed")
            
            print(f"   ⭐ Buffett Score: {buffett_score.get('buffett_score', 0):.1f}/100")
            print(f"   🏆 Buffett Grade: {buffett_score.get('buffett_grade', 'N/A')}")
            print(f"   🎯 Recommendation: {recommendation.get('recommendation', 'UNKNOWN')}")
            print(f"   ✅ Buffett Approved: {recommendation.get('buffett_approved', False)}")
            
        except Exception as e:
            print(f"   ❌ Error analyzing {ticker}: {e}")
    
    # Summary analysis
    print(f"\n" + "=" * 80)
    print("📊 DCF ACCURACY TEST SUMMARY")
    print("=" * 80)
    
    if results:
        total_companies = len(results)
        valid_dcf = len([r for r in results if r['enterprise_value'] > 0])
        valid_per_share = len([r for r in results if r['per_share_value'] > 0])
        undervalued = len([r for r in results if r['margin_of_safety'] > 0])
        overvalued = len([r for r in results if r['margin_of_safety'] < 0])
        buffett_approved = len([r for r in results if r['buffett_approved']])
        
        print(f"📈 ANALYSIS STATISTICS:")
        print(f"   Total Companies Tested: {total_companies}")
        print(f"   Valid DCF Calculations: {valid_dcf} ({valid_dcf/total_companies*100:.1f}%)")
        print(f"   Valid Per Share Calculations: {valid_per_share} ({valid_per_share/total_companies*100:.1f}%)")
        
        print(f"\n💰 VALUATION RESULTS:")
        if valid_per_share > 0:
            print(f"   Undervalued Companies: {undervalued} ({undervalued/valid_per_share*100:.1f}%)")
            print(f"   Overvalued Companies: {overvalued} ({overvalued/valid_per_share*100:.1f}%)")
        
        print(f"\n⭐ BUFFETT ANALYSIS:")
        print(f"   Buffett Approved: {buffett_approved} ({buffett_approved/total_companies*100:.1f}%)")
        
        # Show enterprise values to check for reasonableness
        enterprise_values = [r['enterprise_value'] for r in results if r['enterprise_value'] > 0]
        if enterprise_values:
            avg_enterprise = sum(enterprise_values) / len(enterprise_values)
            min_enterprise = min(enterprise_values)
            max_enterprise = max(enterprise_values)
            
            print(f"\n🏢 ENTERPRISE VALUE ANALYSIS:")
            print(f"   Average Enterprise Value: ₹{avg_enterprise:,.0f} Cr")
            print(f"   Range: ₹{min_enterprise:,.0f} - ₹{max_enterprise:,.0f} Cr")
        
        # Show per share values
        per_share_values = [r['per_share_value'] for r in results if r['per_share_value'] > 0]
        if per_share_values:
            avg_per_share = sum(per_share_values) / len(per_share_values)
            min_per_share = min(per_share_values)
            max_per_share = max(per_share_values)
            
            print(f"\n📊 PER SHARE VALUE ANALYSIS:")
            print(f"   Average Per Share Value: ₹{avg_per_share:,.2f}")
            print(f"   Range: ₹{min_per_share:,.2f} - ₹{max_per_share:,.2f}")
        
        # Show margin of safety distribution
        margins = [r['margin_of_safety'] for r in results if r['margin_of_safety'] != 0]
        if margins:
            avg_margin = sum(margins) / len(margins)
            
            print(f"\n🎯 MARGIN OF SAFETY ANALYSIS:")
            print(f"   Average Margin of Safety: {avg_margin:.1f}%")
            
            # Show individual margins
            print(f"\n📋 INDIVIDUAL COMPANY RESULTS:")
            for r in results:
                if r['per_share_value'] > 0:
                    status = "UNDERVALUED" if r['margin_of_safety'] > 0 else "OVERVALUED"
                    print(f"   {r['ticker']:10} | ₹{r['per_share_value']:8.2f} | {r['margin_of_safety']:6.1f}% | {status}")
        
        # Check for potential issues
        print(f"\n🔍 ACCURACY CHECK:")
        
        # Check if all companies are overvalued (potential issue)
        if valid_per_share > 0 and overvalued == valid_per_share:
            print("   ⚠️  WARNING: ALL companies appear overvalued - check DCF parameters!")
        elif valid_per_share > 0 and undervalued == valid_per_share:
            print("   ⚠️  WARNING: ALL companies appear undervalued - check DCF parameters!")
        else:
            print("   ✅ Valuation distribution looks reasonable")
        
        # Check for zero enterprise values
        zero_enterprise = total_companies - valid_dcf
        if zero_enterprise > 0:
            print(f"   ⚠️  WARNING: {zero_enterprise} companies have zero enterprise value")
        else:
            print("   ✅ All companies have valid enterprise values")
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'output/dcf_accuracy_test_{timestamp}.json'
        
        os.makedirs('output', exist_ok=True)
        with open(output_file, 'w') as f:
            json.dump({
                'test_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'test_companies': test_companies,
                    'total_tested': total_companies
                },
                'results': results,
                'summary': {
                    'total_companies': total_companies,
                    'valid_dcf': valid_dcf,
                    'valid_per_share': valid_per_share,
                    'undervalued': undervalued,
                    'overvalued': overvalued,
                    'buffett_approved': buffett_approved,
                    'average_enterprise_value': avg_enterprise if enterprise_values else 0,
                    'average_per_share_value': avg_per_share if per_share_values else 0,
                    'average_margin_of_safety': avg_margin if margins else 0
                }
            }, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_file}")
    
    else:
        print("❌ No valid results obtained")
    
    print("\n🎯 DCF ACCURACY TEST COMPLETE!")
    print("=" * 80)

if __name__ == "__main__":
    test_dcf_accuracy()
