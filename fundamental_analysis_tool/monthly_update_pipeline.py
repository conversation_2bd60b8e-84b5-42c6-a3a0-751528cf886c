#!/usr/bin/env python3
"""
Monthly Update Pipeline

This script provides automated monthly updates for the investment analysis system.
It updates data, re-runs analysis, and generates updated recommendations.

This is for REAL MONEY - monthly updates ensure current investment decisions.
"""

import os
import sys
import json
import subprocess
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import shutil

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('monthly_update')

class MonthlyUpdatePipeline:
    """
    Monthly update pipeline for investment analysis system
    """
    
    def __init__(self):
        """
        Initialize monthly update pipeline
        """
        self.update_date = datetime.now()
        self.backup_dir = f'backups/monthly_backup_{self.update_date.strftime("%Y%m%d")}'
        
        # Update results
        self.update_results = {
            'metadata': {
                'update_date': self.update_date.isoformat(),
                'update_type': 'monthly_automated',
                'version': '1.0'
            },
            'data_update': {},
            'analysis_update': {},
            'portfolio_update': {},
            'comparison': {},
            'recommendations': {}
        }
    
    def run_monthly_update(self):
        """
        Run complete monthly update pipeline
        """
        print("=" * 100)
        print("📅 MONTHLY INVESTMENT ANALYSIS UPDATE")
        print("=" * 100)
        print(f"📊 Update Date: {self.update_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print("🔄 Pipeline: Data Update → Analysis → Portfolio → Comparison → Recommendations")
        print("=" * 100)
        
        # Stage 1: Backup existing data
        print("\n💾 STAGE 1: BACKUP EXISTING DATA")
        print("-" * 80)
        self._backup_existing_data()
        
        # Stage 2: Update screener data
        print(f"\n📊 STAGE 2: UPDATE SCREENER DATA")
        print("-" * 80)
        data_update_success = self._update_screener_data()
        
        # Stage 3: Re-run fundamental analysis
        print(f"\n📈 STAGE 3: RE-RUN FUNDAMENTAL ANALYSIS")
        print("-" * 80)
        analysis_update_success = self._update_fundamental_analysis()
        
        # Stage 4: Update portfolio optimization
        print(f"\n🎯 STAGE 4: UPDATE PORTFOLIO OPTIMIZATION")
        print("-" * 80)
        portfolio_update_success = self._update_portfolio_optimization()
        
        # Stage 5: Compare with previous results
        print(f"\n📊 STAGE 5: COMPARE WITH PREVIOUS RESULTS")
        print("-" * 80)
        comparison_results = self._compare_with_previous()
        
        # Stage 6: Generate update recommendations
        print(f"\n🏆 STAGE 6: GENERATE UPDATE RECOMMENDATIONS")
        print("-" * 80)
        update_recommendations = self._generate_update_recommendations()
        
        # Save results
        self._save_update_results()
        
        # Display summary
        self._display_update_summary()
        
        return self.update_results
    
    def _backup_existing_data(self):
        """
        Backup existing data and results
        """
        print(f"  🔄 Creating backup in {self.backup_dir}...")
        
        # Create backup directory
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Backup directories to preserve
        backup_sources = [
            'output',
            '../screener_data_collector/data'
        ]
        
        backed_up = 0
        
        for source in backup_sources:
            if os.path.exists(source):
                try:
                    dest = os.path.join(self.backup_dir, os.path.basename(source))
                    if os.path.isdir(source):
                        shutil.copytree(source, dest, dirs_exist_ok=True)
                    else:
                        shutil.copy2(source, dest)
                    backed_up += 1
                    print(f"    ✅ Backed up: {source}")
                except Exception as e:
                    print(f"    ⚠️  Failed to backup {source}: {e}")
        
        print(f"  ✅ Backup completed: {backed_up} items backed up")
        
        self.update_results['data_update']['backup'] = {
            'backup_dir': self.backup_dir,
            'items_backed_up': backed_up,
            'backup_date': self.update_date.isoformat()
        }
    
    def _update_screener_data(self):
        """
        Update screener data collection
        """
        print(f"  🔄 Updating screener data...")
        
        try:
            # Change to screener data collector directory
            screener_dir = '../screener_data_collector'
            
            if not os.path.exists(screener_dir):
                print(f"  ❌ Screener data collector not found at {screener_dir}")
                return False
            
            # Run screener data update
            print(f"    📊 Running screener data collection...")
            
            # Use update mode to get only new/changed data
            cmd = ['python', 'screener_data_collector.py', '--mode', 'update']
            
            result = subprocess.run(
                cmd,
                cwd=screener_dir,
                capture_output=True,
                text=True,
                timeout=3600  # 1 hour timeout
            )
            
            if result.returncode == 0:
                print(f"  ✅ Screener data update completed successfully")
                
                # Count updated companies
                data_dir = os.path.join(screener_dir, 'data')
                if os.path.exists(data_dir):
                    companies_count = len([f for f in os.listdir(data_dir) 
                                         if f.endswith('.json')])
                    print(f"    📊 Total companies in database: {companies_count}")
                    
                    self.update_results['data_update']['screener'] = {
                        'status': 'success',
                        'companies_count': companies_count,
                        'update_time': datetime.now().isoformat()
                    }
                
                return True
            else:
                print(f"  ❌ Screener data update failed")
                print(f"    Error: {result.stderr}")
                
                self.update_results['data_update']['screener'] = {
                    'status': 'failed',
                    'error': result.stderr,
                    'update_time': datetime.now().isoformat()
                }
                
                return False
                
        except subprocess.TimeoutExpired:
            print(f"  ⚠️  Screener data update timed out")
            return False
        except Exception as e:
            print(f"  ❌ Error updating screener data: {e}")
            return False
    
    def _update_fundamental_analysis(self):
        """
        Re-run fundamental analysis with updated data
        """
        print(f"  🔄 Re-running fundamental analysis...")
        
        try:
            # Run the final investment pipeline
            print(f"    📈 Running final investment pipeline...")
            
            result = subprocess.run(
                ['python', 'final_investment_pipeline.py'],
                capture_output=True,
                text=True,
                timeout=3600  # 1 hour timeout
            )
            
            if result.returncode == 0:
                print(f"  ✅ Fundamental analysis update completed")
                
                # Check for new results
                results_dir = 'output/final_pipeline'
                if os.path.exists(results_dir):
                    result_files = [f for f in os.listdir(results_dir) 
                                  if f.startswith('final_investment_pipeline_')]
                    
                    if result_files:
                        latest_file = sorted(result_files)[-1]
                        print(f"    📁 New results: {latest_file}")
                        
                        # Load and summarize results
                        with open(os.path.join(results_dir, latest_file), 'r') as f:
                            results = json.load(f)
                        
                        summary_stats = results.get('stage_5_final_recommendations', {}).get('summary_statistics', {})
                        
                        self.update_results['analysis_update'] = {
                            'status': 'success',
                            'results_file': latest_file,
                            'summary_statistics': summary_stats,
                            'update_time': datetime.now().isoformat()
                        }
                
                return True
            else:
                print(f"  ❌ Fundamental analysis update failed")
                print(f"    Error: {result.stderr}")
                
                self.update_results['analysis_update'] = {
                    'status': 'failed',
                    'error': result.stderr,
                    'update_time': datetime.now().isoformat()
                }
                
                return False
                
        except subprocess.TimeoutExpired:
            print(f"  ⚠️  Fundamental analysis update timed out")
            return False
        except Exception as e:
            print(f"  ❌ Error updating fundamental analysis: {e}")
            return False
    
    def _update_portfolio_optimization(self):
        """
        Update portfolio optimization
        """
        print(f"  🔄 Updating portfolio optimization...")
        
        try:
            # Run portfolio integration
            print(f"    🎯 Running portfolio integration...")
            
            result = subprocess.run(
                ['python', 'portfolio_integration.py'],
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            if result.returncode == 0:
                print(f"  ✅ Portfolio optimization update completed")
                
                # Check for new portfolio results
                portfolio_dir = 'output/portfolio_integration'
                if os.path.exists(portfolio_dir):
                    portfolio_files = [f for f in os.listdir(portfolio_dir) 
                                     if f.startswith('final_portfolio_')]
                    
                    if portfolio_files:
                        latest_portfolio = sorted(portfolio_files)[-1]
                        print(f"    📁 New portfolio: {latest_portfolio}")
                        
                        # Load portfolio summary
                        portfolio_df = pd.read_csv(os.path.join(portfolio_dir, latest_portfolio))
                        
                        self.update_results['portfolio_update'] = {
                            'status': 'success',
                            'portfolio_file': latest_portfolio,
                            'portfolio_assets': len(portfolio_df),
                            'update_time': datetime.now().isoformat()
                        }
                
                return True
            else:
                print(f"  ❌ Portfolio optimization update failed")
                print(f"    Error: {result.stderr}")
                
                self.update_results['portfolio_update'] = {
                    'status': 'failed',
                    'error': result.stderr,
                    'update_time': datetime.now().isoformat()
                }
                
                return False
                
        except subprocess.TimeoutExpired:
            print(f"  ⚠️  Portfolio optimization update timed out")
            return False
        except Exception as e:
            print(f"  ❌ Error updating portfolio optimization: {e}")
            return False
    
    def _compare_with_previous(self):
        """
        Compare current results with previous month
        """
        print(f"  🔄 Comparing with previous results...")
        
        try:
            # Find previous results
            results_dir = 'output/final_pipeline'
            
            if not os.path.exists(results_dir):
                print(f"  ⚠️  No previous results found for comparison")
                return None
            
            result_files = sorted([f for f in os.listdir(results_dir) 
                                 if f.startswith('final_investment_pipeline_')])
            
            if len(result_files) < 2:
                print(f"  ⚠️  Insufficient results for comparison")
                return None
            
            # Load current and previous results
            current_file = result_files[-1]
            previous_file = result_files[-2]
            
            print(f"    📊 Comparing {current_file} vs {previous_file}")
            
            with open(os.path.join(results_dir, current_file), 'r') as f:
                current_results = json.load(f)
            
            with open(os.path.join(results_dir, previous_file), 'r') as f:
                previous_results = json.load(f)
            
            # Compare key metrics
            current_stats = current_results.get('stage_5_final_recommendations', {}).get('summary_statistics', {})
            previous_stats = previous_results.get('stage_5_final_recommendations', {}).get('summary_statistics', {})
            
            comparison = {
                'current_file': current_file,
                'previous_file': previous_file,
                'metrics_comparison': {
                    'total_analyzed': {
                        'current': current_stats.get('total_analyzed', 0),
                        'previous': previous_stats.get('total_analyzed', 0),
                        'change': current_stats.get('total_analyzed', 0) - previous_stats.get('total_analyzed', 0)
                    },
                    'dcf_qualified': {
                        'current': current_stats.get('dcf_qualified', 0),
                        'previous': previous_stats.get('dcf_qualified', 0),
                        'change': current_stats.get('dcf_qualified', 0) - previous_stats.get('dcf_qualified', 0)
                    },
                    'buy_count': {
                        'current': current_stats.get('buy_count', 0),
                        'previous': previous_stats.get('buy_count', 0),
                        'change': current_stats.get('buy_count', 0) - previous_stats.get('buy_count', 0)
                    },
                    'portfolio_companies': {
                        'current': current_stats.get('portfolio_companies', 0),
                        'previous': previous_stats.get('portfolio_companies', 0),
                        'change': current_stats.get('portfolio_companies', 0) - previous_stats.get('portfolio_companies', 0)
                    }
                }
            }
            
            print(f"  ✅ Comparison completed")
            
            # Display key changes
            for metric, data in comparison['metrics_comparison'].items():
                change = data['change']
                if change != 0:
                    direction = "↑" if change > 0 else "↓"
                    print(f"    {metric}: {data['previous']} → {data['current']} ({direction}{abs(change)})")
            
            self.update_results['comparison'] = comparison
            
            return comparison
            
        except Exception as e:
            print(f"  ❌ Error comparing results: {e}")
            return None
    
    def _generate_update_recommendations(self):
        """
        Generate recommendations based on updates
        """
        print(f"  🔄 Generating update recommendations...")
        
        recommendations = []
        
        # Check data update status
        data_status = self.update_results.get('data_update', {})
        if data_status.get('screener', {}).get('status') == 'success':
            recommendations.append({
                'type': 'data_success',
                'message': 'Data successfully updated - analysis reflects latest financial information',
                'priority': 'info'
            })
        else:
            recommendations.append({
                'type': 'data_warning',
                'message': 'Data update failed - consider manual data refresh before investment decisions',
                'priority': 'warning'
            })
        
        # Check analysis changes
        comparison = self.update_results.get('comparison', {})
        if comparison:
            metrics = comparison.get('metrics_comparison', {})
            
            # Check for significant changes in BUY recommendations
            buy_change = metrics.get('buy_count', {}).get('change', 0)
            if abs(buy_change) >= 2:
                direction = "increased" if buy_change > 0 else "decreased"
                recommendations.append({
                    'type': 'portfolio_change',
                    'message': f'BUY recommendations {direction} by {abs(buy_change)} - review portfolio allocation',
                    'priority': 'high'
                })
            
            # Check for changes in portfolio companies
            portfolio_change = metrics.get('portfolio_companies', {}).get('change', 0)
            if portfolio_change != 0:
                direction = "increased" if portfolio_change > 0 else "decreased"
                recommendations.append({
                    'type': 'portfolio_composition',
                    'message': f'Portfolio composition changed - {abs(portfolio_change)} companies {direction}',
                    'priority': 'medium'
                })
        
        # Portfolio update recommendations
        portfolio_status = self.update_results.get('portfolio_update', {})
        if portfolio_status.get('status') == 'success':
            recommendations.append({
                'type': 'portfolio_success',
                'message': 'Portfolio optimization completed - new allocation weights available',
                'priority': 'info'
            })
        
        # General monthly recommendations
        recommendations.append({
            'type': 'monthly_review',
            'message': 'Monthly update completed - review changes before making investment decisions',
            'priority': 'high'
        })
        
        print(f"  ✅ Generated {len(recommendations)} recommendations")
        
        self.update_results['recommendations'] = recommendations
        
        return recommendations
    
    def _save_update_results(self):
        """
        Save monthly update results
        """
        os.makedirs('output/monthly_updates', exist_ok=True)
        
        timestamp = self.update_date.strftime('%Y%m%d_%H%M%S')
        output_file = f'output/monthly_updates/monthly_update_{timestamp}.json'
        
        with open(output_file, 'w') as f:
            json.dump(self.update_results, f, indent=2, default=str)
        
        print(f"📁 Monthly update results saved to: {output_file}")
        
        return output_file
    
    def _display_update_summary(self):
        """
        Display monthly update summary
        """
        print(f"\n" + "=" * 100)
        print("✅ MONTHLY UPDATE COMPLETED!")
        print("=" * 100)
        
        print(f"📅 UPDATE SUMMARY:")
        print(f"   Update Date: {self.update_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   Backup Location: {self.backup_dir}")
        
        # Data update status
        data_status = self.update_results.get('data_update', {}).get('screener', {}).get('status', 'unknown')
        print(f"   Data Update: {'✅ SUCCESS' if data_status == 'success' else '❌ FAILED'}")
        
        # Analysis update status
        analysis_status = self.update_results.get('analysis_update', {}).get('status', 'unknown')
        print(f"   Analysis Update: {'✅ SUCCESS' if analysis_status == 'success' else '❌ FAILED'}")
        
        # Portfolio update status
        portfolio_status = self.update_results.get('portfolio_update', {}).get('status', 'unknown')
        print(f"   Portfolio Update: {'✅ SUCCESS' if portfolio_status == 'success' else '❌ FAILED'}")
        
        # Display recommendations
        recommendations = self.update_results.get('recommendations', [])
        if recommendations:
            print(f"\n🎯 UPDATE RECOMMENDATIONS:")
            
            for rec in recommendations:
                priority = rec.get('priority', 'info')
                icon = {'high': '🔴', 'medium': '🟡', 'warning': '⚠️', 'info': 'ℹ️'}.get(priority, 'ℹ️')
                print(f"   {icon} {rec.get('message', 'No message')}")
        
        print(f"\n📁 Results saved in output/monthly_updates/")
        print("=" * 100)

def main():
    """
    Main function to run monthly update
    """
    print("📅 MONTHLY INVESTMENT ANALYSIS UPDATE")
    print("=" * 80)
    print("Automated Monthly Update for Real Money Investment System")
    print("=" * 80)
    
    # Initialize and run monthly update
    updater = MonthlyUpdatePipeline()
    results = updater.run_monthly_update()
    
    if results:
        print(f"\n✅ Monthly Update Completed Successfully!")
        print(f"📁 Results available in output/monthly_updates/")
    else:
        print(f"\n❌ Monthly update failed to complete")

if __name__ == "__main__":
    main()
