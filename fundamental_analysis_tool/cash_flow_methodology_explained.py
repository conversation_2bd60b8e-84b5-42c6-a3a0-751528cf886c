#!/usr/bin/env python3
"""
Cash Flow Screening Methodology - Detailed Explanation

This module provides a comprehensive explanation of the cash flow screening methodology
used in the fundamental analysis system. It shows EXACTLY what is being analyzed
over the 10-year period.

This addresses your concern: "how the cashflow screening is done is it only based on 
ocf of recent year or it is considering the all types of cash flow in last 10 years?"

ANSWER: It's a COMPREHENSIVE 10-year analysis of ALL types of cash flows.
"""

import os
import sys
import json
import pandas as pd
from typing import Dict, List, Any, Optional
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.data_loader import ScreenerDataLoader
from models.cashflow_prescreener import CashFlowPreScreener

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('cash_flow_methodology')

class CashFlowMethodologyExplainer:
    """
    Explains the comprehensive cash flow screening methodology
    """
    
    def __init__(self):
        """
        Initialize the methodology explainer
        """
        self.data_loader = ScreenerDataLoader('../screener_data_collector/data')
        self.prescreener = CashFlowPreScreener()
    
    def explain_methodology(self):
        """
        Provide comprehensive explanation of cash flow methodology
        """
        print("=" * 100)
        print("📊 COMPREHENSIVE CASH FLOW SCREENING METHODOLOGY")
        print("=" * 100)
        print("🎯 ANSWER: This is a COMPREHENSIVE 10-year analysis of ALL types of cash flows")
        print("=" * 100)
        
        print("\n🔍 WHAT IS ANALYZED (10-YEAR COMPREHENSIVE):")
        print("-" * 80)
        
        print("1. 📈 OPERATING CASH FLOW (OCF) - 10 Years")
        print("   • Cash from Operations")
        print("   • Operating Cash Flow")
        print("   • Cash from Operating Activities")
        print("   • Estimated OCF (Net Profit + Depreciation)")
        
        print("\n2. 💰 FREE CASH FLOW (FCF) - 10 Years")
        print("   • Direct Free Cash Flow data")
        print("   • Calculated FCF (OCF - Capital Expenditure)")
        print("   • FCF from all available sources")
        
        print("\n3. 🏗️ INVESTING CASH FLOW - 10 Years")
        print("   • Capital Expenditure (Capex)")
        print("   • Investments")
        print("   • Asset purchases/sales")
        
        print("\n4. 🏦 FINANCING CASH FLOW - 10 Years")
        print("   • Working Capital Changes")
        print("   • Change in Working Capital")
        print("   • Debt and equity financing")
        
        print("\n5. 📊 QUALITY METRICS - 10 Years")
        print("   • OCF to Net Income Ratio")
        print("   • Cash Flow Predictability")
        print("   • Cash Flow Consistency")
        print("   • Growth Pattern Analysis")
        
        print("\n" + "=" * 100)
        print("🎯 DETAILED SCORING METHODOLOGY (100 POINTS TOTAL)")
        print("=" * 100)
        
        print("\n📈 OPERATING CASH FLOW ANALYSIS (40 POINTS):")
        print("-" * 60)
        print("   • Positive Years (15 points): % of years with positive OCF")
        print("   • Growth Consistency (10 points): % of years with OCF growth")
        print("   • Trend Analysis (10 points): Overall OCF trend (positive/negative)")
        print("   • Volatility (5 points): Coefficient of variation (lower is better)")
        
        print("\n💰 FREE CASH FLOW ANALYSIS (30 POINTS):")
        print("-" * 60)
        print("   • Positive Years (15 points): % of years with positive FCF")
        print("   • Growth Consistency (10 points): % of years with FCF growth")
        print("   • Trend Analysis (5 points): Overall FCF trend")
        
        print("\n🏆 QUALITY ANALYSIS (30 POINTS):")
        print("-" * 60)
        print("   • OCF to Net Income Ratio (20 points): Cash quality vs accounting profit")
        print("   • Predictability (10 points): R-squared of cash flow trend")
        
        print("\n" + "=" * 100)
        print("✅ PASS/FAIL CRITERIA (STRICT 10-YEAR REQUIREMENTS)")
        print("=" * 100)
        
        print("\n🎯 MINIMUM REQUIREMENTS:")
        print("-" * 40)
        print("   • Minimum 7 years of cash flow data")
        print("   • At least 5 years of positive OCF")
        print("   • At least 3 years of positive FCF")
        print("   • Latest year OCF must be positive")
        print("   • Cash flow score ≥ 40 points")
        print("   • OCF/NI consistency ≥ 50%")
        
        print("\n🏆 EXCELLENCE CRITERIA:")
        print("-" * 40)
        print("   • Cash flow score ≥ 60 points")
        print("   • 80%+ positive OCF years")
        print("   • 60%+ positive FCF years")
        print("   • Strong growth consistency")
        print("   • Low volatility")
        
    def demonstrate_with_real_company(self, ticker: str):
        """
        Demonstrate the methodology with a real company
        """
        print(f"\n" + "=" * 100)
        print(f"🔍 DEMONSTRATING METHODOLOGY WITH {ticker}")
        print("=" * 100)
        
        # Load company data
        company_data = self.data_loader.load_company_data(ticker)
        
        if not company_data:
            print(f"❌ No data available for {ticker}")
            return
        
        # Run cash flow analysis
        result = self.prescreener.prescreen_company(company_data)
        
        print(f"\n📊 CASH FLOW ANALYSIS RESULTS FOR {ticker}:")
        print("-" * 80)
        
        # Show overall results
        print(f"   Cash Flow Score: {result.get('cash_flow_score', 0):.1f}/100")
        print(f"   Passed Pre-screening: {'✅ YES' if result.get('passed_prescreen', False) else '❌ NO'}")
        
        # Show detailed analysis
        analysis = result.get('analysis', {})
        
        # Operating Cash Flow Analysis
        ocf_analysis = analysis.get('operating_cash_flow', {})
        if 'error' not in ocf_analysis:
            print(f"\n📈 OPERATING CASH FLOW (10-Year Analysis):")
            print(f"   Total Years Analyzed: {ocf_analysis.get('total_years', 0)}")
            print(f"   Positive Years: {ocf_analysis.get('positive_years', 0)}")
            print(f"   Positive Percentage: {ocf_analysis.get('positive_percentage', 0):.1f}%")
            print(f"   Average OCF: ₹{ocf_analysis.get('average_ocf', 0):,.0f} Cr")
            print(f"   Latest OCF: ₹{ocf_analysis.get('latest_ocf', 0):,.0f} Cr")
            print(f"   Growth Consistency: {ocf_analysis.get('growth_consistency', 0):.1f}%")
            print(f"   Trend Slope: {ocf_analysis.get('trend_slope', 0):.2f}")
            
            # Show year-by-year OCF
            ocf_series = ocf_analysis.get('ocf_series', {})
            if ocf_series:
                print(f"\n   📅 Year-by-Year OCF:")
                for year, value in sorted(ocf_series.items()):
                    print(f"      {year}: ₹{value:,.0f} Cr")
        
        # Free Cash Flow Analysis
        fcf_analysis = analysis.get('free_cash_flow', {})
        if 'error' not in fcf_analysis:
            print(f"\n💰 FREE CASH FLOW (10-Year Analysis):")
            print(f"   Total Years Analyzed: {fcf_analysis.get('total_years', 0)}")
            print(f"   Positive Years: {fcf_analysis.get('positive_years', 0)}")
            print(f"   Positive Percentage: {fcf_analysis.get('positive_percentage', 0):.1f}%")
            print(f"   Average FCF: ₹{fcf_analysis.get('average_fcf', 0):,.0f} Cr")
            print(f"   Latest FCF: ₹{fcf_analysis.get('latest_fcf', 0):,.0f} Cr")
            print(f"   Growth Consistency: {fcf_analysis.get('growth_consistency', 0):.1f}%")
            
            # Show year-by-year FCF
            fcf_series = fcf_analysis.get('fcf_series', {})
            if fcf_series:
                print(f"\n   📅 Year-by-Year FCF:")
                for year, value in sorted(fcf_series.items()):
                    print(f"      {year}: ₹{value:,.0f} Cr")
        
        # Quality Analysis
        quality_analysis = analysis.get('quality', {})
        if quality_analysis:
            print(f"\n🏆 QUALITY ANALYSIS (10-Year):")
            
            ocf_ni_ratio = quality_analysis.get('ocf_to_ni_ratio', {})
            if ocf_ni_ratio:
                print(f"   OCF to Net Income Ratio:")
                print(f"      Average: {ocf_ni_ratio.get('average', 0):.2f}")
                print(f"      Consistency: {ocf_ni_ratio.get('consistency', 0):.1f}%")
                print(f"      Years Above 80%: {ocf_ni_ratio.get('years_above_80_percent', 0)}")
                print(f"      Years Above 100%: {ocf_ni_ratio.get('years_above_100_percent', 0)}")
            
            predictability = quality_analysis.get('predictability', {})
            if predictability:
                print(f"   Cash Flow Predictability:")
                print(f"      R-squared: {predictability.get('r_squared', 0):.3f}")
                print(f"      Trend Strength: {predictability.get('trend_strength', 'Unknown')}")
        
        # Show reasons for pass/fail
        if result.get('passed_prescreen', False):
            print(f"\n✅ REASONS FOR PASSING:")
            for reason in result.get('reasons_passed', []):
                print(f"   • {reason}")
        else:
            print(f"\n❌ REASONS FOR FAILING:")
            for reason in result.get('reasons_failed', []):
                print(f"   • {reason}")
    
    def show_data_sources(self, ticker: str):
        """
        Show exactly what data sources are used for cash flow analysis
        """
        print(f"\n" + "=" * 100)
        print(f"📁 DATA SOURCES USED FOR {ticker}")
        print("=" * 100)
        
        company_data = self.data_loader.load_company_data(ticker)
        
        if not company_data:
            print(f"❌ No data available for {ticker}")
            return
        
        # Show cash flow statement data
        cash_flow = company_data.get('cash_flow', {})
        if cash_flow:
            print(f"\n💰 CASH FLOW STATEMENT DATA:")
            print("-" * 60)
            
            # Remove units and notes
            cf_clean = {k: v for k, v in cash_flow.items() if k not in ['units', 'notes']}
            
            years = [year for year in cf_clean.keys() if year != 'TTM']
            years.sort()
            
            print(f"   Years Available: {len(years)} ({min(years)} to {max(years)})")
            
            if years:
                sample_year = years[-1]  # Latest year
                sample_data = cf_clean[sample_year]
                
                if isinstance(sample_data, dict):
                    print(f"\n   📊 Available Cash Flow Metrics (Sample from {sample_year}):")
                    for metric, value in sample_data.items():
                        print(f"      • {metric}: {value}")
        
        # Show profit & loss data (for OCF estimation)
        profit_loss = company_data.get('profit_loss', {})
        if profit_loss:
            print(f"\n📈 PROFIT & LOSS DATA (for OCF estimation):")
            print("-" * 60)
            
            pl_clean = {k: v for k, v in profit_loss.items() if k not in ['units', 'notes']}
            years = [year for year in pl_clean.keys() if year != 'TTM']
            years.sort()
            
            print(f"   Years Available: {len(years)} ({min(years)} to {max(years)})")
            
            if years:
                sample_year = years[-1]
                sample_data = pl_clean[sample_year]
                
                if isinstance(sample_data, dict):
                    print(f"\n   📊 Key P&L Metrics for OCF Estimation (Sample from {sample_year}):")
                    key_metrics = ['net_profit', 'Net Profit', 'depreciation', 'Depreciation']
                    for metric in key_metrics:
                        if metric in sample_data:
                            print(f"      • {metric}: {sample_data[metric]}")
        
        print(f"\n🔍 CASH FLOW EXTRACTION METHODOLOGY:")
        print("-" * 60)
        print("   1. Primary: Direct cash flow statement data")
        print("   2. Secondary: Estimated OCF = Net Profit + Depreciation")
        print("   3. Calculated FCF = OCF - Capital Expenditure")
        print("   4. Quality checks: OCF vs Net Income ratios")
        print("   5. Trend analysis: 10-year growth patterns")

def main():
    """
    Main function to demonstrate cash flow methodology
    """
    explainer = CashFlowMethodologyExplainer()
    
    # Explain the methodology
    explainer.explain_methodology()
    
    # Demonstrate with real companies
    test_companies = ['TCS', 'HDFCBANK', 'RELIANCE']
    
    for ticker in test_companies:
        explainer.demonstrate_with_real_company(ticker)
        explainer.show_data_sources(ticker)
    
    print(f"\n" + "=" * 100)
    print("🎯 SUMMARY: COMPREHENSIVE 10-YEAR CASH FLOW ANALYSIS")
    print("=" * 100)
    print("✅ Operating Cash Flow: 10-year trend analysis")
    print("✅ Free Cash Flow: 10-year generation capability")
    print("✅ Investing Activities: Capital allocation efficiency")
    print("✅ Quality Metrics: Cash vs accounting profit")
    print("✅ Predictability: Statistical trend analysis")
    print("✅ Consistency: Year-over-year performance")
    print("=" * 100)
    print("🏆 This is NOT just recent year OCF - it's comprehensive 10-year analysis!")
    print("=" * 100)

if __name__ == "__main__":
    main()
