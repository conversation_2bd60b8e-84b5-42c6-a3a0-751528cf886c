#!/usr/bin/env python3
"""
Interactive Dashboard for Fundamental Analysis System

This dashboard provides a comprehensive view of the analysis system including:
- System status and health checks
- Analysis progress tracking
- Results visualization
- Investment recommendations
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules
from utils.data_loader import ScreenerDataLoader
from models.cashflow_prescreener import CashFlowPreScreener
from models.sector_analyzer import SectorAnalyzer
from models.qualitative_analyzer import QualitativeAnalyzer
from models.consistency_analyzer import ConsistencyAnalyzer

# Page configuration
st.set_page_config(
    page_title="Fundamental Analysis Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-metric {
        border-left-color: #28a745;
    }
    .warning-metric {
        border-left-color: #ffc107;
    }
    .danger-metric {
        border-left-color: #dc3545;
    }
</style>
""", unsafe_allow_html=True)

def load_system_status():
    """Load system status and health checks"""
    try:
        data_loader = ScreenerDataLoader('../screener_data_collector/data')
        total_tickers = len(data_loader.get_all_tickers())

        return {
            'status': 'healthy',
            'total_companies': total_tickers,
            'data_path': '../screener_data_collector/data',
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e),
            'total_companies': 0,
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

def load_modular_results():
    """Load modular analysis results (Result_1, Result_2, etc.)"""
    modular_results = {}

    # Check for modular results directory
    modular_dir = 'output/modular_results'
    if os.path.exists(modular_dir):
        # Group files by result type and get latest valid one
        result_files = {}
        for file_path in Path(modular_dir).glob('Result_*.json'):
            try:
                # Try to load and validate JSON
                with open(file_path, 'r') as f:
                    data = json.load(f)

                metadata = data.get('analysis_metadata', {})
                result_id = metadata.get('result_id', file_path.stem.split('_')[0] + '_' + file_path.stem.split('_')[1])

                # Only keep if we don't have this result_id or this file is newer
                if result_id not in result_files or file_path.stat().st_mtime > Path(result_files[result_id]['file_path']).stat().st_mtime:
                    result_files[result_id] = {
                        'data': data,
                        'metadata': metadata,
                        'file_path': str(file_path),
                        'file_name': file_path.name
                    }
            except Exception as e:
                # Skip corrupted files silently
                continue

        modular_results = result_files

    return modular_results

def load_analysis_results():
    """Load latest analysis results from multiple sources"""
    results = {}

    # 1. Load modular results first (preferred)
    modular_results = load_modular_results()
    if modular_results:
        results['modular_results'] = modular_results

    # 2. Try direct DCF analysis results - prioritize all_companies file
    direct_dcf_dir = 'output/direct_dcf_analysis'
    if os.path.exists(direct_dcf_dir):
        dcf_files = [f for f in os.listdir(direct_dcf_dir) if f.startswith('direct_dcf_analysis_')]
        if dcf_files:
            # Prioritize all_companies file, then latest by date
            all_companies_files = [f for f in dcf_files if 'all_companies' in f]
            if all_companies_files:
                latest_dcf_file = sorted(all_companies_files)[-1]
            else:
                latest_dcf_file = sorted(dcf_files)[-1]

            try:
                with open(os.path.join(direct_dcf_dir, latest_dcf_file), 'r') as f:
                    direct_dcf_data = json.load(f)
                    results['direct_dcf'] = direct_dcf_data
                    results['direct_dcf']['_file'] = latest_dcf_file
            except Exception as e:
                st.error(f"Error loading direct DCF results: {e}")

    # 3. Try legacy results for backward compatibility
    full_analysis_dir = 'output/full_4950_analysis'
    if os.path.exists(full_analysis_dir):
        # Check for prescreen results
        prescreen_file = os.path.join(full_analysis_dir, 'prescreen_results.json')
        if os.path.exists(prescreen_file):
            try:
                with open(prescreen_file, 'r') as f:
                    prescreen_data = json.load(f)
                    results['cash_flow_prescreen'] = prescreen_data
            except Exception as e:
                st.error(f"Error loading prescreen results: {e}")

    if results:
        results['_source'] = 'enhanced_modular_analysis'
        return results

    return None

def create_system_overview():
    """Create system overview section"""
    st.markdown('<div class="main-header">📊 Fundamental Analysis System Dashboard</div>', unsafe_allow_html=True)

    # System status
    system_status = load_system_status()

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        status_color = "🟢" if system_status['status'] == 'healthy' else "🔴"
        st.metric(
            label="System Status",
            value=f"{status_color} {system_status['status'].title()}"
        )

    with col2:
        st.metric(
            label="Total Companies",
            value=f"{system_status['total_companies']:,}"
        )

    with col3:
        st.metric(
            label="Data Source",
            value="Screener.in"
        )

    with col4:
        st.metric(
            label="Last Updated",
            value=system_status['last_updated']
        )

def create_analysis_progress():
    """Create analysis progress section"""
    st.header("🔄 Analysis Progress")

    # Check for analysis results
    results = load_analysis_results()

    if results is None:
        st.warning("No analysis results found. Run the final investment pipeline to see progress.")

        # Show how to run analysis
        st.subheader("How to Run Analysis")
        st.code("""
# Run complete final investment pipeline
cd /home/<USER>/Trading/fundamental_analysis_tool
python final_investment_pipeline.py

# Or run portfolio integration
python portfolio_integration.py
        """, language="bash")

        return

    # Show source information
    source = results.get('_source', 'unknown')
    file_name = results.get('_file', 'unknown')
    st.info(f"📁 Data Source: {source} ({file_name})")

    # Display progress metrics based on source
    if source == 'final_pipeline':
        # Final pipeline results
        stage_1 = results.get('stage_1_qualified_companies', {})
        stage_2 = results.get('stage_2_dcf_analysis', {})
        stage_5 = results.get('stage_5_final_recommendations', {}).get('summary_statistics', {})

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            qualified = stage_1.get('total_qualified', 0)
            st.metric(
                label="Cash Flow Qualified",
                value=f"{qualified:,}"
            )

        with col2:
            dcf_analyzed = stage_2.get('total_analyzed', 0)
            st.metric(
                label="DCF Analyzed",
                value=f"{dcf_analyzed:,}"
            )

        with col3:
            dcf_qualified = stage_2.get('dcf_qualified', 0)
            st.metric(
                label="DCF Qualified",
                value=f"{dcf_qualified:,}"
            )

        with col4:
            buy_count = stage_5.get('buy_count', 0)
            st.metric(
                label="BUY Recommendations",
                value=f"{buy_count:,}"
            )

        # Progress visualization for final pipeline
        if stage_1 and stage_2:
            progress_data = {
                'Stage': ['Cash Flow Qualified', 'DCF Analyzed', 'DCF Qualified', 'BUY Recommendations'],
                'Count': [qualified, dcf_analyzed, dcf_qualified, buy_count]
            }

            fig = px.funnel(
                progress_data,
                x='Count',
                y='Stage',
                title="Final Investment Pipeline Progress"
            )
            st.plotly_chart(fig, use_container_width=True)

    else:
        # Legacy production analysis results
        summary = results.get('summary_statistics', {})

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric(
                label="Companies Analyzed",
                value=f"{summary.get('total_companies_analyzed', 0):,}"
            )

        with col2:
            pass_rate = summary.get('cashflow_prescreen_pass_rate', 0)
            st.metric(
                label="Cash Flow Pass Rate",
                value=f"{pass_rate:.1f}%"
            )

        with col3:
            passed = summary.get('companies_passed_prescreen', 0)
            st.metric(
                label="Qualified Companies",
                value=f"{passed:,}"
            )

def create_investment_recommendations():
    """Create investment recommendations section"""
    st.header("🎯 Investment Recommendations")

    results = load_analysis_results()

    if results is None:
        st.info("Run analysis to see investment recommendations")
        return

    source = results.get('_source', 'unknown')

    if source == 'final_pipeline':
        # Final pipeline recommendations
        final_recs = results.get('stage_5_final_recommendations', {})
        summary_stats = final_recs.get('summary_statistics', {})

        # Summary metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            strong_buy_count = summary_stats.get('strong_buy_count', 0)
            st.metric(
                label="🚀 Strong Buy",
                value=strong_buy_count
            )

        with col2:
            buy_count = summary_stats.get('buy_count', 0)
            st.metric(
                label="📈 Buy",
                value=buy_count
            )

        with col3:
            hold_count = summary_stats.get('hold_count', 0)
            st.metric(
                label="📊 Hold",
                value=hold_count
            )

        with col4:
            portfolio_count = summary_stats.get('portfolio_companies', 0)
            st.metric(
                label="💼 Portfolio",
                value=portfolio_count
            )

        # Show portfolio allocation
        portfolio_allocation = final_recs.get('portfolio_allocation', {})
        if portfolio_allocation:
            st.subheader("💼 Portfolio Allocation")

            portfolio_data = []
            for ticker, data in portfolio_allocation.items():
                portfolio_data.append({
                    'Ticker': ticker,
                    'Weight': data.get('weight', 0),
                    'Enhanced Score': data.get('enhanced_score', 0),
                    'DCF Score': data.get('dcf_score', 0),
                    'Sector': data.get('sector', 'Unknown'),
                    'Recommendation': data.get('recommendation', 'Unknown')
                })

            if portfolio_data:
                df_portfolio = pd.DataFrame(portfolio_data)
                df_portfolio = df_portfolio.sort_values('Weight', ascending=False)
                df_portfolio['Weight'] = df_portfolio['Weight'].round(1)
                df_portfolio['Enhanced Score'] = df_portfolio['Enhanced Score'].round(1)
                df_portfolio['DCF Score'] = df_portfolio['DCF Score'].round(1)

                st.dataframe(df_portfolio, use_container_width=True)

        # Show all recommendations
        all_recommendations = []

        for rec in final_recs.get('strong_buy', []):
            all_recommendations.append({**rec, 'recommendation_type': 'STRONG_BUY'})

        for rec in final_recs.get('buy', []):
            all_recommendations.append({**rec, 'recommendation_type': 'BUY'})

        for rec in final_recs.get('hold', [])[:20]:  # Top 20 holds
            all_recommendations.append({**rec, 'recommendation_type': 'HOLD'})

        if all_recommendations:
            st.subheader("📈 All Investment Recommendations")

            # Sort by enhanced score
            all_recommendations.sort(key=lambda x: x.get('enhanced_score', 0), reverse=True)

            df_all = pd.DataFrame(all_recommendations[:30])  # Top 30

            if not df_all.empty:
                df_all['Enhanced Score'] = df_all['enhanced_score'].round(1)
                df_all['DCF Score'] = df_all['dcf_score'].round(1)
                df_all['Ticker'] = df_all['ticker']
                df_all['Sector'] = df_all['sector']
                df_all['Recommendation'] = df_all['recommendation_type']
                df_all['DCF Passed'] = df_all['dcf_passed'].apply(lambda x: '✅' if x else '❌')

                st.dataframe(
                    df_all[['Ticker', 'Sector', 'Enhanced Score', 'DCF Score', 'DCF Passed', 'Recommendation']],
                    use_container_width=True
                )

    else:
        # Legacy recommendations format
        recommendations = results.get('stage_4_investment_recommendations', {})
        top_picks = recommendations.get('top_picks', {})

        # Summary metrics
        col1, col2, col3 = st.columns(3)

        with col1:
            strong_buy_count = len(top_picks.get('strong_buy', []))
            st.metric(
                label="🚀 Strong Buy",
                value=strong_buy_count
            )

        with col2:
            buy_count = len(top_picks.get('buy', []))
            st.metric(
                label="📈 Buy",
                value=buy_count
            )

        with col3:
            hold_count = len(top_picks.get('hold', []))
            st.metric(
                label="📊 Hold",
                value=hold_count
            )

        # Legacy recommendations display
        if strong_buy_count > 0 or buy_count > 0:
            st.subheader("Top Investment Opportunities")

            all_recommendations = []
            for rec in top_picks.get('strong_buy', []):
                all_recommendations.append({**rec, 'recommendation': 'STRONG_BUY'})
            for rec in top_picks.get('buy', []):
                all_recommendations.append({**rec, 'recommendation': 'BUY'})
            for rec in top_picks.get('hold', [])[:10]:
                all_recommendations.append({**rec, 'recommendation': 'HOLD'})

            all_recommendations.sort(key=lambda x: x.get('score', 0), reverse=True)
            df = pd.DataFrame(all_recommendations[:20])

            if not df.empty:
                df['Score'] = df['score'].round(1)
                df['Consistency'] = df['consistency_score'].round(1)
                df['Qualitative'] = df['qualitative_score'].round(1)
                df['Ticker'] = df['ticker']
                df['Sector'] = df['sector'].str.title()
                df['Recommendation'] = df['recommendation']

                st.dataframe(
                    df[['Ticker', 'Sector', 'Score', 'Consistency', 'Qualitative', 'Recommendation']],
                    use_container_width=True
                )

def create_sector_analysis():
    """Create sector analysis section"""
    st.header("🏭 Sector Analysis")

    results = load_analysis_results()

    if results is None:
        st.info("Run analysis to see sector breakdown")
        return

    sector_results = results.get('stage_3_sector_analysis', {})
    sector_discovery = sector_results.get('sector_discovery', {})
    sectors_found = sector_discovery.get('sectors_found', {})

    if not sectors_found:
        st.warning("No sector data available")
        return

    # Sector distribution
    sector_data = []
    for sector, companies in sectors_found.items():
        sector_data.append({
            'Sector': sector.replace('_', ' ').title(),
            'Companies': len(companies),
            'Company_List': companies
        })

    df_sectors = pd.DataFrame(sector_data)
    df_sectors = df_sectors.sort_values('Companies', ascending=False)

    # Sector distribution chart
    fig = px.bar(
        df_sectors.head(10),
        x='Sector',
        y='Companies',
        title="Top 10 Sectors by Company Count"
    )
    fig.update_layout(xaxis_tickangle=45)
    st.plotly_chart(fig, use_container_width=True)

    # Sector leaders
    recommendations = results.get('stage_4_investment_recommendations', {})
    sector_leaders = recommendations.get('sector_leaders', {})

    if sector_leaders:
        st.subheader("Sector Leaders")

        # Create sector leaders table
        leaders_data = []
        for sector, leaders in sector_leaders.items():
            for leader in leaders[:3]:  # Top 3 per sector
                leaders_data.append({
                    'Sector': sector.replace('_', ' ').title(),
                    'Ticker': leader['ticker'],
                    'Score': leader['score'],
                    'Recommendation': leader['recommendation']
                })

        if leaders_data:
            df_leaders = pd.DataFrame(leaders_data)
            df_leaders['Score'] = df_leaders['Score'].round(1)

            st.dataframe(df_leaders, use_container_width=True)

def create_company_analysis():
    """Create company analysis section"""
    st.header("🔍 Company Analysis")

    # Load analysis results to show cash flow passed companies
    results = load_analysis_results()

    if results and results.get('_source') == 'final_pipeline':
        # Show cash flow passed companies
        stage_1 = results.get('stage_1_qualified_companies', {})
        qualified_companies = stage_1.get('qualified_companies', [])

        if qualified_companies:
            st.subheader("💰 Cash Flow Qualified Companies")
            st.info(f"📊 {len(qualified_companies)} companies passed comprehensive 10-year cash flow analysis")

            # Create tabs for different views
            tab1, tab2, tab3 = st.tabs(["📋 Company List", "🔍 Detailed Analysis", "📊 DCF Results"])

            with tab1:
                # Display qualified companies in a nice format
                st.write("**Companies that passed 10-year cash flow screening:**")

                # Create columns for better display
                cols = st.columns(4)
                for i, ticker in enumerate(qualified_companies):
                    col_idx = i % 4
                    with cols[col_idx]:
                        st.write(f"• {ticker}")

                # Download option
                if st.button("📥 Download Cash Flow Qualified Companies"):
                    qualified_df = pd.DataFrame({'Ticker': qualified_companies})
                    csv = qualified_df.to_csv(index=False)
                    st.download_button(
                        label="Download CSV",
                        data=csv,
                        file_name="cashflow_qualified_companies.csv",
                        mime="text/csv"
                    )

            with tab2:
                # Company selector for detailed analysis
                selected_ticker = st.selectbox(
                    "Select a company for detailed analysis:",
                    options=qualified_companies,
                    index=0
                )

                if selected_ticker:
                    # Load company data
                    data_loader = ScreenerDataLoader('../screener_data_collector/data')
                    company_data = data_loader.load_company_data(selected_ticker)

                    if company_data:
                        # Display company overview
                        overview = company_data.get('overview', {})
                        st.subheader(f"📊 {overview.get('name', selected_ticker)} Analysis")

                        # Key metrics
                        col1, col2, col3, col4 = st.columns(4)

                        with col1:
                            market_cap = overview.get('market_cap', 'N/A')
                            st.metric(label="Market Cap", value=market_cap)

                        with col2:
                            current_price = overview.get('current_price', 'N/A')
                            st.metric(label="Current Price", value=current_price)

                        with col3:
                            pe_ratio = overview.get('pe_ratio', 'N/A')
                            st.metric(label="P/E Ratio", value=pe_ratio)

                        with col4:
                            # Show cash flow qualification status
                            st.metric(label="Cash Flow Status", value="✅ QUALIFIED")

                        # Show cash flow analysis details
                        st.subheader("💰 Cash Flow Analysis Details")

                        # Run cash flow analysis
                        from models.cashflow_prescreener import CashFlowPreScreener
                        prescreener = CashFlowPreScreener()
                        cf_result = prescreener.prescreen_company(company_data)

                        if cf_result:
                            col1, col2, col3 = st.columns(3)

                            with col1:
                                st.metric(
                                    label="Cash Flow Score",
                                    value=f"{cf_result.get('cash_flow_score', 0):.1f}/100"
                                )

                            with col2:
                                st.metric(
                                    label="Analysis Years",
                                    value=cf_result.get('analysis', {}).get('operating_cash_flow', {}).get('total_years', 'N/A')
                                )

                            with col3:
                                passed = cf_result.get('passed_prescreen', False)
                                st.metric(
                                    label="Pre-screening",
                                    value="✅ PASSED" if passed else "❌ FAILED"
                                )

                            # Show detailed cash flow metrics
                            analysis = cf_result.get('analysis', {})

                            if 'operating_cash_flow' in analysis:
                                ocf_analysis = analysis['operating_cash_flow']
                                st.write("**📈 Operating Cash Flow (10-Year Analysis):**")

                                ocf_col1, ocf_col2, ocf_col3 = st.columns(3)

                                with ocf_col1:
                                    st.write(f"• Positive Years: {ocf_analysis.get('positive_years', 0)}")
                                    st.write(f"• Positive %: {ocf_analysis.get('positive_percentage', 0):.1f}%")

                                with ocf_col2:
                                    st.write(f"• Growth Consistency: {ocf_analysis.get('growth_consistency', 0):.1f}%")
                                    st.write(f"• Average OCF: ₹{ocf_analysis.get('average_ocf', 0):,.0f} Cr")

                                with ocf_col3:
                                    st.write(f"• Latest OCF: ₹{ocf_analysis.get('latest_ocf', 0):,.0f} Cr")
                                    st.write(f"• Trend Slope: {ocf_analysis.get('trend_slope', 0):.2f}")

                            if 'free_cash_flow' in analysis:
                                fcf_analysis = analysis['free_cash_flow']
                                st.write("**💰 Free Cash Flow (10-Year Analysis):**")

                                fcf_col1, fcf_col2, fcf_col3 = st.columns(3)

                                with fcf_col1:
                                    st.write(f"• Positive Years: {fcf_analysis.get('positive_years', 0)}")
                                    st.write(f"• Positive %: {fcf_analysis.get('positive_percentage', 0):.1f}%")

                                with fcf_col2:
                                    st.write(f"• Growth Consistency: {fcf_analysis.get('growth_consistency', 0):.1f}%")
                                    st.write(f"• Average FCF: ₹{fcf_analysis.get('average_fcf', 0):,.0f} Cr")

                                with fcf_col3:
                                    st.write(f"• Latest FCF: ₹{fcf_analysis.get('latest_fcf', 0):,.0f} Cr")

                        # Financial data tabs
                        fin_tab1, fin_tab2, fin_tab3 = st.tabs(["📈 Profit & Loss", "📊 Balance Sheet", "💰 Cash Flow"])

                        with fin_tab1:
                            profit_loss = company_data.get('profit_loss', {})
                            if profit_loss:
                                # Show in a more readable format
                                pl_clean = {k: v for k, v in profit_loss.items() if k not in ['units', 'notes']}
                                st.json(pl_clean)
                            else:
                                st.info("No profit & loss data available")

                        with fin_tab2:
                            balance_sheet = company_data.get('balance_sheet', {})
                            if balance_sheet:
                                bs_clean = {k: v for k, v in balance_sheet.items() if k not in ['units', 'notes']}
                                st.json(bs_clean)
                            else:
                                st.info("No balance sheet data available")

                        with fin_tab3:
                            cash_flow = company_data.get('cash_flow', {})
                            if cash_flow:
                                cf_clean = {k: v for k, v in cash_flow.items() if k not in ['units', 'notes']}
                                st.json(cf_clean)
                            else:
                                st.info("No cash flow data available")

                    else:
                        st.error(f"No data available for {selected_ticker}")

            with tab3:
                # Show DCF results for qualified companies
                stage_2 = results.get('stage_2_dcf_analysis', {})
                dcf_results = stage_2.get('results', {})

                if dcf_results:
                    st.subheader("💰 DCF Analysis Results")

                    # Filter for companies with DCF results
                    dcf_companies = [ticker for ticker in qualified_companies if ticker in dcf_results]

                    if dcf_companies:
                        # DCF company selector
                        selected_dcf_ticker = st.selectbox(
                            "Select a company for DCF results:",
                            options=dcf_companies,
                            index=0,
                            key="dcf_selector"
                        )

                        if selected_dcf_ticker and selected_dcf_ticker in dcf_results:
                            dcf_result = dcf_results[selected_dcf_ticker]

                            # Display DCF results
                            st.subheader(f"📊 DCF Results for {selected_dcf_ticker}")

                            # Key DCF metrics
                            dcf_col1, dcf_col2, dcf_col3, dcf_col4 = st.columns(4)

                            with dcf_col1:
                                dcf_score = dcf_result.get('dcf_score', 0)
                                st.metric(label="DCF Score", value=f"{dcf_score:.1f}/100")

                            with dcf_col2:
                                dcf_passed = dcf_result.get('dcf_passed', False)
                                st.metric(label="DCF Status", value="✅ PASSED" if dcf_passed else "❌ FAILED")

                            with dcf_col3:
                                recommendation = dcf_result.get('recommendation', 'Unknown')
                                st.metric(label="Recommendation", value=recommendation)

                            with dcf_col4:
                                dcf_method = dcf_result.get('dcf_method', 'Unknown')
                                st.metric(label="DCF Method", value=dcf_method)

                            # Show valuation details if available
                            if 'intrinsic_value_per_share' in dcf_result:
                                st.subheader("💎 Valuation Details")

                                val_col1, val_col2, val_col3 = st.columns(3)

                                with val_col1:
                                    intrinsic_value = dcf_result.get('intrinsic_value_per_share', 0)
                                    st.metric(label="Intrinsic Value", value=f"₹{intrinsic_value:.2f}")

                                with val_col2:
                                    current_price = dcf_result.get('current_price', 0)
                                    st.metric(label="Current Price", value=f"₹{current_price:.2f}")

                                with val_col3:
                                    margin_of_safety = dcf_result.get('margin_of_safety', 0)
                                    st.metric(label="Margin of Safety", value=f"{margin_of_safety:.1f}%")

                                # Show valuation signal
                                valuation_signal = dcf_result.get('valuation_signal', 'Unknown')
                                if margin_of_safety > 0:
                                    st.success(f"🎯 {valuation_signal}")
                                else:
                                    st.warning(f"⚠️ {valuation_signal}")

                    else:
                        st.info("No DCF results available for qualified companies")

                else:
                    st.info("DCF analysis not yet completed")

        else:
            st.warning("No companies have passed cash flow screening yet")

    else:
        # Fallback to original company selector
        st.info("Run the final investment pipeline to see cash flow qualified companies")

        data_loader = ScreenerDataLoader('../screener_data_collector/data')
        tickers = data_loader.get_all_tickers()

        selected_ticker = st.selectbox(
            "Select any company for analysis:",
            options=tickers,
            index=0 if tickers else None
        )

        if selected_ticker:
            company_data = data_loader.load_company_data(selected_ticker)

            if company_data:
                overview = company_data.get('overview', {})
                st.subheader(f"📊 {overview.get('name', selected_ticker)} Analysis")

                col1, col2, col3 = st.columns(3)

                with col1:
                    market_cap = overview.get('market_cap', 'N/A')
                    st.metric(label="Market Cap", value=market_cap)

                with col2:
                    current_price = overview.get('current_price', 'N/A')
                    st.metric(label="Current Price", value=current_price)

                with col3:
                    pe_ratio = overview.get('pe_ratio', 'N/A')
                    st.metric(label="P/E Ratio", value=pe_ratio)

            else:
                st.error(f"No data available for {selected_ticker}")

def create_system_controls():
    """Create system controls section"""
    st.header("⚙️ System Controls")

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Quick Actions")

        if st.button("🧪 Run Test Analysis", help="Test system with 20 companies"):
            with st.spinner("Running test analysis..."):
                try:
                    import subprocess
                    result = subprocess.run(
                        ["python", "test_enhanced_system.py"],
                        capture_output=True,
                        text=True,
                        cwd="/home/<USER>/Trading/fundamental_analysis_tool"
                    )

                    if result.returncode == 0:
                        st.success("✅ Test analysis completed successfully!")
                        st.text(result.stdout[-1000:])  # Show last 1000 chars
                    else:
                        st.error("❌ Test analysis failed")
                        st.text(result.stderr)

                except Exception as e:
                    st.error(f"Error running test: {e}")

        if st.button("🚀 Run Full Production Analysis", help="Analyze all 4903 companies"):
            st.warning("⚠️ This will analyze all 4903 companies and may take several hours.")

            if st.button("Confirm - Start Full Analysis"):
                with st.spinner("Starting production analysis..."):
                    try:
                        import subprocess
                        # Start the process in background
                        subprocess.Popen(
                            ["python", "production_analysis.py"],
                            cwd="/home/<USER>/Trading/fundamental_analysis_tool"
                        )
                        st.success("🚀 Production analysis started! Check the terminal for progress.")
                        st.info("Refresh this dashboard periodically to see updated results.")

                    except Exception as e:
                        st.error(f"Error starting analysis: {e}")

    with col2:
        st.subheader("System Information")

        # Show system components
        components = [
            "✅ Cash Flow Pre-screener",
            "✅ Sector Analyzer",
            "✅ Consistency Analyzer",
            "✅ Qualitative Analyzer",
            "✅ Production Pipeline"
        ]

        for component in components:
            st.text(component)

        st.subheader("Data Status")
        system_status = load_system_status()

        if system_status['status'] == 'healthy':
            st.success(f"✅ Data loaded: {system_status['total_companies']:,} companies")
        else:
            st.error(f"❌ Data error: {system_status.get('error', 'Unknown error')}")

    # Quick Analysis Runner
    st.subheader("🚀 Quick Analysis Runner")

    try:
        from modular_analysis_system import ModularAnalysisSystem
        system = ModularAnalysisSystem()

        col1, col2, col3 = st.columns(3)

        with col1:
            st.write("**🔍 Cash Flow Analysis**")
            if st.button("Run on All Companies", key="quick_cf_all"):
                with st.spinner("Running cash flow analysis on all companies..."):
                    try:
                        result = system.run_analysis('CASHFLOW', 'ALL_TICKERS')
                        if result.get('status') == 'success':
                            st.success("✅ Cash flow analysis completed!")
                            st.rerun()
                        else:
                            st.error(f"❌ Error: {result.get('error', 'Unknown error')}")
                    except Exception as e:
                        st.error(f"❌ Error: {e}")

        with col2:
            st.write("**🎯 DCF Analysis**")
            if st.button("Run on Cash Flow Qualified", key="quick_dcf_cf"):
                with st.spinner("Running DCF on cash flow qualified..."):
                    try:
                        result = system.run_analysis('DCF', 'CASHFLOW_QUALIFIED')
                        if result.get('status') == 'success':
                            st.success("✅ DCF analysis completed!")
                            st.rerun()
                        else:
                            st.error(f"❌ Error: {result.get('error', 'Unknown error')}")
                    except Exception as e:
                        st.error(f"❌ Error: {e}")

        with col3:
            st.write("**📊 Sample Testing**")
            if st.button("DCF Sample (50 companies)", key="quick_dcf_sample"):
                with st.spinner("Running DCF sample..."):
                    try:
                        result = system.run_analysis('DCF', 'ALL_TICKERS', sample_size=50)
                        if result.get('status') == 'success':
                            st.success("✅ Sample DCF completed!")
                            st.rerun()
                        else:
                            st.error(f"❌ Error: {result.get('error', 'Unknown error')}")
                    except Exception as e:
                        st.error(f"❌ Error: {e}")

    except ImportError:
        st.warning("⚠️ Modular analysis system not available")

def create_modular_results():
    """Create professional modular analysis section"""
    st.header("🔧 Professional Cross-Analysis System")

    # Import professional modular system
    try:
        from professional_modular_system import ProfessionalModularSystem
        system = ProfessionalModularSystem()
    except ImportError:
        st.error("❌ Professional modular system not available")
        return

    # Create tabs for different functions
    tab1, tab2, tab3 = st.tabs(["🚀 Cross-Analysis Runner", "📊 Saved Results", "ℹ️ System Info"])

    with tab1:
        st.subheader("🚀 Professional Cross-Analysis Runner")
        st.write("Run any analysis type on any ticker set with smart duplicate checking")

        col1, col2 = st.columns(2)

        with col1:
            # Analysis type selection
            analysis_types = system.analysis_types
            selected_analysis = st.selectbox(
                "Analysis Type",
                list(analysis_types.keys()),
                format_func=lambda x: f"{analysis_types[x]['name']}"
            )
            st.write(f"*{analysis_types[selected_analysis]['description']}*")

        with col2:
            # Ticker set selection
            ticker_sets = system.ticker_sets
            selected_set = st.selectbox(
                "Ticker Set",
                list(ticker_sets.keys()),
                format_func=lambda x: f"{ticker_sets[x]['name']}"
            )
            st.write(f"*{ticker_sets[selected_set]['description']}*")

        # Show ticker set info
        set_info = system.get_ticker_set_info(selected_set)
        if 'error' not in set_info:
            st.info(f"📊 {set_info['ticker_count']:,} companies available")
            if set_info['sample_tickers']:
                st.write(f"**Sample tickers**: {', '.join(set_info['sample_tickers'])}")
        else:
            st.warning(f"⚠️ {set_info['error']}")

        # Additional options
        col1, col2 = st.columns(2)

        with col1:
            sample_size = st.number_input("Sample Size (0 = all)", 0, 1000, 0)

        with col2:
            if selected_set == 'CUSTOM_LIST':
                custom_tickers = st.text_input("Custom Tickers (comma-separated)", "TCS,RELIANCE,SBIN")
            else:
                custom_tickers = None

        # Generate analysis ID and check for duplicates
        custom_list = None
        if custom_tickers:
            custom_list = [t.strip().upper() for t in custom_tickers.split(',')]

        analysis_id = system.generate_analysis_id(
            selected_analysis, selected_set, custom_list,
            sample_size if sample_size > 0 else None
        )

        # Check if analysis already exists
        existing_analysis = system.check_existing_analysis(analysis_id)

        if existing_analysis:
            st.warning(f"⚠️ Analysis already exists: {analysis_id}")
            st.write(f"**Created**: {existing_analysis['timestamp']}")

            col1, col2 = st.columns(2)
            with col1:
                if st.button("📊 View Existing Results", key="view_existing"):
                    st.session_state['view_analysis'] = analysis_id
                    st.rerun()

            with col2:
                force_rerun = st.checkbox("🔄 Force Re-run Analysis")
        else:
            force_rerun = True

        # Run analysis button
        if force_rerun:
            if st.button(f"🚀 Run {selected_analysis} on {selected_set}", key="run_professional"):
                with st.spinner(f"Running {selected_analysis} analysis on {selected_set}..."):
                    try:
                        result = system.run_analysis(
                            selected_analysis, selected_set,
                            custom_list, sample_size if sample_size > 0 else None
                        )

                        if result['status'] == 'success':
                            st.success("✅ Analysis completed successfully!")
                            st.write(f"**Analysis ID**: {result['analysis_id']}")
                            st.rerun()
                        elif result['status'] == 'exists':
                            st.info(f"ℹ️ Analysis already exists: {result['analysis_id']}")
                        elif result['status'] == 'pending':
                            st.warning(f"⏳ {result['message']}")
                        else:
                            st.error(f"❌ Error: {result['error']}")

                    except Exception as e:
                        st.error(f"❌ Error running analysis: {e}")

    with tab2:
        st.subheader("📊 Professional Analysis Results")

        # List all saved analyses
        saved_analyses = system.list_available_analyses()

        if not saved_analyses:
            st.info("No saved analyses found. Run some cross-analysis to see results here.")

            # Show available ticker sets
            st.write("**🎯 Available Ticker Sets:**")
            for set_name in system.ticker_sets.keys():
                info = system.get_ticker_set_info(set_name)
                st.write(f"- **{info['name']}**: {info['ticker_count']:,} companies")
            return

        # Display saved analyses
        st.write(f"**Found {len(saved_analyses)} professional analyses:**")

        for analysis in saved_analyses:
            with st.expander(f"📊 {analysis['analysis_id']} ({analysis['total_companies']:,} companies)"):
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.write(f"**Analysis**: {analysis['analysis_type']}")
                    st.write(f"**Ticker Set**: {analysis['ticker_set']}")

                with col2:
                    st.write(f"**Companies**: {analysis['total_companies']:,}")
                    st.write(f"**Created**: {analysis['timestamp'][:19]}")

                with col3:
                    if st.button(f"📊 View Results", key=f"view_{analysis['analysis_id']}"):
                        st.session_state[f'view_analysis_{analysis["analysis_id"]}'] = True
                        st.rerun()

                    if st.download_button(
                        f"📥 Download",
                        key=f"download_{analysis['analysis_id']}",
                        data=open(analysis['file_path'], 'r').read(),
                        file_name=analysis['file_name'],
                        mime='application/json'
                    ):
                        pass

        # Display detailed view if requested
        for analysis in saved_analyses:
            if st.session_state.get(f'view_analysis_{analysis["analysis_id"]}', False):
                st.markdown("---")
                display_professional_analysis_results(analysis)

                if st.button("🔙 Back to Results List", key=f"back_{analysis['analysis_id']}"):
                    st.session_state[f'view_analysis_{analysis["analysis_id"]}'] = False
                    st.rerun()

    with tab3:
        st.subheader("ℹ️ Professional System Information")

        # Available analysis types
        st.write("**🔧 Available Analysis Types:**")
        for analysis_type, config in system.analysis_types.items():
            status = "✅ Available" if config['script'] else "⏳ Coming Soon"
            st.write(f"- **{config['name']}**: {config['description']} ({status})")

        # Available ticker sets
        st.write("**📊 Available Ticker Sets:**")
        for set_name in system.ticker_sets.keys():
            info = system.get_ticker_set_info(set_name)
            st.write(f"- **{info['name']}**: {info['ticker_count']:,} companies")

        st.write("**📋 CLI Commands:**")
        st.code("""
# List available ticker sets
python professional_modular_system.py --list-sets

# List saved analyses
python professional_modular_system.py --list-analyses

# Get ticker set info
python professional_modular_system.py --info CASHFLOW_QUALIFIED

# Run cross-analysis
python professional_modular_system.py --run DCF:CASHFLOW_QUALIFIED --sample 10
        """)

def get_investment_implications(tier1_pass, tier2_pass, tier3_pass, tier4_pass, tier5_pass):
    """Determine investment implications based on tier performance"""

    # Count passed tiers
    passed_tiers = sum([tier1_pass, tier2_pass, tier3_pass, tier4_pass, tier5_pass])

    # All 5 tiers passed (Rare)
    if passed_tiers == 5:
        return "🏆 Warren Buffett Quality", "Highest quality, low risk, steady returns", "🟢 BUY"

    # 4 tiers passed
    elif passed_tiers == 4:
        if not tier2_pass:
            return "💰 Dividend Aristocrat", "Mature, stable, good for income", "🟢 BUY"
        elif not tier3_pass:
            return "🚀 Quality Growth", "Strong fundamentals, growth premium", "🟡 HOLD/BUY"
        elif not tier5_pass:
            return "⚡ Turnaround Story", "Improving fundamentals, monitor closely", "🟡 WATCH"
        else:
            return "💎 High Quality", "Strong fundamentals, minor weakness", "🟢 BUY"

    # 3 tiers passed
    elif passed_tiers == 3:
        if tier1_pass and tier4_pass:
            return "💰 Cash Cow", "Strong financials & cash flow", "🟡 INCOME"
        elif tier2_pass and tier3_pass:
            return "📈 GARP Candidate", "Growth at reasonable price", "🟡 GROWTH"
        elif tier1_pass and tier3_pass:
            return "⚖️ Value Play", "Solid fundamentals, fair price", "🟡 VALUE"
        else:
            return "🔍 Mixed Signals", "Some strengths, needs analysis", "🟡 RESEARCH"

    # 2 tiers passed
    elif passed_tiers == 2:
        if tier2_pass:
            return "🎢 High Risk Growth", "Growth with concerns", "🟠 SPECULATIVE"
        elif tier1_pass and tier4_pass:
            return "🛡️ Defensive Play", "Stable but limited growth", "🟡 DEFENSIVE"
        else:
            return "⚠️ Proceed with Caution", "Multiple red flags", "🔴 AVOID"

    # 1 or 0 tiers passed
    elif passed_tiers == 1:
        if tier2_pass:
            return "🎰 Lottery Ticket", "High growth, high risk", "🔴 SPECULATIVE"
        else:
            return "❌ Poor Quality", "Fundamental issues", "🔴 AVOID"

    else:
        return "🚫 Investment Grade Fail", "Multiple fundamental failures", "🔴 STRONG AVOID"

def display_professional_analysis_results(analysis):
    """Display professional analysis results like standalone sections"""
    st.subheader(f"📊 {analysis['analysis_id']} - Detailed Results")

    try:
        # Load the analysis data
        with open(analysis['file_path'], 'r') as f:
            data = json.load(f)

        metadata = data.get('metadata', {})
        company_results = data.get('company_results', {})
        summary_stats = data.get('summary_statistics', {})

        # For fundamental analysis, use 'summary' instead of 'summary_statistics'
        if analysis['analysis_type'] == 'FUNDAMENTAL':
            summary_stats = data.get('summary', {})

        # Display metadata
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Analysis Type", metadata.get('analysis_type', 'Unknown'))
        with col2:
            st.metric("Ticker Set", metadata.get('ticker_set', 'Unknown'))
        with col3:
            st.metric("Total Companies", f"{metadata.get('total_companies', 0):,}")
        with col4:
            st.metric("Created", metadata.get('timestamp', 'Unknown')[:19])

        # Display summary statistics
        if summary_stats:
            st.subheader("📈 Summary Statistics")

            if analysis['analysis_type'] == 'CASHFLOW':
                # Cash flow analysis display
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    total = summary_stats.get('total_companies', 0)
                    st.metric("Total Companies", f"{total:,}")

                with col2:
                    qualified = summary_stats.get('qualified_companies', 0)
                    st.metric("Cash Flow Qualified", f"{qualified:,}")

                with col3:
                    rate = summary_stats.get('qualification_rate', 0)
                    st.metric("Qualification Rate", f"{rate:.1f}%")

                with col4:
                    failed = summary_stats.get('failed_companies', 0)
                    st.metric("Failed Analysis", f"{failed:,}")

                # Display qualified companies
                if company_results:
                    st.subheader("💰 Cash Flow Qualified Companies")

                    qualified_companies = []
                    for ticker, result in company_results.items():
                        if result.get('qualified', False):
                            qualified_companies.append({
                                'Ticker': ticker,
                                'Cash Flow Score': result.get('cash_flow_score', result.get('score', 0)),
                                'Reasons Passed': ', '.join(result.get('reasons_passed', [])),
                            })

                    if qualified_companies:
                        df_qualified = pd.DataFrame(qualified_companies)
                        df_qualified = df_qualified.sort_values('Cash Flow Score', ascending=False)
                        df_qualified['Cash Flow Score'] = df_qualified['Cash Flow Score'].round(1)

                        # Add filters
                        col1, col2 = st.columns(2)
                        with col1:
                            min_score = st.slider("Minimum Cash Flow Score", 0.0, 100.0, 0.0, 5.0, key=f"cf_score_{analysis['analysis_id']}")
                        with col2:
                            search_ticker = st.text_input("Search Ticker", "", key=f"cf_search_{analysis['analysis_id']}").upper()

                        # Apply filters
                        filtered_df = df_qualified[df_qualified['Cash Flow Score'] >= min_score]
                        if search_ticker:
                            filtered_df = filtered_df[filtered_df['Ticker'].str.contains(search_ticker)]

                        st.dataframe(filtered_df, use_container_width=True)
                        st.info(f"Showing {len(filtered_df)} of {len(qualified_companies)} qualified companies")

            elif analysis['analysis_type'] == 'DCF':
                # DCF analysis display
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    total = summary_stats.get('total_analyzed', 0)
                    st.metric("Companies Analyzed", f"{total:,}")

                with col2:
                    successful = summary_stats.get('successful_analyses', 0)
                    st.metric("Successful DCF", f"{successful:,}")

                with col3:
                    buffett_approved = summary_stats.get('buffett_approved', 0)
                    st.metric("Buffett Approved", f"{buffett_approved:,}")

                with col4:
                    success_rate = summary_stats.get('success_rate', 0)
                    st.metric("Success Rate", f"{success_rate:.1f}%")

                # Valuation distribution
                valuation_dist = data.get('valuation_distribution', {})
                if valuation_dist:
                    st.subheader("💰 Valuation Distribution")

                    val_data = []
                    for category, count in valuation_dist.items():
                        val_data.append({
                            'Category': category.replace('_', ' ').title(),
                            'Count': count
                        })

                    df_val = pd.DataFrame(val_data)

                    fig = px.pie(
                        df_val,
                        values='Count',
                        names='Category',
                        title="Company Valuation Distribution"
                    )
                    st.plotly_chart(fig, use_container_width=True)

                # Display DCF companies
                if company_results:
                    st.subheader("🎯 DCF Analysis Results by Company")

                    dcf_companies = []
                    for ticker, result in company_results.items():
                        enterprise_value = result.get('enterprise_value', 0)
                        per_share_value = result.get('per_share_value', 0)
                        current_price = result.get('current_price', 0)
                        margin_of_safety = result.get('margin_of_safety', 0)

                        # Determine investment category
                        if margin_of_safety > 50:
                            investment_category = "🔥 High Opportunity"
                        elif margin_of_safety > 20:
                            investment_category = "💰 Good Value"
                        elif margin_of_safety > -20:
                            investment_category = "⚖️ Fair Value"
                        elif margin_of_safety > -50:
                            investment_category = "⚠️ Overvalued"
                        else:
                            investment_category = "❌ Avoid"

                        dcf_companies.append({
                            'Ticker': ticker,
                            'Investment Category': investment_category,
                            'DCF Value (₹)': per_share_value,
                            'Current Price (₹)': current_price,
                            'Margin of Safety (%)': margin_of_safety,
                            'Enterprise Value (Cr)': enterprise_value,
                            'Buffett Score': result.get('buffett_score', 0),
                            'Buffett Approved': result.get('buffett_approved', False)
                        })

                    if dcf_companies:
                        df_dcf = pd.DataFrame(dcf_companies)

                        # Add filters
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            min_enterprise = st.number_input("Min Enterprise Value (Cr)", 0, 10000000, 0, key=f"dcf_enterprise_{analysis['analysis_id']}")
                        with col2:
                            valuation_filter = st.selectbox(
                                "Valuation Filter",
                                ["All", "Severely Undervalued (>50%)", "Undervalued (20-50%)",
                                 "Fairly Valued (-20% to +20%)", "Overvalued (20-50%)",
                                 "Severely Overvalued (>50%)", "Buffett Approved Only"],
                                key=f"dcf_filter_{analysis['analysis_id']}"
                            )
                        with col3:
                            search_ticker = st.text_input("Search Ticker", "", key=f"dcf_search_{analysis['analysis_id']}").upper()

                        # Apply filters
                        filtered_df = df_dcf[df_dcf['Enterprise Value (Cr)'] >= min_enterprise]

                        if valuation_filter == "Severely Undervalued (>50%)":
                            filtered_df = filtered_df[filtered_df['Margin of Safety (%)'] > 50]
                        elif valuation_filter == "Undervalued (20-50%)":
                            filtered_df = filtered_df[(filtered_df['Margin of Safety (%)'] > 20) & (filtered_df['Margin of Safety (%)'] <= 50)]
                        elif valuation_filter == "Fairly Valued (-20% to +20%)":
                            filtered_df = filtered_df[(filtered_df['Margin of Safety (%)'] >= -20) & (filtered_df['Margin of Safety (%)'] <= 20)]
                        elif valuation_filter == "Overvalued (20-50%)":
                            filtered_df = filtered_df[(filtered_df['Margin of Safety (%)'] >= -50) & (filtered_df['Margin of Safety (%)'] < -20)]
                        elif valuation_filter == "Severely Overvalued (>50%)":
                            filtered_df = filtered_df[filtered_df['Margin of Safety (%)'] < -50]
                        elif valuation_filter == "Buffett Approved Only":
                            filtered_df = filtered_df[filtered_df['Buffett Approved'] == True]

                        if search_ticker:
                            filtered_df = filtered_df[filtered_df['Ticker'].str.contains(search_ticker)]

                        # Sort by margin of safety
                        filtered_df = filtered_df.sort_values('Margin of Safety (%)', ascending=False)

                        # Format columns
                        filtered_df['Enterprise Value (Cr)'] = filtered_df['Enterprise Value (Cr)'].round(0)
                        filtered_df['DCF Value (₹)'] = filtered_df['DCF Value (₹)'].round(2)
                        filtered_df['Current Price (₹)'] = filtered_df['Current Price (₹)'].round(2)
                        filtered_df['Margin of Safety (%)'] = filtered_df['Margin of Safety (%)'].round(1)
                        filtered_df['Buffett Score'] = filtered_df['Buffett Score'].round(1)

                        st.dataframe(filtered_df, use_container_width=True)
                        st.info(f"Showing {len(filtered_df)} of {len(dcf_companies)} companies")

            elif analysis['analysis_type'] == 'FUNDAMENTAL':
                # Fundamental analysis display
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    total = summary_stats.get('total_analyzed', 0)
                    st.metric("Companies Analyzed", f"{total:,}")

                with col2:
                    all_tiers = summary_stats.get('all_tiers_pass', 0)
                    st.metric("All Tiers Pass", f"{all_tiers:,}")

                with col3:
                    qualified = len(summary_stats.get('qualified_companies', []))
                    st.metric("Qualified Companies", f"{qualified:,}")

                with col4:
                    rate = summary_stats.get('qualification_rate', 0)
                    st.metric("Qualification Rate", f"{rate:.1f}%")

                # Tier breakdown
                st.subheader("📊 Multi-Tier Analysis Breakdown")

                tier_data = []
                for tier_num in range(1, 6):
                    tier_key = f'tier{tier_num}_pass'
                    tier_count = summary_stats.get(tier_key, 0)
                    tier_data.append({
                        'Tier': f'Tier {tier_num}',
                        'Companies Passed': tier_count,
                        'Pass Rate': f"{(tier_count/total*100):.1f}%" if total > 0 else "0%"
                    })

                df_tiers = pd.DataFrame(tier_data)

                fig = px.bar(
                    df_tiers,
                    x='Tier',
                    y='Companies Passed',
                    title="Companies Passing Each Tier",
                    text='Pass Rate'
                )
                st.plotly_chart(fig, use_container_width=True)

                # Display qualified companies
                qualified_companies = summary_stats.get('qualified_companies', [])
                if qualified_companies:
                    st.subheader("🏆 Qualified Companies (Score ≥ 70)")

                    fund_companies = []
                    for company in qualified_companies:
                        # Get tier results
                        tier1_pass = company.get('tier1_pass', False)
                        tier2_pass = company.get('tier2_pass', False)
                        tier3_pass = company.get('tier3_pass', False)
                        tier4_pass = company.get('tier4_pass', False)
                        tier5_pass = company.get('tier5_pass', False)

                        # Get investment implications
                        investment_type, description, recommendation = get_investment_implications(
                            tier1_pass, tier2_pass, tier3_pass, tier4_pass, tier5_pass
                        )

                        fund_companies.append({
                            'Ticker': company['ticker'],
                            'Investment Type': investment_type,
                            'Description': description,
                            'Recommendation': recommendation,
                            'Fundamental Score': company['fundamental_score'],
                            'Tier 1 (Financial Health)': '✅' if tier1_pass else '❌',
                            'Tier 2 (Growth)': '✅' if tier2_pass else '❌',
                            'Tier 3 (Valuation)': '✅' if tier3_pass else '❌',
                            'Tier 4 (Cash Flow)': '✅' if tier4_pass else '❌',
                            'Tier 5 (Consistency)': '✅' if tier5_pass else '❌'
                        })

                    if fund_companies:
                        df_fund = pd.DataFrame(fund_companies)

                        # Add filters
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            min_score = st.slider("Minimum Fundamental Score", 0.0, 100.0, 70.0, 5.0, key=f"fund_score_{analysis['analysis_id']}")
                        with col2:
                            investment_filter = st.selectbox(
                                "Investment Type Filter",
                                ["All", "🟢 BUY", "🟡 HOLD/BUY", "🟡 INCOME", "🟡 GROWTH", "🟡 VALUE", "🟠 SPECULATIVE", "🔴 AVOID"],
                                key=f"fund_investment_{analysis['analysis_id']}"
                            )
                        with col3:
                            search_ticker = st.text_input("Search Ticker", "", key=f"fund_search_{analysis['analysis_id']}").upper()

                        # Apply filters
                        filtered_df = df_fund[df_fund['Fundamental Score'] >= min_score]

                        if investment_filter != "All":
                            filtered_df = filtered_df[filtered_df['Recommendation'] == investment_filter]

                        if search_ticker:
                            filtered_df = filtered_df[filtered_df['Ticker'].str.contains(search_ticker)]

                        # Sort by score
                        filtered_df = filtered_df.sort_values('Fundamental Score', ascending=False)
                        filtered_df['Fundamental Score'] = filtered_df['Fundamental Score'].round(1)

                        st.dataframe(filtered_df, use_container_width=True)
                        st.info(f"Showing {len(filtered_df)} of {len(qualified_companies)} qualified companies")
                else:
                    st.info("ℹ️ No companies met the qualification criteria (Score ≥ 70)")

                    # Show companies that passed individual tiers for analysis
                    screening_results = data.get('screening_results', {})
                    if screening_results and screening_results.get('results'):
                        st.subheader("📊 Companies by Tier Performance")

                        tier_companies = []
                        for ticker, result in screening_results['results'].items():
                            if 'error' not in result:
                                # Get tier results
                                tier1_pass = result.get('tier1_pass', False)
                                tier2_pass = result.get('tier2_pass', False)
                                tier3_pass = result.get('tier3_pass', False)
                                tier4_pass = result.get('tier4_pass', False)
                                tier5_pass = result.get('tier5_pass', False)

                                # Get investment implications
                                investment_type, description, recommendation = get_investment_implications(
                                    tier1_pass, tier2_pass, tier3_pass, tier4_pass, tier5_pass
                                )

                                tier_companies.append({
                                    'Ticker': ticker,
                                    'Investment Type': investment_type,
                                    'Description': description,
                                    'Recommendation': recommendation,
                                    'Tier 1 (Financial Health)': '✅' if tier1_pass else '❌',
                                    'Tier 2 (Growth)': '✅' if tier2_pass else '❌',
                                    'Tier 3 (Valuation)': '✅' if tier3_pass else '❌',
                                    'Tier 4 (Cash Flow)': '✅' if tier4_pass else '❌',
                                    'Tier 5 (Consistency)': '✅' if tier5_pass else '❌',
                                    'Total Tiers Passed': sum([tier1_pass, tier2_pass, tier3_pass, tier4_pass, tier5_pass])
                                })

                        if tier_companies:
                            df_tiers = pd.DataFrame(tier_companies)
                            df_tiers = df_tiers.sort_values('Total Tiers Passed', ascending=False)

                            # Add filters for tier performance
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                min_tiers = st.slider("Minimum Tiers Passed", 0, 5, 1, 1, key=f"tier_min_{analysis['analysis_id']}")
                            with col2:
                                tier_investment_filter = st.selectbox(
                                    "Investment Type Filter",
                                    ["All", "🟢 BUY", "🟡 HOLD/BUY", "🟡 INCOME", "🟡 GROWTH", "🟡 VALUE", "🟠 SPECULATIVE", "🔴 AVOID"],
                                    key=f"tier_investment_{analysis['analysis_id']}"
                                )
                            with col3:
                                tier_search = st.text_input("Search Ticker", "", key=f"tier_search_{analysis['analysis_id']}").upper()

                            # Apply filters
                            df_tiers_filtered = df_tiers[df_tiers['Total Tiers Passed'] >= min_tiers]

                            if tier_investment_filter != "All":
                                df_tiers_filtered = df_tiers_filtered[df_tiers_filtered['Recommendation'] == tier_investment_filter]

                            if tier_search:
                                df_tiers_filtered = df_tiers_filtered[df_tiers_filtered['Ticker'].str.contains(tier_search)]

                            if len(df_tiers_filtered) > 0:
                                st.dataframe(df_tiers_filtered, use_container_width=True)
                                st.info(f"Showing {len(df_tiers_filtered)} companies (filtered from {len(df_tiers)} total)")
                            else:
                                st.warning("No companies match the selected criteria")
                        else:
                            st.warning("No detailed company results found")

    except Exception as e:
        st.error(f"Error displaying results: {e}")
        st.write("Raw data:")
        st.json(data)

def create_cash_flow_analysis():
    """Create cash flow analysis section"""
    st.header("💰 Cash Flow Pre-screening Results")

    results = load_analysis_results()

    # Check modular results first
    modular_results = results.get('modular_results', {}) if results else {}
    cash_flow_result = None

    # FORCE LOADING OF ALL_TICKERS CASH FLOW ANALYSIS ONLY
    all_tickers_result = None

    for result_id, result_info in modular_results.items():
        if 'cash_flow' in result_info['metadata'].get('analysis_type', '').lower():
            ticker_set = result_info['metadata'].get('ticker_set', '')
            total_companies = result_info['metadata'].get('total_companies', 0)

            # ONLY accept ALL_TICKERS analysis with >4000 companies
            if ticker_set == 'ALL_TICKERS' and total_companies > 4000:
                all_tickers_result = result_info['data']
                st.info(f"📊 Loaded ALL_TICKERS cash flow analysis: {total_companies:,} companies")
                break

    # ONLY use ALL_TICKERS result - ignore all others
    cash_flow_result = all_tickers_result

    # Fallback to legacy results
    if not cash_flow_result and results and 'cash_flow_prescreen' in results:
        cash_flow_result = results['cash_flow_prescreen']

    if not cash_flow_result:
        st.warning("No ALL_TICKERS cash flow analysis results found.")

        col1, col2 = st.columns(2)
        with col1:
            st.info("**Run cash flow analysis on ALL companies:**")
            st.code("python professional_modular_system.py --run CASHFLOW:ALL_TICKERS")
            st.write("*This will analyze all 4,903 companies and create the complete cash flow analysis.*")

        with col2:
            if st.button("🚀 Run Cash Flow on ALL Companies", key="run_cashflow_all"):
                with st.spinner("Running cash flow analysis on all companies..."):
                    try:
                        from professional_modular_system import ProfessionalModularSystem
                        system = ProfessionalModularSystem()
                        result = system.run_analysis('CASHFLOW', 'ALL_TICKERS')

                        if result['status'] == 'success':
                            st.success("✅ Cash flow analysis on ALL companies completed!")
                            st.write(f"**Analysis ID**: {result['analysis_id']}")
                            st.rerun()
                        elif result['status'] == 'exists':
                            st.info(f"ℹ️ Analysis already exists: {result['analysis_id']}")
                            st.rerun()
                        else:
                            st.error(f"❌ Error: {result.get('error', 'Unknown error')}")
                    except Exception as e:
                        st.error(f"❌ Error running analysis: {e}")
        return

    prescreen_data = cash_flow_result
    company_results = prescreen_data.get('company_results', {})
    summary_stats = prescreen_data.get('summary_statistics', {})

    # Summary metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total = summary_stats.get('total_companies', 0)
        st.metric("Total Companies", f"{total:,}")

    with col2:
        qualified = summary_stats.get('qualified_companies', 0)
        st.metric("Cash Flow Qualified", f"{qualified:,}")

    with col3:
        rate = summary_stats.get('qualification_rate', 0)
        st.metric("Qualification Rate", f"{rate:.1f}%")

    with col4:
        failed = summary_stats.get('failed_companies', 0)
        st.metric("Failed Analysis", f"{failed:,}")

    # Qualified companies list
    if company_results:
        st.subheader("💰 Cash Flow Qualified Companies")

        qualified_companies = []
        for ticker, result in company_results.items():
            if result.get('qualified', False):
                qualified_companies.append({
                    'Ticker': ticker,
                    'Cash Flow Score': result.get('cash_flow_score', result.get('score', 0)),
                    'Reasons Passed': ', '.join(result.get('reasons_passed', [])),
                    'Analysis': result.get('analysis', {})
                })

        if qualified_companies:
            df_qualified = pd.DataFrame(qualified_companies)
            df_qualified = df_qualified.sort_values('Cash Flow Score', ascending=False)
            df_qualified['Cash Flow Score'] = df_qualified['Cash Flow Score'].round(1)

            # Add filters
            col1, col2 = st.columns(2)
            with col1:
                min_score = st.slider("Minimum Cash Flow Score", 0.0, 100.0, 0.0, 5.0)
            with col2:
                search_ticker = st.text_input("Search Ticker", "").upper()

            # Apply filters
            filtered_df = df_qualified[df_qualified['Cash Flow Score'] >= min_score]
            if search_ticker:
                filtered_df = filtered_df[filtered_df['Ticker'].str.contains(search_ticker)]

            st.dataframe(
                filtered_df[['Ticker', 'Cash Flow Score', 'Reasons Passed']],
                use_container_width=True
            )

            st.info(f"Showing {len(filtered_df)} of {len(qualified_companies)} qualified companies")

            # Further analysis buttons
            st.subheader("🚀 Run Further Analysis on Cash Flow Qualified Companies")
            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button("🎯 Run DCF on Cash Flow Qualified", key="dcf_on_cashflow"):
                    with st.spinner("Running DCF analysis on cash flow qualified companies..."):
                        try:
                            from modular_analysis_system import ModularAnalysisSystem
                            system = ModularAnalysisSystem()
                            result = system.run_analysis('DCF', 'CASHFLOW_QUALIFIED')
                            if result.get('status') == 'success':
                                st.success("✅ DCF analysis on cash flow qualified completed!")
                                st.rerun()
                            else:
                                st.error(f"❌ Error: {result.get('error', 'Unknown error')}")
                        except Exception as e:
                            st.error(f"❌ Error: {e}")

            with col2:
                if st.button("📊 Run DCF Sample (100)", key="dcf_sample_cashflow"):
                    with st.spinner("Running DCF sample on cash flow qualified companies..."):
                        try:
                            from modular_analysis_system import ModularAnalysisSystem
                            system = ModularAnalysisSystem()
                            result = system.run_analysis('DCF', 'CASHFLOW_QUALIFIED', sample_size=100)
                            if result.get('status') == 'success':
                                st.success("✅ DCF sample analysis completed!")
                                st.rerun()
                            else:
                                st.error(f"❌ Error: {result.get('error', 'Unknown error')}")
                        except Exception as e:
                            st.error(f"❌ Error: {e}")

            with col3:
                if st.button("🔧 Run Fundamental Analysis", key="fundamental_on_cashflow"):
                    with st.spinner("Running fundamental analysis..."):
                        try:
                            from modular_analysis_system import ModularAnalysisSystem
                            system = ModularAnalysisSystem()
                            result = system.run_analysis('FUNDAMENTAL', 'CASHFLOW_QUALIFIED')
                            if result.get('status') == 'success':
                                st.success("✅ Fundamental analysis completed!")
                                st.rerun()
                            elif result.get('status') == 'pending':
                                st.warning(f"⏳ {result.get('message', 'Analysis pending')}")
                            else:
                                st.error(f"❌ Error: {result.get('error', 'Unknown error')}")
                        except Exception as e:
                            st.error(f"❌ Error: {e}")

def create_dcf_analysis():
    """Create DCF analysis section"""
    st.header("🎯 DCF Analysis Results")

    results = load_analysis_results()

    # Check for different DCF result sources
    dcf_data = None
    source_info = ""

    if results and 'direct_dcf' in results:
        dcf_data = results['direct_dcf']
        source_info = f"Direct DCF Analysis ({dcf_data.get('_file', 'Unknown')})"
    elif results and 'warren_buffett_dcf' in results:
        dcf_data = results['warren_buffett_dcf']
        source_info = f"Warren Buffett DCF ({dcf_data.get('_file', 'Unknown')})"
    elif results and 'dcf_analysis' in results:
        dcf_data = results['dcf_analysis']
        source_info = "Pipeline DCF Analysis"

    if not dcf_data:
        st.warning("No DCF analysis results found.")
        st.info("Run DCF analysis with:")
        st.code("""
# Direct DCF on all companies (skip cash flow pre-screening)
python run_direct_dcf_analysis.py

# Or DCF on cash flow qualified companies
python run_full_4950_analysis.py --phase dcf_only
        """)
        return

    st.info(f"📁 Data Source: {source_info}")

    # Extract results based on data structure
    if 'company_results' in dcf_data:
        # Direct DCF format
        company_results = dcf_data['company_results']
        summary_stats = dcf_data.get('summary_statistics', {})
        valuation_dist = dcf_data.get('valuation_distribution', {})
        accuracy_check = dcf_data.get('dcf_accuracy_check', {})

        # Summary metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total = summary_stats.get('total_analyzed', 0)
            st.metric("Companies Analyzed", f"{total:,}")

        with col2:
            successful = summary_stats.get('successful_analyses', 0)
            st.metric("Successful DCF", f"{successful:,}")

        with col3:
            buffett_approved = summary_stats.get('buffett_approved', 0)
            st.metric("Buffett Approved", f"{buffett_approved:,}")

        with col4:
            success_rate = summary_stats.get('success_rate', 0)
            st.metric("Success Rate", f"{success_rate:.1f}%")

        # Valuation distribution
        if valuation_dist:
            st.subheader("💰 Valuation Distribution")

            val_data = []
            for category, count in valuation_dist.items():
                val_data.append({
                    'Category': category.replace('_', ' ').title(),
                    'Count': count
                })

            df_val = pd.DataFrame(val_data)

            fig = px.pie(
                df_val,
                values='Count',
                names='Category',
                title="Company Valuation Distribution"
            )
            st.plotly_chart(fig, use_container_width=True)

        # DCF accuracy check
        if accuracy_check:
            st.subheader("🔍 DCF Accuracy Check")

            col1, col2, col3 = st.columns(3)

            with col1:
                valid_dcf = accuracy_check.get('companies_with_valid_dcf', 0)
                st.metric("Valid DCF Calculations", f"{valid_dcf:,}")

            with col2:
                avg_enterprise = accuracy_check.get('average_enterprise_value', 0)
                st.metric("Avg Enterprise Value", f"₹{avg_enterprise:,.0f} Cr")

            with col3:
                avg_per_share = accuracy_check.get('average_per_share_value', 0)
                st.metric("Avg Per Share Value", f"₹{avg_per_share:,.2f}")

            price_analysis = accuracy_check.get('price_range_analysis', {})
            if price_analysis:
                col1, col2, col3 = st.columns(3)

                with col1:
                    avg_margin = price_analysis.get('average_margin_of_safety', 0)
                    st.metric("Avg Margin of Safety", f"{avg_margin:.1f}%")

                with col2:
                    undervalued_pct = price_analysis.get('undervalued_percentage', 0)
                    st.metric("Undervalued %", f"{undervalued_pct:.1f}%")

                with col3:
                    overvalued_pct = price_analysis.get('overvalued_percentage', 0)
                    st.metric("Overvalued %", f"{overvalued_pct:.1f}%")

        # Company results table
        if company_results:
            st.subheader("🎯 DCF Analysis Results by Company")

            dcf_companies = []
            for ticker, result in company_results.items():
                # Calculate additional insights
                enterprise_value = result.get('enterprise_value', 0)
                per_share_value = result.get('per_share_value', 0)
                current_price = result.get('current_price', 0)
                margin_of_safety = result.get('margin_of_safety', 0)

                # Calculate upside potential
                upside_potential = margin_of_safety if margin_of_safety > 0 else 0

                # Determine investment category
                if margin_of_safety > 50:
                    investment_category = "🔥 High Opportunity"
                elif margin_of_safety > 20:
                    investment_category = "💰 Good Value"
                elif margin_of_safety > -20:
                    investment_category = "⚖️ Fair Value"
                elif margin_of_safety > -50:
                    investment_category = "⚠️ Overvalued"
                else:
                    investment_category = "❌ Avoid"

                # Calculate market cap from enterprise value (rough estimate)
                estimated_market_cap = enterprise_value * 0.9  # Rough estimate

                dcf_companies.append({
                    'Ticker': ticker,
                    'Investment Category': investment_category,
                    'DCF Value (₹)': per_share_value,
                    'Current Price (₹)': current_price,
                    'Margin of Safety (%)': margin_of_safety,
                    'Upside Potential (%)': upside_potential,
                    'Enterprise Value (Cr)': enterprise_value,
                    'Est. Market Cap (Cr)': estimated_market_cap,
                    'Buffett Score': result.get('buffett_score', 0),
                    'Buffett Approved': result.get('buffett_approved', False)
                })

            if dcf_companies:
                df_dcf = pd.DataFrame(dcf_companies)

                # Add filters
                col1, col2, col3 = st.columns(3)
                with col1:
                    min_enterprise = st.number_input("Min Enterprise Value (Cr)", 0, 10000000, 0)
                with col2:
                    valuation_filter = st.selectbox(
                        "Valuation Filter",
                        ["All", "Severely Undervalued (>50%)", "Undervalued (20-50%)",
                         "Fairly Valued (-20% to +20%)", "Overvalued (20-50%)",
                         "Severely Overvalued (>50%)", "Buffett Approved Only"]
                    )
                with col3:
                    search_ticker = st.text_input("Search Ticker", "", key="dcf_search").upper()

                # Apply filters
                filtered_df = df_dcf[df_dcf['Enterprise Value (Cr)'] >= min_enterprise]

                if valuation_filter == "Severely Undervalued (>50%)":
                    filtered_df = filtered_df[filtered_df['Margin of Safety (%)'] > 50]
                elif valuation_filter == "Undervalued (20-50%)":
                    filtered_df = filtered_df[(filtered_df['Margin of Safety (%)'] > 20) & (filtered_df['Margin of Safety (%)'] <= 50)]
                elif valuation_filter == "Fairly Valued (-20% to +20%)":
                    filtered_df = filtered_df[(filtered_df['Margin of Safety (%)'] >= -20) & (filtered_df['Margin of Safety (%)'] <= 20)]
                elif valuation_filter == "Overvalued (20-50%)":
                    filtered_df = filtered_df[(filtered_df['Margin of Safety (%)'] >= -50) & (filtered_df['Margin of Safety (%)'] < -20)]
                elif valuation_filter == "Severely Overvalued (>50%)":
                    filtered_df = filtered_df[filtered_df['Margin of Safety (%)'] < -50]
                elif valuation_filter == "Buffett Approved Only":
                    filtered_df = filtered_df[filtered_df['Buffett Approved'] == True]

                if search_ticker:
                    filtered_df = filtered_df[filtered_df['Ticker'].str.contains(search_ticker)]

                # Sort by margin of safety (best opportunities first)
                filtered_df = filtered_df.sort_values('Margin of Safety (%)', ascending=False)

                # Format columns
                filtered_df['Enterprise Value (Cr)'] = filtered_df['Enterprise Value (Cr)'].round(0)
                filtered_df['DCF Value (₹)'] = filtered_df['DCF Value (₹)'].round(2)
                filtered_df['Current Price (₹)'] = filtered_df['Current Price (₹)'].round(2)
                filtered_df['Margin of Safety (%)'] = filtered_df['Margin of Safety (%)'].round(1)
                filtered_df['Buffett Score'] = filtered_df['Buffett Score'].round(1)

                st.dataframe(
                    filtered_df,
                    use_container_width=True
                )

                st.info(f"Showing {len(filtered_df)} of {len(dcf_companies)} companies")

                # Further analysis buttons
                st.subheader("🚀 Run Further Analysis on DCF Results")
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    if st.button("🔍 Cash Flow on Undervalued", key="cf_on_undervalued"):
                        with st.spinner("Running cash flow analysis on DCF undervalued companies..."):
                            try:
                                from modular_analysis_system import ModularAnalysisSystem
                                system = ModularAnalysisSystem()
                                result = system.run_analysis('CASHFLOW', 'DCF_UNDERVALUED')
                                if result.get('status') == 'success':
                                    st.success("✅ Cash flow analysis on undervalued completed!")
                                    st.rerun()
                                else:
                                    st.error(f"❌ Error: {result.get('error', 'Unknown error')}")
                            except Exception as e:
                                st.error(f"❌ Error: {e}")

                with col2:
                    if st.button("🔥 Analyze Severely Undervalued", key="analyze_severely_undervalued"):
                        with st.spinner("Running analysis on severely undervalued companies..."):
                            try:
                                from modular_analysis_system import ModularAnalysisSystem
                                system = ModularAnalysisSystem()
                                result = system.run_analysis('FUNDAMENTAL', 'DCF_SEVERELY_UNDERVALUED')
                                if result.get('status') == 'success':
                                    st.success("✅ Analysis on severely undervalued completed!")
                                    st.rerun()
                                elif result.get('status') == 'pending':
                                    st.warning(f"⏳ {result.get('message', 'Analysis pending')}")
                                else:
                                    st.error(f"❌ Error: {result.get('error', 'Unknown error')}")
                            except Exception as e:
                                st.error(f"❌ Error: {e}")

                with col3:
                    if st.button("💰 Analyze Buffett Approved", key="analyze_buffett"):
                        with st.spinner("Running analysis on Buffett approved companies..."):
                            try:
                                from modular_analysis_system import ModularAnalysisSystem
                                system = ModularAnalysisSystem()
                                result = system.run_analysis('PORTFOLIO', 'DCF_BUFFETT_APPROVED')
                                if result.get('status') == 'success':
                                    st.success("✅ Portfolio analysis on Buffett approved completed!")
                                    st.rerun()
                                elif result.get('status') == 'pending':
                                    st.warning(f"⏳ {result.get('message', 'Analysis pending')}")
                                else:
                                    st.error(f"❌ Error: {result.get('error', 'Unknown error')}")
                            except Exception as e:
                                st.error(f"❌ Error: {e}")

                with col4:
                    if st.button("🔧 Run Custom Analysis", key="custom_dcf_analysis"):
                        st.info("💡 Use the 'Modular Results' tab for custom analysis combinations")

def main():
    """Main dashboard function"""

    # Sidebar navigation
    st.sidebar.title("📊 Navigation")

    pages = {
        "🏠 Overview": create_system_overview,
        "🔧 Modular Results": create_modular_results,
        "🔄 Progress": create_analysis_progress,
        "💰 Cash Flow Analysis": create_cash_flow_analysis,
        "🎯 DCF Analysis": create_dcf_analysis,
        "📈 Recommendations": create_investment_recommendations,
        "🔍 Companies": create_company_analysis,
        "🏭 Sectors": create_sector_analysis,
        "⚙️ Controls": create_system_controls
    }

    selected_page = st.sidebar.selectbox("Select Page", list(pages.keys()))

    # Add refresh button
    if st.sidebar.button("🔄 Refresh Data"):
        st.rerun()

    # Show system status in sidebar
    st.sidebar.markdown("---")
    st.sidebar.subheader("System Status")

    system_status = load_system_status()
    if system_status['status'] == 'healthy':
        st.sidebar.success("✅ System Healthy")
        st.sidebar.info(f"📊 {system_status['total_companies']:,} companies loaded")
    else:
        st.sidebar.error("❌ System Error")
        st.sidebar.error(system_status.get('error', 'Unknown error'))

    # Display selected page
    pages[selected_page]()

    # Footer
    st.markdown("---")
    st.markdown(
        "**Fundamental Analysis System** | "
        "Built for comprehensive investment screening | "
        f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    )

if __name__ == "__main__":
    main()
