#!/usr/bin/env python3
"""
🔧 COMPREHENSIVE FUNDAMENTAL ANALYSIS ENGINE

This is the final analysis model that can run on any ticker set:
- ALL_TICKERS: Complete fundamental screening
- CASHFLOW_QUALIFIED: Final analysis on cash flow qualified companies
- DCF_FILTERED: Analysis on all DCF categories
- CROSS_ANALYSIS: Analysis on combinations of DCF and Cash Flow results

Implements comprehensive fundamental screening with:
- Financial health metrics
- Profitability analysis
- Growth analysis
- Valuation metrics
- Quality scoring
- Risk assessment
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.data_loader import ScreenerDataLoader

class FundamentalAnalysisEngine:
    """
    Comprehensive fundamental analysis engine for final company screening
    """
    
    def __init__(self):
        self.data_loader = ScreenerDataLoader('../screener_data_collector/data')
        self.results_dir = Path('output/fundamental_analysis')
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Fundamental analysis criteria
        self.criteria = {
            'financial_health': {
                'debt_to_equity_max': 0.5,
                'current_ratio_min': 1.5,
                'interest_coverage_min': 5.0,
                'debt_to_assets_max': 0.3
            },
            'profitability': {
                'roe_min': 15.0,
                'roa_min': 8.0,
                'net_margin_min': 10.0,
                'gross_margin_min': 25.0
            },
            'growth': {
                'revenue_growth_min': 10.0,
                'earnings_growth_min': 15.0,
                'book_value_growth_min': 12.0
            },
            'valuation': {
                'pe_ratio_max': 25.0,
                'pb_ratio_max': 3.0,
                'price_to_sales_max': 5.0,
                'ev_ebitda_max': 15.0
            },
            'quality': {
                'dividend_yield_min': 1.0,
                'payout_ratio_max': 60.0,
                'asset_turnover_min': 0.5
            }
        }
        
        # Scoring weights
        self.weights = {
            'financial_health': 0.25,
            'profitability': 0.25,
            'growth': 0.20,
            'valuation': 0.15,
            'quality': 0.15
        }
    
    def analyze_company(self, ticker: str) -> Dict[str, Any]:
        """
        Perform comprehensive fundamental analysis on a single company
        """
        try:
            # Get company data
            company_data = self.data_loader.get_company_data(ticker)
            if not company_data:
                return {
                    'ticker': ticker,
                    'status': 'failed',
                    'error': 'No data available'
                }
            
            # Extract financial metrics
            metrics = self._extract_financial_metrics(company_data)
            if not metrics:
                return {
                    'ticker': ticker,
                    'status': 'failed',
                    'error': 'Insufficient financial data'
                }
            
            # Perform analysis
            analysis_results = {
                'ticker': ticker,
                'status': 'success',
                'financial_health': self._analyze_financial_health(metrics),
                'profitability': self._analyze_profitability(metrics),
                'growth': self._analyze_growth(metrics),
                'valuation': self._analyze_valuation(metrics),
                'quality': self._analyze_quality(metrics)
            }
            
            # Calculate overall score
            overall_score = self._calculate_overall_score(analysis_results)
            analysis_results['overall_score'] = overall_score
            
            # Determine qualification
            analysis_results['qualified'] = overall_score >= 70.0
            analysis_results['grade'] = self._get_grade(overall_score)
            
            # Add raw metrics for reference
            analysis_results['raw_metrics'] = metrics
            
            return analysis_results
            
        except Exception as e:
            return {
                'ticker': ticker,
                'status': 'failed',
                'error': str(e)
            }
    
    def _extract_financial_metrics(self, company_data: Dict) -> Dict[str, float]:
        """Extract key financial metrics from company data"""
        try:
            metrics = {}
            
            # Financial Health Metrics
            metrics['debt_to_equity'] = float(company_data.get('Debt to equity', 0))
            metrics['current_ratio'] = float(company_data.get('Current ratio', 0))
            metrics['interest_coverage'] = float(company_data.get('Interest Coverage Ratio', 0))
            metrics['debt_to_assets'] = float(company_data.get('Debt to Assets', 0))
            
            # Profitability Metrics
            metrics['roe'] = float(company_data.get('ROE %', 0))
            metrics['roa'] = float(company_data.get('ROA %', 0))
            metrics['net_margin'] = float(company_data.get('Net profit margin %', 0))
            metrics['gross_margin'] = float(company_data.get('Gross profit margin %', 0))
            
            # Growth Metrics
            metrics['revenue_growth'] = float(company_data.get('Sales growth %', 0))
            metrics['earnings_growth'] = float(company_data.get('Profit growth %', 0))
            metrics['book_value_growth'] = float(company_data.get('Book value growth %', 0))
            
            # Valuation Metrics
            metrics['pe_ratio'] = float(company_data.get('PE', 0))
            metrics['pb_ratio'] = float(company_data.get('Price to book value', 0))
            metrics['price_to_sales'] = float(company_data.get('Price to sales', 0))
            metrics['ev_ebitda'] = float(company_data.get('EV/EBITDA', 0))
            
            # Quality Metrics
            metrics['dividend_yield'] = float(company_data.get('Dividend yield %', 0))
            metrics['payout_ratio'] = float(company_data.get('Dividend payout %', 0))
            metrics['asset_turnover'] = float(company_data.get('Asset turnover', 0))
            
            return metrics
            
        except Exception as e:
            print(f"Error extracting metrics for company: {e}")
            return {}
    
    def _analyze_financial_health(self, metrics: Dict[str, float]) -> Dict[str, Any]:
        """Analyze financial health"""
        criteria = self.criteria['financial_health']
        score = 0
        max_score = 100
        reasons = []
        
        # Debt to Equity
        if metrics['debt_to_equity'] <= criteria['debt_to_equity_max']:
            score += 25
            reasons.append(f"Low debt-to-equity: {metrics['debt_to_equity']:.2f}")
        
        # Current Ratio
        if metrics['current_ratio'] >= criteria['current_ratio_min']:
            score += 25
            reasons.append(f"Good current ratio: {metrics['current_ratio']:.2f}")
        
        # Interest Coverage
        if metrics['interest_coverage'] >= criteria['interest_coverage_min']:
            score += 25
            reasons.append(f"Strong interest coverage: {metrics['interest_coverage']:.2f}")
        
        # Debt to Assets
        if metrics['debt_to_assets'] <= criteria['debt_to_assets_max']:
            score += 25
            reasons.append(f"Low debt-to-assets: {metrics['debt_to_assets']:.2f}")
        
        return {
            'score': score,
            'max_score': max_score,
            'percentage': (score / max_score) * 100,
            'reasons': reasons
        }
    
    def _analyze_profitability(self, metrics: Dict[str, float]) -> Dict[str, Any]:
        """Analyze profitability"""
        criteria = self.criteria['profitability']
        score = 0
        max_score = 100
        reasons = []
        
        # ROE
        if metrics['roe'] >= criteria['roe_min']:
            score += 25
            reasons.append(f"High ROE: {metrics['roe']:.1f}%")
        
        # ROA
        if metrics['roa'] >= criteria['roa_min']:
            score += 25
            reasons.append(f"Good ROA: {metrics['roa']:.1f}%")
        
        # Net Margin
        if metrics['net_margin'] >= criteria['net_margin_min']:
            score += 25
            reasons.append(f"Strong net margin: {metrics['net_margin']:.1f}%")
        
        # Gross Margin
        if metrics['gross_margin'] >= criteria['gross_margin_min']:
            score += 25
            reasons.append(f"Good gross margin: {metrics['gross_margin']:.1f}%")
        
        return {
            'score': score,
            'max_score': max_score,
            'percentage': (score / max_score) * 100,
            'reasons': reasons
        }
    
    def _analyze_growth(self, metrics: Dict[str, float]) -> Dict[str, Any]:
        """Analyze growth metrics"""
        criteria = self.criteria['growth']
        score = 0
        max_score = 100
        reasons = []
        
        # Revenue Growth
        if metrics['revenue_growth'] >= criteria['revenue_growth_min']:
            score += 35
            reasons.append(f"Strong revenue growth: {metrics['revenue_growth']:.1f}%")
        
        # Earnings Growth
        if metrics['earnings_growth'] >= criteria['earnings_growth_min']:
            score += 35
            reasons.append(f"High earnings growth: {metrics['earnings_growth']:.1f}%")
        
        # Book Value Growth
        if metrics['book_value_growth'] >= criteria['book_value_growth_min']:
            score += 30
            reasons.append(f"Good book value growth: {metrics['book_value_growth']:.1f}%")
        
        return {
            'score': score,
            'max_score': max_score,
            'percentage': (score / max_score) * 100,
            'reasons': reasons
        }
    
    def _analyze_valuation(self, metrics: Dict[str, float]) -> Dict[str, Any]:
        """Analyze valuation metrics"""
        criteria = self.criteria['valuation']
        score = 0
        max_score = 100
        reasons = []
        
        # PE Ratio
        if 0 < metrics['pe_ratio'] <= criteria['pe_ratio_max']:
            score += 25
            reasons.append(f"Reasonable PE: {metrics['pe_ratio']:.1f}")
        
        # PB Ratio
        if 0 < metrics['pb_ratio'] <= criteria['pb_ratio_max']:
            score += 25
            reasons.append(f"Good PB ratio: {metrics['pb_ratio']:.2f}")
        
        # Price to Sales
        if 0 < metrics['price_to_sales'] <= criteria['price_to_sales_max']:
            score += 25
            reasons.append(f"Reasonable P/S: {metrics['price_to_sales']:.2f}")
        
        # EV/EBITDA
        if 0 < metrics['ev_ebitda'] <= criteria['ev_ebitda_max']:
            score += 25
            reasons.append(f"Good EV/EBITDA: {metrics['ev_ebitda']:.1f}")
        
        return {
            'score': score,
            'max_score': max_score,
            'percentage': (score / max_score) * 100,
            'reasons': reasons
        }
    
    def _analyze_quality(self, metrics: Dict[str, float]) -> Dict[str, Any]:
        """Analyze quality metrics"""
        criteria = self.criteria['quality']
        score = 0
        max_score = 100
        reasons = []
        
        # Dividend Yield
        if metrics['dividend_yield'] >= criteria['dividend_yield_min']:
            score += 35
            reasons.append(f"Good dividend yield: {metrics['dividend_yield']:.1f}%")
        
        # Payout Ratio
        if 0 < metrics['payout_ratio'] <= criteria['payout_ratio_max']:
            score += 35
            reasons.append(f"Sustainable payout: {metrics['payout_ratio']:.1f}%")
        
        # Asset Turnover
        if metrics['asset_turnover'] >= criteria['asset_turnover_min']:
            score += 30
            reasons.append(f"Efficient asset use: {metrics['asset_turnover']:.2f}")
        
        return {
            'score': score,
            'max_score': max_score,
            'percentage': (score / max_score) * 100,
            'reasons': reasons
        }
    
    def _calculate_overall_score(self, analysis_results: Dict[str, Any]) -> float:
        """Calculate weighted overall score"""
        total_score = 0
        
        for category, weight in self.weights.items():
            if category in analysis_results:
                category_percentage = analysis_results[category]['percentage']
                total_score += category_percentage * weight
        
        return round(total_score, 1)
    
    def _get_grade(self, score: float) -> str:
        """Get letter grade based on score"""
        if score >= 90:
            return "A+"
        elif score >= 80:
            return "A"
        elif score >= 70:
            return "B+"
        elif score >= 60:
            return "B"
        elif score >= 50:
            return "C+"
        elif score >= 40:
            return "C"
        else:
            return "D"
    
    def analyze_ticker_set(self, tickers: List[str], analysis_id: str, 
                          ticker_set: str) -> Dict[str, Any]:
        """
        Perform fundamental analysis on a set of tickers
        """
        print(f"🔧 Starting fundamental analysis on {len(tickers)} companies...")
        
        results = {
            'metadata': {
                'analysis_id': analysis_id,
                'analysis_type': 'FUNDAMENTAL',
                'ticker_set': ticker_set,
                'total_companies': len(tickers),
                'timestamp': datetime.now().isoformat(),
                'description': f'Comprehensive Fundamental Analysis on {ticker_set}'
            },
            'company_results': {},
            'summary_statistics': {},
            'qualified_companies': [],
            'grade_distribution': {}
        }
        
        successful_analyses = 0
        qualified_companies = 0
        failed_analyses = 0
        grade_counts = {'A+': 0, 'A': 0, 'B+': 0, 'B': 0, 'C+': 0, 'C': 0, 'D': 0}
        
        for i, ticker in enumerate(tickers, 1):
            print(f"Analyzing {ticker} ({i}/{len(tickers)})...")
            
            analysis_result = self.analyze_company(ticker)
            results['company_results'][ticker] = analysis_result
            
            if analysis_result['status'] == 'success':
                successful_analyses += 1
                
                if analysis_result['qualified']:
                    qualified_companies += 1
                    results['qualified_companies'].append({
                        'ticker': ticker,
                        'overall_score': analysis_result['overall_score'],
                        'grade': analysis_result['grade']
                    })
                
                # Count grades
                grade = analysis_result['grade']
                if grade in grade_counts:
                    grade_counts[grade] += 1
            else:
                failed_analyses += 1
        
        # Calculate summary statistics
        results['summary_statistics'] = {
            'total_companies': len(tickers),
            'successful_analyses': successful_analyses,
            'qualified_companies': qualified_companies,
            'failed_analyses': failed_analyses,
            'success_rate': (successful_analyses / len(tickers)) * 100,
            'qualification_rate': (qualified_companies / successful_analyses) * 100 if successful_analyses > 0 else 0
        }
        
        results['grade_distribution'] = grade_counts
        
        # Sort qualified companies by score
        results['qualified_companies'].sort(key=lambda x: x['overall_score'], reverse=True)
        
        print(f"✅ Fundamental analysis completed!")
        print(f"   📊 Successful: {successful_analyses}/{len(tickers)}")
        print(f"   ✅ Qualified: {qualified_companies}")
        print(f"   📈 Qualification Rate: {results['summary_statistics']['qualification_rate']:.1f}%")
        
        return results
    
    def save_results(self, results: Dict[str, Any], analysis_id: str) -> str:
        """Save analysis results"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{analysis_id}_{timestamp}.json"
        filepath = self.results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"📁 Results saved: {filename}")
        return str(filepath)

def main():
    """Main function for testing"""
    engine = FundamentalAnalysisEngine()
    
    # Test with a few companies
    test_tickers = ['TCS', 'RELIANCE', 'SBIN']
    analysis_id = 'FUNDAMENTAL_ALL_TICKERS_test'
    
    results = engine.analyze_ticker_set(test_tickers, analysis_id, 'ALL_TICKERS')
    filepath = engine.save_results(results, analysis_id)
    
    print(f"\n🎯 Test completed! Results saved to: {filepath}")

if __name__ == "__main__":
    main()
