#!/usr/bin/env python3
"""
Comprehensive Analysis Script

This script analyzes all 4950 companies in the database and identifies
the best investment opportunities based on multiple criteria including
historical consistency, sector analysis, and peer comparison.
"""

import os
import sys
import json
import logging
import pandas as pd
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules
from utils.data_loader import ScreenerDataLoader
from models.screener import FundamentalScreener
from models.consistency_analyzer import ConsistencyAnalyzer
from models.sector_analyzer import SectorAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('comprehensive_analysis')

class ComprehensiveAnalyzer:
    """
    Class to perform comprehensive analysis of all companies
    """

    def __init__(self, data_path: str = '../screener_data_collector/data'):
        """
        Initialize the comprehensive analyzer

        Parameters:
        -----------
        data_path : str
            Path to the data directory
        """
        self.data_loader = ScreenerDataLoader(data_path)
        self.screener = FundamentalScreener(self.data_loader)
        self.consistency_analyzer = ConsistencyAnalyzer()
        self.sector_analyzer = SectorAnalyzer()

        # Load all tickers
        self.all_tickers = self.data_loader.get_all_tickers()
        logger.info(f"Loaded {len(self.all_tickers)} companies for analysis")

    def analyze_all_companies(self,
                            max_workers: int = 4,
                            batch_size: int = 100,
                            save_intermediate: bool = True) -> Dict[str, Any]:
        """
        Analyze all companies in the database

        Parameters:
        -----------
        max_workers : int
            Number of parallel workers
        batch_size : int
            Number of companies to process in each batch
        save_intermediate : bool
            Whether to save intermediate results

        Returns:
        --------
        Dictionary containing comprehensive analysis results
        """
        logger.info(f"Starting comprehensive analysis of {len(self.all_tickers)} companies")

        # Initialize results storage
        results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_companies': len(self.all_tickers),
            'companies_analyzed': 0,
            'companies_with_data': 0,
            'sector_analysis': {},
            'top_performers': {
                'overall_consistency': [],
                'growth_consistency': [],
                'financial_health': [],
                'sector_leaders': {}
            },
            'company_results': {}
        }

        # Process companies in batches
        for i in range(0, len(self.all_tickers), batch_size):
            batch_tickers = self.all_tickers[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(self.all_tickers) + batch_size - 1) // batch_size

            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch_tickers)} companies)")

            # Process batch
            batch_results = self._process_batch(batch_tickers, max_workers)

            # Update results
            results['company_results'].update(batch_results)
            results['companies_analyzed'] += len(batch_results)
            results['companies_with_data'] += sum(1 for r in batch_results.values() if not r.get('error'))

            # Save intermediate results
            if save_intermediate and batch_num % 5 == 0:  # Save every 5 batches
                self._save_intermediate_results(results, batch_num)

            logger.info(f"Batch {batch_num} completed. Total analyzed: {results['companies_analyzed']}")

        # Perform sector analysis
        logger.info("Performing sector analysis...")
        results['sector_analysis'] = self._analyze_sectors(results['company_results'])

        # Identify top performers
        logger.info("Identifying top performers...")
        results['top_performers'] = self._identify_top_performers(results['company_results'])

        # Generate summary statistics
        results['summary'] = self._generate_summary(results)

        logger.info("Comprehensive analysis completed")
        return results

    def _process_batch(self, tickers: List[str], max_workers: int) -> Dict[str, Any]:
        """
        Process a batch of companies

        Parameters:
        -----------
        tickers : List[str]
            List of tickers to process
        max_workers : int
            Number of parallel workers

        Returns:
        --------
        Dictionary containing batch results
        """
        batch_results = {}

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_ticker = {
                executor.submit(self._analyze_single_company, ticker): ticker
                for ticker in tickers
            }

            # Collect results
            for future in as_completed(future_to_ticker):
                ticker = future_to_ticker[future]
                try:
                    result = future.result()
                    batch_results[ticker] = result
                except Exception as e:
                    logger.error(f"Error analyzing {ticker}: {e}")
                    batch_results[ticker] = {'error': str(e)}

        return batch_results

    def _analyze_single_company(self, ticker: str) -> Dict[str, Any]:
        """
        Analyze a single company

        Parameters:
        -----------
        ticker : str
            Company ticker

        Returns:
        --------
        Dictionary containing company analysis results
        """
        try:
            # Load company data
            company_data = self.data_loader.load_company_data(ticker)

            if not company_data:
                return {'error': 'No data found'}

            # Extract basic metrics
            metrics = self.screener._extract_metrics(company_data)

            # Analyze consistency
            consistency_results = self.consistency_analyzer.analyze_company_consistency(company_data)

            # Classify sector
            sector = self.sector_analyzer.classify_sector(company_data)

            # Get peer companies
            peers = self.sector_analyzer.get_peer_companies(company_data)

            # Compile results
            result = {
                'ticker': ticker,
                'sector': sector,
                'peers': peers,
                'metrics': metrics,
                'consistency': consistency_results,
                'overall_consistency_score': consistency_results.get('overall_consistency_score'),
                'growth_consistency_score': consistency_results.get('growth_consistency', {}).get('overall_score'),
                'quarterly_consistency_score': consistency_results.get('quarterly_consistency', {}).get('overall_score'),
                'analysis_timestamp': datetime.now().isoformat()
            }

            # Add financial health score
            result['financial_health_score'] = self._calculate_financial_health_score(metrics)

            return result

        except Exception as e:
            logger.error(f"Error analyzing {ticker}: {e}")
            return {'error': str(e)}

    def _calculate_financial_health_score(self, metrics: Dict[str, Any]) -> float:
        """
        Calculate a financial health score based on key metrics

        Parameters:
        -----------
        metrics : Dict[str, Any]
            Company financial metrics

        Returns:
        --------
        Financial health score (0-100)
        """
        score = 0
        components = 0

        # ROE component (25 points)
        roe = metrics.get('roe')
        if roe is not None and isinstance(roe, (int, float)):
            score += min(25, max(0, roe * 1.25))  # 20% ROE = 25 points
            components += 1

        # ROCE component (25 points)
        roce = metrics.get('roce')
        if roce is not None and isinstance(roce, (int, float)):
            score += min(25, max(0, roce * 1.25))  # 20% ROCE = 25 points
            components += 1

        # Debt to Equity component (25 points)
        debt_to_equity = metrics.get('debt_to_equity')
        if debt_to_equity is not None and isinstance(debt_to_equity, (int, float)):
            score += max(0, 25 - debt_to_equity * 12.5)  # 0 D/E = 25 points, 2 D/E = 0 points
            components += 1

        # Interest Coverage component (25 points)
        interest_coverage = metrics.get('interest_coverage')
        if interest_coverage is not None and isinstance(interest_coverage, (int, float)):
            score += min(25, max(0, interest_coverage * 2.5))  # 10x coverage = 25 points
            components += 1

        # Return average score if we have at least 2 components
        if components >= 2:
            return score / components * (100 / 25)  # Normalize to 0-100
        else:
            return 0

    def _analyze_sectors(self, company_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze performance by sector

        Parameters:
        -----------
        company_results : Dict[str, Any]
            Results for all companies

        Returns:
        --------
        Dictionary containing sector analysis
        """
        # Group companies by sector
        sector_companies = {}
        for ticker, result in company_results.items():
            if not result.get('error'):
                sector = result.get('sector', 'unknown')
                if sector not in sector_companies:
                    sector_companies[sector] = []
                sector_companies[sector].append(ticker)

        # Analyze each sector
        sector_analysis = {}
        for sector, companies in sector_companies.items():
            if len(companies) >= 3:  # Only analyze sectors with at least 3 companies
                sector_data = {}
                for ticker in companies:
                    if ticker in company_results:
                        sector_data[ticker] = {'ratios': {'ratios': company_results[ticker].get('metrics', {})}}

                # Use sector analyzer
                analysis = self.sector_analyzer.analyze_sector_performance(sector_data, sector)

                # Add consistency statistics
                consistency_scores = []
                growth_scores = []
                health_scores = []

                for ticker in companies:
                    result = company_results.get(ticker, {})
                    if not result.get('error'):
                        if result.get('overall_consistency_score') is not None:
                            consistency_scores.append(result['overall_consistency_score'])
                        if result.get('growth_consistency_score') is not None:
                            growth_scores.append(result['growth_consistency_score'])
                        if result.get('financial_health_score') is not None:
                            health_scores.append(result['financial_health_score'])

                sector_analysis[sector] = {
                    'company_count': len(companies),
                    'companies': companies,
                    'consistency_stats': {
                        'overall': self._calculate_stats(consistency_scores),
                        'growth': self._calculate_stats(growth_scores),
                        'health': self._calculate_stats(health_scores)
                    },
                    'top_companies': self._get_sector_top_companies(company_results, companies)
                }

        return sector_analysis

    def _calculate_stats(self, values: List[float]) -> Dict[str, float]:
        """Calculate basic statistics for a list of values"""
        if not values:
            return {}

        import numpy as np
        return {
            'count': len(values),
            'mean': np.mean(values),
            'median': np.median(values),
            'std': np.std(values),
            'min': np.min(values),
            'max': np.max(values)
        }

    def _get_sector_top_companies(self, company_results: Dict[str, Any], companies: List[str]) -> List[Dict[str, Any]]:
        """Get top companies in a sector"""
        sector_companies = []

        for ticker in companies:
            result = company_results.get(ticker, {})
            if not result.get('error'):
                sector_companies.append({
                    'ticker': ticker,
                    'overall_consistency': result.get('overall_consistency_score', 0),
                    'growth_consistency': result.get('growth_consistency_score', 0),
                    'financial_health': result.get('financial_health_score', 0)
                })

        # Sort by overall consistency and return top 5
        sector_companies.sort(key=lambda x: x['overall_consistency'], reverse=True)
        return sector_companies[:5]

    def _identify_top_performers(self, company_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Identify top performing companies across different metrics

        Parameters:
        -----------
        company_results : Dict[str, Any]
            Results for all companies

        Returns:
        --------
        Dictionary containing top performers
        """
        # Collect all valid companies
        valid_companies = []
        for ticker, result in company_results.items():
            if not result.get('error'):
                valid_companies.append({
                    'ticker': ticker,
                    'sector': result.get('sector', 'unknown'),
                    'overall_consistency': result.get('overall_consistency_score', 0),
                    'growth_consistency': result.get('growth_consistency_score', 0),
                    'financial_health': result.get('financial_health_score', 0)
                })

        # Sort and get top performers
        top_performers = {
            'overall_consistency': sorted(valid_companies,
                                        key=lambda x: x['overall_consistency'],
                                        reverse=True)[:50],
            'growth_consistency': sorted(valid_companies,
                                       key=lambda x: x['growth_consistency'],
                                       reverse=True)[:50],
            'financial_health': sorted(valid_companies,
                                     key=lambda x: x['financial_health'],
                                     reverse=True)[:50]
        }

        # Get sector leaders
        sector_leaders = {}
        sectors = set(c['sector'] for c in valid_companies)
        for sector in sectors:
            sector_companies = [c for c in valid_companies if c['sector'] == sector]
            if len(sector_companies) >= 3:
                sector_leaders[sector] = sorted(sector_companies,
                                              key=lambda x: x['overall_consistency'],
                                              reverse=True)[:10]

        top_performers['sector_leaders'] = sector_leaders

        return top_performers

    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary statistics"""
        company_results = results['company_results']

        # Count companies with data
        companies_with_data = sum(1 for r in company_results.values() if not r.get('error'))

        # Count by sector
        sector_counts = {}
        for result in company_results.values():
            if not result.get('error'):
                sector = result.get('sector', 'unknown')
                sector_counts[sector] = sector_counts.get(sector, 0) + 1

        return {
            'total_companies': results['total_companies'],
            'companies_with_data': companies_with_data,
            'data_coverage_pct': (companies_with_data / results['total_companies']) * 100,
            'sectors_identified': len(sector_counts),
            'sector_distribution': sector_counts,
            'analysis_completion_time': datetime.now().isoformat()
        }

    def _save_intermediate_results(self, results: Dict[str, Any], batch_num: int):
        """Save intermediate results"""
        output_dir = 'output/comprehensive_analysis'
        os.makedirs(output_dir, exist_ok=True)

        filename = f"{output_dir}/intermediate_batch_{batch_num}.json"
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        logger.info(f"Intermediate results saved to {filename}")

def main():
    """Main function to run comprehensive analysis"""
    print("=" * 80)
    print("COMPREHENSIVE ANALYSIS OF ALL 4950 COMPANIES")
    print("=" * 80)

    # Initialize analyzer
    analyzer = ComprehensiveAnalyzer()

    # Run analysis
    results = analyzer.analyze_all_companies(
        max_workers=6,  # Adjust based on your system
        batch_size=50,  # Smaller batches for better progress tracking
        save_intermediate=True
    )

    # Save final results
    output_dir = 'output/comprehensive_analysis'
    os.makedirs(output_dir, exist_ok=True)

    final_output = f"{output_dir}/comprehensive_analysis_final.json"
    with open(final_output, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\n✓ Comprehensive analysis completed!")
    print(f"✓ Results saved to {final_output}")

    # Print summary
    summary = results['summary']
    print(f"\nSUMMARY:")
    print(f"  Total companies: {summary['total_companies']}")
    print(f"  Companies with data: {summary['companies_with_data']}")
    print(f"  Data coverage: {summary['data_coverage_pct']:.1f}%")
    print(f"  Sectors identified: {summary['sectors_identified']}")

    # Print top performers
    top_performers = results['top_performers']
    print(f"\nTOP 10 COMPANIES BY OVERALL CONSISTENCY:")
    for i, company in enumerate(top_performers['overall_consistency'][:10], 1):
        print(f"  {i:2d}. {company['ticker']:12s} ({company['sector']:15s}) - Score: {company['overall_consistency']:.1f}")

if __name__ == "__main__":
    main()
