#!/usr/bin/env python3
"""
Financial Ratios Module

This module provides functions to calculate various financial ratios for fundamental analysis.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('financial_ratios')

class FinancialRatios:
    """
    Class to calculate financial ratios for fundamental analysis
    """

    def __init__(self):
        """
        Initialize the financial ratios calculator
        """
        pass

    def calculate_all_ratios(self, company_data: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """
        Calculate all financial ratios for a company

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Dictionary containing company financial data

        Returns:
        --------
        Dictionary mapping ratio categories to dictionaries of ratio values
        """
        ratios = {
            'profitability': self.calculate_profitability_ratios(company_data),
            'leverage': self.calculate_leverage_ratios(company_data),
            'valuation': self.calculate_valuation_ratios(company_data),
            'operating': self.calculate_operating_ratios(company_data),
            'cash_flow': self.calculate_cash_flow_ratios(company_data),
            'cash_flow_analysis': self.analyze_cash_flow_patterns(company_data)
        }

        return ratios

    def calculate_profitability_ratios(self, company_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate profitability ratios

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Dictionary containing company financial data

        Returns:
        --------
        Dictionary of profitability ratios
        """
        ratios = {}

        try:
            # Helper function to convert values to float
            def safe_float(value):
                if isinstance(value, (int, float)):
                    return float(value)
                elif isinstance(value, str):
                    value = value.replace(',', '').strip()
                    if value:
                        try:
                            return float(value)
                        except ValueError:
                            return 0.0
                return 0.0

            # First, check if we have pre-calculated ratios from screener.in
            if 'ratios' in company_data and 'ratios' in company_data['ratios']:
                screener_ratios = company_data['ratios']['ratios']

                # Map screener.in ratios to our format
                if 'gross_profit_margin' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['gross_profit_margin'], str):
                            if screener_ratios['gross_profit_margin'].strip():
                                ratios['gross_profit_margin'] = float(screener_ratios['gross_profit_margin'].replace('%', ''))
                        else:
                            ratios['gross_profit_margin'] = float(screener_ratios['gross_profit_margin'])
                    except (ValueError, TypeError):
                        pass

                if 'operating_profit_margin' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['operating_profit_margin'], str):
                            if screener_ratios['operating_profit_margin'].strip():
                                ratios['ebitda_margin'] = float(screener_ratios['operating_profit_margin'].replace('%', ''))
                        else:
                            ratios['ebitda_margin'] = float(screener_ratios['operating_profit_margin'])
                    except (ValueError, TypeError):
                        pass

                if 'net_profit_margin' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['net_profit_margin'], str):
                            if screener_ratios['net_profit_margin'].strip():
                                ratios['pat_margin'] = float(screener_ratios['net_profit_margin'].replace('%', ''))
                        else:
                            ratios['pat_margin'] = float(screener_ratios['net_profit_margin'])
                    except (ValueError, TypeError):
                        pass

                if 'roe_%' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['roe_%'], str):
                            if screener_ratios['roe_%'].strip():
                                ratios['roe'] = float(screener_ratios['roe_%'].replace('%', ''))
                        else:
                            ratios['roe'] = float(screener_ratios['roe_%'])
                    except (ValueError, TypeError):
                        pass
                elif 'roe' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['roe'], str):
                            if screener_ratios['roe'].strip():
                                ratios['roe'] = float(screener_ratios['roe'].replace('%', ''))
                        else:
                            ratios['roe'] = float(screener_ratios['roe'])
                    except (ValueError, TypeError):
                        pass

                if 'roa' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['roa'], str):
                            if screener_ratios['roa'].strip():
                                ratios['roa'] = float(screener_ratios['roa'].replace('%', ''))
                        else:
                            ratios['roa'] = float(screener_ratios['roa'])
                    except (ValueError, TypeError):
                        pass

                if 'roce_%' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['roce_%'], str):
                            if screener_ratios['roce_%'].strip():
                                ratios['roce'] = float(screener_ratios['roce_%'].replace('%', ''))
                        else:
                            ratios['roce'] = float(screener_ratios['roce_%'])
                    except (ValueError, TypeError):
                        pass
                elif 'roce' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['roce'], str):
                            if screener_ratios['roce'].strip():
                                ratios['roce'] = float(screener_ratios['roce'].replace('%', ''))
                        else:
                            ratios['roce'] = float(screener_ratios['roce'])
                    except (ValueError, TypeError):
                        pass

                # If we have some ratios, return them
                if ratios:
                    return ratios

            # If we don't have pre-calculated ratios, calculate them from financial statements
            # Extract profit and loss data
            if 'profit_loss' in company_data:
                pl_data = company_data['profit_loss']

                # Remove 'units' key if present
                if 'units' in pl_data:
                    pl_data.pop('units')

                # Get the most recent year's data
                latest_year = sorted([k for k in pl_data.keys() if k != 'units'])[-1] if pl_data else None

                if latest_year:
                    latest_data = pl_data[latest_year]

                    # Calculate EBITDA Margin
                    if 'revenue' in latest_data and 'ebitda' in latest_data:
                        revenue = safe_float(latest_data['revenue'])
                        ebitda = safe_float(latest_data['ebitda'])

                        if revenue > 0:
                            ratios['ebitda_margin'] = (ebitda / revenue) * 100

                    # Calculate PAT Margin
                    if 'revenue' in latest_data and 'pat' in latest_data:
                        revenue = safe_float(latest_data['revenue'])
                        pat = safe_float(latest_data['pat'])

                        if revenue > 0:
                            ratios['pat_margin'] = (pat / revenue) * 100

            # Extract balance sheet data
            if 'balance_sheet' in company_data:
                bs_data = company_data['balance_sheet']

                # Remove 'units' key if present
                if 'units' in bs_data:
                    bs_data.pop('units')

                # Get the most recent year's data
                latest_year = sorted([k for k in bs_data.keys() if k != 'units'])[-1] if bs_data else None

                if latest_year:
                    latest_data = bs_data[latest_year]

                    # Calculate ROE
                    if 'profit_loss' in company_data and latest_year in company_data['profit_loss']:
                        pl_latest = company_data['profit_loss'][latest_year]

                        if 'pat' in pl_latest and 'total_equity' in latest_data:
                            pat = safe_float(pl_latest['pat'])
                            equity = safe_float(latest_data['total_equity'])

                            if equity > 0:
                                ratios['roe'] = (pat / equity) * 100

                    # Calculate ROA
                    if 'profit_loss' in company_data and latest_year in company_data['profit_loss']:
                        pl_latest = company_data['profit_loss'][latest_year]

                        if 'pat' in pl_latest and 'total_assets' in latest_data:
                            pat = safe_float(pl_latest['pat'])
                            assets = safe_float(latest_data['total_assets'])

                            if assets > 0:
                                ratios['roa'] = (pat / assets) * 100

                    # Calculate ROCE
                    if 'profit_loss' in company_data and latest_year in company_data['profit_loss']:
                        pl_latest = company_data['profit_loss'][latest_year]

                        if 'ebit' in pl_latest and 'total_assets' in latest_data and 'current_liabilities' in latest_data:
                            ebit = safe_float(pl_latest['ebit'])
                            assets = safe_float(latest_data['total_assets'])
                            current_liabilities = safe_float(latest_data['current_liabilities'])
                            capital_employed = assets - current_liabilities

                            if capital_employed > 0:
                                ratios['roce'] = (ebit / capital_employed) * 100

        except Exception as e:
            logger.error(f"Error calculating profitability ratios: {str(e)}")

        return ratios

    def calculate_leverage_ratios(self, company_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate leverage ratios

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Dictionary containing company financial data

        Returns:
        --------
        Dictionary of leverage ratios
        """
        ratios = {}

        try:
            # Helper function to convert values to float
            def safe_float(value):
                if isinstance(value, (int, float)):
                    return float(value)
                elif isinstance(value, str):
                    value = value.replace(',', '').strip()
                    if value:
                        try:
                            return float(value)
                        except ValueError:
                            return 0.0
                return 0.0

            # First, check if we have pre-calculated ratios from screener.in
            if 'ratios' in company_data and 'ratios' in company_data['ratios']:
                screener_ratios = company_data['ratios']['ratios']

                # Map screener.in ratios to our format
                if 'debt_to_equity' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['debt_to_equity'], str):
                            if screener_ratios['debt_to_equity'].strip():
                                ratios['debt_to_equity'] = float(screener_ratios['debt_to_equity'].replace('ratio', '').strip())
                        else:
                            ratios['debt_to_equity'] = float(screener_ratios['debt_to_equity'])
                    except (ValueError, TypeError):
                        pass

                if 'current_ratio' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['current_ratio'], str):
                            if screener_ratios['current_ratio'].strip():
                                ratios['current_ratio'] = float(screener_ratios['current_ratio'].replace('ratio', '').strip())
                        else:
                            ratios['current_ratio'] = float(screener_ratios['current_ratio'])
                    except (ValueError, TypeError):
                        pass

                if 'quick_ratio' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['quick_ratio'], str):
                            if screener_ratios['quick_ratio'].strip():
                                ratios['quick_ratio'] = float(screener_ratios['quick_ratio'].replace('ratio', '').strip())
                        else:
                            ratios['quick_ratio'] = float(screener_ratios['quick_ratio'])
                    except (ValueError, TypeError):
                        pass

                if 'interest_coverage_ratio' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['interest_coverage_ratio'], str):
                            if screener_ratios['interest_coverage_ratio'].strip():
                                ratios['interest_coverage'] = float(screener_ratios['interest_coverage_ratio'].replace('ratio', '').strip())
                        else:
                            ratios['interest_coverage'] = float(screener_ratios['interest_coverage_ratio'])
                    except (ValueError, TypeError):
                        pass

                # If we have some ratios, return them
                if ratios:
                    return ratios

            # If we don't have pre-calculated ratios, calculate them from financial statements
            # Extract balance sheet data
            if 'balance_sheet' in company_data:
                bs_data = company_data['balance_sheet']

                # Remove 'units' key if present
                if 'units' in bs_data:
                    bs_data.pop('units')

                # Get the most recent year's data
                latest_year = sorted([k for k in bs_data.keys() if k != 'units'])[-1] if bs_data else None

                if latest_year:
                    latest_data = bs_data[latest_year]

                    # Calculate Debt to Equity Ratio
                    if 'total_debt' in latest_data and 'total_equity' in latest_data:
                        debt = safe_float(latest_data['total_debt'])
                        equity = safe_float(latest_data['total_equity'])

                        if equity > 0:
                            ratios['debt_to_equity'] = debt / equity

                    # Calculate Debt to Asset Ratio
                    if 'total_debt' in latest_data and 'total_assets' in latest_data:
                        debt = safe_float(latest_data['total_debt'])
                        assets = safe_float(latest_data['total_assets'])

                        if assets > 0:
                            ratios['debt_to_asset'] = debt / assets

                    # Calculate Financial Leverage Ratio
                    if 'total_assets' in latest_data and 'total_equity' in latest_data:
                        assets = safe_float(latest_data['total_assets'])
                        equity = safe_float(latest_data['total_equity'])

                        if equity > 0:
                            ratios['financial_leverage'] = assets / equity

                    # Calculate Current Ratio
                    if 'current_assets' in latest_data and 'current_liabilities' in latest_data:
                        current_assets = safe_float(latest_data['current_assets'])
                        current_liabilities = safe_float(latest_data['current_liabilities'])

                        if current_liabilities > 0:
                            ratios['current_ratio'] = current_assets / current_liabilities

            # Calculate Interest Coverage Ratio
            if 'profit_loss' in company_data:
                pl_data = company_data['profit_loss']

                # Remove 'units' key if present
                if 'units' in pl_data:
                    pl_data.pop('units')

                # Get the most recent year's data
                latest_year = sorted([k for k in pl_data.keys() if k != 'units'])[-1] if pl_data else None

                if latest_year:
                    latest_data = pl_data[latest_year]

                    if 'ebit' in latest_data and 'interest_expense' in latest_data:
                        ebit = safe_float(latest_data['ebit'])
                        interest = safe_float(latest_data['interest_expense'])

                        if interest > 0:
                            ratios['interest_coverage'] = ebit / interest

        except Exception as e:
            logger.error(f"Error calculating leverage ratios: {str(e)}")

        return ratios

    def calculate_valuation_ratios(self, company_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate valuation ratios

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Dictionary containing company financial data

        Returns:
        --------
        Dictionary of valuation ratios
        """
        ratios = {}

        try:
            # Helper function to convert values to float
            def safe_float(value):
                if isinstance(value, (int, float)):
                    return float(value)
                elif isinstance(value, str):
                    value = value.replace(',', '').strip()
                    if value:
                        try:
                            return float(value)
                        except ValueError:
                            return 0.0
                return 0.0

            # First, check if we have pre-calculated ratios from screener.in
            if 'overview' in company_data:
                overview = company_data['overview']

                # Get P/E ratio
                if 'stock_p/e' in overview:
                    try:
                        pe_value = overview['stock_p/e']
                        if isinstance(pe_value, str):
                            pe_value = pe_value.replace('ratio', '').strip()
                            if pe_value:
                                ratios['pe_ratio'] = safe_float(pe_value)
                        else:
                            ratios['pe_ratio'] = safe_float(pe_value)
                    except (ValueError, TypeError):
                        pass

                # Get current price
                if 'current_price' in overview:
                    try:
                        price_value = overview['current_price']
                        if isinstance(price_value, str):
                            price_value = price_value.replace('Rs.', '').strip()
                            current_price = safe_float(price_value)
                        else:
                            current_price = safe_float(price_value)
                    except (ValueError, TypeError):
                        current_price = None
                else:
                    current_price = None

                # Get book value
                if 'book_value' in overview and current_price:
                    try:
                        bv_value = overview['book_value']
                        if isinstance(bv_value, str):
                            bv_value = bv_value.replace('Rs.', '').strip()
                            book_value = safe_float(bv_value)
                        else:
                            book_value = safe_float(bv_value)

                        if book_value > 0:
                            ratios['pb_ratio'] = current_price / book_value
                    except (ValueError, TypeError):
                        pass

            # If we don't have pre-calculated ratios, calculate them from financial statements
            # Get the current stock price if not already set
            if 'current_price' not in locals() or current_price is None:
                current_price_value = company_data.get('overview', {}).get('current_price', None)
                if current_price_value:
                    if isinstance(current_price_value, str):
                        current_price_value = current_price_value.replace('Rs.', '').strip()
                        current_price = safe_float(current_price_value)
                    else:
                        current_price = safe_float(current_price_value)

            if current_price:
                # Calculate P/E Ratio if not already set
                if 'pe_ratio' not in ratios and 'profit_loss' in company_data:
                    pl_data = company_data['profit_loss']

                    # Remove 'units' key if present
                    if 'units' in pl_data:
                        pl_data.pop('units')

                    # Get the most recent year's data
                    latest_year = sorted([k for k in pl_data.keys() if k != 'units'])[-1] if pl_data else None

                    if latest_year:
                        latest_data = pl_data[latest_year]

                        if 'eps' in latest_data:
                            eps = safe_float(latest_data['eps'])

                            if eps > 0:
                                ratios['pe_ratio'] = current_price / eps

                # Calculate P/B Ratio if not already set
                if 'pb_ratio' not in ratios and 'balance_sheet' in company_data:
                    bs_data = company_data['balance_sheet']

                    # Remove 'units' key if present
                    if 'units' in bs_data:
                        bs_data.pop('units')

                    # Get the most recent year's data
                    latest_year = sorted([k for k in bs_data.keys() if k != 'units'])[-1] if bs_data else None

                    if latest_year:
                        latest_data = bs_data[latest_year]

                        if 'book_value_per_share' in latest_data:
                            bvps = safe_float(latest_data['book_value_per_share'])

                            if bvps > 0:
                                ratios['pb_ratio'] = current_price / bvps

                # Calculate P/S Ratio
                if 'profit_loss' in company_data:
                    pl_data = company_data['profit_loss']

                    # Remove 'units' key if present
                    if 'units' in pl_data:
                        pl_data.pop('units')

                    # Get the most recent year's data
                    latest_year = sorted([k for k in pl_data.keys() if k != 'units'])[-1] if pl_data else None

                    if latest_year:
                        latest_data = pl_data[latest_year]

                        if 'revenue' in latest_data:
                            revenue = safe_float(latest_data['revenue'])

                            # Try to get shares outstanding
                            shares = None
                            if 'overview' in company_data and 'shares_outstanding' in company_data['overview']:
                                shares = safe_float(company_data['overview']['shares_outstanding'])

                            if revenue > 0 and shares and shares > 0:
                                sales_per_share = revenue / shares
                                ratios['ps_ratio'] = current_price / sales_per_share

        except Exception as e:
            logger.error(f"Error calculating valuation ratios: {str(e)}")

        return ratios

    def calculate_operating_ratios(self, company_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate operating ratios

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Dictionary containing company financial data

        Returns:
        --------
        Dictionary of operating ratios
        """
        ratios = {}

        try:
            # First, check if we have pre-calculated ratios from screener.in
            if 'ratios' in company_data and 'ratios' in company_data['ratios']:
                screener_ratios = company_data['ratios']['ratios']

                # Map screener.in ratios to our format
                if 'debtor_days' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['debtor_days'], str):
                            if screener_ratios['debtor_days'].strip():
                                ratios['days_sales_outstanding'] = float(screener_ratios['debtor_days'])
                        else:
                            ratios['days_sales_outstanding'] = float(screener_ratios['debtor_days'])
                    except (ValueError, TypeError):
                        pass

                if 'inventory_days' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['inventory_days'], str):
                            if screener_ratios['inventory_days'].strip():
                                ratios['inventory_days'] = float(screener_ratios['inventory_days'])
                        else:
                            ratios['inventory_days'] = float(screener_ratios['inventory_days'])
                    except (ValueError, TypeError):
                        pass

                if 'working_capital_days' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['working_capital_days'], str):
                            if screener_ratios['working_capital_days'].strip():
                                ratios['working_capital_days'] = float(screener_ratios['working_capital_days'])
                        else:
                            ratios['working_capital_days'] = float(screener_ratios['working_capital_days'])
                    except (ValueError, TypeError):
                        pass

                # If we have inventory_days, calculate inventory_turnover
                if 'inventory_days' in ratios and ratios['inventory_days'] > 0:
                    ratios['inventory_turnover'] = 365 / ratios['inventory_days']

                # If we have days_sales_outstanding, calculate receivables_turnover
                if 'days_sales_outstanding' in ratios and ratios['days_sales_outstanding'] > 0:
                    ratios['receivables_turnover'] = 365 / ratios['days_sales_outstanding']

                return ratios

            # If we don't have pre-calculated ratios, calculate them from financial statements
            # Extract balance sheet and profit & loss data
            bs_data = company_data.get('balance_sheet', {})
            pl_data = company_data.get('profit_loss', {})

            # Remove 'units' key if present
            if 'units' in bs_data:
                bs_data.pop('units')
            if 'units' in pl_data:
                pl_data.pop('units')

            # Get the most recent year's data
            latest_year_bs = sorted([k for k in bs_data.keys() if k != 'units'])[-1] if bs_data else None
            latest_year_pl = sorted([k for k in pl_data.keys() if k != 'units'])[-1] if pl_data else None

            if latest_year_bs and latest_year_pl:
                latest_bs = bs_data[latest_year_bs]
                latest_pl = pl_data[latest_year_pl]

                # Helper function to convert values to float
                def safe_float(value):
                    if isinstance(value, (int, float)):
                        return float(value)
                    elif isinstance(value, str):
                        value = value.replace(',', '').strip()
                        if value:
                            try:
                                return float(value)
                            except ValueError:
                                return 0.0
                    return 0.0

                # Calculate Fixed Assets Turnover Ratio
                if 'revenue' in latest_pl and 'fixed_assets' in latest_bs:
                    revenue = safe_float(latest_pl['revenue'])
                    fixed_assets = safe_float(latest_bs['fixed_assets'])

                    if fixed_assets > 0:
                        ratios['fixed_assets_turnover'] = revenue / fixed_assets

                # Calculate Working Capital Turnover Ratio
                if 'revenue' in latest_pl and 'current_assets' in latest_bs and 'current_liabilities' in latest_bs:
                    revenue = safe_float(latest_pl['revenue'])
                    current_assets = safe_float(latest_bs['current_assets'])
                    current_liabilities = safe_float(latest_bs['current_liabilities'])
                    working_capital = current_assets - current_liabilities

                    if working_capital > 0:
                        ratios['working_capital_turnover'] = revenue / working_capital

                # Calculate Total Assets Turnover Ratio
                if 'revenue' in latest_pl and 'total_assets' in latest_bs:
                    revenue = safe_float(latest_pl['revenue'])
                    total_assets = safe_float(latest_bs['total_assets'])

                    if total_assets > 0:
                        ratios['total_assets_turnover'] = revenue / total_assets

                # Calculate Inventory Turnover Ratio
                if 'cogs' in latest_pl and 'inventory' in latest_bs:
                    cogs = safe_float(latest_pl['cogs'])
                    inventory = safe_float(latest_bs['inventory'])

                    if inventory > 0:
                        ratios['inventory_turnover'] = cogs / inventory
                        ratios['inventory_days'] = 365 / ratios['inventory_turnover']

                # Calculate Accounts Receivable Turnover Ratio
                if 'revenue' in latest_pl and 'accounts_receivable' in latest_bs:
                    revenue = safe_float(latest_pl['revenue'])
                    accounts_receivable = safe_float(latest_bs['accounts_receivable'])

                    if accounts_receivable > 0:
                        ratios['receivables_turnover'] = revenue / accounts_receivable
                        ratios['days_sales_outstanding'] = 365 / ratios['receivables_turnover']

        except Exception as e:
            logger.error(f"Error calculating operating ratios: {str(e)}")

        return ratios

    def calculate_cash_flow_ratios(self, company_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate cash flow ratios

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Dictionary containing company financial data

        Returns:
        --------
        Dictionary of cash flow ratios
        """
        ratios = {}

        try:
            # Helper function to convert values to float
            def safe_float(value):
                if isinstance(value, (int, float)):
                    return float(value)
                elif isinstance(value, str):
                    value = value.replace(',', '').strip()
                    if value:
                        try:
                            return float(value)
                        except ValueError:
                            return 0.0
                return 0.0

            # Extract cash flow data
            cf_data = company_data.get('cash_flow', {})
            pl_data = company_data.get('profit_loss', {})
            bs_data = company_data.get('balance_sheet', {})

            # Remove 'units' key if present
            if 'units' in cf_data:
                cf_data.pop('units')
            if 'notes' in cf_data:
                cf_data.pop('notes')
            if 'units' in pl_data:
                pl_data.pop('units')
            if 'units' in bs_data:
                bs_data.pop('units')

            # For cash flow, we need to look inside each key to find the years
            # First, check if we have the cash flow data in the expected format
            cf_years = []

            # Try to access the cash flow data by checking all keys
            operating_key = None
            investing_key = None
            financing_key = None

            # Log all keys for debugging
            logger.info(f"Cash flow keys: {list(cf_data.keys())}")

            # Find the keys that contain the cash flow data
            for key in cf_data.keys():
                if isinstance(key, str):
                    if 'operating' in key:
                        operating_key = key
                    elif 'investing' in key:
                        investing_key = key
                    elif 'financing' in key:
                        financing_key = key

            logger.info(f"Found cash flow keys: operating={operating_key}, investing={investing_key}, financing={financing_key}")

            # If we have the operating key, get the years
            if operating_key and isinstance(cf_data[operating_key], dict):
                cf_years = sorted(list(cf_data[operating_key].keys()))

            pl_years = sorted([k for k in pl_data.keys() if k != 'units' and k != 'notes'])
            bs_years = sorted([k for k in bs_data.keys() if k != 'units' and k != 'notes'])

            logger.info(f"Years found - CF: {cf_years}, PL: {pl_years}, BS: {bs_years}")

            # If we have cash flow data, let's log it for debugging
            if cf_years:
                latest_cf_year = cf_years[-1]

                operating_cf = cf_data.get(operating_key, {}).get(latest_cf_year) if operating_key else None
                investing_cf = cf_data.get(investing_key, {}).get(latest_cf_year) if investing_key else None
                financing_cf = cf_data.get(financing_key, {}).get(latest_cf_year) if financing_key else None
                net_cf = cf_data.get('net_cash_flow', {}).get(latest_cf_year)

                logger.info(f"Cash flow data for {latest_cf_year}: operating={operating_cf}, investing={investing_cf}, financing={financing_cf}, net={net_cf}")

            if cf_years and pl_years and bs_years:
                latest_cf_year = cf_years[-1]
                latest_pl_year = pl_years[-1]
                latest_bs_year = bs_years[-1]

                # Try to access the cash flow data by checking all keys
                operating_key = None
                investing_key = None
                financing_key = None

                # Find the keys that contain the cash flow data
                for key in cf_data.keys():
                    if isinstance(key, str):
                        if 'operating' in key:
                            operating_key = key
                        elif 'investing' in key:
                            investing_key = key
                        elif 'financing' in key:
                            financing_key = key

                # Get operating cash flow
                operating_cash_flow = None
                if operating_key and latest_cf_year in cf_data[operating_key]:
                    operating_cash_flow = safe_float(cf_data[operating_key][latest_cf_year])

                # Get investing cash flow
                investing_cash_flow = None
                if investing_key and latest_cf_year in cf_data[investing_key]:
                    investing_cash_flow = safe_float(cf_data[investing_key][latest_cf_year])

                # Get financing cash flow
                financing_cash_flow = None
                if financing_key and latest_cf_year in cf_data[financing_key]:
                    financing_cash_flow = safe_float(cf_data[financing_key][latest_cf_year])

                # Get net cash flow
                net_cash_flow = None
                if 'net_cash_flow' in cf_data and latest_cf_year in cf_data['net_cash_flow']:
                    net_cash_flow = safe_float(cf_data['net_cash_flow'][latest_cf_year])

                logger.info(f"Cash flow data for {latest_cf_year}: operating={operating_cash_flow}, investing={investing_cash_flow}, financing={financing_cash_flow}, net={net_cash_flow}")



                # Store the raw cash flow values
                if operating_cash_flow is not None:
                    ratios['operating_cash_flow'] = operating_cash_flow
                if investing_cash_flow is not None:
                    ratios['investing_cash_flow'] = investing_cash_flow
                if financing_cash_flow is not None:
                    ratios['financing_cash_flow'] = financing_cash_flow
                if net_cash_flow is not None:
                    ratios['net_cash_flow'] = net_cash_flow

                # Calculate cash flow ratios if we have the necessary data
                if operating_cash_flow is not None and pl_data.get(latest_pl_year, {}).get('revenue'):
                    revenue = safe_float(pl_data[latest_pl_year]['revenue'])
                    if revenue > 0:
                        # Operating Cash Flow to Revenue ratio
                        ratios['ocf_to_revenue'] = (operating_cash_flow / revenue) * 100

                if operating_cash_flow is not None and pl_data.get(latest_pl_year, {}).get('pat'):
                    pat = safe_float(pl_data[latest_pl_year]['pat'])
                    if pat > 0:
                        # Operating Cash Flow to Net Income ratio
                        ratios['ocf_to_net_income'] = (operating_cash_flow / pat) * 100

                if operating_cash_flow is not None and bs_data.get(latest_bs_year, {}).get('total_assets'):
                    total_assets = safe_float(bs_data[latest_bs_year]['total_assets'])
                    if total_assets > 0:
                        # Cash Return on Assets
                        ratios['cash_return_on_assets'] = (operating_cash_flow / total_assets) * 100

                if operating_cash_flow is not None and investing_cash_flow is not None:
                    # Free Cash Flow
                    free_cash_flow = operating_cash_flow + investing_cash_flow
                    ratios['free_cash_flow'] = free_cash_flow

                    if pl_data.get(latest_pl_year, {}).get('revenue'):
                        revenue = safe_float(pl_data[latest_pl_year]['revenue'])
                        if revenue > 0:
                            # Free Cash Flow to Revenue ratio
                            ratios['fcf_to_revenue'] = (free_cash_flow / revenue) * 100

        except Exception as e:
            logger.error(f"Error calculating cash flow ratios: {str(e)}")

        return ratios

    def analyze_cash_flow_patterns(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze cash flow patterns to identify financial health indicators

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Dictionary containing company financial data

        Returns:
        --------
        Dictionary of cash flow pattern analysis
        """
        analysis = {}

        try:
            # Helper function to convert values to float
            def safe_float(value):
                if isinstance(value, (int, float)):
                    return float(value)
                elif isinstance(value, str):
                    value = value.replace(',', '').strip()
                    if value:
                        try:
                            return float(value)
                        except ValueError:
                            return 0.0
                return 0.0

            # Extract cash flow data
            cf_data = company_data.get('cash_flow', {})

            # Remove 'units' key if present
            if 'units' in cf_data:
                cf_data.pop('units')
            if 'notes' in cf_data:
                cf_data.pop('notes')

            # For cash flow, we need to look inside each key to find the years
            cf_years = []

            # Try to access the cash flow data by checking all keys
            operating_key = None
            investing_key = None
            financing_key = None

            # Find the keys that contain the cash flow data
            for key in cf_data.keys():
                if isinstance(key, str):
                    if 'operating' in key:
                        operating_key = key
                    elif 'investing' in key:
                        investing_key = key
                    elif 'financing' in key:
                        financing_key = key

            # Look for years in the cash_from_operating_activity+ key
            if operating_key and isinstance(cf_data[operating_key], dict):
                cf_years = list(cf_data[operating_key].keys())
                cf_years = sorted(cf_years)

            if cf_years:
                # Initialize data structures for analysis
                operating_trend = []
                investing_trend = []
                financing_trend = []
                net_trend = []

                # Collect cash flow data for each year
                for year in cf_years:
                    # Get operating cash flow
                    operating_cash_flow = None
                    if operating_key and year in cf_data[operating_key]:
                        operating_cash_flow = safe_float(cf_data[operating_key][year])
                        operating_trend.append((year, operating_cash_flow))

                    # Get investing cash flow
                    investing_cash_flow = None
                    if investing_key and year in cf_data[investing_key]:
                        investing_cash_flow = safe_float(cf_data[investing_key][year])
                        investing_trend.append((year, investing_cash_flow))

                    # Get financing cash flow
                    financing_cash_flow = None
                    if financing_key and year in cf_data[financing_key]:
                        financing_cash_flow = safe_float(cf_data[financing_key][year])
                        financing_trend.append((year, financing_cash_flow))

                    # Get net cash flow
                    if 'net_cash_flow' in cf_data and year in cf_data['net_cash_flow']:
                        net_cash_flow = safe_float(cf_data['net_cash_flow'][year])
                        net_trend.append((year, net_cash_flow))

                # Analyze the latest year's cash flow pattern
                if operating_trend and investing_trend and financing_trend:
                    latest_operating = operating_trend[-1][1]
                    latest_investing = investing_trend[-1][1]
                    latest_financing = financing_trend[-1][1]

                    # Determine cash flow pattern
                    pattern = {
                        'operating_sign': 'positive' if latest_operating > 0 else 'negative',
                        'investing_sign': 'positive' if latest_investing > 0 else 'negative',
                        'financing_sign': 'positive' if latest_financing > 0 else 'negative'
                    }

                    # Interpret the pattern
                    if pattern['operating_sign'] == 'positive':
                        if pattern['investing_sign'] == 'negative':
                            if pattern['financing_sign'] == 'negative':
                                # +/-/- : Ideal mature business
                                pattern['interpretation'] = 'Ideal mature business: Generating cash from operations, investing in growth, and returning value to shareholders.'
                                pattern['health'] = 'excellent'
                            else:
                                # +/-/+ : Growing business with external funding
                                pattern['interpretation'] = 'Growing business: Generating cash from operations, investing heavily in growth with additional external funding.'
                                pattern['health'] = 'good'
                        else:
                            if pattern['financing_sign'] == 'negative':
                                # +/+/- : Mature business divesting or reducing operations
                                pattern['interpretation'] = 'Mature business possibly divesting: Generating cash from operations, selling assets, and returning value to shareholders.'
                                pattern['health'] = 'caution'
                            else:
                                # +/+/+ : Unusual pattern, possibly restructuring
                                pattern['interpretation'] = 'Unusual pattern: Generating cash from operations, selling assets, and raising external funding. Possible restructuring.'
                                pattern['health'] = 'caution'
                    else:  # Operating cash flow negative
                        if pattern['investing_sign'] == 'negative':
                            if pattern['financing_sign'] == 'positive':
                                # -/-/+ : Early stage or troubled business
                                pattern['interpretation'] = 'Early stage or troubled business: Not generating cash from operations, investing heavily, and relying on external funding.'
                                pattern['health'] = 'warning'
                            else:
                                # -/-/- : Severe distress
                                pattern['interpretation'] = 'Severe distress: Not generating cash from operations, still investing, and not able to raise additional funding.'
                                pattern['health'] = 'danger'
                        else:
                            if pattern['financing_sign'] == 'positive':
                                # -/+/+ : Restructuring or decline
                                pattern['interpretation'] = 'Restructuring or decline: Not generating cash from operations, selling assets, and raising external funding.'
                                pattern['health'] = 'warning'
                            else:
                                # -/+/- : Terminal decline
                                pattern['interpretation'] = 'Terminal decline: Not generating cash from operations, selling assets to stay afloat, and returning value to shareholders or paying down debt.'
                                pattern['health'] = 'danger'

                    analysis['pattern'] = pattern

                    # Analyze operating cash flow trend
                    if len(operating_trend) >= 3:
                        recent_years = operating_trend[-3:]
                        if all(year[1] > 0 for year in recent_years):
                            if recent_years[2][1] > recent_years[1][1] > recent_years[0][1]:
                                analysis['operating_trend'] = 'Consistently positive and increasing operating cash flow - excellent sign.'
                            elif recent_years[2][1] > recent_years[0][1]:
                                analysis['operating_trend'] = 'Positive operating cash flow with overall upward trend - good sign.'
                            else:
                                analysis['operating_trend'] = 'Positive but declining operating cash flow - monitor closely.'
                        elif recent_years[2][1] > 0:
                            analysis['operating_trend'] = 'Recently turned positive operating cash flow - potential improvement.'
                        else:
                            analysis['operating_trend'] = 'Negative operating cash flow - concerning sign.'

        except Exception as e:
            logger.error(f"Error analyzing cash flow patterns: {str(e)}")

        return analysis

    def calculate_growth_rates(self, company_data: Dict[str, Any], years: int = 5) -> Dict[str, float]:
        """
        Calculate growth rates for key financial metrics

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Dictionary containing company financial data
        years : int
            Number of years to consider for growth rate calculation

        Returns:
        --------
        Dictionary of growth rates
        """
        growth_rates = {}

        try:
            # Helper function to convert values to float
            def safe_float(value):
                if isinstance(value, (int, float)):
                    return float(value)
                elif isinstance(value, str):
                    value = value.replace(',', '').strip()
                    if value:
                        try:
                            return float(value)
                        except ValueError:
                            return 0.0
                return 0.0

            # First, check if we have pre-calculated growth rates from screener.in
            if 'ratios' in company_data and 'ratios' in company_data['ratios']:
                screener_ratios = company_data['ratios']['ratios']

                # Map screener.in growth rates to our format
                if 'sales_growth_5years' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['sales_growth_5years'], str):
                            if screener_ratios['sales_growth_5years'].strip():
                                growth_rates['revenue_cagr'] = float(screener_ratios['sales_growth_5years'].replace('%', ''))
                        else:
                            growth_rates['revenue_cagr'] = float(screener_ratios['sales_growth_5years'])
                    except (ValueError, TypeError):
                        pass

                if 'profit_growth_5years' in screener_ratios:
                    try:
                        if isinstance(screener_ratios['profit_growth_5years'], str):
                            if screener_ratios['profit_growth_5years'].strip():
                                growth_rates['pat_cagr'] = float(screener_ratios['profit_growth_5years'].replace('%', ''))
                        else:
                            growth_rates['pat_cagr'] = float(screener_ratios['profit_growth_5years'])
                    except (ValueError, TypeError):
                        pass

                # If we have some growth rates, return them
                if growth_rates:
                    return growth_rates

            # If we don't have pre-calculated growth rates, calculate them from financial statements
            # Extract profit and loss data
            pl_data = company_data.get('profit_loss', {})

            # Remove 'units' key if present
            if 'units' in pl_data:
                pl_data.pop('units')

            # Get the years in descending order
            all_years = sorted([k for k in pl_data.keys() if k != 'units'], reverse=True)

            if len(all_years) >= years:
                latest_year = all_years[0]
                earliest_year = all_years[years-1]

                latest_data = pl_data[latest_year]
                earliest_data = pl_data[earliest_year]

                # Calculate Revenue CAGR
                if 'revenue' in latest_data and 'revenue' in earliest_data:
                    latest_revenue = safe_float(latest_data['revenue'])
                    earliest_revenue = safe_float(earliest_data['revenue'])

                    if earliest_revenue > 0:
                        cagr = ((latest_revenue / earliest_revenue) ** (1 / years)) - 1
                        growth_rates['revenue_cagr'] = cagr * 100

                # Calculate EBITDA CAGR
                if 'ebitda' in latest_data and 'ebitda' in earliest_data:
                    latest_ebitda = safe_float(latest_data['ebitda'])
                    earliest_ebitda = safe_float(earliest_data['ebitda'])

                    if earliest_ebitda > 0:
                        cagr = ((latest_ebitda / earliest_ebitda) ** (1 / years)) - 1
                        growth_rates['ebitda_cagr'] = cagr * 100

                # Calculate PAT CAGR
                if 'pat' in latest_data and 'pat' in earliest_data:
                    latest_pat = safe_float(latest_data['pat'])
                    earliest_pat = safe_float(earliest_data['pat'])

                    if earliest_pat > 0:
                        cagr = ((latest_pat / earliest_pat) ** (1 / years)) - 1
                        growth_rates['pat_cagr'] = cagr * 100

                # Calculate EPS CAGR
                if 'eps' in latest_data and 'eps' in earliest_data:
                    latest_eps = safe_float(latest_data['eps'])
                    earliest_eps = safe_float(earliest_data['eps'])

                    if earliest_eps > 0:
                        cagr = ((latest_eps / earliest_eps) ** (1 / years)) - 1
                        growth_rates['eps_cagr'] = cagr * 100

        except Exception as e:
            logger.error(f"Error calculating growth rates: {str(e)}")

        return growth_rates

# Example usage
if __name__ == "__main__":
    # Example company data
    company_data = {
        'profit_loss': {
            '2022': {'revenue': 1000, 'ebitda': 200, 'ebit': 150, 'pat': 100, 'eps': 10},
            '2021': {'revenue': 900, 'ebitda': 180, 'ebit': 130, 'pat': 90, 'eps': 9},
            '2020': {'revenue': 800, 'ebitda': 160, 'ebit': 120, 'pat': 80, 'eps': 8},
            '2019': {'revenue': 700, 'ebitda': 140, 'ebit': 100, 'pat': 70, 'eps': 7},
            '2018': {'revenue': 600, 'ebitda': 120, 'ebit': 90, 'pat': 60, 'eps': 6}
        },
        'balance_sheet': {
            '2022': {'total_assets': 2000, 'total_equity': 1000, 'total_debt': 500, 'current_assets': 800, 'current_liabilities': 400}
        },
        'overview': {
            'current_price': 200,
            'shares_outstanding': 10
        }
    }

    ratios = FinancialRatios()
    all_ratios = ratios.calculate_all_ratios(company_data)
    growth_rates = ratios.calculate_growth_rates(company_data)

    print("Profitability Ratios:")
    for key, value in all_ratios['profitability'].items():
        print(f"  {key}: {value:.2f}")

    print("\nGrowth Rates:")
    for key, value in growth_rates.items():
        print(f"  {key}: {value:.2f}%")
