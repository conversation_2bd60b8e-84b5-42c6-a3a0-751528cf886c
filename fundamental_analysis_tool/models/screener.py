#!/usr/bin/env python3
"""
Screener Module

This module provides a multi-tier screening system for fundamental analysis.
It allows for efficient screening of large numbers of companies based on
customizable criteria.
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Set
from concurrent.futures import ThreadPoolExecutor, as_completed

# Import consistency analyzer
from models.consistency_analyzer import ConsistencyAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('screener')

class FundamentalScreener:
    """
    Class to screen companies based on fundamental analysis criteria
    """

    def __init__(self, data_loader):
        """
        Initialize the screener

        Parameters:
        -----------
        data_loader : ScreenerDataLoader
            Data loader instance to fetch company data
        """
        self.data_loader = data_loader
        self.consistency_analyzer = ConsistencyAnalyzer()

        # Default screening criteria
        self.default_criteria = {
            # Tier 1: Basic financial health
            'tier1': {
                'roe': {'min': 15.0, 'max': None},  # ROE > 15%
                'roce': {'min': 15.0, 'max': None},  # ROCE > 15%
                'debt_to_equity': {'min': None, 'max': 1.0},  # D/E < 1
                'interest_coverage': {'min': 3.0, 'max': None},  # Interest coverage > 3
            },
            # Tier 2: Growth metrics
            'tier2': {
                'revenue_cagr': {'min': 10.0, 'max': None},  # Revenue CAGR > 10%
                'profit_cagr': {'min': 10.0, 'max': None},  # Profit CAGR > 10%
            },
            # Tier 3: Valuation metrics
            'tier3': {
                'pe_ratio': {'min': None, 'max': 25.0},  # P/E < 25
                'pb_ratio': {'min': None, 'max': 3.0},  # P/B < 3
            },
            # Tier 4: Cash flow analysis
            'tier4': {
                'operating_cash_flow': {'min': 0.0, 'max': None},  # Positive operating cash flow
                'free_cash_flow': {'min': 0.0, 'max': None},  # Positive free cash flow
                'ocf_to_net_income': {'min': 80.0, 'max': None},  # OCF/NI > 80%
            },
            # Tier 5: Historical consistency
            'tier5': {
                'overall_consistency_score': {'min': 60.0, 'max': None},  # Overall consistency score > 60
                'growth_consistency_score': {'min': 60.0, 'max': None},  # Growth consistency score > 60
                'profitability_consistency_score': {'min': 60.0, 'max': None},  # Profitability consistency score > 60
                'quarterly_consistency_score': {'min': 60.0, 'max': None}  # Quarterly consistency score > 60
            }
        }

    def screen_companies(self,
                        tickers: List[str] = None,
                        criteria: Dict[str, Dict[str, Dict[str, float]]] = None,
                        max_workers: int = 4) -> Dict[str, Any]:
        """
        Screen companies based on fundamental criteria

        Parameters:
        -----------
        tickers : List[str], optional
            List of tickers to screen (default: all tickers)
        criteria : Dict[str, Dict[str, Dict[str, float]]], optional
            Screening criteria (default: self.default_criteria)
        max_workers : int
            Maximum number of worker threads for parallel processing

        Returns:
        --------
        Dictionary containing screening results
        """
        # Use default criteria if none provided
        if criteria is None:
            criteria = self.default_criteria

        # Use all tickers if none provided
        if tickers is None:
            tickers = self.data_loader.get_all_tickers()

        logger.info(f"Screening {len(tickers)} companies")

        # Pre-filter companies based on summary data
        pre_filtered_tickers = self._pre_filter_companies(tickers, criteria)

        logger.info(f"Pre-filtered to {len(pre_filtered_tickers)} companies")

        # Process companies in parallel
        results = {}
        passed_companies = {
            'tier1': set(),
            'tier2': set(),
            'tier3': set(),
            'tier4': set(),
            'tier5': set(),
            'all': set()
        }

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit tasks
            future_to_ticker = {
                executor.submit(self._screen_company, ticker, criteria): ticker
                for ticker in pre_filtered_tickers
            }

            # Process results as they complete
            for future in as_completed(future_to_ticker):
                ticker = future_to_ticker[future]
                try:
                    result = future.result()
                    results[ticker] = result

                    # Track companies that pass each tier
                    for tier in ['tier1', 'tier2', 'tier3', 'tier4', 'tier5']:
                        if result.get(f'{tier}_pass', False):
                            passed_companies[tier].add(ticker)

                    # Track companies that pass all tiers
                    if all(result.get(f'{tier}_pass', False) for tier in ['tier1', 'tier2', 'tier3', 'tier4', 'tier5']):
                        passed_companies['all'].add(ticker)

                except Exception as e:
                    logger.error(f"Error screening {ticker}: {str(e)}")
                    results[ticker] = {'error': str(e)}

        # Convert sets to lists for JSON serialization
        for key in passed_companies:
            passed_companies[key] = list(passed_companies[key])

        return {
            'results': results,
            'passed_companies': passed_companies,
            'summary': {
                'total': len(tickers),
                'pre_filtered': len(pre_filtered_tickers),
                'tier1_pass': len(passed_companies['tier1']),
                'tier2_pass': len(passed_companies['tier2']),
                'tier3_pass': len(passed_companies['tier3']),
                'tier4_pass': len(passed_companies['tier4']),
                'tier5_pass': len(passed_companies['tier5']),
                'all_pass': len(passed_companies['all'])
            }
        }

    def _pre_filter_companies(self,
                             tickers: List[str],
                             criteria: Dict[str, Dict[str, Dict[str, float]]]) -> List[str]:
        """
        Pre-filter companies based on summary data

        Parameters:
        -----------
        tickers : List[str]
            List of tickers to pre-filter
        criteria : Dict[str, Dict[str, Dict[str, float]]]
            Screening criteria

        Returns:
        --------
        List of pre-filtered tickers
        """
        try:
            # Load summary data
            summary_data = self.data_loader.load_all_companies_summary()

            if summary_data.empty:
                logger.warning("Summary data is empty, skipping pre-filtering")
                return tickers

            # Set ticker as index if it's not already
            if 'ticker' in summary_data.columns:
                summary_data = summary_data.set_index('ticker')

            # Pre-filter based on summary data
            filtered_tickers = []

            for ticker in tickers:
                if ticker not in summary_data.index:
                    # Include tickers not in summary data for full analysis
                    filtered_tickers.append(ticker)
                    continue

                row = summary_data.loc[ticker]

                # Apply pre-filters based on available summary data
                passes_filters = True

                # Market cap filter
                if 'market_cap_cr' in row:
                    market_cap = row['market_cap_cr']
                    min_market_cap = criteria.get('pre_filter', {}).get('market_cap', {}).get('min')
                    max_market_cap = criteria.get('pre_filter', {}).get('market_cap', {}).get('max')

                    if min_market_cap is not None and market_cap < min_market_cap:
                        passes_filters = False
                    if max_market_cap is not None and market_cap > max_market_cap:
                        passes_filters = False

                # P/E filter
                if 'pe' in row:
                    pe = row['pe']
                    max_pe = criteria.get('tier3', {}).get('pe_ratio', {}).get('max')

                    if max_pe is not None and pe is not None and pe > max_pe:
                        passes_filters = False

                # ROCE filter
                if 'roce_percent' in row:
                    roce = row['roce_percent']
                    min_roce = criteria.get('tier1', {}).get('roce', {}).get('min')

                    if min_roce is not None and roce is not None and roce < min_roce:
                        passes_filters = False

                if passes_filters:
                    filtered_tickers.append(ticker)

            return filtered_tickers

        except Exception as e:
            logger.error(f"Error in pre-filtering: {str(e)}")
            return tickers  # Return all tickers if pre-filtering fails

    def _screen_company(self,
                       ticker: str,
                       criteria: Dict[str, Dict[str, Dict[str, float]]]) -> Dict[str, Any]:
        """
        Screen a single company against the criteria

        Parameters:
        -----------
        ticker : str
            Ticker symbol of the company
        criteria : Dict[str, Dict[str, Dict[str, float]]]
            Screening criteria

        Returns:
        --------
        Dictionary containing screening results for the company
        """
        result = {
            'ticker': ticker,
            'tier1_pass': False,
            'tier2_pass': False,
            'tier3_pass': False,
            'tier4_pass': False,
            'tier5_pass': False,
            'metrics': {},
            'consistency': {}
        }

        try:
            # Load company data
            company_data = self.data_loader.load_company_data(ticker)

            if not company_data:
                return {**result, 'error': 'No data found'}

            # Extract metrics from company data
            metrics = self._extract_metrics(company_data)
            result['metrics'] = metrics

            # Analyze historical consistency
            consistency_results = self.consistency_analyzer.analyze_company_consistency(company_data)
            result['consistency'] = consistency_results

            # Add consistency metrics to the metrics dictionary
            if consistency_results['overall_consistency_score'] is not None:
                metrics['overall_consistency_score'] = consistency_results['overall_consistency_score']

            if 'overall_score' in consistency_results['growth_consistency']:
                metrics['growth_consistency_score'] = consistency_results['growth_consistency']['overall_score']

            if 'overall_score' in consistency_results['profitability_consistency']:
                metrics['profitability_consistency_score'] = consistency_results['profitability_consistency']['overall_score']

            if 'overall_score' in consistency_results['quarterly_consistency']:
                metrics['quarterly_consistency_score'] = consistency_results['quarterly_consistency']['overall_score']

            # Store company data for detailed analysis
            result['company_data'] = company_data

            # Check each tier
            result['tier1_pass'] = self._check_tier(metrics, criteria.get('tier1', {}))
            result['tier2_pass'] = self._check_tier(metrics, criteria.get('tier2', {}))
            result['tier3_pass'] = self._check_tier(metrics, criteria.get('tier3', {}))
            result['tier4_pass'] = self._check_tier(metrics, criteria.get('tier4', {}))
            result['tier5_pass'] = self._check_tier(metrics, criteria.get('tier5', {}))

        except Exception as e:
            logger.error(f"Error screening {ticker}: {str(e)}")
            result['error'] = str(e)

        return result

    def _extract_metrics(self, company_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Extract metrics from company data

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data

        Returns:
        --------
        Dictionary of metrics
        """
        metrics = {}

        # Helper function to safely get nested values
        def safe_get(data, *keys, default=None):
            current = data
            for key in keys:
                if not isinstance(current, dict) or key not in current:
                    return default
                current = current[key]
            return current

        # Extract basic financial health metrics (Tier 1)
        metrics['roe'] = safe_get(company_data, 'ratios', 'ratios', 'roe_%')
        metrics['roce'] = safe_get(company_data, 'ratios', 'ratios', 'roce_%')
        metrics['debt_to_equity'] = safe_get(company_data, 'ratios', 'ratios', 'debt_to_equity')
        metrics['interest_coverage'] = safe_get(company_data, 'ratios', 'ratios', 'interest_coverage')

        # Extract growth metrics (Tier 2)
        metrics['revenue_cagr'] = safe_get(company_data, 'growth_rates', 'revenue_cagr')
        metrics['profit_cagr'] = safe_get(company_data, 'growth_rates', 'pat_cagr')

        # Extract valuation metrics (Tier 3)
        metrics['pe_ratio'] = safe_get(company_data, 'ratios', 'ratios', 'pe_ratio')
        metrics['pb_ratio'] = safe_get(company_data, 'ratios', 'ratios', 'pb_ratio')

        # Extract cash flow metrics (Tier 4)
        metrics['operating_cash_flow'] = safe_get(company_data, 'ratios', 'ratios', 'operating_cash_flow')
        metrics['free_cash_flow'] = safe_get(company_data, 'ratios', 'ratios', 'free_cash_flow')
        metrics['ocf_to_net_income'] = safe_get(company_data, 'ratios', 'ratios', 'ocf_to_net_income')

        return metrics

    def _check_tier(self, metrics: Dict[str, float], criteria: Dict[str, Dict[str, float]]) -> bool:
        """
        Check if metrics pass a tier's criteria

        Parameters:
        -----------
        metrics : Dict[str, float]
            Company metrics
        criteria : Dict[str, Dict[str, float]]
            Tier criteria

        Returns:
        --------
        True if all criteria are met, False otherwise
        """
        # If no criteria for this tier, consider it passed
        if not criteria:
            return True

        # Check each criterion
        for metric, limits in criteria.items():
            value = metrics.get(metric)

            # Skip if metric is not available
            if value is None:
                continue

            min_value = limits.get('min')
            max_value = limits.get('max')

            # Check minimum value
            if min_value is not None and (value is None or value < min_value):
                return False

            # Check maximum value
            if max_value is not None and (value is not None and value > max_value):
                return False

        return True
