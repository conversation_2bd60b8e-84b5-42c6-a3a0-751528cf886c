#!/usr/bin/env python3
"""
Cash Flow Pre-screener Module

This module implements the first-stage screening based purely on 
cash flow analysis over the last 10 years. Only companies that 
pass this screening proceed to more rigorous analysis.
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('cashflow_prescreener')

class CashFlowPreScreener:
    """
    Class to perform cash flow-based pre-screening
    """
    
    def __init__(self, min_years: int = 7, min_positive_years: int = 5):
        """
        Initialize the cash flow pre-screener
        
        Parameters:
        -----------
        min_years : int
            Minimum number of years of cash flow data required
        min_positive_years : int
            Minimum number of years with positive operating cash flow
        """
        self.min_years = min_years
        self.min_positive_years = min_positive_years
        
        # Define cash flow quality criteria
        self.quality_criteria = {
            'operating_cash_flow_positive_years': min_positive_years,
            'free_cash_flow_positive_years': 3,  # At least 3 years of positive FCF
            'cash_flow_growth_consistency': 0.6,  # 60% of years should show growth
            'operating_margin_consistency': 0.5,  # 50% stability in operating margins
            'working_capital_efficiency': True,  # Efficient working capital management
        }
    
    def prescreen_company(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Pre-screen a company based on cash flow analysis
        
        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data including cash flow information
            
        Returns:
        --------
        Dictionary containing pre-screening results
        """
        result = {
            'ticker': company_data.get('overview', {}).get('ticker', 'Unknown'),
            'passed_prescreen': False,
            'cash_flow_score': 0,
            'analysis': {},
            'reasons_failed': [],
            'reasons_passed': []
        }
        
        try:
            # Extract cash flow time series
            cash_flow_data = self._extract_cash_flow_time_series(company_data)
            
            if not cash_flow_data:
                result['reasons_failed'].append('No cash flow data available')
                return result
            
            # Analyze operating cash flow
            ocf_analysis = self._analyze_operating_cash_flow(cash_flow_data)
            result['analysis']['operating_cash_flow'] = ocf_analysis
            
            # Analyze free cash flow
            fcf_analysis = self._analyze_free_cash_flow(cash_flow_data)
            result['analysis']['free_cash_flow'] = fcf_analysis
            
            # Analyze cash flow quality
            quality_analysis = self._analyze_cash_flow_quality(company_data, cash_flow_data)
            result['analysis']['quality'] = quality_analysis
            
            # Calculate overall cash flow score
            cash_flow_score = self._calculate_cash_flow_score(
                ocf_analysis, fcf_analysis, quality_analysis
            )
            result['cash_flow_score'] = cash_flow_score
            
            # Determine if company passes pre-screening
            passed, reasons = self._evaluate_prescreen_criteria(
                ocf_analysis, fcf_analysis, quality_analysis, cash_flow_score
            )
            
            result['passed_prescreen'] = passed
            if passed:
                result['reasons_passed'] = reasons
            else:
                result['reasons_failed'] = reasons
            
        except Exception as e:
            logger.error(f"Error in cash flow pre-screening: {e}")
            result['reasons_failed'].append(f'Analysis error: {str(e)}')
        
        return result
    
    def _extract_cash_flow_time_series(self, company_data: Dict[str, Any]) -> Dict[str, pd.Series]:
        """
        Extract cash flow time series from company data
        
        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data
            
        Returns:
        --------
        Dictionary containing cash flow time series
        """
        cash_flow_series = {}
        
        # Extract from cash flow statement
        if 'cash_flow' in company_data:
            cf_data = company_data['cash_flow'].copy()
            
            # Remove units and notes
            if 'units' in cf_data:
                cf_data.pop('units')
            if 'notes' in cf_data:
                cf_data.pop('notes')
            
            # Get all years (excluding TTM)
            years = [year for year in cf_data.keys() if year != 'TTM']
            years.sort()  # Sort chronologically
            
            if len(years) >= self.min_years:
                # Extract key cash flow metrics
                metrics_to_extract = [
                    'cash_from_operations', 'operating_cash_flow', 'cash_from_operating_activities',
                    'free_cash_flow', 'fcf', 'cash_flow_from_operations',
                    'capex', 'capital_expenditure', 'investments',
                    'working_capital_changes', 'change_in_working_capital'
                ]
                
                for metric in metrics_to_extract:
                    values = []
                    valid_years = []
                    
                    for year in years:
                        if isinstance(cf_data[year], dict):
                            # Try different possible names for the metric
                            value = None
                            for possible_name in [metric, metric.replace('_', ' '), metric.title()]:
                                if possible_name in cf_data[year]:
                                    value = cf_data[year][possible_name]
                                    break
                            
                            if value is not None:
                                try:
                                    # Convert to numeric
                                    if isinstance(value, str):
                                        value = float(value.replace(',', ''))
                                    elif value is not None:
                                        value = float(value)
                                    
                                    values.append(value)
                                    valid_years.append(year)
                                except (ValueError, TypeError):
                                    continue
                    
                    if len(values) >= self.min_years:
                        cash_flow_series[metric] = pd.Series(values, index=valid_years)
        
        # Also extract from profit & loss for operating cash flow estimation
        if 'profit_loss' in company_data and not cash_flow_series:
            pl_data = company_data['profit_loss'].copy()
            
            # Remove units and notes
            if 'units' in pl_data:
                pl_data.pop('units')
            if 'notes' in pl_data:
                pl_data.pop('notes')
            
            years = [year for year in pl_data.keys() if year != 'TTM']
            years.sort()
            
            if len(years) >= self.min_years:
                # Extract net profit and depreciation to estimate operating cash flow
                net_profit_values = []
                depreciation_values = []
                valid_years = []
                
                for year in years:
                    if isinstance(pl_data[year], dict):
                        net_profit = pl_data[year].get('net_profit', pl_data[year].get('Net Profit'))
                        depreciation = pl_data[year].get('depreciation', pl_data[year].get('Depreciation', 0))
                        
                        if net_profit is not None:
                            try:
                                if isinstance(net_profit, str):
                                    net_profit = float(net_profit.replace(',', ''))
                                if isinstance(depreciation, str):
                                    depreciation = float(depreciation.replace(',', ''))
                                elif depreciation is None:
                                    depreciation = 0
                                
                                net_profit_values.append(float(net_profit))
                                depreciation_values.append(float(depreciation))
                                valid_years.append(year)
                            except (ValueError, TypeError):
                                continue
                
                if len(net_profit_values) >= self.min_years:
                    # Estimate operating cash flow as net profit + depreciation
                    estimated_ocf = [np + dep for np, dep in zip(net_profit_values, depreciation_values)]
                    cash_flow_series['estimated_operating_cash_flow'] = pd.Series(estimated_ocf, index=valid_years)
        
        return cash_flow_series
    
    def _analyze_operating_cash_flow(self, cash_flow_data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """
        Analyze operating cash flow patterns
        
        Parameters:
        -----------
        cash_flow_data : Dict[str, pd.Series]
            Cash flow time series data
            
        Returns:
        --------
        Dictionary containing operating cash flow analysis
        """
        # Find operating cash flow series
        ocf_series = None
        for key in ['cash_from_operations', 'operating_cash_flow', 'cash_from_operating_activities', 'estimated_operating_cash_flow']:
            if key in cash_flow_data:
                ocf_series = cash_flow_data[key]
                break
        
        if ocf_series is None or len(ocf_series) < self.min_years:
            return {'error': 'Insufficient operating cash flow data'}
        
        # Calculate key metrics
        positive_years = (ocf_series > 0).sum()
        total_years = len(ocf_series)
        positive_percentage = (positive_years / total_years) * 100
        
        # Calculate growth metrics
        ocf_growth = ocf_series.pct_change().dropna()
        positive_growth_years = (ocf_growth > 0).sum()
        growth_consistency = (positive_growth_years / len(ocf_growth)) * 100 if len(ocf_growth) > 0 else 0
        
        # Calculate trend
        years_numeric = np.arange(len(ocf_series))
        if len(ocf_series) > 2:
            trend_slope = np.polyfit(years_numeric, ocf_series.values, 1)[0]
        else:
            trend_slope = 0
        
        # Calculate volatility
        ocf_cv = (ocf_series.std() / abs(ocf_series.mean())) if ocf_series.mean() != 0 else float('inf')
        
        return {
            'total_years': total_years,
            'positive_years': positive_years,
            'positive_percentage': positive_percentage,
            'average_ocf': ocf_series.mean(),
            'median_ocf': ocf_series.median(),
            'latest_ocf': ocf_series.iloc[-1],
            'growth_consistency': growth_consistency,
            'trend_slope': trend_slope,
            'coefficient_of_variation': ocf_cv,
            'min_ocf': ocf_series.min(),
            'max_ocf': ocf_series.max(),
            'ocf_series': ocf_series.to_dict()
        }
    
    def _analyze_free_cash_flow(self, cash_flow_data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """
        Analyze free cash flow patterns
        
        Parameters:
        -----------
        cash_flow_data : Dict[str, pd.Series]
            Cash flow time series data
            
        Returns:
        --------
        Dictionary containing free cash flow analysis
        """
        # Find free cash flow series or calculate it
        fcf_series = None
        
        # Try to find direct FCF data
        for key in ['free_cash_flow', 'fcf']:
            if key in cash_flow_data:
                fcf_series = cash_flow_data[key]
                break
        
        # If not found, try to calculate FCF = OCF - Capex
        if fcf_series is None:
            ocf_series = None
            capex_series = None
            
            for key in ['cash_from_operations', 'operating_cash_flow', 'estimated_operating_cash_flow']:
                if key in cash_flow_data:
                    ocf_series = cash_flow_data[key]
                    break
            
            for key in ['capex', 'capital_expenditure', 'investments']:
                if key in cash_flow_data:
                    capex_series = cash_flow_data[key]
                    break
            
            if ocf_series is not None and capex_series is not None:
                # Align the series
                common_index = ocf_series.index.intersection(capex_series.index)
                if len(common_index) >= self.min_years:
                    fcf_series = ocf_series[common_index] - abs(capex_series[common_index])
        
        if fcf_series is None or len(fcf_series) < self.min_years:
            return {'error': 'Insufficient free cash flow data'}
        
        # Calculate key metrics
        positive_years = (fcf_series > 0).sum()
        total_years = len(fcf_series)
        positive_percentage = (positive_years / total_years) * 100
        
        # Calculate growth metrics
        fcf_growth = fcf_series.pct_change().dropna()
        positive_growth_years = (fcf_growth > 0).sum()
        growth_consistency = (positive_growth_years / len(fcf_growth)) * 100 if len(fcf_growth) > 0 else 0
        
        # Calculate trend
        years_numeric = np.arange(len(fcf_series))
        if len(fcf_series) > 2:
            trend_slope = np.polyfit(years_numeric, fcf_series.values, 1)[0]
        else:
            trend_slope = 0
        
        return {
            'total_years': total_years,
            'positive_years': positive_years,
            'positive_percentage': positive_percentage,
            'average_fcf': fcf_series.mean(),
            'median_fcf': fcf_series.median(),
            'latest_fcf': fcf_series.iloc[-1],
            'growth_consistency': growth_consistency,
            'trend_slope': trend_slope,
            'min_fcf': fcf_series.min(),
            'max_fcf': fcf_series.max(),
            'fcf_series': fcf_series.to_dict()
        }
    
    def _analyze_cash_flow_quality(self, company_data: Dict[str, Any], cash_flow_data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """
        Analyze the quality of cash flows
        
        Parameters:
        -----------
        company_data : Dict[str, Any]
            Complete company data
        cash_flow_data : Dict[str, pd.Series]
            Cash flow time series data
            
        Returns:
        --------
        Dictionary containing cash flow quality analysis
        """
        quality_metrics = {}
        
        # 1. Operating Cash Flow to Net Income Ratio
        if 'profit_loss' in company_data:
            pl_data = company_data['profit_loss'].copy()
            if 'units' in pl_data:
                pl_data.pop('units')
            if 'notes' in pl_data:
                pl_data.pop('notes')
            
            # Extract net income
            years = [year for year in pl_data.keys() if year != 'TTM']
            years.sort()
            
            net_income_values = []
            valid_years = []
            
            for year in years:
                if isinstance(pl_data[year], dict):
                    net_income = pl_data[year].get('net_profit', pl_data[year].get('Net Profit'))
                    if net_income is not None:
                        try:
                            if isinstance(net_income, str):
                                net_income = float(net_income.replace(',', ''))
                            net_income_values.append(float(net_income))
                            valid_years.append(year)
                        except (ValueError, TypeError):
                            continue
            
            if net_income_values and len(valid_years) >= self.min_years:
                net_income_series = pd.Series(net_income_values, index=valid_years)
                
                # Find matching OCF series
                ocf_series = None
                for key in ['cash_from_operations', 'operating_cash_flow', 'estimated_operating_cash_flow']:
                    if key in cash_flow_data:
                        ocf_series = cash_flow_data[key]
                        break
                
                if ocf_series is not None:
                    # Align series
                    common_index = ocf_series.index.intersection(net_income_series.index)
                    if len(common_index) >= 3:
                        ocf_aligned = ocf_series[common_index]
                        ni_aligned = net_income_series[common_index]
                        
                        # Calculate OCF/NI ratio
                        ocf_ni_ratio = []
                        for ocf, ni in zip(ocf_aligned, ni_aligned):
                            if ni != 0:
                                ocf_ni_ratio.append(ocf / ni)
                        
                        if ocf_ni_ratio:
                            quality_metrics['ocf_to_ni_ratio'] = {
                                'average': np.mean(ocf_ni_ratio),
                                'median': np.median(ocf_ni_ratio),
                                'years_above_80_percent': sum(1 for ratio in ocf_ni_ratio if ratio >= 0.8),
                                'years_above_100_percent': sum(1 for ratio in ocf_ni_ratio if ratio >= 1.0),
                                'consistency': len([r for r in ocf_ni_ratio if r >= 0.8]) / len(ocf_ni_ratio) * 100
                            }
        
        # 2. Cash Flow Predictability
        ocf_series = None
        for key in ['cash_from_operations', 'operating_cash_flow', 'estimated_operating_cash_flow']:
            if key in cash_flow_data:
                ocf_series = cash_flow_data[key]
                break
        
        if ocf_series is not None and len(ocf_series) >= 5:
            # Calculate R-squared for trend
            years_numeric = np.arange(len(ocf_series))
            correlation_matrix = np.corrcoef(years_numeric, ocf_series.values)
            r_squared = correlation_matrix[0, 1] ** 2
            
            quality_metrics['predictability'] = {
                'r_squared': r_squared,
                'trend_strength': 'Strong' if r_squared > 0.7 else 'Moderate' if r_squared > 0.4 else 'Weak'
            }
        
        return quality_metrics
    
    def _calculate_cash_flow_score(self, ocf_analysis: Dict[str, Any], 
                                 fcf_analysis: Dict[str, Any], 
                                 quality_analysis: Dict[str, Any]) -> float:
        """
        Calculate overall cash flow score (0-100)
        
        Parameters:
        -----------
        ocf_analysis : Dict[str, Any]
            Operating cash flow analysis
        fcf_analysis : Dict[str, Any]
            Free cash flow analysis
        quality_analysis : Dict[str, Any]
            Cash flow quality analysis
            
        Returns:
        --------
        Overall cash flow score
        """
        score = 0
        max_score = 100
        
        # OCF Score (40 points)
        if 'error' not in ocf_analysis:
            ocf_score = 0
            
            # Positive years (15 points)
            positive_pct = ocf_analysis.get('positive_percentage', 0)
            ocf_score += min(15, positive_pct * 0.15)
            
            # Growth consistency (10 points)
            growth_consistency = ocf_analysis.get('growth_consistency', 0)
            ocf_score += min(10, growth_consistency * 0.1)
            
            # Trend (10 points)
            trend_slope = ocf_analysis.get('trend_slope', 0)
            if trend_slope > 0:
                ocf_score += 10
            elif trend_slope > -0.1:
                ocf_score += 5
            
            # Volatility (5 points)
            cv = ocf_analysis.get('coefficient_of_variation', float('inf'))
            if cv < 0.5:
                ocf_score += 5
            elif cv < 1.0:
                ocf_score += 2
            
            score += min(40, ocf_score)
        
        # FCF Score (30 points)
        if 'error' not in fcf_analysis:
            fcf_score = 0
            
            # Positive years (15 points)
            positive_pct = fcf_analysis.get('positive_percentage', 0)
            fcf_score += min(15, positive_pct * 0.15)
            
            # Growth consistency (10 points)
            growth_consistency = fcf_analysis.get('growth_consistency', 0)
            fcf_score += min(10, growth_consistency * 0.1)
            
            # Trend (5 points)
            trend_slope = fcf_analysis.get('trend_slope', 0)
            if trend_slope > 0:
                fcf_score += 5
            
            score += min(30, fcf_score)
        
        # Quality Score (30 points)
        quality_score = 0
        
        # OCF to NI ratio (20 points)
        if 'ocf_to_ni_ratio' in quality_analysis:
            ratio_data = quality_analysis['ocf_to_ni_ratio']
            consistency = ratio_data.get('consistency', 0)
            quality_score += min(20, consistency * 0.2)
        
        # Predictability (10 points)
        if 'predictability' in quality_analysis:
            r_squared = quality_analysis['predictability'].get('r_squared', 0)
            quality_score += min(10, r_squared * 10)
        
        score += min(30, quality_score)
        
        return min(max_score, score)
    
    def _evaluate_prescreen_criteria(self, ocf_analysis: Dict[str, Any], 
                                   fcf_analysis: Dict[str, Any], 
                                   quality_analysis: Dict[str, Any],
                                   cash_flow_score: float) -> Tuple[bool, List[str]]:
        """
        Evaluate if company passes pre-screening criteria
        
        Parameters:
        -----------
        ocf_analysis : Dict[str, Any]
            Operating cash flow analysis
        fcf_analysis : Dict[str, Any]
            Free cash flow analysis
        quality_analysis : Dict[str, Any]
            Cash flow quality analysis
        cash_flow_score : float
            Overall cash flow score
            
        Returns:
        --------
        Tuple of (passed, reasons)
        """
        reasons = []
        passed = True
        
        # Minimum cash flow score threshold
        if cash_flow_score < 40:
            passed = False
            reasons.append(f'Cash flow score too low: {cash_flow_score:.1f} < 40')
        
        # Operating cash flow criteria
        if 'error' not in ocf_analysis:
            positive_years = ocf_analysis.get('positive_years', 0)
            if positive_years < self.min_positive_years:
                passed = False
                reasons.append(f'Insufficient positive OCF years: {positive_years} < {self.min_positive_years}')
            
            # Latest OCF should be positive
            latest_ocf = ocf_analysis.get('latest_ocf', 0)
            if latest_ocf <= 0:
                passed = False
                reasons.append(f'Latest OCF is negative: {latest_ocf}')
        else:
            passed = False
            reasons.append('No operating cash flow data available')
        
        # Free cash flow criteria (more lenient)
        if 'error' not in fcf_analysis:
            fcf_positive_years = fcf_analysis.get('positive_years', 0)
            if fcf_positive_years < 3:  # At least 3 years of positive FCF
                passed = False
                reasons.append(f'Insufficient positive FCF years: {fcf_positive_years} < 3')
        
        # Quality criteria
        if 'ocf_to_ni_ratio' in quality_analysis:
            consistency = quality_analysis['ocf_to_ni_ratio'].get('consistency', 0)
            if consistency < 50:  # At least 50% of years with OCF/NI > 80%
                passed = False
                reasons.append(f'Poor OCF to NI consistency: {consistency:.1f}% < 50%')
        
        # If passed, provide positive reasons
        if passed:
            reasons = []
            if cash_flow_score >= 60:
                reasons.append(f'Excellent cash flow score: {cash_flow_score:.1f}')
            else:
                reasons.append(f'Good cash flow score: {cash_flow_score:.1f}')
            
            if 'error' not in ocf_analysis:
                positive_pct = ocf_analysis.get('positive_percentage', 0)
                reasons.append(f'Strong OCF consistency: {positive_pct:.1f}% positive years')
            
            if 'error' not in fcf_analysis:
                fcf_positive_pct = fcf_analysis.get('positive_percentage', 0)
                if fcf_positive_pct >= 60:
                    reasons.append(f'Good FCF generation: {fcf_positive_pct:.1f}% positive years')
        
        return passed, reasons
