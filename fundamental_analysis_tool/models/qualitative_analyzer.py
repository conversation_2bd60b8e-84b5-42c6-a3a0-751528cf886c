#!/usr/bin/env python3
"""
Qualitative Analysis Framework

This module provides a framework for qualitative analysis of companies
including management quality, governance, business model strength, and
competitive advantages. Ready for integration with annual reports.
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('qualitative_analyzer')

class QualitativeAnalyzer:
    """
    Class to perform qualitative analysis of companies
    """
    
    def __init__(self):
        """
        Initialize the qualitative analyzer
        """
        # Define qualitative scoring framework
        self.scoring_framework = {
            'management_quality': {
                'weight': 0.25,
                'components': {
                    'leadership_track_record': 0.3,
                    'strategic_vision': 0.25,
                    'execution_capability': 0.25,
                    'transparency': 0.2
                }
            },
            'business_model': {
                'weight': 0.30,
                'components': {
                    'competitive_moat': 0.4,
                    'scalability': 0.25,
                    'recurring_revenue': 0.2,
                    'market_position': 0.15
                }
            },
            'governance': {
                'weight': 0.20,
                'components': {
                    'board_independence': 0.3,
                    'audit_quality': 0.25,
                    'shareholder_rights': 0.25,
                    'regulatory_compliance': 0.2
                }
            },
            'industry_dynamics': {
                'weight': 0.15,
                'components': {
                    'industry_growth': 0.4,
                    'competitive_intensity': 0.3,
                    'regulatory_environment': 0.3
                }
            },
            'esg_factors': {
                'weight': 0.10,
                'components': {
                    'environmental_impact': 0.4,
                    'social_responsibility': 0.3,
                    'governance_practices': 0.3
                }
            }
        }
        
        # Define sector-specific qualitative factors
        self.sector_qualitative_factors = {
            'banking': {
                'key_factors': ['asset_quality', 'risk_management', 'digital_transformation', 'regulatory_compliance'],
                'risk_factors': ['credit_risk', 'operational_risk', 'regulatory_changes'],
                'competitive_advantages': ['branch_network', 'digital_capabilities', 'customer_relationships']
            },
            'it_services': {
                'key_factors': ['client_relationships', 'talent_retention', 'innovation_capability', 'digital_transformation'],
                'risk_factors': ['client_concentration', 'visa_dependency', 'technology_disruption'],
                'competitive_advantages': ['domain_expertise', 'global_delivery_model', 'client_stickiness']
            },
            'pharmaceuticals': {
                'key_factors': ['r_and_d_pipeline', 'regulatory_approvals', 'patent_protection', 'manufacturing_quality'],
                'risk_factors': ['patent_expiry', 'regulatory_risks', 'clinical_trial_failures'],
                'competitive_advantages': ['patent_portfolio', 'manufacturing_scale', 'distribution_network']
            },
            'fmcg': {
                'key_factors': ['brand_strength', 'distribution_reach', 'innovation_pipeline', 'supply_chain'],
                'risk_factors': ['raw_material_volatility', 'changing_consumer_preferences', 'competition'],
                'competitive_advantages': ['brand_portfolio', 'distribution_network', 'manufacturing_efficiency']
            },
            'default': {
                'key_factors': ['market_position', 'operational_efficiency', 'innovation', 'management_quality'],
                'risk_factors': ['market_competition', 'regulatory_changes', 'economic_cycles'],
                'competitive_advantages': ['cost_leadership', 'differentiation', 'customer_loyalty']
            }
        }
    
    def analyze_company_qualitative_factors(self, 
                                          company_data: Dict[str, Any],
                                          annual_report_data: Dict[str, Any] = None,
                                          sector: str = 'default') -> Dict[str, Any]:
        """
        Analyze qualitative factors for a company
        
        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data including overview and financial information
        annual_report_data : Dict[str, Any], optional
            Annual report data (when available)
        sector : str
            Company sector for sector-specific analysis
            
        Returns:
        --------
        Dictionary containing qualitative analysis results
        """
        result = {
            'ticker': company_data.get('overview', {}).get('ticker', 'Unknown'),
            'sector': sector,
            'qualitative_score': 0,
            'component_scores': {},
            'sector_specific_analysis': {},
            'strengths': [],
            'weaknesses': [],
            'risks': [],
            'opportunities': [],
            'investment_thesis': '',
            'data_sources': []
        }
        
        try:
            # Analyze each component of qualitative scoring
            for component, config in self.scoring_framework.items():
                component_score = self._analyze_qualitative_component(
                    component, company_data, annual_report_data, sector
                )
                result['component_scores'][component] = component_score
            
            # Calculate overall qualitative score
            overall_score = self._calculate_overall_qualitative_score(result['component_scores'])
            result['qualitative_score'] = overall_score
            
            # Perform sector-specific analysis
            sector_analysis = self._perform_sector_specific_analysis(
                company_data, annual_report_data, sector
            )
            result['sector_specific_analysis'] = sector_analysis
            
            # Generate SWOT analysis
            swot = self._generate_swot_analysis(
                company_data, result['component_scores'], sector_analysis, sector
            )
            result.update(swot)
            
            # Generate investment thesis
            result['investment_thesis'] = self._generate_investment_thesis(
                company_data, result, sector
            )
            
            # Track data sources
            result['data_sources'] = self._identify_data_sources(company_data, annual_report_data)
            
        except Exception as e:
            logger.error(f"Error in qualitative analysis: {e}")
            result['error'] = str(e)
        
        return result
    
    def _analyze_qualitative_component(self, 
                                     component: str,
                                     company_data: Dict[str, Any],
                                     annual_report_data: Dict[str, Any],
                                     sector: str) -> Dict[str, Any]:
        """
        Analyze a specific qualitative component
        
        Parameters:
        -----------
        component : str
            Component name (e.g., 'management_quality', 'business_model')
        company_data : Dict[str, Any]
            Company data
        annual_report_data : Dict[str, Any]
            Annual report data
        sector : str
            Company sector
            
        Returns:
        --------
        Component analysis results
        """
        component_result = {
            'score': 0,
            'sub_scores': {},
            'evidence': [],
            'data_availability': 'limited'
        }
        
        if component == 'management_quality':
            component_result = self._analyze_management_quality(
                company_data, annual_report_data
            )
        elif component == 'business_model':
            component_result = self._analyze_business_model(
                company_data, annual_report_data, sector
            )
        elif component == 'governance':
            component_result = self._analyze_governance(
                company_data, annual_report_data
            )
        elif component == 'industry_dynamics':
            component_result = self._analyze_industry_dynamics(
                company_data, sector
            )
        elif component == 'esg_factors':
            component_result = self._analyze_esg_factors(
                company_data, annual_report_data, sector
            )
        
        return component_result
    
    def _analyze_management_quality(self, 
                                  company_data: Dict[str, Any],
                                  annual_report_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze management quality
        """
        result = {
            'score': 50,  # Default neutral score
            'sub_scores': {},
            'evidence': [],
            'data_availability': 'limited'
        }
        
        # Basic analysis from available data
        overview = company_data.get('overview', {})
        
        # Check for basic management information
        if 'management' in overview or 'directors' in overview:
            result['evidence'].append('Management information available')
            result['score'] += 10
        
        # Analyze financial performance consistency as proxy for management quality
        if 'profit_loss' in company_data:
            pl_data = company_data['profit_loss']
            if isinstance(pl_data, dict):
                years = [year for year in pl_data.keys() if year != 'TTM' and year not in ['units', 'notes']]
                if len(years) >= 5:
                    # Check revenue growth consistency
                    revenue_growth_consistency = self._check_revenue_growth_consistency(pl_data, years)
                    if revenue_growth_consistency > 0.7:
                        result['evidence'].append('Consistent revenue growth indicates good management execution')
                        result['score'] += 15
                    elif revenue_growth_consistency > 0.5:
                        result['score'] += 10
        
        # If annual report data is available, perform detailed analysis
        if annual_report_data:
            result['data_availability'] = 'comprehensive'
            # TODO: Add detailed annual report analysis when available
            result['score'] += 20  # Bonus for having annual report data
        
        result['score'] = min(100, max(0, result['score']))
        return result
    
    def _analyze_business_model(self, 
                              company_data: Dict[str, Any],
                              annual_report_data: Dict[str, Any],
                              sector: str) -> Dict[str, Any]:
        """
        Analyze business model strength
        """
        result = {
            'score': 50,  # Default neutral score
            'sub_scores': {},
            'evidence': [],
            'data_availability': 'limited'
        }
        
        # Analyze based on financial metrics
        ratios = company_data.get('ratios', {}).get('ratios', {})
        
        # High ROE/ROCE indicates strong business model
        roe = ratios.get('roe_%')
        roce = ratios.get('roce_%')
        
        if roe and isinstance(roe, (int, float)):
            if roe > 20:
                result['evidence'].append(f'High ROE ({roe}%) indicates strong business model')
                result['score'] += 20
            elif roe > 15:
                result['score'] += 10
        
        if roce and isinstance(roce, (int, float)):
            if roce > 20:
                result['evidence'].append(f'High ROCE ({roce}%) indicates efficient capital allocation')
                result['score'] += 15
            elif roce > 15:
                result['score'] += 10
        
        # Analyze margins
        operating_margin = ratios.get('operating_margin_%')
        if operating_margin and isinstance(operating_margin, (int, float)):
            if operating_margin > 20:
                result['evidence'].append(f'High operating margin ({operating_margin}%) indicates pricing power')
                result['score'] += 15
            elif operating_margin > 10:
                result['score'] += 10
        
        # Sector-specific business model analysis
        sector_factors = self.sector_qualitative_factors.get(sector, self.sector_qualitative_factors['default'])
        competitive_advantages = sector_factors['competitive_advantages']
        
        result['evidence'].append(f'Key competitive advantages for {sector}: {", ".join(competitive_advantages)}')
        
        result['score'] = min(100, max(0, result['score']))
        return result
    
    def _analyze_governance(self, 
                          company_data: Dict[str, Any],
                          annual_report_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze corporate governance
        """
        result = {
            'score': 60,  # Default score assuming basic governance
            'sub_scores': {},
            'evidence': [],
            'data_availability': 'limited'
        }
        
        # Basic governance indicators from financial data
        ratios = company_data.get('ratios', {}).get('ratios', {})
        
        # Low debt indicates prudent financial management
        debt_to_equity = ratios.get('debt_to_equity')
        if debt_to_equity and isinstance(debt_to_equity, (int, float)):
            if debt_to_equity < 0.5:
                result['evidence'].append('Conservative debt management indicates good governance')
                result['score'] += 15
            elif debt_to_equity < 1.0:
                result['score'] += 10
            elif debt_to_equity > 2.0:
                result['evidence'].append('High debt levels may indicate governance concerns')
                result['score'] -= 10
        
        # Interest coverage indicates financial discipline
        interest_coverage = ratios.get('interest_coverage')
        if interest_coverage and isinstance(interest_coverage, (int, float)):
            if interest_coverage > 5:
                result['evidence'].append('Strong interest coverage indicates financial discipline')
                result['score'] += 10
            elif interest_coverage < 2:
                result['evidence'].append('Low interest coverage may indicate financial stress')
                result['score'] -= 15
        
        # If annual report data is available, perform detailed governance analysis
        if annual_report_data:
            result['data_availability'] = 'comprehensive'
            # TODO: Add detailed governance analysis from annual reports
            result['score'] += 15  # Bonus for transparency (having annual reports)
        
        result['score'] = min(100, max(0, result['score']))
        return result
    
    def _analyze_industry_dynamics(self, 
                                 company_data: Dict[str, Any],
                                 sector: str) -> Dict[str, Any]:
        """
        Analyze industry dynamics
        """
        result = {
            'score': 50,  # Default neutral score
            'sub_scores': {},
            'evidence': [],
            'data_availability': 'sector_knowledge'
        }
        
        # Sector-specific industry analysis
        if sector == 'it_services':
            result['score'] = 75  # Generally positive industry dynamics
            result['evidence'].append('IT services sector benefits from digital transformation trends')
        elif sector == 'banking':
            result['score'] = 60  # Moderate industry dynamics
            result['evidence'].append('Banking sector faces regulatory challenges but benefits from economic growth')
        elif sector == 'pharmaceuticals':
            result['score'] = 70  # Generally positive due to aging population
            result['evidence'].append('Pharmaceuticals benefit from aging demographics and healthcare spending')
        elif sector == 'fmcg':
            result['score'] = 65  # Stable but competitive
            result['evidence'].append('FMCG sector offers stability but faces intense competition')
        elif sector == 'oil_gas':
            result['score'] = 45  # Challenging due to energy transition
            result['evidence'].append('Oil & gas sector faces long-term challenges from energy transition')
        
        return result
    
    def _analyze_esg_factors(self, 
                           company_data: Dict[str, Any],
                           annual_report_data: Dict[str, Any],
                           sector: str) -> Dict[str, Any]:
        """
        Analyze ESG factors
        """
        result = {
            'score': 50,  # Default neutral score
            'sub_scores': {},
            'evidence': [],
            'data_availability': 'limited'
        }
        
        # Basic ESG assessment based on sector
        if sector in ['pharmaceuticals', 'healthcare_services']:
            result['score'] += 15  # Positive social impact
            result['evidence'].append('Healthcare sector has positive social impact')
        elif sector in ['oil_gas', 'metals_mining']:
            result['score'] -= 10  # Environmental concerns
            result['evidence'].append('Sector faces environmental challenges')
        elif sector == 'it_services':
            result['score'] += 10  # Generally positive ESG profile
            result['evidence'].append('IT services sector has relatively positive ESG profile')
        
        # If annual report data is available, perform detailed ESG analysis
        if annual_report_data:
            result['data_availability'] = 'comprehensive'
            # TODO: Add detailed ESG analysis from annual reports
            result['score'] += 10  # Bonus for ESG disclosure
        
        result['score'] = min(100, max(0, result['score']))
        return result
    
    def _calculate_overall_qualitative_score(self, component_scores: Dict[str, Any]) -> float:
        """
        Calculate overall qualitative score from component scores
        """
        total_score = 0
        total_weight = 0
        
        for component, config in self.scoring_framework.items():
            if component in component_scores:
                component_score = component_scores[component].get('score', 0)
                weight = config['weight']
                total_score += component_score * weight
                total_weight += weight
        
        if total_weight > 0:
            return total_score / total_weight
        else:
            return 50  # Default neutral score
    
    def _perform_sector_specific_analysis(self, 
                                        company_data: Dict[str, Any],
                                        annual_report_data: Dict[str, Any],
                                        sector: str) -> Dict[str, Any]:
        """
        Perform sector-specific qualitative analysis
        """
        sector_factors = self.sector_qualitative_factors.get(sector, self.sector_qualitative_factors['default'])
        
        return {
            'key_factors': sector_factors['key_factors'],
            'risk_factors': sector_factors['risk_factors'],
            'competitive_advantages': sector_factors['competitive_advantages'],
            'sector_outlook': self._get_sector_outlook(sector),
            'regulatory_environment': self._assess_regulatory_environment(sector)
        }
    
    def _generate_swot_analysis(self, 
                              company_data: Dict[str, Any],
                              component_scores: Dict[str, Any],
                              sector_analysis: Dict[str, Any],
                              sector: str) -> Dict[str, Any]:
        """
        Generate SWOT analysis
        """
        strengths = []
        weaknesses = []
        opportunities = []
        threats = []
        
        # Analyze component scores for strengths and weaknesses
        for component, score_data in component_scores.items():
            score = score_data.get('score', 0)
            if score > 70:
                strengths.append(f"Strong {component.replace('_', ' ')}")
            elif score < 40:
                weaknesses.append(f"Weak {component.replace('_', ' ')}")
        
        # Add sector-specific opportunities and threats
        if sector == 'it_services':
            opportunities.extend(['Digital transformation', 'Cloud adoption', 'AI/ML services'])
            threats.extend(['Automation', 'Visa restrictions', 'Client concentration'])
        elif sector == 'banking':
            opportunities.extend(['Digital banking', 'Financial inclusion', 'Fintech partnerships'])
            threats.extend(['Regulatory changes', 'Fintech disruption', 'Economic cycles'])
        
        return {
            'strengths': strengths,
            'weaknesses': weaknesses,
            'opportunities': opportunities,
            'risks': threats
        }
    
    def _generate_investment_thesis(self, 
                                  company_data: Dict[str, Any],
                                  qualitative_result: Dict[str, Any],
                                  sector: str) -> str:
        """
        Generate investment thesis based on qualitative analysis
        """
        ticker = company_data.get('overview', {}).get('ticker', 'Company')
        score = qualitative_result.get('qualitative_score', 0)
        
        if score > 75:
            thesis = f"{ticker} demonstrates strong qualitative fundamentals with excellent "
        elif score > 60:
            thesis = f"{ticker} shows good qualitative characteristics with solid "
        elif score > 40:
            thesis = f"{ticker} has mixed qualitative factors with moderate "
        else:
            thesis = f"{ticker} faces qualitative challenges with weak "
        
        # Add sector context
        thesis += f"positioning in the {sector} sector. "
        
        # Add key strengths
        strengths = qualitative_result.get('strengths', [])
        if strengths:
            thesis += f"Key strengths include {', '.join(strengths[:2])}. "
        
        # Add key risks
        risks = qualitative_result.get('risks', [])
        if risks:
            thesis += f"Main risks are {', '.join(risks[:2])}."
        
        return thesis
    
    def _check_revenue_growth_consistency(self, pl_data: Dict[str, Any], years: List[str]) -> float:
        """
        Check revenue growth consistency as a proxy for management quality
        """
        revenues = []
        for year in sorted(years):
            if isinstance(pl_data[year], dict):
                revenue = pl_data[year].get('revenue', pl_data[year].get('sales'))
                if revenue and isinstance(revenue, (int, float, str)):
                    try:
                        if isinstance(revenue, str):
                            revenue = float(revenue.replace(',', ''))
                        revenues.append(float(revenue))
                    except (ValueError, TypeError):
                        continue
        
        if len(revenues) < 3:
            return 0
        
        # Calculate year-over-year growth rates
        growth_rates = []
        for i in range(1, len(revenues)):
            if revenues[i-1] > 0:
                growth_rate = (revenues[i] - revenues[i-1]) / revenues[i-1]
                growth_rates.append(growth_rate)
        
        if not growth_rates:
            return 0
        
        # Calculate consistency (percentage of positive growth years)
        positive_growth_years = sum(1 for gr in growth_rates if gr > 0)
        return positive_growth_years / len(growth_rates)
    
    def _get_sector_outlook(self, sector: str) -> str:
        """
        Get sector outlook
        """
        outlooks = {
            'it_services': 'Positive - Digital transformation driving growth',
            'banking': 'Moderate - Economic growth offset by regulatory challenges',
            'pharmaceuticals': 'Positive - Aging demographics and healthcare spending',
            'fmcg': 'Stable - Defensive characteristics with steady demand',
            'oil_gas': 'Challenging - Energy transition and environmental concerns',
            'default': 'Neutral - Sector-specific factors need evaluation'
        }
        return outlooks.get(sector, outlooks['default'])
    
    def _assess_regulatory_environment(self, sector: str) -> str:
        """
        Assess regulatory environment for sector
        """
        environments = {
            'banking': 'Highly regulated - RBI oversight and Basel norms',
            'pharmaceuticals': 'Regulated - Drug approval and pricing controls',
            'it_services': 'Moderate - Data privacy and visa regulations',
            'fmcg': 'Moderate - Food safety and advertising regulations',
            'oil_gas': 'High - Environmental and pricing regulations',
            'default': 'Varies - Sector-specific regulatory framework'
        }
        return environments.get(sector, environments['default'])
    
    def _identify_data_sources(self, 
                             company_data: Dict[str, Any],
                             annual_report_data: Dict[str, Any]) -> List[str]:
        """
        Identify available data sources for analysis
        """
        sources = ['Financial statements']
        
        if company_data.get('overview'):
            sources.append('Company overview')
        
        if company_data.get('peers'):
            sources.append('Peer information')
        
        if annual_report_data:
            sources.append('Annual reports')
        
        return sources
