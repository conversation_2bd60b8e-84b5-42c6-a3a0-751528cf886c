#!/usr/bin/env python3
"""
Sector Analyzer Module

This module provides sector-specific analysis and peer comparison functionality.
It helps identify industry categories and compare companies within their sectors.
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from collections import defaultdict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('sector_analyzer')

class SectorAnalyzer:
    """
    Class to analyze companies within their sectors and provide peer comparisons
    """

    def __init__(self):
        """
        Initialize the sector analyzer
        """
        # Enhanced sector classification based on business characteristics and peer analysis
        self.sector_keywords = {
            'banking': ['bank', 'banking', 'financial services', 'hdfc bank', 'icici bank', 'axis bank', 'kotak', 'sbi'],
            'it_services': ['infotech', 'software', 'it services', 'technology', 'tcs', 'infosys', 'wipro', 'hcl tech', 'tech mahindra'],
            'pharmaceuticals': ['pharma', 'pharmaceutical', 'drugs', 'medicine', 'sun pharma', 'dr reddy', 'cipla', 'lupin', 'biocon'],
            'fmcg': ['consumer goods', 'fmcg', 'personal care', 'food', 'hindustan unilever', 'itc', 'nestle', 'britannia', 'godrej'],
            'automotive': ['auto', 'automotive', 'vehicles', 'motors', 'maruti', 'tata motors', 'mahindra', 'bajaj auto', 'hero motocorp'],
            'oil_gas': ['oil', 'gas', 'petroleum', 'energy', 'refinery', 'reliance', 'ongc', 'ioc', 'bpcl', 'hpcl'],
            'metals_mining': ['steel', 'iron', 'metals', 'mining', 'aluminium', 'tata steel', 'jsl', 'hindalco', 'vedanta', 'nmdc'],
            'telecom': ['telecom', 'telecommunications', 'mobile', 'bharti airtel', 'vodafone idea', 'jio'],
            'cement': ['cement', 'building materials', 'ultratech', 'acc', 'ambuja', 'shree cement'],
            'textiles': ['textiles', 'garments', 'apparel', 'raymond', 'page industries', 'aditya birla fashion'],
            'real_estate': ['real estate', 'construction', 'infrastructure', 'dlf', 'godrej properties', 'oberoi realty'],
            'power_utilities': ['power', 'electricity', 'utilities', 'ntpc', 'power grid', 'tata power', 'adani power'],
            'chemicals': ['chemicals', 'petrochemicals', 'specialty chemicals', 'asian paints', 'berger paints', 'pidilite'],
            'capital_goods': ['engineering', 'capital goods', 'machinery', 'larsen toubro', 'bhel', 'siemens'],
            'aviation': ['aviation', 'airlines', 'airport', 'indigo', 'spicejet', 'air india'],
            'retail': ['retail', 'shopping', 'mall', 'avenue supermarts', 'future retail', 'trent'],
            'media_entertainment': ['media', 'entertainment', 'broadcasting', 'zee', 'star', 'sony'],
            'insurance': ['insurance', 'life insurance', 'general insurance', 'lic', 'icici prudential', 'hdfc life'],
            'nbfc': ['nbfc', 'financial services', 'lending', 'bajaj finance', 'mahindra finance', 'shriram transport'],
            'logistics': ['logistics', 'transportation', 'shipping', 'blue dart', 'gati', 'mahindra logistics'],
            'agriculture': ['agriculture', 'fertilizer', 'seeds', 'upl', 'pi industries', 'rallis india'],
            'hospitality': ['hotel', 'hospitality', 'tourism', 'indian hotels', 'lemon tree', 'mahindra holidays'],
            'education': ['education', 'training', 'career point', 'aptech', 'niit'],
            'healthcare_services': ['hospital', 'healthcare', 'diagnostic', 'apollo hospitals', 'fortis', 'max healthcare'],
            'gems_jewellery': ['gems', 'jewellery', 'diamond', 'titan', 'kalyan jewellers', 'pc jeweller']
        }

        # Sector hierarchy for better classification
        self.sector_hierarchy = {
            'financial_services': ['banking', 'insurance', 'nbfc'],
            'technology': ['it_services'],
            'healthcare': ['pharmaceuticals', 'healthcare_services'],
            'consumer': ['fmcg', 'retail', 'textiles', 'gems_jewellery'],
            'industrials': ['automotive', 'capital_goods', 'metals_mining', 'cement', 'chemicals'],
            'energy': ['oil_gas', 'power_utilities'],
            'infrastructure': ['real_estate', 'logistics'],
            'services': ['media_entertainment', 'hospitality', 'education', 'aviation'],
            'commodities': ['agriculture', 'metals_mining']
        }

        # Define sector-specific metrics importance
        self.sector_metrics = {
            'banking': {
                'key_metrics': ['roe', 'net_interest_margin', 'asset_quality', 'provision_coverage'],
                'growth_focus': ['net_profit', 'interest_income', 'deposits'],
                'risk_metrics': ['gross_npa', 'net_npa', 'credit_cost'],
                'negative_fcf_acceptable': True  # Banks typically have negative FCF
            },
            'it_services': {
                'key_metrics': ['operating_margin', 'employee_productivity', 'client_concentration'],
                'growth_focus': ['revenue', 'operating_profit', 'eps'],
                'risk_metrics': ['client_dependency', 'currency_exposure'],
                'negative_fcf_acceptable': False
            },
            'oil_gas': {
                'key_metrics': ['refining_margin', 'capacity_utilization', 'debt_to_equity'],
                'growth_focus': ['revenue', 'operating_profit'],
                'risk_metrics': ['commodity_price_sensitivity', 'environmental_compliance'],
                'negative_fcf_acceptable': True  # High capex industry
            },
            'fmcg': {
                'key_metrics': ['brand_strength', 'distribution_reach', 'operating_margin'],
                'growth_focus': ['revenue', 'volume_growth', 'market_share'],
                'risk_metrics': ['raw_material_cost_volatility'],
                'negative_fcf_acceptable': False
            },
            'default': {
                'key_metrics': ['roe', 'roce', 'operating_margin'],
                'growth_focus': ['revenue', 'operating_profit', 'net_profit'],
                'risk_metrics': ['debt_to_equity', 'interest_coverage'],
                'negative_fcf_acceptable': False
            }
        }

    def classify_sector(self, company_data: Dict[str, Any]) -> str:
        """
        Classify a company into a sector based on its business description and peers

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data including overview and peers information

        Returns:
        --------
        Sector classification string
        """
        # Try to get sector from company overview
        overview = company_data.get('overview', {})
        company_name = overview.get('name', '').lower()
        business_description = overview.get('business', '').lower()

        # Check peers for sector hints
        peers = company_data.get('peers', {})
        peer_names = []
        if isinstance(peers, dict):
            for peer_list in peers.values():
                if isinstance(peer_list, list):
                    peer_names.extend([peer.lower() for peer in peer_list])

        # Combine all text for analysis
        text_to_analyze = f"{company_name} {business_description} {' '.join(peer_names)}"

        # Score each sector based on keyword matches
        sector_scores = {}
        for sector, keywords in self.sector_keywords.items():
            score = 0
            for keyword in keywords:
                score += text_to_analyze.count(keyword)
            sector_scores[sector] = score

        # Return the sector with highest score, or 'default' if no clear match
        if max(sector_scores.values()) > 0:
            return max(sector_scores, key=sector_scores.get)
        else:
            return 'default'

    def get_peer_companies(self, company_data: Dict[str, Any]) -> List[str]:
        """
        Extract peer companies from company data

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data including peers information

        Returns:
        --------
        List of peer company tickers/names
        """
        peers = company_data.get('peers', {})
        peer_list = []

        if isinstance(peers, dict):
            for peer_group in peers.values():
                if isinstance(peer_group, list):
                    peer_list.extend(peer_group)

        return peer_list

    def analyze_sector_performance(self,
                                 companies_data: Dict[str, Dict[str, Any]],
                                 sector: str = None) -> Dict[str, Any]:
        """
        Analyze performance metrics for companies in a specific sector

        Parameters:
        -----------
        companies_data : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to their data
        sector : str, optional
            Specific sector to analyze. If None, analyzes all sectors

        Returns:
        --------
        Dictionary containing sector analysis results
        """
        sector_analysis = {}

        # Group companies by sector
        sector_companies = defaultdict(list)
        for ticker, company_data in companies_data.items():
            company_sector = self.classify_sector(company_data)
            sector_companies[company_sector].append(ticker)

        # Analyze each sector (or specific sector if provided)
        sectors_to_analyze = [sector] if sector else sector_companies.keys()

        for sector_name in sectors_to_analyze:
            if sector_name not in sector_companies:
                continue

            companies_in_sector = sector_companies[sector_name]
            sector_metrics = self._calculate_sector_metrics(
                companies_data, companies_in_sector, sector_name
            )

            sector_analysis[sector_name] = {
                'company_count': len(companies_in_sector),
                'companies': companies_in_sector,
                'metrics': sector_metrics,
                'top_performers': self._identify_top_performers(
                    companies_data, companies_in_sector, sector_name
                ),
                'sector_characteristics': self.sector_metrics.get(sector_name, self.sector_metrics['default'])
            }

        return sector_analysis

    def _calculate_sector_metrics(self,
                                companies_data: Dict[str, Dict[str, Any]],
                                companies_in_sector: List[str],
                                sector: str) -> Dict[str, Any]:
        """
        Calculate aggregate metrics for a sector

        Parameters:
        -----------
        companies_data : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to their data
        companies_in_sector : List[str]
            List of company tickers in the sector
        sector : str
            Sector name

        Returns:
        --------
        Dictionary containing sector metrics
        """
        sector_config = self.sector_metrics.get(sector, self.sector_metrics['default'])
        key_metrics = sector_config['key_metrics']

        # Collect metrics from all companies in sector
        metrics_data = defaultdict(list)

        for ticker in companies_in_sector:
            company_data = companies_data.get(ticker, {})
            ratios = company_data.get('ratios', {}).get('ratios', {})

            # Extract key metrics
            for metric in key_metrics:
                value = ratios.get(f"{metric}_%", ratios.get(metric))
                if value is not None and isinstance(value, (int, float)):
                    metrics_data[metric].append(value)

        # Calculate sector statistics
        sector_stats = {}
        for metric, values in metrics_data.items():
            if values:
                sector_stats[metric] = {
                    'median': np.median(values),
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'percentile_25': np.percentile(values, 25),
                    'percentile_75': np.percentile(values, 75),
                    'sample_size': len(values)
                }

        return sector_stats

    def _identify_top_performers(self,
                               companies_data: Dict[str, Dict[str, Any]],
                               companies_in_sector: List[str],
                               sector: str) -> List[Dict[str, Any]]:
        """
        Identify top performing companies in a sector

        Parameters:
        -----------
        companies_data : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to their data
        companies_in_sector : List[str]
            List of company tickers in the sector
        sector : str
            Sector name

        Returns:
        --------
        List of top performing companies with their scores
        """
        sector_config = self.sector_metrics.get(sector, self.sector_metrics['default'])
        key_metrics = sector_config['key_metrics']

        company_scores = []

        for ticker in companies_in_sector:
            company_data = companies_data.get(ticker, {})
            ratios = company_data.get('ratios', {}).get('ratios', {})

            # Calculate composite score based on key metrics
            score = 0
            valid_metrics = 0

            for metric in key_metrics:
                value = ratios.get(f"{metric}_%", ratios.get(metric))
                if value is not None and isinstance(value, (int, float)):
                    # Normalize score (higher is better for most metrics)
                    if metric in ['debt_to_equity']:  # Lower is better
                        normalized_score = max(0, 100 - value * 10)
                    else:  # Higher is better
                        normalized_score = min(100, value * 2)

                    score += normalized_score
                    valid_metrics += 1

            if valid_metrics > 0:
                average_score = score / valid_metrics
                company_scores.append({
                    'ticker': ticker,
                    'score': average_score,
                    'valid_metrics': valid_metrics
                })

        # Sort by score and return top performers
        company_scores.sort(key=lambda x: x['score'], reverse=True)
        return company_scores[:10]  # Top 10 performers

    def compare_with_peers(self,
                          target_company: str,
                          companies_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Compare a target company with its sector peers

        Parameters:
        -----------
        target_company : str
            Ticker of the target company
        companies_data : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to their data

        Returns:
        --------
        Dictionary containing peer comparison results
        """
        if target_company not in companies_data:
            return {'error': f'Company {target_company} not found in data'}

        target_data = companies_data[target_company]
        target_sector = self.classify_sector(target_data)

        # Get all companies in the same sector
        sector_companies = []
        for ticker, company_data in companies_data.items():
            if self.classify_sector(company_data) == target_sector:
                sector_companies.append(ticker)

        # Calculate sector metrics
        sector_metrics = self._calculate_sector_metrics(
            companies_data, sector_companies, target_sector
        )

        # Get target company metrics
        target_ratios = target_data.get('ratios', {}).get('ratios', {})

        # Compare target with sector
        comparison = {
            'target_company': target_company,
            'sector': target_sector,
            'sector_size': len(sector_companies),
            'peer_companies': [c for c in sector_companies if c != target_company],
            'metrics_comparison': {},
            'percentile_ranking': {},
            'strengths': [],
            'weaknesses': []
        }

        # Compare each metric
        sector_config = self.sector_metrics.get(target_sector, self.sector_metrics['default'])
        for metric in sector_config['key_metrics']:
            target_value = target_ratios.get(f"{metric}_%", target_ratios.get(metric))

            if target_value is not None and metric in sector_metrics:
                sector_stats = sector_metrics[metric]

                comparison['metrics_comparison'][metric] = {
                    'target_value': target_value,
                    'sector_median': sector_stats['median'],
                    'sector_mean': sector_stats['mean'],
                    'vs_median': target_value - sector_stats['median'],
                    'vs_mean': target_value - sector_stats['mean']
                }

                # Calculate percentile ranking
                percentile = self._calculate_percentile(
                    target_value, sector_stats
                )
                comparison['percentile_ranking'][metric] = percentile

                # Identify strengths and weaknesses
                if percentile >= 75:
                    comparison['strengths'].append(metric)
                elif percentile <= 25:
                    comparison['weaknesses'].append(metric)

        return comparison

    def _calculate_percentile(self, value: float, sector_stats: Dict[str, float]) -> float:
        """
        Calculate the percentile ranking of a value within sector statistics

        Parameters:
        -----------
        value : float
            The value to rank
        sector_stats : Dict[str, float]
            Sector statistics including percentiles

        Returns:
        --------
        Percentile ranking (0-100)
        """
        if value <= sector_stats['percentile_25']:
            return 25 * (value - sector_stats['min']) / (sector_stats['percentile_25'] - sector_stats['min'])
        elif value <= sector_stats['median']:
            return 25 + 25 * (value - sector_stats['percentile_25']) / (sector_stats['median'] - sector_stats['percentile_25'])
        elif value <= sector_stats['percentile_75']:
            return 50 + 25 * (value - sector_stats['median']) / (sector_stats['percentile_75'] - sector_stats['median'])
        else:
            return 75 + 25 * (value - sector_stats['percentile_75']) / (sector_stats['max'] - sector_stats['percentile_75'])

    def handle_negative_fcf_companies(self,
                                    companies_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Identify and analyze companies with negative free cash flow

        Parameters:
        -----------
        companies_data : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to their data

        Returns:
        --------
        Dictionary containing analysis of negative FCF companies
        """
        negative_fcf_companies = {}

        for ticker, company_data in companies_data.items():
            # Check for negative FCF (this would need to be calculated from cash flow data)
            # For now, we'll identify sectors where negative FCF is acceptable
            sector = self.classify_sector(company_data)
            sector_config = self.sector_metrics.get(sector, self.sector_metrics['default'])

            if sector_config['negative_fcf_acceptable']:
                negative_fcf_companies[ticker] = {
                    'sector': sector,
                    'reason': 'High capex industry - negative FCF acceptable',
                    'alternative_metrics': sector_config['key_metrics'],
                    'peer_comparison_required': True
                }

        return negative_fcf_companies

    def discover_all_sectors_and_players(self, companies_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Discover all sectors and their players from the complete dataset

        Parameters:
        -----------
        companies_data : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to their data

        Returns:
        --------
        Dictionary containing comprehensive sector mapping
        """
        logger.info("Discovering sectors and players from complete dataset...")

        # Initialize sector discovery
        sector_discovery = {
            'sectors_found': {},
            'peer_networks': {},
            'unclassified_companies': [],
            'sector_statistics': {},
            'cross_sector_relationships': {}
        }

        # Step 1: Classify all companies
        company_sectors = {}
        peer_mentions = defaultdict(set)

        for ticker, company_data in companies_data.items():
            # Classify sector
            sector = self.classify_sector(company_data)
            company_sectors[ticker] = sector

            # Extract peer mentions
            peers = self.get_peer_companies(company_data)
            for peer in peers:
                peer_mentions[ticker].add(peer.upper())
                peer_mentions[peer.upper()].add(ticker)

        # Step 2: Group companies by sector
        for ticker, sector in company_sectors.items():
            if sector not in sector_discovery['sectors_found']:
                sector_discovery['sectors_found'][sector] = []
            sector_discovery['sectors_found'][sector].append(ticker)

        # Step 3: Analyze peer networks to refine sector classification
        refined_sectors = self._refine_sectors_using_peer_networks(
            company_sectors, peer_mentions
        )

        # Step 4: Calculate sector statistics
        for sector, companies in sector_discovery['sectors_found'].items():
            if len(companies) >= 3:  # Only analyze sectors with sufficient companies
                sector_stats = self._calculate_comprehensive_sector_stats(
                    companies, companies_data
                )
                sector_discovery['sector_statistics'][sector] = sector_stats

        # Step 5: Identify cross-sector relationships
        sector_discovery['cross_sector_relationships'] = self._identify_cross_sector_relationships(
            company_sectors, peer_mentions
        )

        # Step 6: Generate sector insights
        sector_discovery['insights'] = self._generate_sector_insights(sector_discovery)

        logger.info(f"Sector discovery completed. Found {len(sector_discovery['sectors_found'])} sectors")

        return sector_discovery

    def _refine_sectors_using_peer_networks(self,
                                          company_sectors: Dict[str, str],
                                          peer_mentions: Dict[str, set]) -> Dict[str, str]:
        """
        Refine sector classification using peer network analysis

        Parameters:
        -----------
        company_sectors : Dict[str, str]
            Initial sector classification
        peer_mentions : Dict[str, set]
            Peer mention networks

        Returns:
        --------
        Refined sector classification
        """
        refined_sectors = company_sectors.copy()

        # Find companies that are frequently mentioned together
        for ticker, peers in peer_mentions.items():
            if ticker in company_sectors:
                current_sector = company_sectors[ticker]

                # Count sector distribution of peers
                peer_sectors = defaultdict(int)
                for peer in peers:
                    if peer in company_sectors:
                        peer_sectors[company_sectors[peer]] += 1

                # If majority of peers are in a different sector, consider reclassification
                if peer_sectors:
                    most_common_peer_sector = max(peer_sectors, key=peer_sectors.get)
                    peer_count = peer_sectors[most_common_peer_sector]

                    # Reclassify if >70% of peers are in different sector and we have enough peers
                    if (most_common_peer_sector != current_sector and
                        peer_count >= 3 and
                        peer_count / len(peers) > 0.7):

                        logger.info(f"Reclassifying {ticker} from {current_sector} to {most_common_peer_sector} based on peer network")
                        refined_sectors[ticker] = most_common_peer_sector

        return refined_sectors

    def _calculate_comprehensive_sector_stats(self,
                                            companies: List[str],
                                            companies_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate comprehensive statistics for a sector

        Parameters:
        -----------
        companies : List[str]
            List of companies in the sector
        companies_data : Dict[str, Dict[str, Any]]
            Complete company data

        Returns:
        --------
        Comprehensive sector statistics
        """
        stats = {
            'company_count': len(companies),
            'companies': companies,
            'market_cap_distribution': {},
            'financial_metrics': {},
            'growth_patterns': {},
            'risk_characteristics': {}
        }

        # Collect financial metrics
        metrics_data = defaultdict(list)

        for ticker in companies:
            company_data = companies_data.get(ticker, {})
            ratios = company_data.get('ratios', {}).get('ratios', {})

            # Extract key metrics
            key_metrics = ['roe_%', 'roce_%', 'debt_to_equity', 'pe_ratio', 'pb_ratio',
                          'operating_margin_%', 'net_profit_margin_%']

            for metric in key_metrics:
                value = ratios.get(metric)
                if value is not None and isinstance(value, (int, float)):
                    metrics_data[metric].append(value)

        # Calculate statistics for each metric
        for metric, values in metrics_data.items():
            if values and len(values) >= 3:
                stats['financial_metrics'][metric] = {
                    'count': len(values),
                    'mean': np.mean(values),
                    'median': np.median(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'percentile_25': np.percentile(values, 25),
                    'percentile_75': np.percentile(values, 75)
                }

        return stats

    def _identify_cross_sector_relationships(self,
                                           company_sectors: Dict[str, str],
                                           peer_mentions: Dict[str, set]) -> Dict[str, Any]:
        """
        Identify relationships between different sectors

        Parameters:
        -----------
        company_sectors : Dict[str, str]
            Sector classification
        peer_mentions : Dict[str, set]
            Peer mention networks

        Returns:
        --------
        Cross-sector relationship analysis
        """
        cross_sector_links = defaultdict(lambda: defaultdict(int))

        for ticker, peers in peer_mentions.items():
            if ticker in company_sectors:
                ticker_sector = company_sectors[ticker]

                for peer in peers:
                    if peer in company_sectors:
                        peer_sector = company_sectors[peer]
                        if peer_sector != ticker_sector:
                            cross_sector_links[ticker_sector][peer_sector] += 1

        # Convert to regular dict and calculate relationship strength
        relationships = {}
        for sector1, connections in cross_sector_links.items():
            relationships[sector1] = {}
            total_connections = sum(connections.values())

            for sector2, count in connections.items():
                strength = count / total_connections if total_connections > 0 else 0
                if strength > 0.1:  # Only include significant relationships
                    relationships[sector1][sector2] = {
                        'connection_count': count,
                        'strength': strength
                    }

        return relationships

    def _generate_sector_insights(self, sector_discovery: Dict[str, Any]) -> List[str]:
        """
        Generate insights from sector discovery

        Parameters:
        -----------
        sector_discovery : Dict[str, Any]
            Complete sector discovery results

        Returns:
        --------
        List of insights
        """
        insights = []

        sectors_found = sector_discovery['sectors_found']
        sector_stats = sector_discovery['sector_statistics']

        # Overall insights
        total_companies = sum(len(companies) for companies in sectors_found.values())
        insights.append(f"Analyzed {total_companies} companies across {len(sectors_found)} sectors")

        # Largest sectors
        largest_sectors = sorted(sectors_found.items(), key=lambda x: len(x[1]), reverse=True)[:5]
        insights.append(f"Largest sectors: {', '.join([f'{sector} ({len(companies)})' for sector, companies in largest_sectors])}")

        # Sector-specific insights
        for sector, companies in largest_sectors:
            if sector in sector_stats and len(companies) >= 10:
                stats = sector_stats[sector]
                financial_metrics = stats.get('financial_metrics', {})

                if 'roe_%' in financial_metrics:
                    avg_roe = financial_metrics['roe_%']['mean']
                    insights.append(f"{sector.title()}: {len(companies)} companies, avg ROE {avg_roe:.1f}%")

        # Cross-sector relationships
        cross_relationships = sector_discovery.get('cross_sector_relationships', {})
        if cross_relationships:
            strong_relationships = []
            for sector1, connections in cross_relationships.items():
                for sector2, data in connections.items():
                    if data['strength'] > 0.3:
                        strong_relationships.append(f"{sector1}-{sector2}")

            if strong_relationships:
                insights.append(f"Strong cross-sector relationships: {', '.join(strong_relationships[:3])}")

        return insights

    def export_sector_mapping(self, sector_discovery: Dict[str, Any], output_file: str = None) -> str:
        """
        Export sector mapping to a file for future use

        Parameters:
        -----------
        sector_discovery : Dict[str, Any]
            Complete sector discovery results
        output_file : str, optional
            Output file path

        Returns:
        --------
        Path to the exported file
        """
        import json
        import os
        from datetime import datetime

        if output_file is None:
            os.makedirs('output/sector_analysis', exist_ok=True)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'output/sector_analysis/sector_mapping_{timestamp}.json'

        # Prepare export data
        export_data = {
            'timestamp': datetime.now().isoformat(),
            'sectors': sector_discovery['sectors_found'],
            'sector_statistics': sector_discovery['sector_statistics'],
            'cross_sector_relationships': sector_discovery['cross_sector_relationships'],
            'insights': sector_discovery['insights'],
            'summary': {
                'total_sectors': len(sector_discovery['sectors_found']),
                'total_companies': sum(len(companies) for companies in sector_discovery['sectors_found'].values()),
                'largest_sector': max(sector_discovery['sectors_found'].items(), key=lambda x: len(x[1]))[0],
                'average_companies_per_sector': sum(len(companies) for companies in sector_discovery['sectors_found'].values()) / len(sector_discovery['sectors_found'])
            }
        }

        with open(output_file, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)

        logger.info(f"Sector mapping exported to {output_file}")
        return output_file
