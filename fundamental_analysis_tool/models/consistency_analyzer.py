#!/usr/bin/env python3
"""
Consistency Analyzer Module

This module provides functions to analyze the historical consistency
of key financial metrics over time. It helps identify companies with
stable and predictable financial performance.
"""

import os
import logging
import numpy as np
import pandas as pd
import warnings
from typing import Dict, List, Optional, Union, Any, Tuple
from scipy import stats

# Suppress specific warnings for cleaner output
warnings.filterwarnings('ignore', category=RuntimeWarning, message='invalid value encountered in subtract')
warnings.filterwarnings('ignore', category=RuntimeWarning, message='invalid value encountered in reduce')
warnings.filterwarnings('ignore', category=FutureWarning, message='.*fill_method.*')
from models.sector_analyzer import SectorAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('consistency_analyzer')

class ConsistencyAnalyzer:
    """
    Class to analyze the historical consistency of financial metrics
    """

    def __init__(self, min_data_points: int = 4, min_years: int = 3):
        """
        Initialize the consistency analyzer

        Parameters:
        -----------
        min_data_points : int
            Minimum number of data points required for consistency analysis
        min_years : int
            Minimum number of years required for long-term consistency analysis
        """
        self.min_data_points = min_data_points
        self.min_years = min_years
        self.sector_analyzer = SectorAnalyzer()

        # Define key metrics to analyze for consistency
        self.key_metrics = {
            'profit_loss': ['sales', 'operating_profit', 'net_profit', 'eps'],
            'balance_sheet': ['total_assets', 'total_liabilities', 'net_worth', 'debt'],
            'cash_flow': ['operating_cash_flow', 'free_cash_flow', 'cash_from_operations'],
            'ratios': ['roe', 'roce', 'debt_to_equity', 'interest_coverage'],
            'quarters': ['sales', 'operating_profit', 'net_profit', 'eps']
        }

        # Define growth metrics
        self.growth_metrics = ['sales', 'operating_profit', 'net_profit', 'eps', 'operating_cash_flow']

        # Define profitability metrics
        self.profitability_metrics = ['roe', 'roce', 'operating_profit_margin', 'net_profit_margin']

        # Define leverage metrics
        self.leverage_metrics = ['debt_to_equity', 'interest_coverage']

    def analyze_company_consistency(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze the historical consistency of a company's financial metrics

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data including historical financial information

        Returns:
        --------
        Dictionary containing consistency metrics
        """
        consistency_results = {
            'growth_consistency': {},
            'profitability_consistency': {},
            'leverage_consistency': {},
            'quarterly_consistency': {},
            'overall_consistency_score': None
        }

        # Extract time series data
        time_series_data = self._extract_time_series(company_data)

        if not time_series_data:
            logger.warning("No time series data found for consistency analysis")
            return consistency_results

        # Analyze growth consistency
        growth_consistency = self._analyze_growth_consistency(time_series_data)
        consistency_results['growth_consistency'] = growth_consistency

        # Analyze profitability consistency
        profitability_consistency = self._analyze_profitability_consistency(time_series_data)
        consistency_results['profitability_consistency'] = profitability_consistency

        # Analyze leverage consistency
        leverage_consistency = self._analyze_leverage_consistency(time_series_data)
        consistency_results['leverage_consistency'] = leverage_consistency

        # Analyze quarterly consistency
        quarterly_consistency = self._analyze_quarterly_consistency(time_series_data)
        consistency_results['quarterly_consistency'] = quarterly_consistency

        # Calculate overall consistency score
        overall_score = self._calculate_overall_consistency_score(
            growth_consistency,
            profitability_consistency,
            leverage_consistency,
            quarterly_consistency
        )
        consistency_results['overall_consistency_score'] = overall_score

        return consistency_results

    def analyze_company_consistency_with_sector(self,
                                               company_data: Dict[str, Any],
                                               peer_data: Dict[str, Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze the historical consistency of a company's financial metrics with sector context

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data including historical financial information
        peer_data : Dict[str, Dict[str, Any]], optional
            Data for peer companies for sector comparison

        Returns:
        --------
        Dictionary containing consistency metrics with sector context
        """
        # Get basic consistency analysis
        consistency_results = self.analyze_company_consistency(company_data)

        # Add sector analysis
        sector = self.sector_analyzer.classify_sector(company_data)
        consistency_results['sector'] = sector
        consistency_results['sector_characteristics'] = self.sector_analyzer.sector_metrics.get(
            sector, self.sector_analyzer.sector_metrics['default']
        )

        # Add peer comparison if peer data is available
        if peer_data:
            # Get companies in the same sector
            sector_companies = {}
            for ticker, data in peer_data.items():
                if self.sector_analyzer.classify_sector(data) == sector:
                    sector_companies[ticker] = data

            if len(sector_companies) >= 3:  # Need at least 3 companies for meaningful comparison
                sector_consistency = self._analyze_sector_consistency(sector_companies)
                consistency_results['sector_comparison'] = sector_consistency

                # Calculate relative consistency ranking
                overall_score = consistency_results.get('overall_consistency_score')
                if overall_score is not None:
                    sector_scores = [
                        self.analyze_company_consistency(data).get('overall_consistency_score', 0)
                        for data in sector_companies.values()
                    ]
                    sector_scores = [s for s in sector_scores if s is not None]

                    if sector_scores:
                        percentile_rank = (sum(1 for s in sector_scores if s < overall_score) / len(sector_scores)) * 100
                        consistency_results['sector_percentile_rank'] = percentile_rank

        # Add sector-specific insights
        consistency_results['sector_insights'] = self._generate_sector_insights(
            consistency_results, sector
        )

        return consistency_results

    def _analyze_sector_consistency(self, sector_companies: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze consistency patterns across companies in a sector

        Parameters:
        -----------
        sector_companies : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to their data for companies in the same sector

        Returns:
        --------
        Dictionary containing sector consistency analysis
        """
        sector_consistency_scores = []
        sector_growth_scores = []
        sector_quarterly_scores = []

        for ticker, company_data in sector_companies.items():
            consistency_result = self.analyze_company_consistency(company_data)

            overall_score = consistency_result.get('overall_consistency_score')
            if overall_score is not None:
                sector_consistency_scores.append(overall_score)

            growth_score = consistency_result.get('growth_consistency', {}).get('overall_score')
            if growth_score is not None:
                sector_growth_scores.append(growth_score)

            quarterly_score = consistency_result.get('quarterly_consistency', {}).get('overall_score')
            if quarterly_score is not None:
                sector_quarterly_scores.append(quarterly_score)

        # Calculate sector statistics
        sector_stats = {}

        if sector_consistency_scores:
            sector_stats['overall_consistency'] = {
                'median': np.median(sector_consistency_scores),
                'mean': np.mean(sector_consistency_scores),
                'std': np.std(sector_consistency_scores),
                'min': np.min(sector_consistency_scores),
                'max': np.max(sector_consistency_scores)
            }

        if sector_growth_scores:
            sector_stats['growth_consistency'] = {
                'median': np.median(sector_growth_scores),
                'mean': np.mean(sector_growth_scores),
                'std': np.std(sector_growth_scores),
                'min': np.min(sector_growth_scores),
                'max': np.max(sector_growth_scores)
            }

        if sector_quarterly_scores:
            sector_stats['quarterly_consistency'] = {
                'median': np.median(sector_quarterly_scores),
                'mean': np.mean(sector_quarterly_scores),
                'std': np.std(sector_quarterly_scores),
                'min': np.min(sector_quarterly_scores),
                'max': np.max(sector_quarterly_scores)
            }

        return {
            'sector_size': len(sector_companies),
            'companies_analyzed': list(sector_companies.keys()),
            'sector_statistics': sector_stats
        }

    def _generate_sector_insights(self, consistency_results: Dict[str, Any], sector: str) -> List[str]:
        """
        Generate sector-specific insights based on consistency analysis

        Parameters:
        -----------
        consistency_results : Dict[str, Any]
            Consistency analysis results
        sector : str
            Sector classification

        Returns:
        --------
        List of insight strings
        """
        insights = []

        overall_score = consistency_results.get('overall_consistency_score', 0)
        growth_score = consistency_results.get('growth_consistency', {}).get('overall_score', 0)
        quarterly_score = consistency_results.get('quarterly_consistency', {}).get('overall_score', 0)

        # Sector-specific insights
        if sector == 'banking':
            if overall_score > 50:
                insights.append("Strong consistency for a banking company - indicates stable business model")
            if quarterly_score < 30:
                insights.append("Quarterly volatility is normal for banks due to regulatory and economic cycles")
            insights.append("For banks, focus on asset quality and NPA trends over FCF metrics")

        elif sector == 'it_services':
            if growth_score > 55:
                insights.append("Excellent growth consistency for IT services - indicates strong client relationships")
            if quarterly_score > 40:
                insights.append("Good quarterly consistency suggests stable revenue streams")
            insights.append("IT services companies should show consistent margin expansion")

        elif sector == 'oil_gas':
            if overall_score > 40:
                insights.append("Good consistency despite commodity price volatility")
            insights.append("Oil & gas companies may have negative FCF due to high capex - use peer comparison")
            if quarterly_score < 35:
                insights.append("Quarterly volatility expected due to commodity price fluctuations")

        elif sector == 'fmcg':
            if overall_score > 45:
                insights.append("Strong consistency indicates brand strength and market position")
            if growth_score > 50:
                insights.append("Consistent growth suggests effective distribution and brand building")

        # General insights
        if 'sector_percentile_rank' in consistency_results:
            rank = consistency_results['sector_percentile_rank']
            if rank > 75:
                insights.append(f"Top quartile consistency within {sector} sector")
            elif rank > 50:
                insights.append(f"Above average consistency within {sector} sector")
            elif rank < 25:
                insights.append(f"Below average consistency within {sector} sector - investigate further")

        return insights

    def _extract_time_series(self, company_data: Dict[str, Any]) -> Dict[str, pd.Series]:
        """
        Extract time series data from company data

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data including historical financial information

        Returns:
        --------
        Dictionary mapping metric names to pandas Series with time series data
        """
        time_series = {}

        # Process yearly data from profit_loss
        if 'profit_loss' in company_data:
            pl_data = company_data['profit_loss'].copy()

            # Remove units and notes
            if 'units' in pl_data:
                pl_data.pop('units')
            if 'notes' in pl_data:
                pl_data.pop('notes')

            # Get all years (excluding TTM)
            years = [year for year in pl_data.keys() if year != 'TTM']
            years.sort()  # Sort chronologically

            if len(years) >= self.min_data_points:
                # Extract metrics from each year
                all_metrics = set()
                for year in years:
                    if isinstance(pl_data[year], dict):
                        all_metrics.update(pl_data[year].keys())

                # Create time series for each metric
                for metric in all_metrics:
                    # Clean metric name
                    clean_metric = metric.strip().replace('\u00a0', '').replace('+', '').replace('_%', '').replace('_in_rs', '').lower()

                    # Extract values for this metric across years
                    values = []
                    valid_years = []

                    for year in years:
                        if isinstance(pl_data[year], dict) and metric in pl_data[year]:
                            value = pl_data[year][metric]
                            try:
                                # Convert to numeric value
                                if isinstance(value, str):
                                    if '%' in value:
                                        value = float(value.replace('%', ''))
                                    else:
                                        value = float(value.replace(',', ''))
                                elif value is not None:
                                    value = float(value)

                                values.append(value)
                                valid_years.append(year)
                            except (ValueError, TypeError):
                                continue

                    if len(values) >= self.min_data_points:
                        time_series[clean_metric] = pd.Series(values, index=valid_years)

        # Process quarterly data
        if 'quarters' in company_data:
            quarters_data = company_data['quarters'].copy()

            # Remove units, notes, and other non-metric keys
            if 'units' in quarters_data:
                quarters_data.pop('units')
            if 'notes' in quarters_data:
                quarters_data.pop('notes')
            if 'raw_pdf' in quarters_data:
                quarters_data.pop('raw_pdf')

            # Get all metrics (keys that have quarterly data)
            metric_keys = [key for key in quarters_data.keys() if isinstance(quarters_data[key], dict)]

            # Process each metric
            for metric in metric_keys:
                values_dict = quarters_data[metric]

                # Clean metric name
                clean_metric_name = metric.strip().replace('\u00a0', '').replace('+', '').replace('_%', '').replace('_in_rs', '').lower()
                clean_metric = f"quarterly_{clean_metric_name}"

                # Extract quarters and values
                quarters = []
                values = []

                for quarter, value in values_dict.items():
                    try:
                        # Convert to numeric value
                        if isinstance(value, str):
                            if '%' in value:
                                value = float(value.replace('%', ''))
                            else:
                                value = float(value.replace(',', ''))
                        elif value is not None:
                            value = float(value)

                        quarters.append(quarter)
                        values.append(value)
                    except (ValueError, TypeError):
                        continue

                if len(values) >= self.min_data_points:
                    # Sort by quarter chronologically
                    quarter_value_pairs = list(zip(quarters, values))
                    quarter_value_pairs.sort(key=lambda x: x[0])  # Sort by quarter

                    sorted_quarters, sorted_values = zip(*quarter_value_pairs)
                    time_series[clean_metric] = pd.Series(sorted_values, index=sorted_quarters)

        # Process cash flow data
        if 'cash_flow' in company_data:
            cf_data = company_data['cash_flow'].copy()

            # Remove units and notes
            if 'units' in cf_data:
                cf_data.pop('units')
            if 'notes' in cf_data:
                cf_data.pop('notes')

            # Get all years (excluding TTM)
            years = [year for year in cf_data.keys() if year != 'TTM']
            years.sort()  # Sort chronologically

            if len(years) >= self.min_data_points:
                # Extract metrics from each year
                all_metrics = set()
                for year in years:
                    if isinstance(cf_data[year], dict):
                        all_metrics.update(cf_data[year].keys())

                # Create time series for each metric
                for metric in all_metrics:
                    # Clean metric name
                    clean_metric = metric.strip().replace('\u00a0', '').replace('+', '').replace('_%', '').replace('_in_rs', '').lower()

                    # Extract values for this metric across years
                    values = []
                    valid_years = []

                    for year in years:
                        if isinstance(cf_data[year], dict) and metric in cf_data[year]:
                            value = cf_data[year][metric]
                            try:
                                # Convert to numeric value
                                if isinstance(value, str):
                                    if '%' in value:
                                        value = float(value.replace('%', ''))
                                    else:
                                        value = float(value.replace(',', ''))
                                elif value is not None:
                                    value = float(value)

                                values.append(value)
                                valid_years.append(year)
                            except (ValueError, TypeError):
                                continue

                    if len(values) >= self.min_data_points:
                        time_series[clean_metric] = pd.Series(values, index=valid_years)

        return time_series

    def _analyze_growth_consistency(self, time_series: Dict[str, pd.Series]) -> Dict[str, float]:
        """
        Analyze the consistency of growth metrics

        Parameters:
        -----------
        time_series : Dict[str, pd.Series]
            Dictionary mapping metric names to pandas Series with time series data

        Returns:
        --------
        Dictionary containing growth consistency metrics
        """
        growth_consistency = {}

        # Analyze each growth metric
        for metric in self.growth_metrics:
            if metric in time_series:
                series = time_series[metric]

                # Calculate year-over-year growth rates
                growth_rates = series.pct_change(fill_method=None).dropna()

                if len(growth_rates) >= self.min_data_points - 1:
                    # Calculate consistency metrics with error handling
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        mean_growth = growth_rates.mean() * 100 if not growth_rates.isna().all() else 0
                        std_growth = growth_rates.std() * 100 if not growth_rates.isna().all() else 0
                        cv_growth = std_growth / abs(mean_growth) if mean_growth != 0 and not np.isnan(mean_growth) else float('inf')

                    # Calculate trend consistency (R-squared of linear regression)
                    try:
                        x = np.arange(len(series))
                        valid_indices = ~np.isnan(series.values)
                        if np.sum(valid_indices) >= 2:
                            _, _, r_value, _, _ = stats.linregress(x[valid_indices], series.values[valid_indices])
                            r_squared = r_value ** 2 if not np.isnan(r_value) else 0
                        else:
                            r_squared = 0
                    except (ValueError, TypeError):
                        r_squared = 0

                    # Calculate percentage of positive growth years
                    positive_growth_pct = (growth_rates > 0).mean() * 100

                    # Store results
                    growth_consistency[metric] = {
                        'mean_growth': mean_growth,
                        'std_growth': std_growth,
                        'cv_growth': cv_growth,
                        'r_squared': r_squared,
                        'positive_growth_pct': positive_growth_pct,
                        'consistency_score': self._calculate_growth_consistency_score(
                            mean_growth, cv_growth, r_squared, positive_growth_pct
                        )
                    }

        # Calculate overall growth consistency score
        if growth_consistency:
            scores = [metrics['consistency_score'] for metrics in growth_consistency.values()]
            growth_consistency['overall_score'] = sum(scores) / len(scores)

        return growth_consistency

    def _analyze_profitability_consistency(self, time_series: Dict[str, pd.Series]) -> Dict[str, float]:
        """
        Analyze the consistency of profitability metrics

        Parameters:
        -----------
        time_series : Dict[str, pd.Series]
            Dictionary mapping metric names to pandas Series with time series data

        Returns:
        --------
        Dictionary containing profitability consistency metrics
        """
        profitability_consistency = {}

        # Analyze each profitability metric
        for metric in self.profitability_metrics:
            if metric in time_series:
                series = time_series[metric]

                if len(series) >= self.min_data_points:
                    # Calculate consistency metrics with error handling
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        mean_value = series.mean() if not series.isna().all() else 0
                        std_value = series.std() if not series.isna().all() else 0
                        cv_value = std_value / abs(mean_value) if mean_value != 0 and not np.isnan(mean_value) else float('inf')

                    # Calculate trend consistency (R-squared of linear regression)
                    try:
                        x = np.arange(len(series))
                        valid_indices = ~np.isnan(series.values)
                        if np.sum(valid_indices) >= 2:
                            _, _, r_value, _, _ = stats.linregress(x[valid_indices], series.values[valid_indices])
                            r_squared = r_value ** 2 if not np.isnan(r_value) else 0
                        else:
                            r_squared = 0
                    except (ValueError, TypeError):
                        r_squared = 0

                    # Store results
                    profitability_consistency[metric] = {
                        'mean_value': mean_value,
                        'std_value': std_value,
                        'cv_value': cv_value,
                        'r_squared': r_squared,
                        'consistency_score': self._calculate_profitability_consistency_score(
                            mean_value, cv_value, r_squared
                        )
                    }

        # Calculate overall profitability consistency score
        if profitability_consistency:
            scores = [metrics['consistency_score'] for metrics in profitability_consistency.values()]
            profitability_consistency['overall_score'] = sum(scores) / len(scores)

        return profitability_consistency

    def _analyze_leverage_consistency(self, time_series: Dict[str, pd.Series]) -> Dict[str, float]:
        """
        Analyze the consistency of leverage metrics

        Parameters:
        -----------
        time_series : Dict[str, pd.Series]
            Dictionary mapping metric names to pandas Series with time series data

        Returns:
        --------
        Dictionary containing leverage consistency metrics
        """
        leverage_consistency = {}

        # Analyze each leverage metric
        for metric in self.leverage_metrics:
            if metric in time_series:
                series = time_series[metric]

                if len(series) >= self.min_data_points:
                    # Calculate consistency metrics with error handling
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        mean_value = series.mean() if not series.isna().all() else 0
                        std_value = series.std() if not series.isna().all() else 0
                        cv_value = std_value / abs(mean_value) if mean_value != 0 and not np.isnan(mean_value) else float('inf')

                    # Calculate trend consistency (R-squared of linear regression)
                    try:
                        x = np.arange(len(series))
                        valid_indices = ~np.isnan(series.values)
                        if np.sum(valid_indices) >= 2:
                            _, _, r_value, _, _ = stats.linregress(x[valid_indices], series.values[valid_indices])
                            r_squared = r_value ** 2 if not np.isnan(r_value) else 0
                        else:
                            r_squared = 0
                    except (ValueError, TypeError):
                        r_squared = 0

                    # Store results
                    leverage_consistency[metric] = {
                        'mean_value': mean_value,
                        'std_value': std_value,
                        'cv_value': cv_value,
                        'r_squared': r_squared,
                        'consistency_score': self._calculate_leverage_consistency_score(
                            mean_value, cv_value, r_squared, metric
                        )
                    }

        # Calculate overall leverage consistency score
        if leverage_consistency:
            scores = [metrics['consistency_score'] for metrics in leverage_consistency.values()]
            leverage_consistency['overall_score'] = sum(scores) / len(scores)

        return leverage_consistency

    def _analyze_quarterly_consistency(self, time_series: Dict[str, pd.Series]) -> Dict[str, float]:
        """
        Analyze the consistency of quarterly metrics

        Parameters:
        -----------
        time_series : Dict[str, pd.Series]
            Dictionary mapping metric names to pandas Series with time series data

        Returns:
        --------
        Dictionary containing quarterly consistency metrics
        """
        quarterly_consistency = {}

        # Find quarterly metrics
        quarterly_metrics = [metric for metric in time_series.keys() if metric.startswith('quarterly_')]

        # Analyze each quarterly metric
        for metric in quarterly_metrics:
            series = time_series[metric]

            if len(series) >= self.min_data_points:
                # Calculate quarter-over-quarter growth rates
                qoq_growth = series.pct_change(fill_method=None).dropna()

                if len(qoq_growth) >= self.min_data_points - 1:
                    # Calculate consistency metrics with error handling
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        mean_growth = qoq_growth.mean() * 100 if not qoq_growth.isna().all() else 0
                        std_growth = qoq_growth.std() * 100 if not qoq_growth.isna().all() else 0
                        cv_growth = std_growth / abs(mean_growth) if mean_growth != 0 and not np.isnan(mean_growth) else float('inf')

                    # Calculate trend consistency (R-squared of linear regression)
                    try:
                        x = np.arange(len(series))
                        # Filter out NaN values for regression
                        valid_indices = ~np.isnan(series.values)
                        if np.sum(valid_indices) >= 2:
                            _, _, r_value, _, _ = stats.linregress(x[valid_indices], series.values[valid_indices])
                            r_squared = r_value ** 2 if not np.isnan(r_value) else 0
                        else:
                            r_squared = 0
                    except (ValueError, TypeError):
                        r_squared = 0

                    # Calculate percentage of positive growth quarters
                    positive_growth_pct = (qoq_growth > 0).mean() * 100

                    # Store results
                    base_metric = metric.replace('quarterly_', '')
                    quarterly_consistency[base_metric] = {
                        'mean_qoq_growth': mean_growth,
                        'std_qoq_growth': std_growth,
                        'cv_qoq_growth': cv_growth,
                        'r_squared': r_squared,
                        'positive_qoq_pct': positive_growth_pct,
                        'consistency_score': self._calculate_quarterly_consistency_score(
                            mean_growth, cv_growth, r_squared, positive_growth_pct
                        )
                    }

        # Calculate overall quarterly consistency score
        if quarterly_consistency:
            scores = [metrics['consistency_score'] for metrics in quarterly_consistency.values()]
            quarterly_consistency['overall_score'] = sum(scores) / len(scores)

        return quarterly_consistency

    def _calculate_growth_consistency_score(self,
                                          mean_growth: float,
                                          cv_growth: float,
                                          r_squared: float,
                                          positive_growth_pct: float) -> float:
        """
        Calculate a consistency score for growth metrics

        Parameters:
        -----------
        mean_growth : float
            Mean growth rate
        cv_growth : float
            Coefficient of variation of growth rate
        r_squared : float
            R-squared of linear regression
        positive_growth_pct : float
            Percentage of periods with positive growth

        Returns:
        --------
        Consistency score (0-100)
        """
        # Score components
        growth_score = min(100, max(0, mean_growth)) / 2  # Cap at 50 points for 100% growth
        cv_score = max(0, 30 - min(30, cv_growth * 10))  # Lower CV is better, max 30 points
        trend_score = r_squared * 10  # Max 10 points for perfect trend
        positive_score = positive_growth_pct / 10  # Max 10 points for 100% positive growth

        # Combine scores
        total_score = growth_score + cv_score + trend_score + positive_score

        # Normalize to 0-100 scale
        normalized_score = min(100, max(0, total_score))

        return normalized_score

    def _calculate_profitability_consistency_score(self,
                                                 mean_value: float,
                                                 cv_value: float,
                                                 r_squared: float) -> float:
        """
        Calculate a consistency score for profitability metrics

        Parameters:
        -----------
        mean_value : float
            Mean value of the metric
        cv_value : float
            Coefficient of variation
        r_squared : float
            R-squared of linear regression

        Returns:
        --------
        Consistency score (0-100)
        """
        # Score components
        value_score = min(50, max(0, mean_value * 2.5))  # Max 50 points for 20% ROE/ROCE
        cv_score = max(0, 40 - min(40, cv_value * 20))  # Lower CV is better, max 40 points
        trend_score = r_squared * 10  # Max 10 points for perfect trend

        # Combine scores
        total_score = value_score + cv_score + trend_score

        # Normalize to 0-100 scale
        normalized_score = min(100, max(0, total_score))

        return normalized_score

    def _calculate_leverage_consistency_score(self,
                                            mean_value: float,
                                            cv_value: float,
                                            r_squared: float,
                                            metric: str) -> float:
        """
        Calculate a consistency score for leverage metrics

        Parameters:
        -----------
        mean_value : float
            Mean value of the metric
        cv_value : float
            Coefficient of variation
        r_squared : float
            R-squared of linear regression
        metric : str
            Name of the metric

        Returns:
        --------
        Consistency score (0-100)
        """
        # Score components
        if metric == 'debt_to_equity':
            # Lower debt-to-equity is better
            value_score = max(0, 50 - min(50, mean_value * 25))  # Max 50 points for 0 D/E
        else:  # interest_coverage
            # Higher interest coverage is better
            value_score = min(50, max(0, mean_value * 5))  # Max 50 points for 10x coverage

        cv_score = max(0, 40 - min(40, cv_value * 20))  # Lower CV is better, max 40 points
        trend_score = r_squared * 10  # Max 10 points for perfect trend

        # Combine scores
        total_score = value_score + cv_score + trend_score

        # Normalize to 0-100 scale
        normalized_score = min(100, max(0, total_score))

        return normalized_score

    def _calculate_quarterly_consistency_score(self,
                                             mean_growth: float,
                                             cv_growth: float,
                                             r_squared: float,
                                             positive_growth_pct: float) -> float:
        """
        Calculate a consistency score for quarterly metrics

        Parameters:
        -----------
        mean_growth : float
            Mean quarter-over-quarter growth rate
        cv_growth : float
            Coefficient of variation of growth rate
        r_squared : float
            R-squared of linear regression
        positive_growth_pct : float
            Percentage of quarters with positive growth

        Returns:
        --------
        Consistency score (0-100)
        """
        # Score components
        growth_score = min(40, max(0, mean_growth * 2))  # Cap at 40 points for 20% QoQ growth
        cv_score = max(0, 30 - min(30, cv_growth * 5))  # Lower CV is better, max 30 points
        trend_score = r_squared * 10  # Max 10 points for perfect trend
        positive_score = positive_growth_pct / 5  # Max 20 points for 100% positive growth

        # Combine scores
        total_score = growth_score + cv_score + trend_score + positive_score

        # Normalize to 0-100 scale
        normalized_score = min(100, max(0, total_score))

        return normalized_score

    def _calculate_overall_consistency_score(self,
                                           growth_consistency: Dict[str, Any],
                                           profitability_consistency: Dict[str, Any],
                                           leverage_consistency: Dict[str, Any],
                                           quarterly_consistency: Dict[str, Any]) -> float:
        """
        Calculate an overall consistency score

        Parameters:
        -----------
        growth_consistency : Dict[str, Any]
            Growth consistency metrics
        profitability_consistency : Dict[str, Any]
            Profitability consistency metrics
        leverage_consistency : Dict[str, Any]
            Leverage consistency metrics
        quarterly_consistency : Dict[str, Any]
            Quarterly consistency metrics

        Returns:
        --------
        Overall consistency score (0-100)
        """
        scores = []
        weights = []

        # Add growth consistency score (higher weight for annual growth trends)
        if 'overall_score' in growth_consistency:
            scores.append(growth_consistency['overall_score'])
            weights.append(0.50)  # 50% weight - annual growth is more important

        # Add profitability consistency score
        if 'overall_score' in profitability_consistency:
            scores.append(profitability_consistency['overall_score'])
            weights.append(0.20)  # 20% weight

        # Add leverage consistency score
        if 'overall_score' in leverage_consistency:
            scores.append(leverage_consistency['overall_score'])
            weights.append(0.10)  # 10% weight

        # Add quarterly consistency score (lower weight as quarterly data is naturally more volatile)
        if 'overall_score' in quarterly_consistency:
            scores.append(quarterly_consistency['overall_score'])
            weights.append(0.20)  # 20% weight - quarterly volatility is expected

        # Calculate weighted average
        if scores:
            # Normalize weights
            weights = [w / sum(weights) for w in weights]

            # Calculate weighted score
            overall_score = sum(s * w for s, w in zip(scores, weights))

            return overall_score

        return None
