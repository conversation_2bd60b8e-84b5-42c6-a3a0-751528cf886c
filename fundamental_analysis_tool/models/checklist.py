#!/usr/bin/env python3
"""
Checklist Module

This module provides functions to evaluate companies against due diligence checklists.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Callable

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('checklist')

class Checklist:
    """
    Class to evaluate companies against due diligence checklists
    """

    def __init__(self):
        """
        Initialize the checklist evaluator
        """
        # Define the default checklist
        self.default_checklist = {
            # Profitability Checks
            'gross_profit_margin': {
                'description': 'Gross Profit Margin > 20%',
                'threshold': 20.0,
                'comparison': 'greater_than',
                'weight': 1.0,
                'category': 'profitability'
            },
            'return_on_equity': {
                'description': 'Return on Equity (ROE) > 15%',
                'threshold': 15.0,
                'comparison': 'greater_than',
                'weight': 1.0,
                'category': 'profitability'
            },
            'return_on_capital': {
                'description': 'Return on Capital Employed (ROCE) > 15%',
                'threshold': 15.0,
                'comparison': 'greater_than',
                'weight': 1.0,
                'category': 'profitability'
            },

            # Growth Checks
            'revenue_profit_alignment': {
                'description': 'Revenue growth in line with profit growth',
                'threshold': 0.8,  # Revenue CAGR / Profit CAGR should be at least 0.8
                'comparison': 'greater_than',
                'weight': 1.0,
                'category': 'growth'
            },
            'eps_consistency': {
                'description': 'EPS consistent with Net Profits (check for equity dilution)',
                'threshold': 0.9,  # EPS CAGR / PAT CAGR should be at least 0.9
                'comparison': 'greater_than',
                'weight': 1.0,
                'category': 'growth'
            },

            # Financial Health Checks
            'debt_level': {
                'description': 'Manageable debt levels (Debt to Equity < 1.0)',
                'threshold': 1.0,  # Debt-to-Equity ratio should be less than 1.0
                'comparison': 'less_than',
                'weight': 1.0,
                'category': 'financial_health'
            },
            'current_ratio': {
                'description': 'Current Ratio > 1.5',
                'threshold': 1.5,
                'comparison': 'greater_than',
                'weight': 1.0,
                'category': 'financial_health'
            },
            'interest_coverage': {
                'description': 'Interest Coverage Ratio > 3.0',
                'threshold': 3.0,
                'comparison': 'greater_than',
                'weight': 1.0,
                'category': 'financial_health'
            },

            # Operational Efficiency Checks
            'inventory_management': {
                'description': 'Growing inventory with growing PAT margin',
                'threshold': 0.0,  # Correlation between inventory growth and PAT margin growth should be positive
                'comparison': 'greater_than',
                'weight': 1.0,
                'category': 'operational_efficiency'
            },
            'receivables_management': {
                'description': 'Days Sales Outstanding < 60 days',
                'threshold': 60.0,  # Days Sales Outstanding should be less than 60 days
                'comparison': 'less_than',
                'weight': 1.0,
                'category': 'operational_efficiency'
            },

            # Cash Flow Checks
            'operating_cash_flow': {
                'description': 'Positive cash flow from operations',
                'threshold': 0.0,
                'comparison': 'greater_than',
                'weight': 1.0,
                'category': 'cash_flow'
            },
            'free_cash_flow': {
                'description': 'Positive Free Cash Flow',
                'threshold': 0.0,
                'comparison': 'greater_than',
                'weight': 1.0,
                'category': 'cash_flow'
            },
            'ocf_to_net_income': {
                'description': 'Operating Cash Flow to Net Income > 80%',
                'threshold': 80.0,
                'comparison': 'greater_than',
                'weight': 1.0,
                'category': 'cash_flow'
            },
            'cash_flow_pattern': {
                'description': 'Healthy Cash Flow Pattern (Operating positive)',
                'threshold': 0.0,  # Special check for cash flow pattern
                'comparison': 'special',
                'weight': 1.0,
                'category': 'cash_flow'
            },

            # Business Structure Checks
            'business_simplicity': {
                'description': 'Simple business structure (1 or 2 business lines)',
                'threshold': 2.0,  # Number of business segments should be less than or equal to 2
                'comparison': 'less_than_equal',
                'weight': 1.0,
                'category': 'business_structure'
            },
            'subsidiaries_count': {
                'description': 'Not too many subsidiaries',
                'threshold': 5.0,  # Number of subsidiaries should be less than or equal to 5
                'comparison': 'less_than_equal',
                'weight': 1.0,
                'category': 'business_structure'
            }
        }

    def evaluate_company(self,
                        company_data: Dict[str, Any],
                        checklist: Optional[Dict[str, Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Evaluate a company against a checklist

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Dictionary containing company financial data
        checklist : Dict[str, Dict[str, Any]], optional
            Checklist to evaluate against (uses default if None)

        Returns:
        --------
        Dictionary containing evaluation results
        """
        if checklist is None:
            checklist = self.default_checklist

        results = {
            'items': {},
            'score': 0.0,
            'max_score': 0.0,
            'percentage': 0.0
        }

        try:
            # Helper function to convert values to float
            def safe_float(value):
                if isinstance(value, (int, float)):
                    return float(value)
                elif isinstance(value, str):
                    value = value.replace(',', '').strip()
                    if value:
                        try:
                            return float(value)
                        except ValueError:
                            return 0.0
                return 0.0

            # Extract financial data
            pl_data = company_data.get('profit_loss', {})
            bs_data = company_data.get('balance_sheet', {})
            cf_data = company_data.get('cash_flow', {})
            overview = company_data.get('overview', {})
            ratios_data = company_data.get('ratios', {}).get('ratios', {})

            # Remove 'units' key if present
            if 'units' in pl_data:
                pl_data.pop('units')
            if 'units' in bs_data:
                bs_data.pop('units')
            if 'units' in cf_data:
                cf_data.pop('units')

            # Get the most recent year's data
            latest_year_pl = sorted([k for k in pl_data.keys() if k != 'units' and k != 'notes'])[-1] if pl_data else None
            latest_year_bs = sorted([k for k in bs_data.keys() if k != 'units' and k != 'notes'])[-1] if bs_data else None
            latest_year_cf = sorted([k for k in cf_data.keys() if k != 'units' and k != 'notes'])[-1] if cf_data else None

            # Calculate total possible score
            total_possible_score = sum(item['weight'] for item in checklist.values())
            results['max_score'] = total_possible_score

            # Evaluate each checklist item
            for key, item in checklist.items():
                item_result = {
                    'description': item['description'],
                    'threshold': item['threshold'],
                    'comparison': item['comparison'],
                    'weight': item['weight'],
                    'value': None,
                    'pass': False,
                    'score': 0.0
                }

                # Evaluate based on the checklist item
                if key == 'gross_profit_margin':
                    if latest_year_pl and 'gross_profit' in pl_data[latest_year_pl] and 'revenue' in pl_data[latest_year_pl]:
                        gross_profit = pl_data[latest_year_pl]['gross_profit']
                        revenue = pl_data[latest_year_pl]['revenue']

                        if revenue and revenue > 0:
                            gross_margin = (gross_profit / revenue) * 100
                            item_result['value'] = gross_margin
                            item_result['pass'] = gross_margin > item['threshold']

                elif key == 'revenue_profit_alignment':
                    # Calculate 5-year CAGR for revenue and profit
                    all_years = sorted(pl_data.keys())
                    if len(all_years) >= 5:
                        latest_year = all_years[-1]
                        earliest_year = all_years[-5]

                        if 'revenue' in pl_data[latest_year] and 'revenue' in pl_data[earliest_year] and \
                           'pat' in pl_data[latest_year] and 'pat' in pl_data[earliest_year]:

                            latest_revenue = pl_data[latest_year]['revenue']
                            earliest_revenue = pl_data[earliest_year]['revenue']
                            latest_pat = pl_data[latest_year]['pat']
                            earliest_pat = pl_data[earliest_year]['pat']

                            if earliest_revenue > 0 and earliest_pat > 0:
                                revenue_cagr = ((latest_revenue / earliest_revenue) ** (1 / 5)) - 1
                                pat_cagr = ((latest_pat / earliest_pat) ** (1 / 5)) - 1

                                if pat_cagr > 0:
                                    ratio = revenue_cagr / pat_cagr
                                    item_result['value'] = ratio
                                    item_result['pass'] = ratio > item['threshold']

                elif key == 'eps_consistency':
                    # Calculate 5-year CAGR for EPS and PAT
                    all_years = sorted(pl_data.keys())
                    if len(all_years) >= 5:
                        latest_year = all_years[-1]
                        earliest_year = all_years[-5]

                        if 'eps' in pl_data[latest_year] and 'eps' in pl_data[earliest_year] and \
                           'pat' in pl_data[latest_year] and 'pat' in pl_data[earliest_year]:

                            latest_eps = pl_data[latest_year]['eps']
                            earliest_eps = pl_data[earliest_year]['eps']
                            latest_pat = pl_data[latest_year]['pat']
                            earliest_pat = pl_data[earliest_year]['pat']

                            if earliest_eps > 0 and earliest_pat > 0:
                                eps_cagr = ((latest_eps / earliest_eps) ** (1 / 5)) - 1
                                pat_cagr = ((latest_pat / earliest_pat) ** (1 / 5)) - 1

                                if pat_cagr > 0:
                                    ratio = eps_cagr / pat_cagr
                                    item_result['value'] = ratio
                                    item_result['pass'] = ratio > item['threshold']

                elif key == 'debt_level':
                    if latest_year_bs and 'total_debt' in bs_data[latest_year_bs] and 'total_equity' in bs_data[latest_year_bs]:
                        debt = bs_data[latest_year_bs]['total_debt']
                        equity = bs_data[latest_year_bs]['total_equity']

                        if equity and equity > 0:
                            debt_to_equity = debt / equity
                            item_result['value'] = debt_to_equity
                            item_result['pass'] = debt_to_equity < item['threshold']

                elif key == 'inventory_management':
                    # Calculate correlation between inventory growth and PAT margin growth
                    all_years = sorted(pl_data.keys())
                    if len(all_years) >= 3 and all(year in bs_data for year in all_years[-3:]):
                        inventory_growth = []
                        pat_margin_growth = []

                        for i in range(len(all_years) - 3, len(all_years)):
                            year = all_years[i]
                            prev_year = all_years[i-1] if i > 0 else None

                            if prev_year and 'inventory' in bs_data[year] and 'inventory' in bs_data[prev_year] and \
                               'pat' in pl_data[year] and 'revenue' in pl_data[year] and \
                               'pat' in pl_data[prev_year] and 'revenue' in pl_data[prev_year]:

                                inventory = bs_data[year]['inventory']
                                prev_inventory = bs_data[prev_year]['inventory']
                                pat = pl_data[year]['pat']
                                revenue = pl_data[year]['revenue']
                                prev_pat = pl_data[prev_year]['pat']
                                prev_revenue = pl_data[prev_year]['revenue']

                                if prev_inventory > 0 and prev_revenue > 0 and revenue > 0:
                                    inventory_growth_rate = (inventory / prev_inventory) - 1
                                    pat_margin = pat / revenue
                                    prev_pat_margin = prev_pat / prev_revenue
                                    pat_margin_growth_rate = (pat_margin / prev_pat_margin) - 1

                                    inventory_growth.append(inventory_growth_rate)
                                    pat_margin_growth.append(pat_margin_growth_rate)

                        if inventory_growth and pat_margin_growth:
                            correlation = np.corrcoef(inventory_growth, pat_margin_growth)[0, 1]
                            item_result['value'] = correlation
                            item_result['pass'] = correlation > item['threshold']

                elif key == 'receivables_management':
                    # First check if we have the value from screener.in ratios
                    if 'debtor_days' in ratios_data:
                        days_sales_outstanding = safe_float(ratios_data['debtor_days'])
                        if days_sales_outstanding > 0:
                            item_result['value'] = days_sales_outstanding
                            item_result['pass'] = days_sales_outstanding < item['threshold']
                    # If not, calculate from financial statements
                    elif latest_year_bs and latest_year_pl and \
                         'accounts_receivable' in bs_data[latest_year_bs] and 'revenue' in pl_data[latest_year_pl]:

                        receivables = safe_float(bs_data[latest_year_bs]['accounts_receivable'])
                        revenue = safe_float(pl_data[latest_year_pl]['revenue'])

                        if revenue > 0:
                            days_sales_outstanding = (receivables / revenue) * 365
                            item_result['value'] = days_sales_outstanding
                            item_result['pass'] = days_sales_outstanding < item['threshold']

                elif key == 'operating_cash_flow':
                    # Check if we have the value from our cash flow ratios
                    if 'ratios' in company_data and 'cash_flow' in company_data['ratios']:
                        if 'operating_cash_flow' in company_data['ratios']['cash_flow']:
                            operating_cash_flow = safe_float(company_data['ratios']['cash_flow']['operating_cash_flow'])
                            item_result['value'] = operating_cash_flow
                            item_result['pass'] = operating_cash_flow > item['threshold']
                    # If not, try to get it from the cash flow statement
                    elif latest_year_cf:
                        operating_cash_flow = None
                        for key_name in ['cash_from_operating_activity', 'cash_from_operating_activity\u00a0+', 'operating_cash_flow']:
                            if key_name in cf_data and latest_year_cf in cf_data[key_name]:
                                operating_cash_flow = safe_float(cf_data[key_name][latest_year_cf])
                                break

                        if operating_cash_flow is not None:
                            item_result['value'] = operating_cash_flow
                            item_result['pass'] = operating_cash_flow > item['threshold']

                elif key == 'free_cash_flow':
                    # Check if we have the value from our cash flow ratios
                    if 'ratios' in company_data and 'cash_flow' in company_data['ratios']:
                        if 'free_cash_flow' in company_data['ratios']['cash_flow']:
                            free_cash_flow = safe_float(company_data['ratios']['cash_flow']['free_cash_flow'])
                            item_result['value'] = free_cash_flow
                            item_result['pass'] = free_cash_flow > item['threshold']
                    # If not, try to calculate it from operating and investing cash flows
                    elif latest_year_cf:
                        operating_cash_flow = None
                        investing_cash_flow = None

                        # Get operating cash flow
                        for key_name in ['cash_from_operating_activity', 'cash_from_operating_activity\u00a0+', 'operating_cash_flow']:
                            if key_name in cf_data and latest_year_cf in cf_data[key_name]:
                                operating_cash_flow = safe_float(cf_data[key_name][latest_year_cf])
                                break

                        # Get investing cash flow
                        for key_name in ['cash_from_investing_activity', 'cash_from_investing_activity\u00a0+', 'investing_cash_flow']:
                            if key_name in cf_data and latest_year_cf in cf_data[key_name]:
                                investing_cash_flow = safe_float(cf_data[key_name][latest_year_cf])
                                break

                        if operating_cash_flow is not None and investing_cash_flow is not None:
                            free_cash_flow = operating_cash_flow + investing_cash_flow
                            item_result['value'] = free_cash_flow
                            item_result['pass'] = free_cash_flow > item['threshold']

                elif key == 'ocf_to_net_income':
                    # Check if we have the value from our cash flow ratios
                    if 'ratios' in company_data and 'cash_flow' in company_data['ratios']:
                        if 'ocf_to_net_income' in company_data['ratios']['cash_flow']:
                            ocf_to_net_income = safe_float(company_data['ratios']['cash_flow']['ocf_to_net_income'])
                            item_result['value'] = ocf_to_net_income
                            item_result['pass'] = ocf_to_net_income > item['threshold']
                    # If not, try to calculate it from operating cash flow and net income
                    elif latest_year_cf and latest_year_pl and 'pat' in pl_data[latest_year_pl]:
                        operating_cash_flow = None

                        # Get operating cash flow
                        for key_name in ['cash_from_operating_activity', 'cash_from_operating_activity\u00a0+', 'operating_cash_flow']:
                            if key_name in cf_data and latest_year_cf in cf_data[key_name]:
                                operating_cash_flow = safe_float(cf_data[key_name][latest_year_cf])
                                break

                        if operating_cash_flow is not None:
                            net_income = safe_float(pl_data[latest_year_pl]['pat'])
                            if net_income > 0:
                                ocf_to_net_income = (operating_cash_flow / net_income) * 100
                                item_result['value'] = ocf_to_net_income
                                item_result['pass'] = ocf_to_net_income > item['threshold']

                elif key == 'cash_flow_pattern':
                    # Check if we have the cash flow pattern analysis
                    if 'ratios' in company_data and 'cash_flow_analysis' in company_data['ratios'] and 'pattern' in company_data['ratios']['cash_flow_analysis']:
                        pattern = company_data['ratios']['cash_flow_analysis']['pattern']
                        # Consider it a pass if operating cash flow is positive and health is excellent or good
                        if pattern['operating_sign'] == 'positive' and pattern['health'] in ['excellent', 'good']:
                            item_result['value'] = 1.0  # Arbitrary value for a good pattern
                            item_result['pass'] = True
                        else:
                            item_result['value'] = 0.0
                            item_result['pass'] = False
                    # If not, just check if operating cash flow is positive
                    elif latest_year_cf:
                        operating_cash_flow = None

                        # Get operating cash flow
                        for key_name in ['cash_from_operating_activity', 'cash_from_operating_activity\u00a0+', 'operating_cash_flow']:
                            if key_name in cf_data and latest_year_cf in cf_data[key_name]:
                                operating_cash_flow = safe_float(cf_data[key_name][latest_year_cf])
                                break

                        if operating_cash_flow is not None:
                            item_result['value'] = operating_cash_flow
                            item_result['pass'] = operating_cash_flow > item['threshold']

                elif key == 'return_on_capital':
                    # First check if we have the value from screener.in ratios
                    if 'roce_%' in ratios_data:
                        roce = safe_float(ratios_data['roce_%'])
                        if roce > 0:
                            item_result['value'] = roce
                            item_result['pass'] = roce > item['threshold']
                    elif 'roce' in ratios_data:
                        roce = safe_float(ratios_data['roce'])
                        if roce > 0:
                            item_result['value'] = roce
                            item_result['pass'] = roce > item['threshold']
                    # If not, calculate from financial statements
                    elif latest_year_pl and latest_year_bs and \
                         'ebit' in pl_data[latest_year_pl] and 'total_assets' in bs_data[latest_year_bs] and 'current_liabilities' in bs_data[latest_year_bs]:

                        ebit = safe_float(pl_data[latest_year_pl]['ebit'])
                        total_assets = safe_float(bs_data[latest_year_bs]['total_assets'])
                        current_liabilities = safe_float(bs_data[latest_year_bs]['current_liabilities'])

                        capital_employed = total_assets - current_liabilities

                        if capital_employed > 0:
                            roce = (ebit / capital_employed) * 100
                            item_result['value'] = roce
                            item_result['pass'] = roce > item['threshold']

                elif key == 'current_ratio':
                    # First check if we have the value from screener.in ratios
                    if 'current_ratio' in ratios_data:
                        current_ratio = safe_float(ratios_data['current_ratio'])
                        if current_ratio > 0:
                            item_result['value'] = current_ratio
                            item_result['pass'] = current_ratio > item['threshold']
                    # If not, calculate from financial statements
                    elif latest_year_bs and 'current_assets' in bs_data[latest_year_bs] and 'current_liabilities' in bs_data[latest_year_bs]:
                        current_assets = safe_float(bs_data[latest_year_bs]['current_assets'])
                        current_liabilities = safe_float(bs_data[latest_year_bs]['current_liabilities'])

                        if current_liabilities > 0:
                            current_ratio = current_assets / current_liabilities
                            item_result['value'] = current_ratio
                            item_result['pass'] = current_ratio > item['threshold']

                elif key == 'interest_coverage':
                    # First check if we have the value from screener.in ratios
                    if 'interest_coverage_ratio' in ratios_data:
                        interest_coverage = safe_float(ratios_data['interest_coverage_ratio'])
                        if interest_coverage > 0:
                            item_result['value'] = interest_coverage
                            item_result['pass'] = interest_coverage > item['threshold']
                    # If not, calculate from financial statements
                    elif latest_year_pl and 'ebit' in pl_data[latest_year_pl] and 'interest' in pl_data[latest_year_pl]:
                        ebit = safe_float(pl_data[latest_year_pl]['ebit'])
                        interest = safe_float(pl_data[latest_year_pl]['interest'])

                        if interest > 0:
                            interest_coverage = ebit / interest
                            item_result['value'] = interest_coverage
                            item_result['pass'] = interest_coverage > item['threshold']

                elif key == 'return_on_equity':
                    # First check if we have the value from screener.in ratios
                    if 'roe_%' in ratios_data:
                        roe = safe_float(ratios_data['roe_%'])
                        if roe > 0:
                            item_result['value'] = roe
                            item_result['pass'] = roe > item['threshold']
                    elif 'roe' in ratios_data:
                        roe = safe_float(ratios_data['roe'])
                        if roe > 0:
                            item_result['value'] = roe
                            item_result['pass'] = roe > item['threshold']
                    # If not, calculate from financial statements
                    elif latest_year_pl and latest_year_bs and \
                         'pat' in pl_data[latest_year_pl] and 'total_equity' in bs_data[latest_year_bs]:

                        pat = safe_float(pl_data[latest_year_pl]['pat'])
                        equity = safe_float(bs_data[latest_year_bs]['total_equity'])

                        if equity > 0:
                            roe = (pat / equity) * 100
                            item_result['value'] = roe
                            item_result['pass'] = roe > item['threshold']

                elif key == 'business_simplicity':
                    if 'business_segments' in overview:
                        segments_count = overview['business_segments']
                        item_result['value'] = segments_count
                        item_result['pass'] = segments_count <= item['threshold']

                elif key == 'subsidiaries_count':
                    if 'subsidiaries' in overview:
                        subsidiaries_count = overview['subsidiaries']
                        item_result['value'] = subsidiaries_count
                        item_result['pass'] = subsidiaries_count <= item['threshold']

                # Calculate score for this item
                if item_result['pass']:
                    item_result['score'] = item['weight']

                # Add to total score
                results['score'] += item_result['score']

                # Add to results
                results['items'][key] = item_result

            # Calculate percentage score
            if results['max_score'] > 0:
                results['percentage'] = (results['score'] / results['max_score']) * 100

        except Exception as e:
            logger.error(f"Error evaluating company against checklist: {str(e)}")

        return results

    def create_custom_checklist(self, items: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        Create a custom checklist

        Parameters:
        -----------
        items : List[Dict[str, Any]]
            List of checklist items

        Returns:
        --------
        Custom checklist dictionary
        """
        custom_checklist = {}

        for i, item in enumerate(items):
            key = item.get('key', f'item_{i+1}')

            custom_checklist[key] = {
                'description': item.get('description', f'Checklist item {i+1}'),
                'threshold': item.get('threshold', 0.0),
                'comparison': item.get('comparison', 'greater_than'),
                'weight': item.get('weight', 1.0)
            }

        return custom_checklist

    def evaluate_multiple_companies(self,
                                  companies_data: Dict[str, Dict[str, Any]],
                                  checklist: Optional[Dict[str, Dict[str, Any]]] = None) -> Dict[str, Dict[str, Any]]:
        """
        Evaluate multiple companies against a checklist

        Parameters:
        -----------
        companies_data : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to company data
        checklist : Dict[str, Dict[str, Any]], optional
            Checklist to evaluate against (uses default if None)

        Returns:
        --------
        Dictionary mapping company tickers to evaluation results
        """
        results = {}

        for ticker, company_data in companies_data.items():
            results[ticker] = self.evaluate_company(company_data, checklist)

        return results

    def rank_companies(self,
                     evaluation_results: Dict[str, Dict[str, Any]]) -> List[Tuple[str, float]]:
        """
        Rank companies based on checklist evaluation results

        Parameters:
        -----------
        evaluation_results : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to evaluation results

        Returns:
        --------
        List of tuples (ticker, score) sorted by score in descending order
        """
        rankings = []

        for ticker, result in evaluation_results.items():
            rankings.append((ticker, result['percentage']))

        # Sort by percentage score in descending order
        rankings.sort(key=lambda x: x[1], reverse=True)

        return rankings

# Example usage
if __name__ == "__main__":
    # Example company data
    company_data = {
        'profit_loss': {
            '2022': {'revenue': 1000, 'gross_profit': 300, 'pat': 100, 'eps': 10},
            '2021': {'revenue': 900, 'gross_profit': 270, 'pat': 90, 'eps': 9},
            '2020': {'revenue': 800, 'gross_profit': 240, 'pat': 80, 'eps': 8},
            '2019': {'revenue': 700, 'gross_profit': 210, 'pat': 70, 'eps': 7},
            '2018': {'revenue': 600, 'gross_profit': 180, 'pat': 60, 'eps': 6}
        },
        'balance_sheet': {
            '2022': {'total_equity': 500, 'total_debt': 300, 'inventory': 100, 'accounts_receivable': 50},
            '2021': {'total_equity': 450, 'total_debt': 280, 'inventory': 90, 'accounts_receivable': 45},
            '2020': {'total_equity': 400, 'total_debt': 260, 'inventory': 80, 'accounts_receivable': 40}
        },
        'cash_flow': {
            '2022': {'operating_cash_flow': 120}
        },
        'overview': {
            'business_segments': 2,
            'subsidiaries': 3
        }
    }

    checklist = Checklist()
    result = checklist.evaluate_company(company_data)

    print("Checklist Evaluation Results:")
    print(f"Total Score: {result['score']:.1f}/{result['max_score']:.1f} ({result['percentage']:.1f}%)")
    print("\nIndividual Items:")
    for key, item in result['items'].items():
        status = "✓" if item['pass'] else "✗"
        value = f"{item['value']:.2f}" if item['value'] is not None else "N/A"
        print(f"  {status} {item['description']}: {value}")
