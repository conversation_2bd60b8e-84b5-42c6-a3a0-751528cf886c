#!/usr/bin/env python3
"""
Ranking Module

This module provides functions to rank companies based on fundamental analysis.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Callable

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ranking')

class CompanyRanker:
    """
    Class to rank companies based on fundamental analysis
    """
    
    def __init__(self):
        """
        Initialize the company ranker
        """
        # Define default ranking criteria
        self.default_criteria = {
            'financial_strength': {
                'debt_to_equity': {'weight': 1.0, 'higher_is_better': False},
                'interest_coverage': {'weight': 1.0, 'higher_is_better': True},
                'operating_cash_flow': {'weight': 1.0, 'higher_is_better': True}
            },
            'profitability': {
                'gross_profit_margin': {'weight': 1.0, 'higher_is_better': True},
                'ebitda_margin': {'weight': 1.0, 'higher_is_better': True},
                'pat_margin': {'weight': 1.0, 'higher_is_better': True},
                'roe': {'weight': 1.5, 'higher_is_better': True},
                'roa': {'weight': 1.0, 'higher_is_better': True},
                'roce': {'weight': 1.0, 'higher_is_better': True}
            },
            'growth': {
                'revenue_cagr': {'weight': 1.0, 'higher_is_better': True},
                'ebitda_cagr': {'weight': 1.0, 'higher_is_better': True},
                'pat_cagr': {'weight': 1.5, 'higher_is_better': True},
                'eps_cagr': {'weight': 1.0, 'higher_is_better': True}
            },
            'valuation': {
                'pe_ratio': {'weight': 1.0, 'higher_is_better': False},
                'pb_ratio': {'weight': 1.0, 'higher_is_better': False},
                'ps_ratio': {'weight': 0.5, 'higher_is_better': False},
                'margin_of_safety': {'weight': 2.0, 'higher_is_better': True}
            },
            'operational_efficiency': {
                'fixed_assets_turnover': {'weight': 0.5, 'higher_is_better': True},
                'working_capital_turnover': {'weight': 0.5, 'higher_is_better': True},
                'total_assets_turnover': {'weight': 0.5, 'higher_is_better': True},
                'inventory_turnover': {'weight': 0.5, 'higher_is_better': True},
                'receivables_turnover': {'weight': 0.5, 'higher_is_better': True}
            }
        }
    
    def extract_metrics(self, company_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Extract metrics from company data
        
        Parameters:
        -----------
        company_data : Dict[str, Any]
            Dictionary containing company financial data
        
        Returns:
        --------
        Dictionary of metrics
        """
        metrics = {}
        
        try:
            # Extract financial ratios
            if 'ratios' in company_data:
                ratios = company_data['ratios']
                
                # Extract profitability ratios
                if 'profitability' in ratios:
                    for key, value in ratios['profitability'].items():
                        metrics[key] = value
                
                # Extract leverage ratios
                if 'leverage' in ratios:
                    for key, value in ratios['leverage'].items():
                        metrics[key] = value
                
                # Extract valuation ratios
                if 'valuation' in ratios:
                    for key, value in ratios['valuation'].items():
                        metrics[key] = value
                
                # Extract operating ratios
                if 'operating' in ratios:
                    for key, value in ratios['operating'].items():
                        metrics[key] = value
            
            # Extract growth rates
            if 'growth_rates' in company_data:
                for key, value in company_data['growth_rates'].items():
                    metrics[key] = value
            
            # Extract DCF analysis results
            if 'dcf_analysis' in company_data and 'margin_of_safety' in company_data['dcf_analysis']:
                metrics['margin_of_safety'] = company_data['dcf_analysis']['margin_of_safety']
            
            # Extract cash flow data
            if 'cash_flow' in company_data:
                cf_data = company_data['cash_flow']
                latest_year = sorted(cf_data.keys())[-1] if cf_data else None
                
                if latest_year and 'operating_cash_flow' in cf_data[latest_year]:
                    metrics['operating_cash_flow'] = cf_data[latest_year]['operating_cash_flow']
        
        except Exception as e:
            logger.error(f"Error extracting metrics: {str(e)}")
        
        return metrics
    
    def normalize_metrics(self, 
                        metrics_data: Dict[str, Dict[str, float]], 
                        criteria: Dict[str, Dict[str, Dict[str, Union[float, bool]]]]) -> Dict[str, Dict[str, float]]:
        """
        Normalize metrics across companies
        
        Parameters:
        -----------
        metrics_data : Dict[str, Dict[str, float]]
            Dictionary mapping company tickers to metrics
        criteria : Dict[str, Dict[str, Dict[str, Union[float, bool]]]]
            Ranking criteria
        
        Returns:
        --------
        Dictionary mapping company tickers to normalized metrics
        """
        normalized_data = {ticker: {} for ticker in metrics_data}
        
        # Collect all metrics for normalization
        all_metrics = {}
        for category, metrics in criteria.items():
            for metric in metrics:
                all_metrics[metric] = {'min': float('inf'), 'max': float('-inf'), 'values': {}}
        
        # Find min and max values for each metric
        for ticker, metrics in metrics_data.items():
            for metric, value in metrics.items():
                if metric in all_metrics:
                    all_metrics[metric]['values'][ticker] = value
                    all_metrics[metric]['min'] = min(all_metrics[metric]['min'], value)
                    all_metrics[metric]['max'] = max(all_metrics[metric]['max'], value)
        
        # Normalize metrics
        for metric, data in all_metrics.items():
            min_val = data['min']
            max_val = data['max']
            
            # Skip metrics with no range
            if max_val == min_val:
                continue
            
            for ticker, value in data['values'].items():
                # Find the category and whether higher is better
                higher_is_better = False
                for category, metrics in criteria.items():
                    if metric in metrics:
                        higher_is_better = metrics[metric]['higher_is_better']
                        break
                
                # Normalize based on whether higher is better
                if higher_is_better:
                    normalized_value = (value - min_val) / (max_val - min_val)
                else:
                    normalized_value = (max_val - value) / (max_val - min_val)
                
                normalized_data[ticker][metric] = normalized_value
        
        return normalized_data
    
    def calculate_scores(self, 
                       normalized_data: Dict[str, Dict[str, float]], 
                       criteria: Dict[str, Dict[str, Dict[str, Union[float, bool]]]]) -> Dict[str, Dict[str, float]]:
        """
        Calculate scores for each company
        
        Parameters:
        -----------
        normalized_data : Dict[str, Dict[str, float]]
            Dictionary mapping company tickers to normalized metrics
        criteria : Dict[str, Dict[str, Dict[str, Union[float, bool]]]]
            Ranking criteria
        
        Returns:
        --------
        Dictionary mapping company tickers to category scores and total score
        """
        scores = {}
        
        for ticker, metrics in normalized_data.items():
            ticker_scores = {'total': 0.0}
            
            # Calculate category scores
            for category, category_metrics in criteria.items():
                category_score = 0.0
                category_weight_sum = 0.0
                
                for metric, config in category_metrics.items():
                    if metric in metrics:
                        weight = config['weight']
                        category_score += metrics[metric] * weight
                        category_weight_sum += weight
                
                # Normalize category score
                if category_weight_sum > 0:
                    category_score /= category_weight_sum
                    ticker_scores[category] = category_score
                    ticker_scores['total'] += category_score
            
            # Normalize total score
            ticker_scores['total'] /= len(criteria)
            
            scores[ticker] = ticker_scores
        
        return scores
    
    def rank_companies(self, 
                     companies_data: Dict[str, Dict[str, Any]], 
                     criteria: Optional[Dict[str, Dict[str, Dict[str, Union[float, bool]]]]] = None) -> Dict[str, Any]:
        """
        Rank companies based on fundamental analysis
        
        Parameters:
        -----------
        companies_data : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to company data
        criteria : Dict[str, Dict[str, Dict[str, Union[float, bool]]]], optional
            Ranking criteria (uses default if None)
        
        Returns:
        --------
        Dictionary containing ranking results
        """
        if criteria is None:
            criteria = self.default_criteria
        
        results = {
            'metrics': {},
            'normalized': {},
            'scores': {},
            'rankings': {
                'total': [],
                'categories': {}
            }
        }
        
        try:
            # Extract metrics for each company
            for ticker, company_data in companies_data.items():
                results['metrics'][ticker] = self.extract_metrics(company_data)
            
            # Normalize metrics
            results['normalized'] = self.normalize_metrics(results['metrics'], criteria)
            
            # Calculate scores
            results['scores'] = self.calculate_scores(results['normalized'], criteria)
            
            # Rank companies by total score
            total_rankings = [(ticker, scores['total']) for ticker, scores in results['scores'].items()]
            total_rankings.sort(key=lambda x: x[1], reverse=True)
            results['rankings']['total'] = total_rankings
            
            # Rank companies by category
            for category in criteria:
                category_rankings = []
                
                for ticker, scores in results['scores'].items():
                    if category in scores:
                        category_rankings.append((ticker, scores[category]))
                
                category_rankings.sort(key=lambda x: x[1], reverse=True)
                results['rankings']['categories'][category] = category_rankings
        
        except Exception as e:
            logger.error(f"Error ranking companies: {str(e)}")
        
        return results
    
    def filter_companies(self, 
                       companies_data: Dict[str, Dict[str, Any]], 
                       filters: Dict[str, Dict[str, Any]]) -> List[str]:
        """
        Filter companies based on criteria
        
        Parameters:
        -----------
        companies_data : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to company data
        filters : Dict[str, Dict[str, Any]]
            Filtering criteria
        
        Returns:
        --------
        List of tickers that pass the filters
        """
        filtered_tickers = []
        
        try:
            for ticker, company_data in companies_data.items():
                # Extract metrics
                metrics = self.extract_metrics(company_data)
                
                # Check if the company passes all filters
                passes_all = True
                
                for metric, filter_config in filters.items():
                    if metric in metrics:
                        value = metrics[metric]
                        
                        if 'min' in filter_config and value < filter_config['min']:
                            passes_all = False
                            break
                        
                        if 'max' in filter_config and value > filter_config['max']:
                            passes_all = False
                            break
                    else:
                        # If the metric is required but not available, the company fails
                        if filter_config.get('required', False):
                            passes_all = False
                            break
                
                if passes_all:
                    filtered_tickers.append(ticker)
        
        except Exception as e:
            logger.error(f"Error filtering companies: {str(e)}")
        
        return filtered_tickers
    
    def create_custom_criteria(self, criteria_config: Dict[str, Dict[str, Dict[str, Any]]]) -> Dict[str, Dict[str, Dict[str, Union[float, bool]]]]:
        """
        Create custom ranking criteria
        
        Parameters:
        -----------
        criteria_config : Dict[str, Dict[str, Dict[str, Any]]]
            Custom criteria configuration
        
        Returns:
        --------
        Custom ranking criteria
        """
        custom_criteria = {}
        
        for category, metrics in criteria_config.items():
            custom_criteria[category] = {}
            
            for metric, config in metrics.items():
                custom_criteria[category][metric] = {
                    'weight': config.get('weight', 1.0),
                    'higher_is_better': config.get('higher_is_better', True)
                }
        
        return custom_criteria

# Example usage
if __name__ == "__main__":
    # Example company data
    companies_data = {
        'AAPL': {
            'ratios': {
                'profitability': {'roe': 30.0, 'roa': 15.0},
                'leverage': {'debt_to_equity': 0.8},
                'valuation': {'pe_ratio': 25.0}
            },
            'growth_rates': {'revenue_cagr': 12.0, 'eps_cagr': 15.0},
            'dcf_analysis': {'margin_of_safety': 10.0}
        },
        'MSFT': {
            'ratios': {
                'profitability': {'roe': 35.0, 'roa': 18.0},
                'leverage': {'debt_to_equity': 0.6},
                'valuation': {'pe_ratio': 30.0}
            },
            'growth_rates': {'revenue_cagr': 15.0, 'eps_cagr': 18.0},
            'dcf_analysis': {'margin_of_safety': 5.0}
        }
    }
    
    ranker = CompanyRanker()
    results = ranker.rank_companies(companies_data)
    
    print("Company Rankings:")
    for i, (ticker, score) in enumerate(results['rankings']['total'], start=1):
        print(f"{i}. {ticker}: {score:.4f}")
    
    print("\nCategory Rankings:")
    for category, rankings in results['rankings']['categories'].items():
        print(f"\n{category.capitalize()}:")
        for i, (ticker, score) in enumerate(rankings, start=1):
            print(f"{i}. {ticker}: {score:.4f}")
