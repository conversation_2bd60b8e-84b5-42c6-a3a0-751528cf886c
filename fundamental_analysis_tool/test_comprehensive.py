#!/usr/bin/env python3
"""
Test Comprehensive Analysis System

This script tests the comprehensive analysis system with a small sample
before running it on all 4950 companies.
"""

import os
import sys
import json

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from comprehensive_analysis import ComprehensiveAnalyzer

def test_comprehensive_analysis():
    """Test the comprehensive analysis system"""
    print("=" * 60)
    print("TESTING COMPREHENSIVE ANALYSIS SYSTEM")
    print("=" * 60)
    
    # Initialize analyzer
    analyzer = ComprehensiveAnalyzer()
    
    # Test with a small sample
    test_tickers = analyzer.all_tickers[:20]  # First 20 companies
    print(f"Testing with {len(test_tickers)} companies: {', '.join(test_tickers)}")
    
    # Process the test batch
    print("\nProcessing test batch...")
    batch_results = analyzer._process_batch(test_tickers, max_workers=2)
    
    # Analyze results
    successful = sum(1 for r in batch_results.values() if not r.get('error'))
    print(f"\nResults: {successful}/{len(test_tickers)} companies analyzed successfully")
    
    # Show sample results
    print(f"\nSample Results:")
    for ticker, result in list(batch_results.items())[:5]:
        if not result.get('error'):
            print(f"\n{ticker}:")
            print(f"  Sector: {result.get('sector', 'Unknown')}")
            print(f"  Overall Consistency: {result.get('overall_consistency_score', 'N/A'):.1f}")
            print(f"  Growth Consistency: {result.get('growth_consistency_score', 'N/A'):.1f}")
            print(f"  Financial Health: {result.get('financial_health_score', 'N/A'):.1f}")
            print(f"  Peers: {len(result.get('peers', []))} identified")
        else:
            print(f"\n{ticker}: ERROR - {result.get('error')}")
    
    # Test sector analysis
    print(f"\nTesting sector analysis...")
    sector_analysis = analyzer._analyze_sectors(batch_results)
    
    print(f"Sectors identified: {list(sector_analysis.keys())}")
    for sector, data in sector_analysis.items():
        print(f"  {sector}: {data['company_count']} companies")
        if data['top_companies']:
            top_company = data['top_companies'][0]
            print(f"    Top performer: {top_company['ticker']} (Score: {top_company['overall_consistency']:.1f})")
    
    # Test top performers identification
    print(f"\nTesting top performers identification...")
    top_performers = analyzer._identify_top_performers(batch_results)
    
    print(f"Top 5 by Overall Consistency:")
    for i, company in enumerate(top_performers['overall_consistency'][:5], 1):
        print(f"  {i}. {company['ticker']} ({company['sector']}) - {company['overall_consistency']:.1f}")
    
    print(f"\nTop 5 by Growth Consistency:")
    for i, company in enumerate(top_performers['growth_consistency'][:5], 1):
        print(f"  {i}. {company['ticker']} ({company['sector']}) - {company['growth_consistency']:.1f}")
    
    print(f"\nTop 5 by Financial Health:")
    for i, company in enumerate(top_performers['financial_health'][:5], 1):
        print(f"  {i}. {company['ticker']} ({company['sector']}) - {company['financial_health']:.1f}")
    
    # Save test results
    test_results = {
        'batch_results': batch_results,
        'sector_analysis': sector_analysis,
        'top_performers': top_performers
    }
    
    os.makedirs('output', exist_ok=True)
    with open('output/comprehensive_test_results.json', 'w') as f:
        json.dump(test_results, f, indent=2, default=str)
    
    print(f"\n✓ Test completed successfully!")
    print(f"✓ Test results saved to output/comprehensive_test_results.json")
    
    return successful == len(test_tickers)

if __name__ == "__main__":
    success = test_comprehensive_analysis()
    if success:
        print(f"\n🎉 All tests passed! Ready to run comprehensive analysis on all 4950 companies.")
        print(f"\nTo run the full analysis, execute:")
        print(f"python comprehensive_analysis.py")
    else:
        print(f"\n⚠️  Some tests failed. Please review the results before running the full analysis.")
