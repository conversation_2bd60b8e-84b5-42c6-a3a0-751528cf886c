#!/usr/bin/env python3
"""
🔧 MODULAR ANALYSIS SYSTEM - PROPER IMPLEMENTATION

This system allows running analysis on different ticker sets:
- ALL TICKERS: Run on complete dataset (4903 companies)
- CASHFLOW_QUALIFIED: Run on tickers that passed cash flow analysis
- DCF_UNDERVALUED: Run on undervalued companies from DCF
- DCF_OVERVALUED: Run on overvalued companies from DCF
- CUSTOM_LIST: Run on user-specified tickers

Each analysis can be run on any of these ticker sets from dashboard commands.
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.data_loader import ScreenerDataLoader

class ModularAnalysisSystem:
    """
    Modular analysis system that can run any analysis on different ticker sets
    """

    def __init__(self):
        self.data_loader = ScreenerDataLoader('../screener_data_collector/data')
        self.results_dir = Path('output/modular_results')
        self.results_dir.mkdir(parents=True, exist_ok=True)

        # Available ticker sets
        self.ticker_sets = {
            'ALL_TICKERS': 'Complete dataset (all companies)',
            'CASHFLOW_QUALIFIED': 'Companies that passed cash flow analysis',
            'DCF_UNDERVALUED': 'Undervalued companies from DCF analysis',
            'DCF_OVERVALUED': 'Overvalued companies from DCF analysis',
            'DCF_SEVERELY_UNDERVALUED': 'Severely undervalued (>50% margin)',
            'DCF_BUFFETT_APPROVED': 'Warren Buffett approved companies',
            'CUSTOM_LIST': 'User-specified ticker list'
        }

        # Available analysis types
        self.analysis_types = {
            'CASHFLOW': 'Cash Flow Pre-screening Analysis',
            'DCF': 'DCF Valuation Analysis',
            'FUNDAMENTAL': 'Fundamental Analysis (Final Model)',
            'PORTFOLIO': 'Portfolio Optimization'
        }

    def get_ticker_set(self, set_name: str, custom_tickers: Optional[List[str]] = None) -> List[str]:
        """
        Get tickers for specified set

        Parameters:
        -----------
        set_name : str
            Name of ticker set (ALL_TICKERS, CASHFLOW_QUALIFIED, etc.)
        custom_tickers : List[str], optional
            Custom ticker list if set_name is CUSTOM_LIST

        Returns:
        --------
        List of tickers
        """
        if set_name == 'ALL_TICKERS':
            return self.data_loader.get_all_tickers()

        elif set_name == 'CASHFLOW_QUALIFIED':
            return self._get_cashflow_qualified_tickers()

        elif set_name == 'DCF_UNDERVALUED':
            return self._get_dcf_filtered_tickers(lambda margin: margin > 0)

        elif set_name == 'DCF_OVERVALUED':
            return self._get_dcf_filtered_tickers(lambda margin: margin < 0)

        elif set_name == 'DCF_SEVERELY_UNDERVALUED':
            return self._get_dcf_filtered_tickers(lambda margin: margin > 50)

        elif set_name == 'DCF_BUFFETT_APPROVED':
            return self._get_buffett_approved_tickers()

        elif set_name == 'CUSTOM_LIST':
            return custom_tickers or []

        else:
            raise ValueError(f"Unknown ticker set: {set_name}")

    def _get_cashflow_qualified_tickers(self) -> List[str]:
        """Get tickers that passed cash flow analysis"""
        # Find latest cash flow result
        cashflow_files = list(self.results_dir.glob('Result_1_*.json'))
        if not cashflow_files:
            print("❌ No cash flow results found. Run cash flow analysis first.")
            return []

        latest_file = sorted(cashflow_files)[-1]
        with open(latest_file, 'r') as f:
            data = json.load(f)

        qualified_tickers = []
        company_results = data.get('company_results', {})

        for ticker, result in company_results.items():
            if result.get('qualified', False) or result.get('passed_prescreen', False):
                qualified_tickers.append(ticker)

        print(f"📊 Found {len(qualified_tickers)} cash flow qualified tickers")
        return qualified_tickers

    def _get_dcf_filtered_tickers(self, filter_func) -> List[str]:
        """Get tickers based on DCF margin of safety filter"""
        # Find latest DCF result
        dcf_files = list(self.results_dir.glob('Result_2_*.json'))
        if not dcf_files:
            # Try direct DCF directory
            dcf_dir = Path('output/direct_dcf_analysis')
            if dcf_dir.exists():
                dcf_files = list(dcf_dir.glob('direct_dcf_analysis_*.json'))

        if not dcf_files:
            print("❌ No DCF results found. Run DCF analysis first.")
            return []

        latest_file = sorted(dcf_files)[-1]
        with open(latest_file, 'r') as f:
            data = json.load(f)

        filtered_tickers = []
        company_results = data.get('company_results', {})

        for ticker, result in company_results.items():
            margin = result.get('margin_of_safety', 0)
            if filter_func(margin):
                filtered_tickers.append(ticker)

        print(f"📊 Found {len(filtered_tickers)} DCF filtered tickers")
        return filtered_tickers

    def _get_buffett_approved_tickers(self) -> List[str]:
        """Get Warren Buffett approved tickers"""
        # Find latest DCF result
        dcf_files = list(self.results_dir.glob('Result_2_*.json'))
        if not dcf_files:
            dcf_dir = Path('output/direct_dcf_analysis')
            if dcf_dir.exists():
                dcf_files = list(dcf_dir.glob('direct_dcf_analysis_*.json'))

        if not dcf_files:
            print("❌ No DCF results found. Run DCF analysis first.")
            return []

        latest_file = sorted(dcf_files)[-1]
        with open(latest_file, 'r') as f:
            data = json.load(f)

        buffett_tickers = []
        company_results = data.get('company_results', {})

        for ticker, result in company_results.items():
            if result.get('buffett_approved', False):
                buffett_tickers.append(ticker)

        print(f"📊 Found {len(buffett_tickers)} Buffett approved tickers")
        return buffett_tickers

    def run_analysis(self, analysis_type: str, ticker_set: str,
                    custom_tickers: Optional[List[str]] = None,
                    sample_size: Optional[int] = None) -> Dict[str, Any]:
        """
        Run specified analysis on specified ticker set

        Parameters:
        -----------
        analysis_type : str
            Type of analysis (CASHFLOW, DCF, FUNDAMENTAL, PORTFOLIO)
        ticker_set : str
            Ticker set to analyze (ALL_TICKERS, CASHFLOW_QUALIFIED, etc.)
        custom_tickers : List[str], optional
            Custom ticker list if ticker_set is CUSTOM_LIST
        sample_size : int, optional
            Limit analysis to sample size for testing

        Returns:
        --------
        Analysis results
        """
        print(f"🚀 MODULAR ANALYSIS: {analysis_type} on {ticker_set}")
        print("=" * 80)

        # Get tickers for analysis
        tickers = self.get_ticker_set(ticker_set, custom_tickers)

        if not tickers:
            print("❌ No tickers found for specified set")
            return {}

        # Apply sample size if specified
        if sample_size and sample_size < len(tickers):
            tickers = tickers[:sample_size]
            print(f"📊 Using sample of {sample_size} companies from {ticker_set}")

        print(f"📊 Running {analysis_type} on {len(tickers)} companies from {ticker_set}")

        # Run the specified analysis
        if analysis_type == 'CASHFLOW':
            return self._run_cashflow_analysis(tickers, ticker_set)

        elif analysis_type == 'DCF':
            return self._run_dcf_analysis(tickers, ticker_set)

        elif analysis_type == 'FUNDAMENTAL':
            return self._run_fundamental_analysis(tickers, ticker_set)

        elif analysis_type == 'PORTFOLIO':
            return self._run_portfolio_analysis(tickers, ticker_set)

        else:
            raise ValueError(f"Unknown analysis type: {analysis_type}")

    def _run_cashflow_analysis(self, tickers: List[str], ticker_set: str) -> Dict[str, Any]:
        """Run cash flow analysis on specified tickers"""
        import subprocess

        # Create temporary ticker file
        temp_file = self.results_dir / 'temp_tickers.json'
        with open(temp_file, 'w') as f:
            json.dump(tickers, f)

        # Run cash flow analysis
        cmd = ['python', 'run_cashflow_analysis.py', '--ticker-file', str(temp_file)]
        result = subprocess.run(cmd, capture_output=True, text=True)

        # Clean up
        temp_file.unlink()

        if result.returncode == 0:
            print("✅ Cash flow analysis completed successfully")
            return {'status': 'success', 'output': result.stdout}
        else:
            print(f"❌ Cash flow analysis failed: {result.stderr}")
            return {'status': 'error', 'error': result.stderr}

    def _run_dcf_analysis(self, tickers: List[str], ticker_set: str) -> Dict[str, Any]:
        """Run DCF analysis on specified tickers"""
        import subprocess

        # Create ticker string
        ticker_string = ','.join(tickers)

        # Run DCF analysis
        cmd = ['python', 'run_direct_dcf_analysis.py', '--tickers', ticker_string]
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ DCF analysis completed successfully")
            return {'status': 'success', 'output': result.stdout}
        else:
            print(f"❌ DCF analysis failed: {result.stderr}")
            return {'status': 'error', 'error': result.stderr}

    def _run_fundamental_analysis(self, tickers: List[str], ticker_set: str) -> Dict[str, Any]:
        """Run fundamental analysis (final model) on specified tickers"""
        # TODO: Implement when final analysis model is ready
        print("⚠️ Fundamental analysis model not yet implemented")
        return {'status': 'pending', 'message': 'Final analysis model to be implemented'}

    def _run_portfolio_analysis(self, tickers: List[str], ticker_set: str) -> Dict[str, Any]:
        """Run portfolio optimization on specified tickers"""
        # TODO: Implement portfolio optimization integration
        print("⚠️ Portfolio analysis not yet implemented")
        return {'status': 'pending', 'message': 'Portfolio optimization to be implemented'}

    def list_available_sets(self) -> Dict[str, str]:
        """List all available ticker sets"""
        return self.ticker_sets

    def list_available_analyses(self) -> Dict[str, str]:
        """List all available analysis types"""
        return self.analysis_types

    def get_set_info(self, set_name: str) -> Dict[str, Any]:
        """Get information about a ticker set"""
        try:
            tickers = self.get_ticker_set(set_name)
            return {
                'set_name': set_name,
                'description': self.ticker_sets.get(set_name, 'Unknown'),
                'ticker_count': len(tickers),
                'sample_tickers': tickers[:5] if tickers else []
            }
        except Exception as e:
            return {
                'set_name': set_name,
                'description': self.ticker_sets.get(set_name, 'Unknown'),
                'ticker_count': 0,
                'error': str(e)
            }

def main():
    """Main function for command line usage"""
    import argparse

    parser = argparse.ArgumentParser(description='Modular Analysis System')
    parser.add_argument('--analysis', choices=['CASHFLOW', 'DCF', 'FUNDAMENTAL', 'PORTFOLIO'],
                       help='Type of analysis to run')
    parser.add_argument('--ticker-set', choices=['ALL_TICKERS', 'CASHFLOW_QUALIFIED',
                       'DCF_UNDERVALUED', 'DCF_OVERVALUED', 'DCF_SEVERELY_UNDERVALUED',
                       'DCF_BUFFETT_APPROVED', 'CUSTOM_LIST'],
                       help='Ticker set to analyze')
    parser.add_argument('--custom-tickers', help='Comma-separated list of tickers for CUSTOM_LIST')
    parser.add_argument('--sample', type=int, help='Sample size for testing')
    parser.add_argument('--list-sets', action='store_true', help='List available ticker sets')
    parser.add_argument('--list-analyses', action='store_true', help='List available analyses')

    args = parser.parse_args()

    system = ModularAnalysisSystem()

    if args.list_sets:
        print("📊 AVAILABLE TICKER SETS:")
        for set_name, description in system.list_available_sets().items():
            info = system.get_set_info(set_name)
            print(f"   {set_name:25} | {description:40} | {info['ticker_count']:,} tickers")
        return

    if args.list_analyses:
        print("🔧 AVAILABLE ANALYSES:")
        for analysis_type, description in system.list_available_analyses().items():
            print(f"   {analysis_type:15} | {description}")
        return

    # Check if analysis and ticker-set are provided
    if not args.analysis or not args.ticker_set:
        print("❌ Both --analysis and --ticker-set are required for running analysis")
        print("Use --list-sets or --list-analyses to see available options")
        return

    # Parse custom tickers if provided
    custom_tickers = None
    if args.custom_tickers:
        custom_tickers = [t.strip().upper() for t in args.custom_tickers.split(',')]

    # Run analysis
    result = system.run_analysis(
        analysis_type=args.analysis,
        ticker_set=args.ticker_set,
        custom_tickers=custom_tickers,
        sample_size=args.sample
    )

    print(f"\n🎯 ANALYSIS RESULT: {result.get('status', 'unknown')}")

if __name__ == "__main__":
    main()
