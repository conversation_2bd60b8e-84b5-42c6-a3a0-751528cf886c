#!/usr/bin/env python3
"""
💰 CASH FLOW ANALYSIS - Using Existing Working Implementation

This script runs cash flow pre-screening analysis using the existing CashFlowPreScreener
that was already working and filtering ~1500 companies from 4920.
Results are saved as Result_1 for modular analysis system.
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import existing working components
from utils.data_loader import ScreenerDataLoader
from models.cashflow_prescreener import CashFlowPreScreener

class CashFlowAnalysisRunner:
    """
    Cash flow analysis runner using existing working CashFlowPreScreener
    """

    def __init__(self):
        self.data_loader = ScreenerDataLoader('../screener_data_collector/data')
        self.prescreener = CashFlowPreScreener()
        self.output_dir = Path('output/modular_results')
        self.output_dir.mkdir(parents=True, exist_ok=True)

        print("💰 CASH FLOW ANALYSIS - USING EXISTING WORKING IMPLEMENTATION")
        print("=" * 80)
        print(f"📁 Data Directory: {self.data_loader.data_dir}")
        print(f"📁 Output Directory: {self.output_dir}")
        print("=" * 80)

    def run_analysis(self, tickers=None):
        """Run cash flow analysis using existing working prescreener"""
        if tickers is None:
            tickers = self.data_loader.get_all_tickers()

        print(f"🔍 Running cash flow pre-screening on {len(tickers)} companies...")
        print("Using existing working CashFlowPreScreener implementation")

        # Process companies one by one using the existing prescreener
        qualified_count = 0
        company_results = {}

        for i, ticker in enumerate(tickers, 1):
            try:
                print(f"📊 Analyzing {ticker} ({i}/{len(tickers)})...")

                # Load company data
                company_data = self.data_loader.load_company_data(ticker)

                if company_data:
                    # Use existing prescreener
                    result = self.prescreener.prescreen_company(company_data)

                    passed = result.get('passed_prescreen', False)
                    score = result.get('cash_flow_score', 0)

                    company_results[ticker] = {
                        'qualified': passed,
                        'score': score,
                        'passed_prescreen': passed,
                        'cash_flow_score': score,
                        'reasons_passed': result.get('reasons_passed', []),
                        'reasons_failed': result.get('reasons_failed', []),
                        'analysis': result.get('analysis', {})
                    }

                    if passed:
                        qualified_count += 1
                        print(f"   ✅ Qualified (Score: {score:.1f})")
                    else:
                        print(f"   ❌ Failed (Score: {score:.1f})")
                else:
                    company_results[ticker] = {
                        'qualified': False,
                        'score': 0,
                        'passed_prescreen': False,
                        'cash_flow_score': 0,
                        'reasons_passed': [],
                        'reasons_failed': ['No company data available'],
                        'analysis': {}
                    }
                    print(f"   ❌ No data available")

                # Progress update
                if i % 100 == 0:
                    print(f"📈 Progress: {i}/{len(tickers)} ({qualified_count} qualified)")

            except Exception as e:
                print(f"❌ Error analyzing {ticker}: {e}")
                company_results[ticker] = {
                    'qualified': False,
                    'score': 0,
                    'passed_prescreen': False,
                    'cash_flow_score': 0,
                    'reasons_passed': [],
                    'reasons_failed': [f'Analysis error: {str(e)}'],
                    'analysis': {}
                }

        # Create modular results format
        results = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'analysis_type': 'cash_flow_prescreen',
                'result_id': 'Result_1',
                'description': 'Cash Flow Pre-screening Analysis (Existing Implementation)',
                'total_companies': len(tickers)
            },
            'company_results': company_results,
            'summary_statistics': {
                'total_companies': len(tickers),
                'processed_companies': len(company_results),
                'qualified_companies': qualified_count,
                'failed_companies': len(company_results) - qualified_count,
                'qualification_rate': (qualified_count / len(tickers)) * 100 if len(tickers) > 0 else 0
            }
        }

        return results

    def save_results(self, results):
        """Save results as Result_1"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'Result_1_CashFlow_{timestamp}.json'
        filepath = self.output_dir / filename

        # Convert numpy types for JSON serialization
        def convert_numpy_types(obj):
            import numpy as np
            if isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            elif isinstance(obj, (np.integer, np.int64, np.int32)):
                return int(obj)
            elif isinstance(obj, (np.floating, np.float64, np.float32)):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj

        try:
            serializable_results = convert_numpy_types(results)

            with open(filepath, 'w') as f:
                json.dump(serializable_results, f, indent=2)

            print(f"\n💾 Results saved as Result_1: {filepath}")
            return filepath

        except Exception as e:
            print(f"❌ Error saving results: {e}")
            return None

    def print_summary(self, results):
        """Print analysis summary"""
        stats = results['summary_statistics']

        print(f"\n" + "=" * 80)
        print("💰 CASH FLOW ANALYSIS SUMMARY (Result_1)")
        print("=" * 80)

        print(f"📊 ANALYSIS STATISTICS:")
        print(f"   Total Companies: {stats['total_companies']:,}")
        print(f"   Qualified Companies: {stats['qualified_companies']:,}")
        print(f"   Failed Companies: {stats['failed_companies']:,}")
        print(f"   Qualification Rate: {stats['qualification_rate']:.1f}%")

        # Show top qualified companies
        company_results = results['company_results']
        qualified = [(ticker, result['score']) for ticker, result in company_results.items()
                    if result['qualified']]

        if qualified:
            qualified.sort(key=lambda x: x[1], reverse=True)
            print(f"\n🏆 TOP 10 QUALIFIED COMPANIES:")
            for i, (ticker, score) in enumerate(qualified[:10], 1):
                print(f"   {i:2d}. {ticker:12} | Score: {score:5.1f}")

        print(f"\n✅ Result_1 (Cash Flow Analysis) Complete!")
        print("=" * 80)

def main():
    """Main function"""
    import sys
    import argparse

    parser = argparse.ArgumentParser(description='Cash Flow Analysis')
    parser.add_argument('--sample', type=int, help='Sample size for testing')
    parser.add_argument('--ticker-file', help='JSON file containing list of tickers')
    parser.add_argument('--tickers', help='Comma-separated list of tickers')

    args = parser.parse_args()

    analyzer = CashFlowAnalysisRunner()

    # Determine tickers to analyze
    tickers = None

    if args.ticker_file:
        # Load tickers from file
        with open(args.ticker_file, 'r') as f:
            tickers = json.load(f)
        print(f"🔍 Running on {len(tickers)} tickers from file: {args.ticker_file}")

    elif args.tickers:
        # Parse tickers from command line
        tickers = [t.strip().upper() for t in args.tickers.split(',')]
        print(f"🔍 Running on {len(tickers)} specified tickers")

    elif args.sample:
        # Sample mode
        all_tickers = analyzer.data_loader.get_all_tickers()
        tickers = all_tickers[:args.sample]
        print(f"🔍 Running in sample mode: {args.sample} companies")

    else:
        # Full analysis
        tickers = analyzer.data_loader.get_all_tickers()
        print(f"🔍 Running full analysis on all {len(tickers)} companies")

    # Run analysis
    results = analyzer.run_analysis(tickers)

    # Save results
    filepath = analyzer.save_results(results)

    # Print summary
    analyzer.print_summary(results)

    print(f"\n🎯 CASH FLOW ANALYSIS COMPLETE!")
    print(f"📁 Results saved as Result_1: {filepath}")
    print(f"📊 Use this for modular analysis combinations")

if __name__ == "__main__":
    main()
