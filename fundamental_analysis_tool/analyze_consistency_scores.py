#!/usr/bin/env python3
"""
Analyze consistency scores in detail to understand the scoring system
"""

import os
import sys
import json

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.data_loader import ScreenerDataLoader
from models.consistency_analyzer import ConsistencyAnalyzer

def analyze_consistency_scores():
    """
    Analyze consistency scores in detail
    """
    data_loader = ScreenerDataLoader('../screener_data_collector/data')
    consistency_analyzer = ConsistencyAnalyzer()
    
    # Test with a few companies
    test_tickers = ['TCS', 'HDFCBANK', 'BAJFINANCE', 'RELIANCE']
    
    for ticker in test_tickers:
        print(f"\n{'='*60}")
        print(f"DETAILED ANALYSIS FOR {ticker}")
        print(f"{'='*60}")
        
        # Load company data
        company_data = data_loader.load_company_data(ticker)
        
        # Extract time series data
        time_series = consistency_analyzer._extract_time_series(company_data)
        
        print(f"Available time series metrics:")
        for metric, series in time_series.items():
            print(f"  {metric}: {len(series)} data points")
            print(f"    Values: {list(series.values)[:5]}...")  # Show first 5 values
        
        # Analyze consistency
        consistency_results = consistency_analyzer.analyze_company_consistency(company_data)
        
        print(f"\nConsistency Results:")
        print(f"  Overall Score: {consistency_results.get('overall_consistency_score', 'N/A')}")
        
        # Growth consistency details
        growth_consistency = consistency_results.get('growth_consistency', {})
        print(f"\nGrowth Consistency:")
        print(f"  Overall Score: {growth_consistency.get('overall_score', 'N/A')}")
        for metric, details in growth_consistency.items():
            if isinstance(details, dict) and 'consistency_score' in details:
                print(f"  {metric}:")
                print(f"    Score: {details['consistency_score']:.2f}")
                print(f"    Mean Growth: {details['mean_growth']:.2f}%")
                print(f"    CV Growth: {details['cv_growth']:.2f}")
                print(f"    R-squared: {details['r_squared']:.3f}")
                print(f"    Positive Growth %: {details['positive_growth_pct']:.1f}%")
        
        # Quarterly consistency details
        quarterly_consistency = consistency_results.get('quarterly_consistency', {})
        print(f"\nQuarterly Consistency:")
        print(f"  Overall Score: {quarterly_consistency.get('overall_score', 'N/A')}")
        for metric, details in quarterly_consistency.items():
            if isinstance(details, dict) and 'consistency_score' in details:
                print(f"  {metric}:")
                print(f"    Score: {details['consistency_score']:.2f}")
                print(f"    Mean QoQ Growth: {details['mean_qoq_growth']:.2f}%")
                print(f"    CV QoQ Growth: {details['cv_qoq_growth']:.2f}")
                print(f"    R-squared: {details['r_squared']:.3f}")
                print(f"    Positive QoQ %: {details['positive_qoq_pct']:.1f}%")

if __name__ == "__main__":
    analyze_consistency_scores()
