#!/usr/bin/env python3
"""
📊 REAL-TIME ANALYSIS MONITORING

Monitor the progress of the 4950 company Warren Buffett DCF analysis in real-time.
Shows live updates of processing status, performance metrics, and estimated completion time.

Usage:
    python monitor_analysis_progress.py           # Real-time monitoring
    python monitor_analysis_progress.py --once    # Single status check
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime, timedelta
from pathlib import Path
import psutil

def clear_screen():
    """Clear the terminal screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def format_time_delta(seconds):
    """Format time delta in human readable format"""
    if seconds < 60:
        return f"{seconds:.0f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"

def get_system_stats():
    """Get current system resource usage"""
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    return {
        'memory_used_gb': (memory.total - memory.available) / (1024**3),
        'memory_total_gb': memory.total / (1024**3),
        'memory_percent': memory.percent,
        'cpu_percent': cpu_percent
    }

def load_progress():
    """Load current progress from tracking file"""
    progress_file = Path('output/intermediate_results/progress_tracking.json')
    
    if not progress_file.exists():
        return None
    
    try:
        with open(progress_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error loading progress: {e}")
        return None

def display_progress_dashboard(progress, system_stats):
    """Display comprehensive progress dashboard"""
    
    clear_screen()
    
    print("🚀 WARREN BUFFETT DCF ANALYSIS - REAL-TIME MONITORING")
    print("=" * 80)
    
    if not progress:
        print("❌ No analysis currently running or progress file not found")
        print("💡 Start analysis with: python run_full_4950_analysis.py")
        return
    
    # Analysis Overview
    print("📊 ANALYSIS OVERVIEW")
    print("-" * 40)
    print(f"🎯 Current Phase: {progress.get('phase', 'Unknown').replace('_', ' ').title()}")
    print(f"📅 Started: {progress.get('start_time', 'Unknown')}")
    print(f"⏰ Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Progress Statistics
    print(f"\n📈 PROGRESS STATISTICS")
    print("-" * 40)
    total = progress.get('total_companies', 0)
    processed = progress.get('processed_companies', 0)
    qualified = progress.get('qualified_companies', 0)
    dcf_analyzed = progress.get('dcf_analyzed', 0)
    buffett_approved = progress.get('buffett_approved', 0)
    
    print(f"📊 Total Companies: {total:,}")
    print(f"🔍 Processed: {processed:,} ({(processed/total*100) if total > 0 else 0:.1f}%)")
    print(f"💰 Cash Flow Qualified: {qualified:,}")
    print(f"🎯 DCF Analyzed: {dcf_analyzed:,}")
    print(f"⭐ Buffett Approved: {buffett_approved:,}")
    
    # Progress Bars
    if total > 0:
        print(f"\n📊 PROGRESS VISUALIZATION")
        print("-" * 40)
        
        # Overall progress bar
        overall_progress = processed / total
        bar_length = 50
        filled_length = int(bar_length * overall_progress)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        print(f"Overall: [{bar}] {overall_progress*100:.1f}%")
        
        # Phase-specific progress
        if progress.get('phase') == 'cash_flow_prescreening':
            phase_progress = processed / total
            filled_length = int(bar_length * phase_progress)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            print(f"Pre-screen: [{bar}] {phase_progress*100:.1f}%")
        
        elif progress.get('phase') == 'warren_buffett_dcf' and qualified > 0:
            dcf_progress = dcf_analyzed / qualified
            filled_length = int(bar_length * dcf_progress)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            print(f"DCF Analysis: [{bar}] {dcf_progress*100:.1f}%")
    
    # Time Estimates
    print(f"\n⏱️  TIME ESTIMATES")
    print("-" * 40)
    
    start_time_str = progress.get('start_time')
    if start_time_str:
        start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
        elapsed = datetime.now() - start_time
        print(f"⏰ Elapsed Time: {format_time_delta(elapsed.total_seconds())}")
        
        # Estimate completion time
        if processed > 0 and total > 0:
            rate = processed / elapsed.total_seconds()
            remaining = total - processed
            eta_seconds = remaining / rate if rate > 0 else 0
            eta = datetime.now() + timedelta(seconds=eta_seconds)
            
            print(f"🎯 Estimated Completion: {eta.strftime('%H:%M:%S')}")
            print(f"⏳ Time Remaining: {format_time_delta(eta_seconds)}")
            print(f"📈 Processing Rate: {rate*60:.1f} companies/minute")
    
    # System Resources
    print(f"\n🖥️  SYSTEM RESOURCES")
    print("-" * 40)
    print(f"💾 Memory: {system_stats['memory_used_gb']:.1f}GB / {system_stats['memory_total_gb']:.1f}GB ({system_stats['memory_percent']:.1f}%)")
    print(f"🖥️  CPU Usage: {system_stats['cpu_percent']:.1f}%")
    
    # Memory usage bar
    memory_progress = system_stats['memory_percent'] / 100
    filled_length = int(50 * memory_progress)
    bar = '█' * filled_length + '░' * (50 - filled_length)
    color = '🔴' if memory_progress > 0.85 else '🟡' if memory_progress > 0.70 else '🟢'
    print(f"Memory: [{bar}] {color}")
    
    # Performance Metrics
    if qualified > 0 and dcf_analyzed > 0:
        print(f"\n📊 PERFORMANCE METRICS")
        print("-" * 40)
        prescreen_rate = (qualified / total * 100) if total > 0 else 0
        approval_rate = (buffett_approved / dcf_analyzed * 100) if dcf_analyzed > 0 else 0
        
        print(f"🔍 Pre-screen Pass Rate: {prescreen_rate:.1f}%")
        print(f"⭐ Buffett Approval Rate: {approval_rate:.1f}%")
        
        if buffett_approved > 0:
            print(f"💎 Investment Candidates: {buffett_approved} companies")
    
    # Current Status
    print(f"\n🎯 CURRENT STATUS")
    print("-" * 40)
    
    phase = progress.get('phase', 'unknown')
    if phase == 'initialization':
        print("🚀 Initializing analysis system...")
    elif phase == 'data_loading':
        print("📊 Loading and validating company data...")
    elif phase == 'cash_flow_prescreening':
        print("🔍 Running 10-year cash flow analysis...")
    elif phase == 'warren_buffett_dcf':
        print("🎯 Performing Warren Buffett style DCF analysis...")
    elif phase == 'portfolio_optimization':
        print("💼 Optimizing investment portfolio...")
    elif phase == 'generating_reports':
        print("📋 Generating comprehensive reports...")
    else:
        print(f"🔄 Current phase: {phase}")
    
    # Warnings and Alerts
    warnings = []
    if system_stats['memory_percent'] > 85:
        warnings.append("⚠️  High memory usage detected")
    if system_stats['cpu_percent'] > 90:
        warnings.append("⚠️  High CPU usage detected")
    
    if warnings:
        print(f"\n⚠️  SYSTEM ALERTS")
        print("-" * 40)
        for warning in warnings:
            print(warning)
    
    print("\n" + "=" * 80)
    print("📊 Press Ctrl+C to stop monitoring | 🔄 Updates every 5 seconds")
    print("💡 View detailed results: python show_analysis_summary.py")

def monitor_continuously():
    """Monitor analysis progress continuously"""
    print("🚀 Starting real-time monitoring...")
    print("📊 Press Ctrl+C to stop")
    
    try:
        while True:
            progress = load_progress()
            system_stats = get_system_stats()
            display_progress_dashboard(progress, system_stats)
            time.sleep(5)  # Update every 5 seconds
    
    except KeyboardInterrupt:
        print("\n\n👋 Monitoring stopped by user")
    except Exception as e:
        print(f"\n❌ Monitoring error: {e}")

def show_single_status():
    """Show single status update"""
    progress = load_progress()
    system_stats = get_system_stats()
    display_progress_dashboard(progress, system_stats)

def check_analysis_files():
    """Check for analysis output files"""
    print("\n📁 ANALYSIS FILES STATUS")
    print("-" * 40)
    
    output_dir = Path('output/full_4950_analysis')
    intermediate_dir = Path('output/intermediate_results')
    
    files_to_check = [
        (output_dir / 'prescreen_results.json', 'Cash Flow Pre-screening Results'),
        (output_dir / 'dcf_results.json', 'DCF Analysis Results'),
        (output_dir / 'portfolio_recommendations.json', 'Portfolio Recommendations'),
        (output_dir / 'analysis_summary.json', 'Analysis Summary'),
        (intermediate_dir / 'progress_tracking.json', 'Progress Tracking')
    ]
    
    for file_path, description in files_to_check:
        if file_path.exists():
            size = file_path.stat().st_size / 1024  # Size in KB
            modified = datetime.fromtimestamp(file_path.stat().st_mtime)
            print(f"✅ {description}: {size:.1f}KB (Modified: {modified.strftime('%H:%M:%S')})")
        else:
            print(f"❌ {description}: Not found")

def main():
    """Main function with command line argument parsing"""
    parser = argparse.ArgumentParser(description='Monitor 4950 company analysis progress')
    parser.add_argument('--once', action='store_true', help='Show status once and exit')
    parser.add_argument('--files', action='store_true', help='Check analysis files status')
    
    args = parser.parse_args()
    
    if args.files:
        check_analysis_files()
    elif args.once:
        show_single_status()
    else:
        monitor_continuously()

if __name__ == "__main__":
    main()
