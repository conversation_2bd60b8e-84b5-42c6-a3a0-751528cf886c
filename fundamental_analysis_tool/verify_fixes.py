#!/usr/bin/env python3
"""
🎯 VERIFICATION SCRIPT - All Issues Fixed

This script verifies that all your requirements have been implemented correctly:
1. Cash flow analysis is working (using existing implementation)
2. DCF analysis is working with proper dropdown filters
3. Modular results system is functional
4. Dashboard enhancements are in place
"""

import os
import json
from pathlib import Path
from datetime import datetime

def check_cash_flow_analysis():
    """Check if cash flow analysis is working"""
    print("🔍 CHECKING CASH FLOW ANALYSIS...")
    print("-" * 50)
    
    # Check if Result_1 exists
    modular_dir = Path('output/modular_results')
    result_1_files = list(modular_dir.glob('Result_1_*.json'))
    
    if result_1_files:
        latest_file = sorted(result_1_files)[-1]
        print(f"✅ Found Result_1: {latest_file.name}")
        
        # Load and check content
        with open(latest_file, 'r') as f:
            data = json.load(f)
        
        metadata = data.get('analysis_metadata', {})
        stats = data.get('summary_statistics', {})
        
        print(f"   📊 Analysis Type: {metadata.get('analysis_type', 'Unknown')}")
        print(f"   📅 Created: {metadata.get('timestamp', 'Unknown')[:19]}")
        print(f"   🏢 Total Companies: {stats.get('total_companies', 0):,}")
        print(f"   ✅ Qualified: {stats.get('qualified_companies', 0):,}")
        print(f"   📈 Success Rate: {stats.get('qualification_rate', 0):.1f}%")
        
        # Show sample qualified companies
        company_results = data.get('company_results', {})
        qualified = [(ticker, result['score']) for ticker, result in company_results.items() 
                    if result.get('qualified', False)]
        
        if qualified:
            qualified.sort(key=lambda x: x[1], reverse=True)
            print(f"\n   🏆 TOP QUALIFIED COMPANIES:")
            for i, (ticker, score) in enumerate(qualified[:5], 1):
                print(f"      {i}. {ticker:12} | Score: {score:5.1f}")
        
        return True
    else:
        print("❌ No Result_1 found")
        return False

def check_dcf_analysis():
    """Check if DCF analysis is working"""
    print("\n🎯 CHECKING DCF ANALYSIS...")
    print("-" * 50)
    
    # Check direct DCF results
    dcf_dir = Path('output/direct_dcf_analysis')
    if dcf_dir.exists():
        dcf_files = list(dcf_dir.glob('direct_dcf_analysis_*.json'))
        if dcf_files:
            latest_file = sorted(dcf_files)[-1]
            print(f"✅ Found DCF Results: {latest_file.name}")
            
            # Load and check content
            with open(latest_file, 'r') as f:
                data = json.load(f)
            
            stats = data.get('summary_statistics', {})
            print(f"   📊 Total Analyzed: {stats.get('total_analyzed', 0):,}")
            print(f"   ✅ Successful: {stats.get('successful_analyses', 0):,}")
            print(f"   📈 Success Rate: {stats.get('success_rate', 0):.1f}%")
            
            # Check valuation distribution
            valuation = data.get('valuation_distribution', {})
            if valuation:
                print(f"\n   💰 VALUATION DISTRIBUTION:")
                for category, count in valuation.items():
                    category_name = category.replace('_', ' ').title()
                    print(f"      {category_name}: {count:,}")
            
            return True
        else:
            print("❌ No DCF results found")
            return False
    else:
        print("❌ DCF output directory not found")
        return False

def check_modular_results():
    """Check modular results system"""
    print("\n🔧 CHECKING MODULAR RESULTS SYSTEM...")
    print("-" * 50)
    
    modular_dir = Path('output/modular_results')
    if modular_dir.exists():
        result_files = list(modular_dir.glob('Result_*.json'))
        
        if result_files:
            print(f"✅ Found {len(result_files)} modular result files:")
            
            for file_path in sorted(result_files):
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                    
                    metadata = data.get('analysis_metadata', {})
                    result_id = metadata.get('result_id', file_path.stem)
                    description = metadata.get('description', 'No description')
                    total_companies = metadata.get('total_companies', 0)
                    
                    print(f"   📁 {result_id}: {description}")
                    print(f"      Companies: {total_companies:,} | File: {file_path.name}")
                    
                except Exception as e:
                    print(f"   ❌ Error reading {file_path.name}: {e}")
            
            return True
        else:
            print("❌ No modular result files found")
            return False
    else:
        print("❌ Modular results directory not found")
        return False

def check_dashboard_enhancements():
    """Check dashboard enhancements"""
    print("\n📊 CHECKING DASHBOARD ENHANCEMENTS...")
    print("-" * 50)
    
    dashboard_file = Path('dashboard.py')
    if dashboard_file.exists():
        with open(dashboard_file, 'r') as f:
            content = f.read()
        
        # Check for enhanced dropdown filters
        if 'Severely Undervalued (>50%)' in content:
            print("✅ Enhanced dropdown filters implemented")
        else:
            print("❌ Enhanced dropdown filters not found")
        
        # Check for modular results integration
        if 'load_modular_results' in content:
            print("✅ Modular results integration implemented")
        else:
            print("❌ Modular results integration not found")
        
        # Check for enhanced table columns
        if 'Investment Category' in content:
            print("✅ Enhanced table columns implemented")
        else:
            print("❌ Enhanced table columns not found")
        
        # Check for analysis buttons
        if 'Run Cash Flow Analysis' in content:
            print("✅ Analysis buttons implemented")
        else:
            print("❌ Analysis buttons not found")
        
        return True
    else:
        print("❌ Dashboard file not found")
        return False

def main():
    """Main verification function"""
    print("🎯 VERIFICATION OF ALL FIXES AND ENHANCEMENTS")
    print("=" * 80)
    print(f"🕒 Verification Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Run all checks
    checks = [
        ("Cash Flow Analysis", check_cash_flow_analysis),
        ("DCF Analysis", check_dcf_analysis),
        ("Modular Results System", check_modular_results),
        ("Dashboard Enhancements", check_dashboard_enhancements)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ Error in {check_name}: {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 80)
    
    passed = 0
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status:8} | {check_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{len(results)} checks passed")
    
    if passed == len(results):
        print("🎉 ALL SYSTEMS WORKING PERFECTLY!")
        print("\n💡 NEXT STEPS:")
        print("   1. Run full cash flow analysis: python run_cashflow_analysis.py")
        print("   2. Run DCF analysis: python run_direct_dcf_analysis.py")
        print("   3. Create combinations: python modular_analysis_manager.py")
        print("   4. View in dashboard: streamlit run dashboard.py")
    else:
        print("⚠️  Some issues found - check details above")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
