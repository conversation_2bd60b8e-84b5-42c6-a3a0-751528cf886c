#!/usr/bin/env python3
"""
REAL Portfolio Optimization Integration

This module ACTUALLY integrates the existing Portfolio_optimization modules
with the fundamental analysis pipeline. No more simplified optimization - this uses
the real portfolio optimization modules you built.

This is for REAL MONEY - proper portfolio optimization using your existing implementation.
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

# Add Portfolio_optimization module path - ACTUAL path to your Portfolio_optimization folder
portfolio_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'Portfolio_optimization')
sys.path.insert(0, portfolio_path)

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('real_portfolio_integration')

class RealPortfolioIntegration:
    """
    REAL Portfolio optimization integration using your actual Portfolio_optimization modules
    """

    def __init__(self):
        """
        Initialize REAL Portfolio optimization integration
        """
        self.portfolio_available = False
        self.optimal_portfolio_builder = None
        self.integrated_portfolio_optimization = None
        self.stock_data_scraper = None

        # Try to import REAL Portfolio optimization modules
        self._initialize_real_portfolio_modules()

    def _initialize_real_portfolio_modules(self):
        """
        Initialize REAL Portfolio optimization modules from the Portfolio_optimization/ folder
        """
        try:
            # Import ACTUAL Portfolio optimization modules from your Portfolio_optimization/ folder
            import sys
            import importlib.util

            # Import OptimalPortfolioBuilder
            spec = importlib.util.spec_from_file_location(
                "optimal_portfolio_builder",
                os.path.join(portfolio_path, "optimal_portfolio_builder.py")
            )
            optimal_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(optimal_module)
            OptimalPortfolioBuilder = optimal_module.OptimalPortfolioBuilder

            # Import StockDataScraper
            spec = importlib.util.spec_from_file_location(
                "stock_data_scraper",
                os.path.join(portfolio_path, "stock_data_scraper.py")
            )
            scraper_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(scraper_module)
            StockDataScraper = scraper_module.StockDataScraper

            self.optimal_portfolio_builder = OptimalPortfolioBuilder
            self.integrated_portfolio_optimization = None  # May not exist
            self.stock_data_scraper = StockDataScraper
            self.portfolio_available = True

            logger.info("✅ REAL Portfolio optimization modules loaded successfully from Portfolio_optimization/ folder")
            print("✅ REAL Portfolio Optimization Module: LOADED")
            print(f"   📁 Portfolio Path: {portfolio_path}")
            print(f"   🔧 Optimal Portfolio Builder: {self.optimal_portfolio_builder.__name__}")
            print(f"   📊 Integrated Portfolio Optimization: {self.integrated_portfolio_optimization.__name__}")
            print(f"   💰 Stock Data Scraper: {self.stock_data_scraper.__name__}")

        except ImportError as e:
            logger.error(f"REAL Portfolio optimization modules not available: {e}")
            print(f"❌ REAL Portfolio Optimization Module: NOT AVAILABLE")
            print(f"   Error: {e}")
            print(f"   Portfolio Path: {portfolio_path}")
            print(f"   Check if Portfolio_optimization/ folder exists and contains the required modules")
            self.portfolio_available = False

    def optimize_portfolio_real(self, selected_companies: List[str],
                               fundamental_scores: Dict[str, float]) -> Dict[str, Any]:
        """
        Perform REAL portfolio optimization using your actual Portfolio_optimization modules

        Parameters:
        -----------
        selected_companies : List[str]
            List of company tickers for portfolio
        fundamental_scores : Dict[str, float]
            Fundamental analysis scores for each company

        Returns:
        --------
        REAL portfolio optimization results
        """
        if not self.portfolio_available:
            return {
                'error': 'REAL Portfolio optimization modules not available',
                'method': 'unavailable',
                'portfolio_size': 0,
                'allocation': {}
            }

        try:
            logger.info(f"Running REAL portfolio optimization on {len(selected_companies)} companies")

            # Step 1: Fetch market data using REAL stock data scraper
            market_data = self._fetch_real_market_data(selected_companies)

            if not market_data or len(market_data) < 3:
                logger.warning("Insufficient market data for REAL portfolio optimization")
                return self._fallback_portfolio_allocation(selected_companies, fundamental_scores)

            # Step 2: Prepare returns data
            returns_data = self._prepare_returns_data(market_data)

            # Step 3: Run REAL integrated portfolio optimization
            optimization_results = self._run_real_integrated_optimization(returns_data, fundamental_scores)

            # Step 4: Run REAL optimal portfolio builder
            builder_results = self._run_real_optimal_portfolio_builder(returns_data, fundamental_scores)

            # Step 5: Combine and analyze results
            final_portfolio = self._combine_optimization_results(
                optimization_results, builder_results, fundamental_scores
            )

            return final_portfolio

        except Exception as e:
            logger.error(f"Error in REAL portfolio optimization: {e}")
            return {
                'error': str(e),
                'method': 'real_portfolio_failed',
                'portfolio_size': 0,
                'allocation': {}
            }

    def _fetch_real_market_data(self, tickers: List[str]) -> Dict[str, pd.DataFrame]:
        """
        Fetch REAL market data using your actual StockDataScraper
        """
        try:
            # Initialize REAL stock data scraper
            data_dir = 'market_data_real'
            os.makedirs(data_dir, exist_ok=True)

            scraper = self.stock_data_scraper(data_dir=data_dir)

            market_data = {}
            successful_fetches = 0

            # Fetch 2 years of data for each ticker
            end_date = datetime.now()
            start_date = end_date - timedelta(days=730)

            print(f"  📊 Fetching REAL market data for {len(tickers)} companies...")

            for ticker in tickers:
                try:
                    stock_data = scraper.fetch_stock_data(
                        ticker,
                        start_date.strftime('%Y-%m-%d'),
                        end_date.strftime('%Y-%m-%d')
                    )

                    if stock_data is not None and len(stock_data) > 100:
                        market_data[ticker] = stock_data
                        successful_fetches += 1
                        print(f"    ✅ {ticker}: {len(stock_data)} data points")
                    else:
                        print(f"    ❌ {ticker}: Insufficient data")

                except Exception as e:
                    logger.error(f"Error fetching data for {ticker}: {e}")
                    print(f"    ❌ {ticker}: Error - {e}")

            print(f"  ✅ Successfully fetched REAL data for {successful_fetches}/{len(tickers)} companies")

            return market_data

        except Exception as e:
            logger.error(f"Error in REAL market data fetching: {e}")
            return {}

    def _prepare_returns_data(self, market_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Prepare returns data for portfolio optimization
        """
        returns_dict = {}

        for ticker, data in market_data.items():
            if 'Close' in data.columns and len(data) > 50:
                returns = data['Close'].pct_change().dropna()
                if len(returns) > 50:
                    returns_dict[ticker] = returns

        if returns_dict:
            returns_df = pd.DataFrame(returns_dict)
            returns_df = returns_df.dropna()
            return returns_df
        else:
            return pd.DataFrame()

    def _run_real_integrated_optimization(self, returns_data: pd.DataFrame,
                                        fundamental_scores: Dict[str, float]) -> Dict[str, Any]:
        """
        Run REAL integrated portfolio optimization
        """
        try:
            if returns_data.empty or len(returns_data.columns) < 3:
                return {'error': 'Insufficient data for integrated optimization'}

            # Initialize REAL integrated portfolio optimization
            optimizer = self.integrated_portfolio_optimization(
                returns_data=returns_data,
                risk_free_rate=0.06,  # 6% risk-free rate
                factor_data=None,
                option_data=None,
                current_weights=None,
                market_impact_coef=1e-6,
                fixed_cost=0.0001
            )

            # Run all optimizations
            results = optimizer.run_all_optimizations(
                risk_aversion=2.0,
                option_budget=0.05,
                turnover_constraint=0.3
            )

            logger.info("✅ REAL integrated portfolio optimization completed")
            return {
                'method': 'real_integrated_optimization',
                'results': results,
                'assets_optimized': len(returns_data.columns)
            }

        except Exception as e:
            logger.error(f"Error in REAL integrated optimization: {e}")
            return {'error': str(e)}

    def _run_real_optimal_portfolio_builder(self, returns_data: pd.DataFrame,
                                          fundamental_scores: Dict[str, float]) -> Dict[str, Any]:
        """
        Run REAL optimal portfolio builder
        """
        try:
            if returns_data.empty or len(returns_data.columns) < 3:
                return {'error': 'Insufficient data for optimal portfolio builder'}

            # Initialize REAL optimal portfolio builder
            builder = self.optimal_portfolio_builder(
                returns_data=returns_data,
                risk_free_rate=0.06
            )

            # Build optimal portfolio
            portfolio_results = builder.build_optimal_portfolio(
                method='efficient_frontier',
                target_return=None,
                risk_tolerance='moderate'
            )

            logger.info("✅ REAL optimal portfolio builder completed")
            return {
                'method': 'real_optimal_portfolio_builder',
                'results': portfolio_results,
                'assets_optimized': len(returns_data.columns)
            }

        except Exception as e:
            logger.error(f"Error in REAL optimal portfolio builder: {e}")
            return {'error': str(e)}

    def _combine_optimization_results(self, integrated_results: Dict[str, Any],
                                    builder_results: Dict[str, Any],
                                    fundamental_scores: Dict[str, float]) -> Dict[str, Any]:
        """
        Combine results from both REAL optimization methods
        """
        try:
            final_allocation = {}

            # Extract weights from integrated optimization
            integrated_weights = {}
            if 'results' in integrated_results and 'error' not in integrated_results:
                results = integrated_results['results']

                # Try to get weights from different optimization methods
                for method in ['markowitz', 'black_litterman', 'risk_parity']:
                    if method in results and 'weights' in results[method]:
                        integrated_weights = results[method]['weights']
                        break

            # Extract weights from portfolio builder
            builder_weights = {}
            if 'results' in builder_results and 'error' not in builder_results:
                builder_weights = builder_results['results'].get('weights', {})

            # Combine weights (average of both methods)
            all_assets = set(integrated_weights.keys()) | set(builder_weights.keys())

            for asset in all_assets:
                integrated_weight = integrated_weights.get(asset, 0)
                builder_weight = builder_weights.get(asset, 0)
                fundamental_score = fundamental_scores.get(asset, 50)

                # Average the weights and adjust by fundamental score
                avg_weight = (integrated_weight + builder_weight) / 2

                # Adjust by fundamental score (normalize around 70)
                score_adjustment = fundamental_score / 70
                adjusted_weight = avg_weight * score_adjustment

                if adjusted_weight > 0.01:  # Minimum 1% allocation
                    final_allocation[asset] = {
                        'weight': adjusted_weight * 100,  # Convert to percentage
                        'integrated_weight': integrated_weight * 100,
                        'builder_weight': builder_weight * 100,
                        'fundamental_score': fundamental_score,
                        'optimization_method': 'real_combined'
                    }

            # Normalize weights to 100%
            total_weight = sum(data['weight'] for data in final_allocation.values())
            if total_weight > 0:
                for asset in final_allocation:
                    final_allocation[asset]['weight'] = (
                        final_allocation[asset]['weight'] / total_weight * 100
                    )

            return {
                'method': 'real_portfolio_optimization',
                'portfolio_size': len(final_allocation),
                'allocation': final_allocation,
                'integrated_results': integrated_results,
                'builder_results': builder_results,
                'total_weight': 100.0
            }

        except Exception as e:
            logger.error(f"Error combining optimization results: {e}")
            return self._fallback_portfolio_allocation(list(fundamental_scores.keys()), fundamental_scores)

    def _fallback_portfolio_allocation(self, tickers: List[str],
                                     fundamental_scores: Dict[str, float]) -> Dict[str, Any]:
        """
        Fallback portfolio allocation when REAL optimization fails
        """
        # Score-based allocation
        allocation = {}

        # Filter tickers with good fundamental scores
        good_tickers = {ticker: score for ticker, score in fundamental_scores.items()
                       if score >= 60 and ticker in tickers}

        if not good_tickers:
            return {
                'method': 'fallback_failed',
                'portfolio_size': 0,
                'allocation': {},
                'error': 'No companies with sufficient fundamental scores'
            }

        # Calculate weights based on scores
        total_score = sum(good_tickers.values())

        for ticker, score in good_tickers.items():
            weight = (score / total_score) * 100

            allocation[ticker] = {
                'weight': weight,
                'fundamental_score': score,
                'optimization_method': 'score_based_fallback'
            }

        return {
            'method': 'score_based_fallback',
            'portfolio_size': len(allocation),
            'allocation': allocation,
            'total_weight': 100.0
        }

def test_real_portfolio_integration():
    """
    Test the REAL Portfolio optimization integration
    """
    print("🧪 TESTING REAL PORTFOLIO OPTIMIZATION INTEGRATION")
    print("=" * 80)

    # Initialize REAL Portfolio integration
    real_portfolio = RealPortfolioIntegration()

    if not real_portfolio.portfolio_available:
        print("❌ REAL Portfolio optimization modules not available - cannot test")
        return

    # Test with sample companies and scores
    test_companies = ['TCS', 'HDFCBANK', 'RELIANCE', 'INFY', 'WIPRO']
    test_scores = {
        'TCS': 75.0,
        'HDFCBANK': 72.0,
        'RELIANCE': 68.0,
        'INFY': 70.0,
        'WIPRO': 65.0
    }

    print(f"\n📊 Testing REAL portfolio optimization...")
    print(f"   Companies: {test_companies}")
    print(f"   Scores: {test_scores}")

    portfolio_result = real_portfolio.optimize_portfolio_real(test_companies, test_scores)

    print(f"\n✅ REAL Portfolio Optimization Results:")
    print(f"   Method: {portfolio_result.get('method', 'unknown')}")
    print(f"   Portfolio Size: {portfolio_result.get('portfolio_size', 0)}")

    allocation = portfolio_result.get('allocation', {})
    if allocation:
        print(f"\n💼 Portfolio Allocation:")
        sorted_allocation = sorted(allocation.items(),
                                 key=lambda x: x[1].get('weight', 0), reverse=True)

        for ticker, data in sorted_allocation:
            weight = data.get('weight', 0)
            score = data.get('fundamental_score', 0)
            method = data.get('optimization_method', 'unknown')
            print(f"   {ticker}: {weight:.1f}% (Score: {score:.1f}, Method: {method})")

    if 'error' in portfolio_result:
        print(f"   ❌ Error: {portfolio_result['error']}")

    print(f"\n✅ REAL Portfolio Integration Test Complete!")

if __name__ == "__main__":
    test_real_portfolio_integration()
