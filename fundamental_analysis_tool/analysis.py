#!/usr/bin/env python3
"""
Fundamental Analysis Tool

This script provides a command-line interface for performing fundamental analysis on companies.
"""

import os
import sys
import argparse
import logging
import json
import yaml
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules
from utils.data_loader import ScreenerDataLoader
from utils.visualization import FinancialVisualizer
from utils.dashboard import FundamentalDashboard
from models.financial_ratios import FinancialRatios
from models.valuation import Valuation
from models.checklist import Checklist
from models.ranking import CompanyRanker
from models.screener import FundamentalScreener

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('analysis')

class FundamentalAnalyzer:
    """
    Class to perform fundamental analysis on companies
    """

    def __init__(self, data_dir: str = '../screener_data_collector/data', output_dir: str = 'output'):
        """
        Initialize the fundamental analyzer

        Parameters:
        -----------
        data_dir : str
            Path to the screener_data_collector data directory
        output_dir : str
            Directory to save output files
        """
        self.data_dir = data_dir
        self.output_dir = output_dir

        # Create the output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Initialize components
        self.data_loader = ScreenerDataLoader(data_dir)
        self.visualizer = FinancialVisualizer(output_dir)
        self.dashboard = FundamentalDashboard(output_dir)
        self.financial_ratios = FinancialRatios()
        self.valuation = Valuation()
        self.checklist = Checklist()
        self.ranker = CompanyRanker()
        self.screener = FundamentalScreener(self.data_loader)

        # Default valuation parameters
        self.valuation_params = {
            'growth_rate': 15.0,
            'discount_rate': 12.0,
            'terminal_growth_rate': 3.0
        }

    def analyze_company(self,
                       ticker: str,
                       analysis_types: List[str] = None) -> Dict[str, Any]:
        """
        Perform fundamental analysis on a company

        Parameters:
        -----------
        ticker : str
            Ticker symbol of the company
        analysis_types : List[str], optional
            Types of analysis to perform (default: all)

        Returns:
        --------
        Dictionary containing analysis results
        """
        if analysis_types is None:
            analysis_types = ['financial_ratios', 'valuation', 'checklist']

        results = {'ticker': ticker}

        try:
            # Load company data
            logger.info(f"Loading data for {ticker}")
            company_data = self.data_loader.load_company_data(ticker)

            if not company_data:
                logger.error(f"No data found for {ticker}")
                return {'ticker': ticker, 'error': 'No data found'}

            # Perform financial ratio analysis
            if 'financial_ratios' in analysis_types:
                logger.info(f"Calculating financial ratios for {ticker}")
                ratios = self.financial_ratios.calculate_all_ratios(company_data)
                growth_rates = self.financial_ratios.calculate_growth_rates(company_data)

                results['ratios'] = ratios
                results['growth_rates'] = growth_rates

            # Perform valuation analysis
            if 'valuation' in analysis_types:
                logger.info(f"Performing valuation analysis for {ticker}")

                # Perform DCF analysis with parameters
                dcf_result = self.valuation.calculate_dcf(
                    company_data=company_data,
                    growth_rate=self.valuation_params['growth_rate'],
                    terminal_growth_rate=self.valuation_params['terminal_growth_rate'],
                    discount_rate=self.valuation_params['discount_rate']
                )

                # Calculate intrinsic value band
                intrinsic_value_band = self.valuation.calculate_intrinsic_value_band(
                    company_data=company_data,
                    growth_rate=self.valuation_params['growth_rate'],
                    discount_rate=self.valuation_params['discount_rate'],
                    terminal_growth_rate=self.valuation_params['terminal_growth_rate']
                )

                # Perform sensitivity analysis
                base_growth_rate = self.valuation_params['growth_rate']
                base_discount_rate = self.valuation_params['discount_rate']

                # Create ranges around the base values
                growth_rates = [
                    max(5.0, base_growth_rate - 10.0),
                    max(5.0, base_growth_rate - 5.0),
                    base_growth_rate,
                    base_growth_rate + 5.0,
                    base_growth_rate + 10.0
                ]

                discount_rates = [
                    max(6.0, base_discount_rate - 4.0),
                    max(6.0, base_discount_rate - 2.0),
                    base_discount_rate,
                    base_discount_rate + 2.0,
                    base_discount_rate + 4.0
                ]

                sensitivity_analysis = self.valuation.perform_sensitivity_analysis(
                    company_data=company_data,
                    growth_rates=growth_rates,
                    discount_rates=discount_rates,
                    terminal_growth_rate=self.valuation_params['terminal_growth_rate']
                )

                results['dcf_analysis'] = dcf_result
                results['intrinsic_value_band'] = intrinsic_value_band
                results['sensitivity_analysis'] = sensitivity_analysis

            # Perform checklist analysis
            if 'checklist' in analysis_types:
                logger.info(f"Evaluating {ticker} against checklist")
                checklist_result = self.checklist.evaluate_company(company_data)
                results['checklist'] = checklist_result

            # Add company data to results
            results['company_data'] = company_data

        except Exception as e:
            logger.error(f"Error analyzing {ticker}: {str(e)}")
            results['error'] = str(e)

        return results

    def analyze_multiple_companies(self,
                                 tickers: List[str],
                                 analysis_types: List[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        Perform fundamental analysis on multiple companies

        Parameters:
        -----------
        tickers : List[str]
            List of ticker symbols
        analysis_types : List[str], optional
            Types of analysis to perform (default: all)

        Returns:
        --------
        Dictionary mapping ticker symbols to analysis results
        """
        results = {}

        for ticker in tickers:
            results[ticker] = self.analyze_company(ticker, analysis_types)

        return results

    def rank_companies(self,
                     tickers: List[str],
                     criteria: Optional[Dict[str, Dict[str, Dict[str, Union[float, bool]]]]] = None) -> Dict[str, Any]:
        """
        Rank companies based on fundamental analysis

        Parameters:
        -----------
        tickers : List[str]
            List of ticker symbols
        criteria : Dict[str, Dict[str, Dict[str, Union[float, bool]]]], optional
            Ranking criteria (uses default if None)

        Returns:
        --------
        Dictionary containing ranking results
        """
        # Analyze companies
        analysis_results = self.analyze_multiple_companies(tickers, ['financial_ratios', 'valuation'])

        # Rank companies
        ranking_results = self.ranker.rank_companies(analysis_results, criteria)

        return ranking_results

    def screen_companies(self,
                         tickers: List[str] = None,
                         criteria: Dict[str, Dict[str, Dict[str, float]]] = None,
                         max_workers: int = 4,
                         visualize: bool = True,
                         output_prefix: str = 'screening') -> Dict[str, Any]:
        """
        Screen companies based on fundamental criteria

        Parameters:
        -----------
        tickers : List[str], optional
            List of tickers to screen (default: all tickers)
        criteria : Dict[str, Dict[str, Dict[str, float]]], optional
            Screening criteria (default: screener's default criteria)
        max_workers : int
            Maximum number of worker threads for parallel processing
        visualize : bool
            Whether to generate visualizations
        output_prefix : str
            Prefix for output files

        Returns:
        --------
        Dictionary containing screening results
        """
        logger.info("Starting company screening")

        # Use the screener to screen companies
        screening_results = self.screener.screen_companies(tickers, criteria, max_workers)

        # Generate visualizations if requested
        if visualize:
            # Create static visualizations
            logger.info("Generating static visualizations")
            fig = self.visualizer.plot_screening_results(
                screening_results,
                filename=f"{output_prefix}_summary.png"
            )

            # Create heatmap of top companies
            logger.info("Generating metric heatmap")
            fig = self.visualizer.plot_metric_heatmap(
                screening_results,
                filename=f"{output_prefix}_heatmap.png"
            )

            # Create interactive dashboard
            logger.info("Generating interactive dashboard")
            self.dashboard.create_screening_dashboard(
                screening_results,
                filename=f"{output_prefix}_dashboard.html"
            )

        return screening_results

    def compare_companies(self,
                        tickers: List[str],
                        metrics: List[str] = None,
                        visualize: bool = True,
                        output_prefix: str = 'comparison') -> Dict[str, Any]:
        """
        Compare multiple companies across key metrics

        Parameters:
        -----------
        tickers : List[str]
            List of tickers to compare
        metrics : List[str], optional
            List of metrics to compare (default: key metrics)
        visualize : bool
            Whether to generate visualizations
        output_prefix : str
            Prefix for output files

        Returns:
        --------
        Dictionary containing comparison results
        """
        logger.info(f"Comparing {len(tickers)} companies")

        # Default metrics if none provided
        if metrics is None:
            metrics = [
                'roe', 'roce', 'debt_to_equity', 'interest_coverage',
                'revenue_cagr', 'profit_cagr', 'pe_ratio', 'pb_ratio'
            ]

        # Analyze companies
        analysis_results = self.analyze_multiple_companies(tickers, ['financial_ratios', 'valuation'])

        # Extract metrics for comparison
        comparison_data = {}

        for ticker, result in analysis_results.items():
            if 'error' in result:
                logger.warning(f"Error analyzing {ticker}: {result['error']}")
                continue

            # Extract metrics
            comparison_data[ticker] = {'metrics': {}}

            # Extract from ratios
            if 'ratios' in result:
                for category, ratios in result['ratios'].items():
                    if isinstance(ratios, dict):
                        for name, value in ratios.items():
                            if name in metrics:
                                comparison_data[ticker]['metrics'][name] = value

            # Extract from growth rates
            if 'growth_rates' in result:
                for name, value in result['growth_rates'].items():
                    if name in metrics:
                        comparison_data[ticker]['metrics'][name] = value

        # Generate visualizations if requested
        if visualize:
            # Create radar chart
            logger.info("Generating radar chart")
            fig = self.visualizer.plot_company_comparison(
                comparison_data,
                metrics,
                filename=f"{output_prefix}_radar.png"
            )

            # Create bar charts for each metric
            for metric in metrics:
                metric_data = {}
                for ticker, data in comparison_data.items():
                    if 'metrics' in data and metric in data['metrics']:
                        metric_data[ticker] = data['metrics'][metric]

                if metric_data:
                    logger.info(f"Generating bar chart for {metric}")
                    fig = self.visualizer.plot_financial_comparison(
                        {ticker: {metric: value} for ticker, value in metric_data.items()},
                        metric,
                        f"Comparison of {metric.replace('_', ' ').title()}",
                        filename=f"{output_prefix}_{metric}.png"
                    )

        return comparison_data

    def filter_and_rank_companies(self,
                                filters: Dict[str, Dict[str, Any]],
                                criteria: Optional[Dict[str, Dict[str, Dict[str, Union[float, bool]]]]] = None) -> Dict[str, Any]:
        """
        Filter and rank companies based on fundamental analysis

        Parameters:
        -----------
        filters : Dict[str, Dict[str, Any]]
            Filtering criteria
        criteria : Dict[str, Dict[str, Dict[str, Union[float, bool]]]], optional
            Ranking criteria (uses default if None)

        Returns:
        --------
        Dictionary containing filtered and ranked companies
        """
        # Get all tickers
        all_tickers = self.data_loader.get_all_tickers()

        # Load summary data for all companies
        summary_data = self.data_loader.load_all_companies_summary()

        # Pre-filter companies based on summary data
        pre_filtered_tickers = []
        for ticker in all_tickers:
            if ticker in summary_data.index:
                company_row = summary_data.loc[ticker]

                # Apply pre-filters
                passes_pre_filters = True

                # Example pre-filters (customize as needed)
                if 'market_cap' in filters and 'market_cap' in company_row:
                    if 'min' in filters['market_cap'] and company_row['market_cap'] < filters['market_cap']['min']:
                        passes_pre_filters = False
                    if 'max' in filters['market_cap'] and company_row['market_cap'] > filters['market_cap']['max']:
                        passes_pre_filters = False

                if passes_pre_filters:
                    pre_filtered_tickers.append(ticker)

        logger.info(f"Pre-filtered {len(pre_filtered_tickers)} companies out of {len(all_tickers)}")

        # Analyze pre-filtered companies
        analysis_results = self.analyze_multiple_companies(pre_filtered_tickers, ['financial_ratios', 'valuation'])

        # Apply detailed filters
        filtered_tickers = self.ranker.filter_companies(analysis_results, filters)

        logger.info(f"Filtered down to {len(filtered_tickers)} companies")

        # Extract filtered analysis results
        filtered_results = {ticker: analysis_results[ticker] for ticker in filtered_tickers}

        # Rank filtered companies
        ranking_results = self.ranker.rank_companies(filtered_results, criteria)

        return {
            'filtered_tickers': filtered_tickers,
            'rankings': ranking_results
        }

    def generate_report(self,
                      analysis_results: Dict[str, Any],
                      report_type: str = 'full',
                      output_file: Optional[str] = None) -> str:
        """
        Generate a report from analysis results

        Parameters:
        -----------
        analysis_results : Dict[str, Any]
            Analysis results
        report_type : str
            Type of report to generate ('full', 'summary', 'valuation', 'ratios', 'checklist')
        output_file : str, optional
            File to save the report to

        Returns:
        --------
        Report as a string
        """
        ticker = analysis_results['ticker']
        report = f"# Fundamental Analysis Report: {ticker}\n\n"

        # Check for errors
        if 'error' in analysis_results:
            report += f"Error: {analysis_results['error']}\n"
            return report

        # Add financial ratios section
        if 'ratios' in analysis_results and (report_type == 'full' or report_type == 'ratios'):
            report += "## Financial Ratios\n\n"

            # Add profitability ratios
            if 'profitability' in analysis_results['ratios']:
                report += "### Profitability Ratios\n\n"
                report += "| Ratio | Value |\n"
                report += "|-------|-------|\n"

                for key, value in analysis_results['ratios']['profitability'].items():
                    report += f"| {key.replace('_', ' ').title()} | {value:.2f}% |\n"

                report += "\n"

            # Add leverage ratios
            if 'leverage' in analysis_results['ratios']:
                report += "### Leverage Ratios\n\n"
                report += "| Ratio | Value |\n"
                report += "|-------|-------|\n"

                for key, value in analysis_results['ratios']['leverage'].items():
                    report += f"| {key.replace('_', ' ').title()} | {value:.2f} |\n"

                report += "\n"

            # Add valuation ratios
            if 'valuation' in analysis_results['ratios']:
                report += "### Valuation Ratios\n\n"
                report += "| Ratio | Value |\n"
                report += "|-------|-------|\n"

                for key, value in analysis_results['ratios']['valuation'].items():
                    report += f"| {key.replace('_', ' ').title()} | {value:.2f} |\n"

                report += "\n"

            # Add operating ratios
            if 'operating' in analysis_results['ratios']:
                report += "### Operating Ratios\n\n"
                report += "| Ratio | Value |\n"
                report += "|-------|-------|\n"

                for key, value in analysis_results['ratios']['operating'].items():
                    report += f"| {key.replace('_', ' ').title()} | {value:.2f} |\n"

                report += "\n"

            # Add cash flow ratios
            if 'cash_flow' in analysis_results['ratios']:
                report += "### Cash Flow Ratios\n\n"
                report += "| Ratio | Value |\n"
                report += "|-------|-------|\n"

                # First add the raw cash flow values
                raw_cf_keys = ['operating_cash_flow', 'investing_cash_flow', 'financing_cash_flow', 'net_cash_flow', 'free_cash_flow']
                for key in raw_cf_keys:
                    if key in analysis_results['ratios']['cash_flow']:
                        value = analysis_results['ratios']['cash_flow'][key]
                        # Format with sign to emphasize positive/negative
                        sign = '+' if value > 0 else ''
                        report += f"| {key.replace('_', ' ').title()} | {sign}{value:.2f} |\n"

                # Then add the ratio values
                ratio_keys = [k for k in analysis_results['ratios']['cash_flow'].keys() if k not in raw_cf_keys]
                for key in ratio_keys:
                    value = analysis_results['ratios']['cash_flow'][key]
                    report += f"| {key.replace('_', ' ').title()} | {value:.2f}% |\n"

                report += "\n"

            # Add cash flow section with explanation
            report += "### Cash Flow Analysis\n\n"
            report += "Cash flow analysis is a crucial part of fundamental analysis. It helps us understand how a company generates and uses cash, which is essential for assessing its financial health and sustainability.\n\n"
            report += "Cash flow statements are divided into three key sections:\n\n"
            report += "1. **Operating Activities**: Shows cash generated from core business operations\n"
            report += "   - **Positive**: Company is generating cash from its core business (good sign)\n"
            report += "   - **Negative**: Company is spending more cash than it's generating (concerning)\n\n"
            report += "2. **Investing Activities**: Shows cash used for long-term asset investments\n"
            report += "   - **Negative**: Company is investing in growth (normal for growing companies)\n"
            report += "   - **Positive**: Company might be selling assets (could indicate contraction)\n\n"
            report += "3. **Financing Activities**: Shows cash from debt, equity, and dividend payments\n"
            report += "   - **Positive**: Company is raising capital through debt or equity\n"
            report += "   - **Negative**: Company is paying down debt, buying back shares, or paying dividends\n\n"
            report += "The ideal cash flow pattern for a mature business is:\n"
            report += "- **Operating**: Positive (generating cash from core business)\n"
            report += "- **Investing**: Negative (investing in growth)\n"
            report += "- **Financing**: Negative (returning value to shareholders)\n\n"

            # Add cash flow ratios if available
            if 'cash_flow' in analysis_results['ratios']:
                report += "#### Cash Flow Metrics\n\n"
                report += "| Metric | Value | Interpretation |\n"
                report += "|--------|-------|----------------|\n"

                # Add operating cash flow
                if 'operating_cash_flow' in analysis_results['ratios']['cash_flow']:
                    value = analysis_results['ratios']['cash_flow']['operating_cash_flow']
                    sign = '+' if value > 0 else ''
                    interpretation = "Positive (Good)" if value > 0 else "Negative (Concerning)"
                    report += f"| Operating Cash Flow | {sign}{value:.2f} | {interpretation} |\n"

                # Add investing cash flow
                if 'investing_cash_flow' in analysis_results['ratios']['cash_flow']:
                    value = analysis_results['ratios']['cash_flow']['investing_cash_flow']
                    sign = '+' if value > 0 else ''
                    interpretation = "Positive (Potential Asset Sales)" if value > 0 else "Negative (Investing in Growth)"
                    report += f"| Investing Cash Flow | {sign}{value:.2f} | {interpretation} |\n"

                # Add financing cash flow
                if 'financing_cash_flow' in analysis_results['ratios']['cash_flow']:
                    value = analysis_results['ratios']['cash_flow']['financing_cash_flow']
                    sign = '+' if value > 0 else ''
                    interpretation = "Positive (Raising Capital)" if value > 0 else "Negative (Returning Value to Shareholders)"
                    report += f"| Financing Cash Flow | {sign}{value:.2f} | {interpretation} |\n"

                # Add free cash flow
                if 'free_cash_flow' in analysis_results['ratios']['cash_flow']:
                    value = analysis_results['ratios']['cash_flow']['free_cash_flow']
                    sign = '+' if value > 0 else ''
                    interpretation = "Positive (Good)" if value > 0 else "Negative (Concerning)"
                    report += f"| Free Cash Flow | {sign}{value:.2f} | {interpretation} |\n"

                report += "\n"

            # Add cash flow pattern analysis
            if 'cash_flow_analysis' in analysis_results['ratios'] and 'pattern' in analysis_results['ratios']['cash_flow_analysis']:
                pattern = analysis_results['ratios']['cash_flow_analysis']['pattern']

                report += "### Cash Flow Pattern Analysis\n\n"

                # Add pattern signs
                report += "| Cash Flow Component | Sign |\n"
                report += "|---------------------|------|\n"
                report += f"| Operating Activities | {pattern['operating_sign'].upper()} |\n"
                report += f"| Investing Activities | {pattern['investing_sign'].upper()} |\n"
                report += f"| Financing Activities | {pattern['financing_sign'].upper()} |\n"
                report += "\n"

                # Add interpretation
                report += "**Pattern Interpretation:** " + pattern['interpretation'] + "\n\n"

                # Add health assessment
                health_emoji = {
                    'excellent': '🟢 Excellent',
                    'good': '🟢 Good',
                    'caution': '🟡 Caution',
                    'warning': '🟠 Warning',
                    'danger': '🔴 Danger'
                }

                report += f"**Financial Health Assessment:** {health_emoji.get(pattern['health'], pattern['health'])}\n\n"

                # Add operating trend if available
                if 'operating_trend' in analysis_results['ratios']['cash_flow_analysis']:
                    report += f"**Operating Cash Flow Trend:** {analysis_results['ratios']['cash_flow_analysis']['operating_trend']}\n\n"

        # Add growth rates section
        if 'growth_rates' in analysis_results and (report_type == 'full' or report_type == 'ratios'):
            report += "## Growth Rates\n\n"
            report += "| Metric | CAGR |\n"
            report += "|--------|------|\n"

            for key, value in analysis_results['growth_rates'].items():
                report += f"| {key.replace('_', ' ').title()} | {value:.2f}% |\n"

            report += "\n"

        # Add valuation section
        if 'dcf_analysis' in analysis_results and (report_type == 'full' or report_type == 'valuation'):
            report += "## Valuation Analysis\n\n"
            report += "### Discounted Cash Flow (DCF) Analysis\n\n"

            # Add key DCF results
            report += "| Metric | Value |\n"
            report += "|--------|------|\n"

            key_metrics = ['base_fcf', 'enterprise_value', 'equity_value', 'intrinsic_value_per_share']
            for key in key_metrics:
                if key in analysis_results['dcf_analysis']:
                    report += f"| {key.replace('_', ' ').title()} | {analysis_results['dcf_analysis'][key]:.2f} |\n"

            # Add margin of safety
            if 'margin_of_safety' in analysis_results['dcf_analysis']:
                report += f"| Margin of Safety | {analysis_results['dcf_analysis']['margin_of_safety']:.2f}% |\n"

            report += "\n"

            # Add intrinsic value band
            if 'intrinsic_value_band' in analysis_results:
                report += "### Intrinsic Value Band\n\n"
                report += "| Metric | Value |\n"
                report += "|--------|------|\n"

                key_metrics = ['base_value', 'upper_value', 'lower_value', 'buy_value', 'current_price']
                for key in key_metrics:
                    if key in analysis_results['intrinsic_value_band']:
                        report += f"| {key.replace('_', ' ').title()} | {analysis_results['intrinsic_value_band'][key]:.2f} |\n"

                # Add recommendation
                if 'recommendation' in analysis_results['intrinsic_value_band']:
                    report += f"| Recommendation | {analysis_results['intrinsic_value_band']['recommendation']} |\n"

                report += "\n"

        # Add checklist section
        if 'checklist' in analysis_results and (report_type == 'full' or report_type == 'checklist'):
            report += "## Due Diligence Checklist\n\n"

            # Add checklist score
            report += f"**Score: {analysis_results['checklist']['score']:.1f}/{analysis_results['checklist']['max_score']:.1f} ({analysis_results['checklist']['percentage']:.1f}%)**\n\n"

            # Add checklist items
            report += "| Checklist Item | Value | Pass/Fail |\n"
            report += "|---------------|-------|----------|\n"

            for key, item in analysis_results['checklist']['items'].items():
                value = f"{item['value']:.2f}" if item['value'] is not None else "N/A"
                status = "✓" if item['pass'] else "✗"
                report += f"| {item['description']} | {value} | {status} |\n"

            report += "\n"

        # Save the report to a file
        if output_file:
            # Check if output_file already includes the output_dir
            if output_file.startswith(self.output_dir + '/'):
                output_path = output_file
            else:
                output_path = os.path.join(self.output_dir, output_file)

            # Create the directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            with open(output_path, 'w') as f:
                f.write(report)
            logger.info(f"Report saved to {output_path}")

        return report

def main():
    """
    Main function to run the fundamental analysis tool from the command line
    """
    parser = argparse.ArgumentParser(description='Fundamental Analysis Tool')

    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest='command', help='Command to run')

    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze a specific company')
    analyze_parser.add_argument('--ticker', type=str, required=True, help='Ticker symbol to analyze')
    analyze_parser.add_argument('--analysis', type=str, default='full', help='Type of analysis to perform (financial_ratios, valuation, checklist, full)')
    analyze_parser.add_argument('--report', type=str, help='Generate a report (full, summary, valuation, ratios, checklist)')
    analyze_parser.add_argument('--output', type=str, help='Output file for the report')
    analyze_parser.add_argument('--growth-rate', type=float, default=15.0, help='Growth rate for DCF valuation (in percentage)')
    analyze_parser.add_argument('--discount-rate', type=float, default=12.0, help='Discount rate for DCF valuation (in percentage)')
    analyze_parser.add_argument('--terminal-growth-rate', type=float, default=3.0, help='Terminal growth rate for DCF valuation (in percentage)')
    analyze_parser.add_argument('--dashboard', action='store_true', help='Generate an interactive dashboard')

    # Screen command
    screen_parser = subparsers.add_parser('screen', help='Screen companies based on fundamental criteria')
    screen_parser.add_argument('--tickers', type=str, help='Comma-separated list of tickers to screen (default: all tickers)')
    screen_parser.add_argument('--criteria', type=str, help='Criteria file (JSON or YAML)')
    screen_parser.add_argument('--max-workers', type=int, default=4, help='Maximum number of worker threads')
    screen_parser.add_argument('--no-visualize', action='store_true', help='Disable visualization generation')
    screen_parser.add_argument('--output-prefix', type=str, default='screening', help='Prefix for output files')

    # Compare command
    compare_parser = subparsers.add_parser('compare', help='Compare multiple companies')
    compare_parser.add_argument('--tickers', type=str, required=True, help='Comma-separated list of tickers to compare')
    compare_parser.add_argument('--metrics', type=str, help='Comma-separated list of metrics to compare')
    compare_parser.add_argument('--no-visualize', action='store_true', help='Disable visualization generation')
    compare_parser.add_argument('--output-prefix', type=str, default='comparison', help='Prefix for output files')

    # Filter command
    filter_parser = subparsers.add_parser('filter', help='Filter and rank companies')
    filter_parser.add_argument('--filter', type=str, required=True, help='Filter file (JSON or YAML)')
    filter_parser.add_argument('--rank', action='store_true', help='Rank companies')
    filter_parser.add_argument('--top', type=int, default=10, help='Number of top companies to show')

    # Common arguments
    for subparser in [analyze_parser, screen_parser, compare_parser, filter_parser]:
        subparser.add_argument('--data-dir', type=str, default='../screener_data_collector/data', help='Data directory')
        subparser.add_argument('--output-dir', type=str, default='output', help='Output directory')

    # Parse arguments
    args = parser.parse_args()

    # Create the analyzer
    analyzer = FundamentalAnalyzer(data_dir=args.data_dir, output_dir=args.output_dir)

    # Process commands
    if args.command == 'analyze':
        # Update the valuation parameters in the analyzer
        analyzer.valuation_params = {
            'growth_rate': args.growth_rate,
            'discount_rate': args.discount_rate,
            'terminal_growth_rate': args.terminal_growth_rate
        }

        # Determine analysis types
        analysis_types = []
        if args.analysis == 'full':
            analysis_types = ['financial_ratios', 'valuation', 'checklist']
        else:
            analysis_types = args.analysis.split(',')

        # Analyze the company
        results = analyzer.analyze_company(args.ticker, analysis_types)

        # Generate report if requested
        if args.report:
            report = analyzer.generate_report(results, args.report, args.output)
            print(report)

        # Generate dashboard if requested
        if args.dashboard:
            dashboard_file = f"{args.ticker}_dashboard.html"
            analyzer.dashboard.create_company_dashboard(
                results,
                args.ticker,
                filename=dashboard_file
            )
            print(f"Dashboard saved to {os.path.join(args.output_dir, dashboard_file)}")

    elif args.command == 'screen':
        # Parse tickers if provided
        tickers = None
        if args.tickers:
            tickers = args.tickers.split(',')

        # Load criteria if provided
        criteria = None
        if args.criteria:
            with open(args.criteria, 'r') as f:
                if args.criteria.endswith('.json'):
                    criteria = json.load(f)
                elif args.criteria.endswith('.yaml') or args.criteria.endswith('.yml'):
                    criteria = yaml.safe_load(f)
                else:
                    print(f"Unsupported criteria file format: {args.criteria}")
                    return

        # Screen companies
        results = analyzer.screen_companies(
            tickers=tickers,
            criteria=criteria,
            max_workers=args.max_workers,
            visualize=not args.no_visualize,
            output_prefix=args.output_prefix
        )

        # Print summary
        summary = results.get('summary', {})
        print("\nScreening Summary:")
        print(f"Total companies: {summary.get('total', 0)}")
        print(f"Pre-filtered: {summary.get('pre_filtered', 0)}")
        print(f"Passed Tier 1 (Financial Health): {summary.get('tier1_pass', 0)}")
        print(f"Passed Tier 2 (Growth Metrics): {summary.get('tier2_pass', 0)}")
        print(f"Passed Tier 3 (Valuation Metrics): {summary.get('tier3_pass', 0)}")
        print(f"Passed Tier 4 (Cash Flow Analysis): {summary.get('tier4_pass', 0)}")
        print(f"Passed Tier 5 (Historical Consistency): {summary.get('tier5_pass', 0)}")
        print(f"Passed All Tiers: {summary.get('all_pass', 0)}")

        # Print top companies
        passed_companies = results.get('passed_companies', {}).get('all', [])
        if passed_companies:
            print("\nTop 10 Companies that Passed All Tiers:")
            for i, ticker in enumerate(passed_companies[:10], start=1):
                print(f"{i}. {ticker}")

    elif args.command == 'compare':
        # Parse tickers
        tickers = args.tickers.split(',')

        # Parse metrics if provided
        metrics = None
        if args.metrics:
            metrics = args.metrics.split(',')

        # Compare companies
        results = analyzer.compare_companies(
            tickers=tickers,
            metrics=metrics,
            visualize=not args.no_visualize,
            output_prefix=args.output_prefix
        )

        # Print summary
        print(f"\nCompared {len(tickers)} companies")
        print(f"Visualizations saved with prefix: {args.output_prefix}")

    elif args.command == 'filter':
        # Load filter criteria
        with open(args.filter, 'r') as f:
            if args.filter.endswith('.json'):
                filters = json.load(f)
            elif args.filter.endswith('.yaml') or args.filter.endswith('.yml'):
                filters = yaml.safe_load(f)
            else:
                print(f"Unsupported filter file format: {args.filter}")
                return

        # Filter and rank companies
        results = analyzer.filter_and_rank_companies(filters)

        print(f"Filtered {len(results['filtered_tickers'])} companies")

        if args.rank:
            print(f"\nTop {args.top} Companies:")
            for i, (ticker, score) in enumerate(results['rankings']['rankings']['total'][:args.top], start=1):
                print(f"{i}. {ticker}: {score:.4f}")

    else:
        parser.print_help()

if __name__ == "__main__":
    main()
