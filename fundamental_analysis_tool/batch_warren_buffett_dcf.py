#!/usr/bin/env python3
"""
Batch Warren Buffett Style DCF Analysis

This script runs Warren Buffett style DCF analysis on all cash flow qualified companies
to get an essence of intrinsic valuation across the entire universe.

This addresses your request: "maybe we can apply on the whole company list to get 
an essence of intrinsic the valuation you know the Warren buffet style"
"""

import os
import sys
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import modules
from utils.data_loader import ScreenerDataLoader
from warren_buffett_dcf_analysis import WarrenBuffettDCFAnalysis

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('batch_warren_buffett_dcf')

class BatchWarrenBuffettDCF:
    """
    Batch Warren Buffett style DCF analysis for all qualified companies
    """
    
    def __init__(self):
        """
        Initialize batch Warren Buffett DCF analysis
        """
        self.data_loader = ScreenerDataLoader('../screener_data_collector/data')
        self.warren_buffett_analyzer = WarrenBuffettDCFAnalysis()
        
        # Results storage
        self.batch_results = {
            'metadata': {
                'analysis_timestamp': datetime.now().isoformat(),
                'analysis_type': 'batch_warren_buffett_dcf',
                'description': 'Warren Buffett style DCF analysis on all qualified companies'
            },
            'summary_statistics': {},
            'company_results': {},
            'top_opportunities': {},
            'sector_analysis': {},
            'valuation_distribution': {}
        }
    
    def run_batch_analysis(self, qualified_companies: List[str] = None):
        """
        Run Warren Buffett style DCF analysis on all qualified companies
        
        Parameters:
        -----------
        qualified_companies : List[str], optional
            List of qualified companies. If None, loads from existing results.
        """
        print("🎯 BATCH WARREN BUFFETT STYLE DCF ANALYSIS")
        print("=" * 80)
        print("💰 Running intrinsic valuation analysis on all qualified companies")
        print("📊 Warren Buffett style conservative DCF approach")
        print("=" * 80)
        
        # Step 1: Get qualified companies
        if qualified_companies is None:
            qualified_companies = self._load_qualified_companies()
        
        if not qualified_companies:
            print("❌ No qualified companies found")
            return
        
        print(f"📊 Analyzing {len(qualified_companies)} cash flow qualified companies...")
        
        # Step 2: Run batch DCF analysis
        self._run_batch_dcf_analysis(qualified_companies)
        
        # Step 3: Generate summary statistics
        self._generate_summary_statistics()
        
        # Step 4: Identify top opportunities
        self._identify_top_opportunities()
        
        # Step 5: Analyze by sectors
        self._analyze_by_sectors()
        
        # Step 6: Analyze valuation distribution
        self._analyze_valuation_distribution()
        
        # Step 7: Save results
        self._save_batch_results()
        
        # Step 8: Display summary
        self._display_batch_summary()
        
        return self.batch_results
    
    def _load_qualified_companies(self) -> List[str]:
        """Load qualified companies from existing results"""
        try:
            # Look for latest final pipeline results
            results_dir = 'output/final_pipeline'
            
            if not os.path.exists(results_dir):
                print("❌ No final pipeline results found")
                return []
            
            result_files = [f for f in os.listdir(results_dir) if f.startswith('final_investment_pipeline_')]
            
            if not result_files:
                print("❌ No final pipeline result files found")
                return []
            
            latest_file = sorted(result_files)[-1]
            file_path = os.path.join(results_dir, latest_file)
            
            with open(file_path, 'r') as f:
                results = json.load(f)
            
            stage_1 = results.get('stage_1_qualified_companies', {})
            qualified_companies = stage_1.get('qualified_companies', [])
            
            print(f"✅ Loaded {len(qualified_companies)} qualified companies from {latest_file}")
            return qualified_companies
            
        except Exception as e:
            logger.error(f"Error loading qualified companies: {e}")
            return []
    
    def _run_batch_dcf_analysis(self, qualified_companies: List[str]):
        """Run DCF analysis on all qualified companies"""
        print(f"\n🔄 Running Warren Buffett DCF analysis...")
        
        successful_analyses = 0
        failed_analyses = 0
        batch_size = 20
        
        for i in range(0, len(qualified_companies), batch_size):
            batch = qualified_companies[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(qualified_companies) + batch_size - 1) // batch_size
            
            print(f"  📊 Processing batch {batch_num}/{total_batches} ({len(batch)} companies)")
            
            for ticker in batch:
                try:
                    # Load company data
                    company_data = self.data_loader.load_company_data(ticker)
                    
                    if company_data:
                        # Run Warren Buffett analysis
                        result = self.warren_buffett_analyzer.analyze_company_buffett_style(ticker, company_data)
                        
                        if 'error' not in result:
                            self.batch_results['company_results'][ticker] = result
                            successful_analyses += 1
                        else:
                            failed_analyses += 1
                            logger.warning(f"Analysis failed for {ticker}: {result['error']}")
                    else:
                        failed_analyses += 1
                        logger.warning(f"No data available for {ticker}")
                        
                except Exception as e:
                    failed_analyses += 1
                    logger.error(f"Error analyzing {ticker}: {e}")
            
            # Progress update
            completed = min(i + batch_size, len(qualified_companies))
            progress = (completed / len(qualified_companies)) * 100
            print(f"    Progress: {completed}/{len(qualified_companies)} ({progress:.1f}%)")
        
        print(f"\n✅ Batch DCF analysis complete:")
        print(f"   📊 Successful analyses: {successful_analyses}")
        print(f"   ❌ Failed analyses: {failed_analyses}")
        print(f"   📈 Success rate: {(successful_analyses/(successful_analyses+failed_analyses)*100):.1f}%")
    
    def _generate_summary_statistics(self):
        """Generate summary statistics from batch results"""
        print(f"\n📊 Generating summary statistics...")
        
        company_results = self.batch_results['company_results']
        
        if not company_results:
            print("❌ No company results to analyze")
            return
        
        # Extract key metrics
        buffett_scores = []
        intrinsic_values = []
        current_prices = []
        margins_of_safety = []
        recommendations = {'STRONG_BUY': 0, 'BUY': 0, 'HOLD': 0, 'WATCH': 0, 'AVOID': 0}
        buffett_approved = 0
        
        for ticker, result in company_results.items():
            # Buffett score
            buffett_score_data = result.get('buffett_score', {})
            score = buffett_score_data.get('buffett_score', 0)
            if score > 0:
                buffett_scores.append(score)
            
            # Valuation metrics
            margin_analysis = result.get('margin_of_safety_analysis', {})
            intrinsic_value = margin_analysis.get('intrinsic_value', 0)
            current_price = margin_analysis.get('current_price', 0)
            margin_of_safety = margin_analysis.get('conservative_margin_of_safety', 0)
            
            if intrinsic_value > 0:
                intrinsic_values.append(intrinsic_value)
            if current_price > 0:
                current_prices.append(current_price)
            if margin_of_safety != 0:
                margins_of_safety.append(margin_of_safety)
            
            # Recommendations
            recommendation_data = result.get('recommendation', {})
            recommendation = recommendation_data.get('recommendation', 'AVOID')
            if recommendation in recommendations:
                recommendations[recommendation] += 1
            
            # Buffett approved
            if recommendation_data.get('buffett_approved', False):
                buffett_approved += 1
        
        # Calculate statistics
        self.batch_results['summary_statistics'] = {
            'total_companies_analyzed': len(company_results),
            'buffett_scores': {
                'average': sum(buffett_scores) / len(buffett_scores) if buffett_scores else 0,
                'median': sorted(buffett_scores)[len(buffett_scores)//2] if buffett_scores else 0,
                'min': min(buffett_scores) if buffett_scores else 0,
                'max': max(buffett_scores) if buffett_scores else 0,
                'count': len(buffett_scores)
            },
            'intrinsic_values': {
                'average': sum(intrinsic_values) / len(intrinsic_values) if intrinsic_values else 0,
                'median': sorted(intrinsic_values)[len(intrinsic_values)//2] if intrinsic_values else 0,
                'min': min(intrinsic_values) if intrinsic_values else 0,
                'max': max(intrinsic_values) if intrinsic_values else 0,
                'count': len(intrinsic_values)
            },
            'margins_of_safety': {
                'average': sum(margins_of_safety) / len(margins_of_safety) if margins_of_safety else 0,
                'median': sorted(margins_of_safety)[len(margins_of_safety)//2] if margins_of_safety else 0,
                'min': min(margins_of_safety) if margins_of_safety else 0,
                'max': max(margins_of_safety) if margins_of_safety else 0,
                'count': len(margins_of_safety)
            },
            'recommendations': recommendations,
            'buffett_approved_count': buffett_approved,
            'buffett_approval_rate': (buffett_approved / len(company_results) * 100) if company_results else 0
        }
        
        print(f"✅ Summary statistics generated for {len(company_results)} companies")
    
    def _identify_top_opportunities(self):
        """Identify top investment opportunities"""
        print(f"\n🏆 Identifying top investment opportunities...")
        
        company_results = self.batch_results['company_results']
        
        # Create list of opportunities with key metrics
        opportunities = []
        
        for ticker, result in company_results.items():
            buffett_score_data = result.get('buffett_score', {})
            margin_analysis = result.get('margin_of_safety_analysis', {})
            recommendation_data = result.get('recommendation', {})
            
            opportunity = {
                'ticker': ticker,
                'buffett_score': buffett_score_data.get('buffett_score', 0),
                'buffett_grade': buffett_score_data.get('buffett_grade', 'F'),
                'intrinsic_value': margin_analysis.get('intrinsic_value', 0),
                'conservative_intrinsic_value': margin_analysis.get('conservative_intrinsic_value', 0),
                'current_price': margin_analysis.get('current_price', 0),
                'margin_of_safety': margin_analysis.get('conservative_margin_of_safety', 0),
                'recommendation': recommendation_data.get('recommendation', 'AVOID'),
                'buffett_approved': recommendation_data.get('buffett_approved', False),
                'investment_signal': margin_analysis.get('investment_signal', 'AVOID')
            }
            
            opportunities.append(opportunity)
        
        # Sort by Buffett score and margin of safety
        opportunities.sort(key=lambda x: (x['buffett_score'], x['margin_of_safety']), reverse=True)
        
        # Categorize opportunities
        self.batch_results['top_opportunities'] = {
            'strong_buy': [opp for opp in opportunities if opp['recommendation'] == 'STRONG_BUY'],
            'buy': [opp for opp in opportunities if opp['recommendation'] == 'BUY'],
            'hold': [opp for opp in opportunities if opp['recommendation'] == 'HOLD'],
            'buffett_approved': [opp for opp in opportunities if opp['buffett_approved']],
            'top_10_by_score': opportunities[:10],
            'top_10_by_margin': sorted(opportunities, key=lambda x: x['margin_of_safety'], reverse=True)[:10]
        }
        
        print(f"✅ Top opportunities identified:")
        print(f"   💎 STRONG BUY: {len(self.batch_results['top_opportunities']['strong_buy'])}")
        print(f"   📈 BUY: {len(self.batch_results['top_opportunities']['buy'])}")
        print(f"   📊 HOLD: {len(self.batch_results['top_opportunities']['hold'])}")
        print(f"   🎯 Buffett Approved: {len(self.batch_results['top_opportunities']['buffett_approved'])}")
    
    def _analyze_by_sectors(self):
        """Analyze results by sectors"""
        print(f"\n🏭 Analyzing by sectors...")
        
        # This is a simplified sector analysis
        # In a real implementation, you'd have sector mapping
        sector_analysis = {
            'technology': [],
            'banking': [],
            'energy': [],
            'healthcare': [],
            'consumer': [],
            'industrial': [],
            'other': []
        }
        
        # Simple sector classification based on ticker patterns
        for ticker, result in self.batch_results['company_results'].items():
            sector = 'other'  # Default
            
            # Simple heuristic classification
            if any(keyword in ticker.upper() for keyword in ['TCS', 'INFY', 'WIPRO', 'TECH']):
                sector = 'technology'
            elif any(keyword in ticker.upper() for keyword in ['HDFC', 'ICICI', 'SBI', 'BANK']):
                sector = 'banking'
            elif any(keyword in ticker.upper() for keyword in ['RELIANCE', 'ONGC', 'OIL']):
                sector = 'energy'
            
            sector_analysis[sector].append({
                'ticker': ticker,
                'buffett_score': result.get('buffett_score', {}).get('buffett_score', 0),
                'recommendation': result.get('recommendation', {}).get('recommendation', 'AVOID')
            })
        
        self.batch_results['sector_analysis'] = sector_analysis
        
        print(f"✅ Sector analysis complete")
    
    def _analyze_valuation_distribution(self):
        """Analyze valuation distribution"""
        print(f"\n📊 Analyzing valuation distribution...")
        
        company_results = self.batch_results['company_results']
        
        # Valuation buckets
        valuation_buckets = {
            'severely_undervalued': 0,  # >50% margin of safety
            'undervalued': 0,           # 25-50% margin of safety
            'fairly_valued': 0,         # 0-25% margin of safety
            'overvalued': 0,            # -25-0% margin of safety
            'severely_overvalued': 0    # <-25% margin of safety
        }
        
        for ticker, result in company_results.items():
            margin_analysis = result.get('margin_of_safety_analysis', {})
            margin = margin_analysis.get('conservative_margin_of_safety', 0)
            
            if margin > 50:
                valuation_buckets['severely_undervalued'] += 1
            elif margin > 25:
                valuation_buckets['undervalued'] += 1
            elif margin > 0:
                valuation_buckets['fairly_valued'] += 1
            elif margin > -25:
                valuation_buckets['overvalued'] += 1
            else:
                valuation_buckets['severely_overvalued'] += 1
        
        self.batch_results['valuation_distribution'] = valuation_buckets
        
        print(f"✅ Valuation distribution analyzed")
    
    def _save_batch_results(self):
        """Save batch analysis results"""
        os.makedirs('output/warren_buffett_dcf', exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save full results
        results_file = f'output/warren_buffett_dcf/batch_warren_buffett_dcf_{timestamp}.json'
        with open(results_file, 'w') as f:
            json.dump(self.batch_results, f, indent=2, default=str)
        
        # Save top opportunities as CSV
        top_opportunities = self.batch_results.get('top_opportunities', {})
        buffett_approved = top_opportunities.get('buffett_approved', [])
        
        if buffett_approved:
            df = pd.DataFrame(buffett_approved)
            csv_file = f'output/warren_buffett_dcf/buffett_approved_companies_{timestamp}.csv'
            df.to_csv(csv_file, index=False)
            print(f"📁 Buffett approved companies saved to: {csv_file}")
        
        print(f"📁 Full batch results saved to: {results_file}")
    
    def _display_batch_summary(self):
        """Display batch analysis summary"""
        summary_stats = self.batch_results.get('summary_statistics', {})
        top_opportunities = self.batch_results.get('top_opportunities', {})
        valuation_dist = self.batch_results.get('valuation_distribution', {})
        
        print(f"\n" + "=" * 80)
        print("🎯 WARREN BUFFETT STYLE DCF ANALYSIS - BATCH SUMMARY")
        print("=" * 80)
        
        print(f"📊 ANALYSIS OVERVIEW:")
        print(f"   Companies Analyzed: {summary_stats.get('total_companies_analyzed', 0):,}")
        print(f"   Buffett Approved: {summary_stats.get('buffett_approved_count', 0):,}")
        print(f"   Approval Rate: {summary_stats.get('buffett_approval_rate', 0):.1f}%")
        
        print(f"\n💰 VALUATION SUMMARY:")
        margins = summary_stats.get('margins_of_safety', {})
        print(f"   Average Margin of Safety: {margins.get('average', 0):.1f}%")
        print(f"   Median Margin of Safety: {margins.get('median', 0):.1f}%")
        print(f"   Best Margin of Safety: {margins.get('max', 0):.1f}%")
        
        print(f"\n🏆 INVESTMENT OPPORTUNITIES:")
        print(f"   💎 STRONG BUY: {len(top_opportunities.get('strong_buy', []))}")
        print(f"   📈 BUY: {len(top_opportunities.get('buy', []))}")
        print(f"   📊 HOLD: {len(top_opportunities.get('hold', []))}")
        
        print(f"\n📊 VALUATION DISTRIBUTION:")
        total_companies = summary_stats.get('total_companies_analyzed', 1)
        for bucket, count in valuation_dist.items():
            percentage = (count / total_companies) * 100
            print(f"   {bucket.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")
        
        # Show top 5 Buffett approved companies
        buffett_approved = top_opportunities.get('buffett_approved', [])
        if buffett_approved:
            print(f"\n🎯 TOP 5 BUFFETT APPROVED COMPANIES:")
            for i, company in enumerate(buffett_approved[:5], 1):
                ticker = company.get('ticker', 'Unknown')
                score = company.get('buffett_score', 0)
                margin = company.get('margin_of_safety', 0)
                intrinsic = company.get('conservative_intrinsic_value', 0)
                current = company.get('current_price', 0)
                
                print(f"   {i}. {ticker:12s} - Score: {score:.1f} - "
                      f"Intrinsic: ₹{intrinsic:.0f} - "
                      f"Current: ₹{current:.0f} - "
                      f"MoS: {margin:.1f}%")
        
        print(f"\n🎉 Warren Buffett Style Analysis Complete!")
        print(f"💰 Ready for conservative value investing decisions!")
        print("=" * 80)

def main():
    """Main function"""
    print("🎯 BATCH WARREN BUFFETT STYLE DCF ANALYSIS")
    print("=" * 80)
    
    # Initialize and run batch analysis
    batch_analyzer = BatchWarrenBuffettDCF()
    results = batch_analyzer.run_batch_analysis()
    
    if results:
        print(f"\n✅ Batch Warren Buffett DCF Analysis Completed Successfully!")
        print(f"📁 Results available in output/warren_buffett_dcf/")
    else:
        print(f"\n❌ Batch analysis failed to complete")

if __name__ == "__main__":
    main()
