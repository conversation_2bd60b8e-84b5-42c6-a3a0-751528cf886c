#!/usr/bin/env python3
"""
📊 DCF RESULTS VIEWER

Simple script to view DCF analysis results without dashboard dependencies.
Shows both cash flow pre-screening and DCF analysis results.
"""

import os
import json
import glob
from datetime import datetime

def load_latest_results():
    """Load latest analysis results"""
    results = {}
    
    # 1. Check for cash flow pre-screening results
    prescreen_files = glob.glob('output/full_4950_analysis/prescreen_results*.json')
    if prescreen_files:
        latest_prescreen = sorted(prescreen_files)[-1]
        try:
            with open(latest_prescreen, 'r') as f:
                results['cash_flow_prescreen'] = json.load(f)
                results['prescreen_file'] = latest_prescreen
        except Exception as e:
            print(f"Error loading prescreen results: {e}")
    
    # 2. Check for direct DCF results
    dcf_files = glob.glob('output/direct_dcf_analysis/direct_dcf_analysis*.json')
    if dcf_files:
        latest_dcf = sorted(dcf_files)[-1]
        try:
            with open(latest_dcf, 'r') as f:
                results['direct_dcf'] = json.load(f)
                results['dcf_file'] = latest_dcf
        except Exception as e:
            print(f"Error loading DCF results: {e}")
    
    # 3. Check for DCF accuracy test
    accuracy_files = glob.glob('output/dcf_accuracy_test*.json')
    if accuracy_files:
        latest_accuracy = sorted(accuracy_files)[-1]
        try:
            with open(latest_accuracy, 'r') as f:
                results['accuracy_test'] = json.load(f)
                results['accuracy_file'] = latest_accuracy
        except Exception as e:
            print(f"Error loading accuracy test: {e}")
    
    return results

def show_cash_flow_results(results):
    """Show cash flow pre-screening results"""
    if 'cash_flow_prescreen' not in results:
        print("❌ No cash flow pre-screening results found")
        print("   Run: python run_full_4950_analysis.py --phase prescreen")
        return
    
    print("💰 CASH FLOW PRE-SCREENING RESULTS")
    print("=" * 60)
    
    data = results['cash_flow_prescreen']
    stats = data.get('summary_statistics', {})
    
    print(f"📁 Source: {results.get('prescreen_file', 'Unknown')}")
    print(f"📊 Total Companies: {stats.get('total_companies', 0):,}")
    print(f"✅ Qualified: {stats.get('qualified_companies', 0):,}")
    print(f"❌ Failed: {stats.get('failed_companies', 0):,}")
    print(f"📈 Qualification Rate: {stats.get('qualification_rate', 0):.1f}%")
    
    # Show top qualified companies
    company_results = data.get('company_results', {})
    qualified = []
    for ticker, result in company_results.items():
        if result.get('qualified', False):
            qualified.append((ticker, result.get('cashflow_score', 0)))
    
    if qualified:
        qualified.sort(key=lambda x: x[1], reverse=True)
        print(f"\n🏆 TOP 10 CASH FLOW QUALIFIED COMPANIES:")
        for i, (ticker, score) in enumerate(qualified[:10], 1):
            print(f"   {i:2d}. {ticker:12} | Score: {score:5.1f}")

def show_dcf_results(results):
    """Show DCF analysis results"""
    if 'direct_dcf' not in results:
        print("❌ No DCF analysis results found")
        print("   Run: python run_direct_dcf_analysis.py --sample 100")
        return
    
    print("\n🎯 DCF ANALYSIS RESULTS")
    print("=" * 60)
    
    data = results['direct_dcf']
    stats = data.get('summary_statistics', {})
    accuracy = data.get('dcf_accuracy_check', {})
    valuation = data.get('valuation_distribution', {})
    
    print(f"📁 Source: {results.get('dcf_file', 'Unknown')}")
    print(f"📊 Total Analyzed: {stats.get('total_analyzed', 0):,}")
    print(f"✅ Successful: {stats.get('successful_analyses', 0):,}")
    print(f"⭐ Buffett Approved: {stats.get('buffett_approved', 0):,}")
    print(f"📈 Success Rate: {stats.get('success_rate', 0):.1f}%")
    
    # Valuation distribution
    if valuation:
        print(f"\n💰 VALUATION DISTRIBUTION:")
        total_valued = sum(valuation.values())
        if total_valued > 0:
            for category, count in valuation.items():
                percentage = (count / total_valued) * 100
                category_name = category.replace('_', ' ').title()
                print(f"   {category_name:20} | {count:4d} ({percentage:5.1f}%)")
    
    # DCF accuracy metrics
    if accuracy:
        print(f"\n🔍 DCF ACCURACY METRICS:")
        print(f"   Valid DCF Calculations: {accuracy.get('companies_with_valid_dcf', 0):,}")
        print(f"   Avg Enterprise Value: ₹{accuracy.get('average_enterprise_value', 0):,.0f} Cr")
        print(f"   Avg Per Share Value: ₹{accuracy.get('average_per_share_value', 0):,.2f}")
        
        price_analysis = accuracy.get('price_range_analysis', {})
        if price_analysis:
            print(f"   Avg Margin of Safety: {price_analysis.get('average_margin_of_safety', 0):.1f}%")
            print(f"   Undervalued %: {price_analysis.get('undervalued_percentage', 0):.1f}%")
            print(f"   Overvalued %: {price_analysis.get('overvalued_percentage', 0):.1f}%")
    
    # Show top undervalued companies
    company_results = data.get('company_results', {})
    if company_results:
        undervalued = []
        for ticker, result in company_results.items():
            margin = result.get('margin_of_safety', 0)
            if margin > 0:  # Undervalued
                undervalued.append((ticker, margin, result.get('per_share_value', 0), result.get('current_price', 0)))
        
        if undervalued:
            undervalued.sort(key=lambda x: x[1], reverse=True)  # Sort by margin of safety
            print(f"\n🏆 TOP UNDERVALUED COMPANIES:")
            print(f"   {'Ticker':<12} | {'Margin':<8} | {'DCF Value':<10} | {'Current':<10}")
            print(f"   {'-'*12} | {'-'*8} | {'-'*10} | {'-'*10}")
            for ticker, margin, dcf_val, current in undervalued[:10]:
                print(f"   {ticker:<12} | {margin:6.1f}% | ₹{dcf_val:8.2f} | ₹{current:8.2f}")
        else:
            print(f"\n⚠️  NO UNDERVALUED COMPANIES FOUND")
            print(f"   This is normal in current market conditions")

def show_accuracy_test(results):
    """Show DCF accuracy test results"""
    if 'accuracy_test' not in results:
        print("❌ No DCF accuracy test results found")
        print("   Run: python test_dcf_accuracy.py")
        return
    
    print("\n🔍 DCF ACCURACY TEST RESULTS")
    print("=" * 60)
    
    data = results['accuracy_test']
    summary = data.get('summary', {})
    test_results = data.get('results', [])
    
    print(f"📁 Source: {results.get('accuracy_file', 'Unknown')}")
    print(f"📊 Companies Tested: {summary.get('total_companies', 0)}")
    print(f"✅ Valid DCF: {summary.get('valid_dcf', 0)}")
    print(f"📈 Undervalued: {summary.get('undervalued', 0)}")
    print(f"📉 Overvalued: {summary.get('overvalued', 0)}")
    print(f"⭐ Buffett Approved: {summary.get('buffett_approved', 0)}")
    
    if test_results:
        print(f"\n📋 INDIVIDUAL TEST RESULTS:")
        print(f"   {'Ticker':<12} | {'DCF Value':<10} | {'Current':<10} | {'Margin':<8} | {'Status'}")
        print(f"   {'-'*12} | {'-'*10} | {'-'*10} | {'-'*8} | {'-'*12}")
        
        for result in test_results:
            ticker = result.get('ticker', 'N/A')
            dcf_val = result.get('per_share_value', 0)
            current = result.get('current_price', 0)
            margin = result.get('margin_of_safety', 0)
            status = "UNDERVALUED" if margin > 0 else "OVERVALUED"
            
            print(f"   {ticker:<12} | ₹{dcf_val:8.2f} | ₹{current:8.2f} | {margin:6.1f}% | {status}")

def main():
    """Main function"""
    print("📊 DCF ANALYSIS RESULTS VIEWER")
    print("=" * 80)
    print(f"🕒 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Load results
    results = load_latest_results()
    
    if not results:
        print("❌ No analysis results found!")
        print("\n🚀 RUN ANALYSIS FIRST:")
        print("   python run_direct_dcf_analysis.py --sample 100")
        print("   python test_dcf_accuracy.py")
        print("   python run_full_4950_analysis.py --phase prescreen")
        return
    
    # Show results
    show_cash_flow_results(results)
    show_dcf_results(results)
    show_accuracy_test(results)
    
    print("\n" + "=" * 80)
    print("🎯 ANALYSIS COMPLETE!")
    print("\n💡 NEXT STEPS:")
    print("   📊 Run full DCF analysis: python run_direct_dcf_analysis.py")
    print("   🎯 Test specific companies: python run_direct_dcf_analysis.py --tickers TCS,SBIN")
    print("   📈 View dashboard: streamlit run dashboard.py")
    print("=" * 80)

if __name__ == "__main__":
    main()
