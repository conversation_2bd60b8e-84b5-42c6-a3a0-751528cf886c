#!/usr/bin/env python3
"""
Production Analysis System

This script runs the complete enhanced analysis system on all 4903 companies
following the strategic approach:
1. Cash flow pre-screening (filter out weak companies)
2. Comprehensive analysis for companies that pass
3. Sector-wise investment recommendations
"""

import os
import sys
import json
import logging
import pandas as pd
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules
from utils.data_loader import ScreenerDataLoader
from models.cashflow_prescreener import CashFlowPreScreener
from models.sector_analyzer import SectorAnalyzer
from models.qualitative_analyzer import QualitativeAnalyzer
from models.consistency_analyzer import ConsistencyAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('production_analysis')

class ProductionAnalyzer:
    """
    Production-ready comprehensive analysis system
    """
    
    def __init__(self, data_path: str = '../screener_data_collector/data'):
        """
        Initialize the production analyzer
        """
        self.data_loader = ScreenerDataLoader(data_path)
        self.prescreener = CashFlowPreScreener()
        self.sector_analyzer = SectorAnalyzer()
        self.qualitative_analyzer = QualitativeAnalyzer()
        self.consistency_analyzer = ConsistencyAnalyzer()
        
        # Load all tickers
        self.all_tickers = self.data_loader.get_all_tickers()
        logger.info(f"Loaded {len(self.all_tickers)} companies for analysis")
    
    def run_complete_analysis(self, 
                            max_workers: int = 6,
                            batch_size: int = 100,
                            save_intermediate: bool = True) -> Dict[str, Any]:
        """
        Run complete analysis on all companies
        
        Parameters:
        -----------
        max_workers : int
            Number of parallel workers
        batch_size : int
            Batch size for processing
        save_intermediate : bool
            Whether to save intermediate results
            
        Returns:
        --------
        Complete analysis results
        """
        logger.info("Starting production analysis of all companies")
        
        results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_companies': len(self.all_tickers),
            'stage_1_cashflow_prescreen': {},
            'stage_2_comprehensive_analysis': {},
            'stage_3_sector_analysis': {},
            'stage_4_investment_recommendations': {},
            'summary_statistics': {}
        }
        
        # Stage 1: Cash Flow Pre-screening
        logger.info("Stage 1: Running cash flow pre-screening on all companies")
        prescreen_results = self._run_cashflow_prescreening(max_workers, batch_size, save_intermediate)
        results['stage_1_cashflow_prescreen'] = prescreen_results
        
        # Get companies that passed pre-screening
        passed_companies = [ticker for ticker, result in prescreen_results['results'].items() 
                           if result.get('passed_prescreen', False)]
        
        logger.info(f"Stage 1 Complete: {len(passed_companies)}/{len(self.all_tickers)} companies passed cash flow pre-screening")
        
        # Stage 2: Comprehensive Analysis for qualified companies
        if passed_companies:
            logger.info(f"Stage 2: Running comprehensive analysis on {len(passed_companies)} qualified companies")
            comprehensive_results = self._run_comprehensive_analysis(passed_companies, max_workers, batch_size)
            results['stage_2_comprehensive_analysis'] = comprehensive_results
            
            # Stage 3: Sector Analysis
            logger.info("Stage 3: Performing sector analysis and discovery")
            sector_results = self._run_sector_analysis(comprehensive_results['results'])
            results['stage_3_sector_analysis'] = sector_results
            
            # Stage 4: Investment Recommendations
            logger.info("Stage 4: Generating investment recommendations")
            recommendations = self._generate_investment_recommendations(
                comprehensive_results['results'], sector_results
            )
            results['stage_4_investment_recommendations'] = recommendations
        
        # Generate summary statistics
        results['summary_statistics'] = self._generate_summary_statistics(results)
        
        logger.info("Production analysis completed successfully")
        return results
    
    def _run_cashflow_prescreening(self, max_workers: int, batch_size: int, save_intermediate: bool) -> Dict[str, Any]:
        """
        Run cash flow pre-screening on all companies
        """
        prescreen_results = {
            'total_companies': len(self.all_tickers),
            'companies_passed': 0,
            'pass_rate': 0,
            'results': {}
        }
        
        # Process in batches
        for i in range(0, len(self.all_tickers), batch_size):
            batch_tickers = self.all_tickers[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(self.all_tickers) + batch_size - 1) // batch_size
            
            logger.info(f"Pre-screening batch {batch_num}/{total_batches} ({len(batch_tickers)} companies)")
            
            # Process batch in parallel
            batch_results = self._process_prescreen_batch(batch_tickers, max_workers)
            prescreen_results['results'].update(batch_results)
            
            # Update statistics
            passed_in_batch = sum(1 for r in batch_results.values() if r.get('passed_prescreen', False))
            prescreen_results['companies_passed'] += passed_in_batch
            
            # Save intermediate results
            if save_intermediate and batch_num % 10 == 0:
                self._save_intermediate_prescreen_results(prescreen_results, batch_num)
            
            logger.info(f"Batch {batch_num} complete: {passed_in_batch}/{len(batch_tickers)} passed")
        
        # Calculate final statistics
        prescreen_results['pass_rate'] = (prescreen_results['companies_passed'] / 
                                        prescreen_results['total_companies']) * 100
        
        return prescreen_results
    
    def _process_prescreen_batch(self, tickers: List[str], max_workers: int) -> Dict[str, Any]:
        """
        Process a batch of companies for cash flow pre-screening
        """
        batch_results = {}
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_ticker = {
                executor.submit(self._prescreen_single_company, ticker): ticker
                for ticker in tickers
            }
            
            # Collect results
            for future in as_completed(future_to_ticker):
                ticker = future_to_ticker[future]
                try:
                    result = future.result()
                    batch_results[ticker] = result
                except Exception as e:
                    logger.error(f"Error pre-screening {ticker}: {e}")
                    batch_results[ticker] = {'error': str(e), 'passed_prescreen': False}
        
        return batch_results
    
    def _prescreen_single_company(self, ticker: str) -> Dict[str, Any]:
        """
        Pre-screen a single company
        """
        try:
            company_data = self.data_loader.load_company_data(ticker)
            if not company_data:
                return {'error': 'No data found', 'passed_prescreen': False}
            
            result = self.prescreener.prescreen_company(company_data)
            return result
            
        except Exception as e:
            logger.error(f"Error pre-screening {ticker}: {e}")
            return {'error': str(e), 'passed_prescreen': False}
    
    def _run_comprehensive_analysis(self, qualified_tickers: List[str], max_workers: int, batch_size: int) -> Dict[str, Any]:
        """
        Run comprehensive analysis on qualified companies
        """
        comprehensive_results = {
            'total_qualified': len(qualified_tickers),
            'companies_analyzed': 0,
            'results': {}
        }
        
        # Process in batches
        for i in range(0, len(qualified_tickers), batch_size):
            batch_tickers = qualified_tickers[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(qualified_tickers) + batch_size - 1) // batch_size
            
            logger.info(f"Comprehensive analysis batch {batch_num}/{total_batches} ({len(batch_tickers)} companies)")
            
            # Process batch
            batch_results = self._process_comprehensive_batch(batch_tickers, max_workers)
            comprehensive_results['results'].update(batch_results)
            comprehensive_results['companies_analyzed'] += len(batch_results)
            
            logger.info(f"Comprehensive batch {batch_num} complete")
        
        return comprehensive_results
    
    def _process_comprehensive_batch(self, tickers: List[str], max_workers: int) -> Dict[str, Any]:
        """
        Process a batch of companies for comprehensive analysis
        """
        batch_results = {}
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_ticker = {
                executor.submit(self._analyze_single_company_comprehensive, ticker): ticker
                for ticker in tickers
            }
            
            # Collect results
            for future in as_completed(future_to_ticker):
                ticker = future_to_ticker[future]
                try:
                    result = future.result()
                    batch_results[ticker] = result
                except Exception as e:
                    logger.error(f"Error in comprehensive analysis for {ticker}: {e}")
                    batch_results[ticker] = {'error': str(e)}
        
        return batch_results
    
    def _analyze_single_company_comprehensive(self, ticker: str) -> Dict[str, Any]:
        """
        Perform comprehensive analysis on a single company
        """
        try:
            company_data = self.data_loader.load_company_data(ticker)
            if not company_data:
                return {'error': 'No data found'}
            
            result = {
                'ticker': ticker,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            # Sector classification
            sector = self.sector_analyzer.classify_sector(company_data)
            result['sector'] = sector
            
            # Consistency analysis
            consistency_result = self.consistency_analyzer.analyze_company_consistency(company_data)
            result['consistency'] = consistency_result
            result['consistency_score'] = consistency_result.get('overall_consistency_score', 0)
            
            # Qualitative analysis
            qualitative_result = self.qualitative_analyzer.analyze_company_qualitative_factors(
                company_data, sector=sector
            )
            result['qualitative'] = qualitative_result
            result['qualitative_score'] = qualitative_result.get('qualitative_score', 0)
            
            # Calculate overall investment score
            # Weight: Consistency 60%, Qualitative 40% (cash flow already pre-screened)
            overall_score = (result['consistency_score'] * 0.6 + 
                           result['qualitative_score'] * 0.4)
            result['overall_investment_score'] = overall_score
            
            # Generate recommendation
            if overall_score >= 70:
                result['recommendation'] = 'STRONG_BUY'
            elif overall_score >= 60:
                result['recommendation'] = 'BUY'
            elif overall_score >= 50:
                result['recommendation'] = 'HOLD'
            else:
                result['recommendation'] = 'WEAK'
            
            return result
            
        except Exception as e:
            logger.error(f"Error in comprehensive analysis for {ticker}: {e}")
            return {'error': str(e)}
    
    def _run_sector_analysis(self, comprehensive_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run sector analysis on all analyzed companies
        """
        # Prepare data for sector analysis
        companies_data = {}
        for ticker, result in comprehensive_results.items():
            if not result.get('error'):
                # Load company data for sector analysis
                company_data = self.data_loader.load_company_data(ticker)
                if company_data:
                    companies_data[ticker] = company_data
        
        # Discover sectors and analyze
        sector_discovery = self.sector_analyzer.discover_all_sectors_and_players(companies_data)
        
        # Export sector mapping
        sector_mapping_file = self.sector_analyzer.export_sector_mapping(sector_discovery)
        
        return {
            'sector_discovery': sector_discovery,
            'sector_mapping_file': sector_mapping_file,
            'total_sectors': len(sector_discovery['sectors_found']),
            'companies_classified': len(companies_data)
        }
    
    def _generate_investment_recommendations(self, 
                                           comprehensive_results: Dict[str, Any],
                                           sector_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate final investment recommendations
        """
        recommendations = {
            'top_picks': {
                'strong_buy': [],
                'buy': [],
                'hold': []
            },
            'sector_leaders': {},
            'investment_themes': [],
            'risk_warnings': []
        }
        
        # Collect all valid results
        valid_results = [(ticker, result) for ticker, result in comprehensive_results.items() 
                        if not result.get('error') and result.get('overall_investment_score', 0) > 0]
        
        # Sort by investment score
        valid_results.sort(key=lambda x: x[1]['overall_investment_score'], reverse=True)
        
        # Categorize recommendations
        for ticker, result in valid_results:
            recommendation = result.get('recommendation', 'WEAK')
            score = result.get('overall_investment_score', 0)
            sector = result.get('sector', 'unknown')
            
            company_info = {
                'ticker': ticker,
                'sector': sector,
                'score': score,
                'consistency_score': result.get('consistency_score', 0),
                'qualitative_score': result.get('qualitative_score', 0)
            }
            
            if recommendation == 'STRONG_BUY':
                recommendations['top_picks']['strong_buy'].append(company_info)
            elif recommendation == 'BUY':
                recommendations['top_picks']['buy'].append(company_info)
            elif recommendation == 'HOLD':
                recommendations['top_picks']['hold'].append(company_info)
        
        # Generate sector leaders
        sectors_found = sector_results.get('sector_discovery', {}).get('sectors_found', {})
        for sector, companies in sectors_found.items():
            sector_companies = [(ticker, result) for ticker, result in valid_results 
                              if result.get('sector') == sector]
            
            if sector_companies:
                # Sort by score and take top 3
                sector_companies.sort(key=lambda x: x[1]['overall_investment_score'], reverse=True)
                recommendations['sector_leaders'][sector] = [
                    {
                        'ticker': ticker,
                        'score': result['overall_investment_score'],
                        'recommendation': result.get('recommendation', 'WEAK')
                    }
                    for ticker, result in sector_companies[:3]
                ]
        
        # Generate investment themes
        strong_buy_count = len(recommendations['top_picks']['strong_buy'])
        buy_count = len(recommendations['top_picks']['buy'])
        
        if strong_buy_count > 0:
            recommendations['investment_themes'].append(
                f"Found {strong_buy_count} high-conviction investment opportunities"
            )
        
        if buy_count > 0:
            recommendations['investment_themes'].append(
                f"Identified {buy_count} solid investment candidates"
            )
        
        # Add sector themes
        top_sectors = sorted(recommendations['sector_leaders'].items(), 
                           key=lambda x: len(x[1]), reverse=True)[:3]
        
        for sector, leaders in top_sectors:
            if len(leaders) >= 2:
                recommendations['investment_themes'].append(
                    f"{sector.title()} sector shows strong fundamentals with multiple opportunities"
                )
        
        return recommendations
    
    def _generate_summary_statistics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate summary statistics
        """
        prescreen_results = results.get('stage_1_cashflow_prescreen', {})
        comprehensive_results = results.get('stage_2_comprehensive_analysis', {})
        recommendations = results.get('stage_4_investment_recommendations', {})
        
        summary = {
            'total_companies_analyzed': results['total_companies'],
            'cashflow_prescreen_pass_rate': prescreen_results.get('pass_rate', 0),
            'companies_passed_prescreen': prescreen_results.get('companies_passed', 0),
            'companies_comprehensive_analysis': comprehensive_results.get('companies_analyzed', 0),
            'investment_recommendations': {
                'strong_buy': len(recommendations.get('top_picks', {}).get('strong_buy', [])),
                'buy': len(recommendations.get('top_picks', {}).get('buy', [])),
                'hold': len(recommendations.get('top_picks', {}).get('hold', []))
            },
            'sectors_analyzed': len(recommendations.get('sector_leaders', {})),
            'analysis_completion_time': datetime.now().isoformat()
        }
        
        return summary
    
    def _save_intermediate_prescreen_results(self, results: Dict[str, Any], batch_num: int):
        """
        Save intermediate pre-screening results
        """
        output_dir = 'output/production_analysis'
        os.makedirs(output_dir, exist_ok=True)
        
        filename = f"{output_dir}/prescreen_intermediate_batch_{batch_num}.json"
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Intermediate pre-screening results saved to {filename}")

def main():
    """
    Main function to run production analysis
    """
    print("=" * 100)
    print("PRODUCTION ANALYSIS - COMPREHENSIVE INVESTMENT SCREENING SYSTEM")
    print("=" * 100)
    print("Strategy: Cash Flow Pre-screening → Comprehensive Analysis → Investment Recommendations")
    print("=" * 100)
    
    # Initialize analyzer
    analyzer = ProductionAnalyzer()
    
    # Run complete analysis
    results = analyzer.run_complete_analysis(
        max_workers=8,  # Adjust based on your system
        batch_size=50,  # Smaller batches for better progress tracking
        save_intermediate=True
    )
    
    # Save final results
    output_dir = 'output/production_analysis'
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    final_output = f"{output_dir}/production_analysis_final_{timestamp}.json"
    
    with open(final_output, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Print summary
    summary = results['summary_statistics']
    recommendations = results['stage_4_investment_recommendations']
    
    print(f"\n" + "=" * 100)
    print("PRODUCTION ANALYSIS COMPLETED SUCCESSFULLY!")
    print("=" * 100)
    
    print(f"📊 ANALYSIS SUMMARY:")
    print(f"   Total Companies Analyzed: {summary['total_companies_analyzed']}")
    print(f"   Cash Flow Pre-screen Pass Rate: {summary['cashflow_prescreen_pass_rate']:.1f}%")
    print(f"   Companies Passed Pre-screening: {summary['companies_passed_prescreen']}")
    print(f"   Comprehensive Analysis Completed: {summary['companies_comprehensive_analysis']}")
    
    print(f"\n🎯 INVESTMENT RECOMMENDATIONS:")
    inv_recs = summary['investment_recommendations']
    print(f"   STRONG BUY: {inv_recs['strong_buy']} companies")
    print(f"   BUY: {inv_recs['buy']} companies")
    print(f"   HOLD: {inv_recs['hold']} companies")
    
    print(f"\n🏆 TOP 10 INVESTMENT OPPORTUNITIES:")
    top_picks = recommendations.get('top_picks', {})
    all_picks = (top_picks.get('strong_buy', []) + 
                top_picks.get('buy', []) + 
                top_picks.get('hold', []))
    
    for i, pick in enumerate(all_picks[:10], 1):
        ticker = pick['ticker']
        sector = pick['sector']
        score = pick['score']
        print(f"   {i:2d}. {ticker:12s} ({sector:15s}) - Score: {score:.1f}")
    
    print(f"\n📁 Results saved to: {final_output}")
    print(f"🎉 Ready for investment decision making!")
    print("=" * 100)

if __name__ == "__main__":
    main()
