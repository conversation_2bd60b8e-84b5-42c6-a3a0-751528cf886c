#!/usr/bin/env python3
"""
Analysis Results Viewer

This script provides detailed views of the analysis results including:
1. List of 1245 companies that passed cash flow screening
2. Detailed breakdown of recommendations (STRONG_BUY, BUY, HOLD)
3. Transparent scoring methodology
4. Sector-wise analysis
"""

import os
import sys
import json
import pandas as pd
from typing import Dict, List, Any
import argparse

def load_latest_results():
    """Load the latest production analysis results"""
    results_dir = 'output/production_analysis'
    
    if not os.path.exists(results_dir):
        print("❌ No production analysis results found")
        print("💡 Run: python production_analysis.py")
        return None
    
    # Find latest results file
    result_files = [f for f in os.listdir(results_dir) if f.startswith('production_analysis_final_')]
    
    if not result_files:
        print("❌ No production analysis files found")
        return None
    
    latest_file = sorted(result_files)[-1]
    file_path = os.path.join(results_dir, latest_file)
    
    print(f"📁 Loading results from: {latest_file}")
    
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error loading results: {e}")
        return None

def show_cashflow_passed_companies(results: Dict[str, Any]):
    """Show all companies that passed cash flow screening"""
    print("🔍 COMPANIES THAT PASSED CASH FLOW PRE-SCREENING")
    print("=" * 80)
    
    cashflow_results = results.get('stage_1_cashflow_prescreen', {}).get('results', {})
    
    # Filter companies that passed
    passed_companies = []
    for ticker, result in cashflow_results.items():
        if result.get('passed_prescreen', False):
            passed_companies.append({
                'ticker': ticker,
                'cash_flow_score': result.get('cash_flow_score', 0),
                'reasons': result.get('reasons_passed', [])
            })
    
    # Sort by cash flow score
    passed_companies.sort(key=lambda x: x['cash_flow_score'], reverse=True)
    
    print(f"📊 Total Companies Passed: {len(passed_companies)}")
    print(f"📈 Average Cash Flow Score: {sum(c['cash_flow_score'] for c in passed_companies) / len(passed_companies):.1f}")
    
    # Show top 50 companies
    print(f"\n🏆 TOP 50 COMPANIES BY CASH FLOW SCORE:")
    print("-" * 80)
    print(f"{'Rank':<4} {'Ticker':<12} {'Score':<6} {'Reasons'}")
    print("-" * 80)
    
    for i, company in enumerate(passed_companies[:50], 1):
        ticker = company['ticker']
        score = company['cash_flow_score']
        reasons = ', '.join(company['reasons'][:2])  # Show first 2 reasons
        print(f"{i:<4} {ticker:<12} {score:<6.1f} {reasons}")
    
    # Save complete list to CSV
    df = pd.DataFrame(passed_companies)
    output_file = 'output/cashflow_passed_companies.csv'
    df.to_csv(output_file, index=False)
    print(f"\n📁 Complete list saved to: {output_file}")
    
    return passed_companies

def show_investment_recommendations(results: Dict[str, Any]):
    """Show detailed investment recommendations with transparent scoring"""
    print("\n🎯 INVESTMENT RECOMMENDATIONS - TRANSPARENT SCORING")
    print("=" * 80)
    
    recommendations = results.get('stage_4_investment_recommendations', {})
    top_picks = recommendations.get('top_picks', {})
    
    # Combine all recommendations
    all_recommendations = []
    
    for category in ['strong_buy', 'buy', 'hold']:
        companies = top_picks.get(category, [])
        for company in companies:
            company['category'] = category.upper()
            all_recommendations.append(company)
    
    # Sort by score
    all_recommendations.sort(key=lambda x: x.get('score', 0), reverse=True)
    
    print(f"💎 STRONG BUY: {len(top_picks.get('strong_buy', []))} companies")
    print(f"📈 BUY: {len(top_picks.get('buy', []))} companies")
    print(f"📊 HOLD: {len(top_picks.get('hold', []))} companies")
    
    print(f"\n🏆 DETAILED RECOMMENDATIONS:")
    print("-" * 120)
    print(f"{'Rank':<4} {'Ticker':<12} {'Sector':<15} {'Score':<6} {'Consistency':<11} {'Qualitative':<11} {'Recommendation':<12}")
    print("-" * 120)
    
    for i, rec in enumerate(all_recommendations[:30], 1):  # Show top 30
        ticker = rec.get('ticker', 'Unknown')
        sector = rec.get('sector', 'unknown')[:14]  # Truncate sector name
        score = rec.get('score', 0)
        consistency = rec.get('consistency_score', 0)
        qualitative = rec.get('qualitative_score', 0)
        category = rec.get('category', 'UNKNOWN')
        
        print(f"{i:<4} {ticker:<12} {sector:<15} {score:<6.1f} {consistency:<11.1f} {qualitative:<11.1f} {category:<12}")
    
    # Save recommendations to CSV
    df = pd.DataFrame(all_recommendations)
    output_file = 'output/investment_recommendations.csv'
    df.to_csv(output_file, index=False)
    print(f"\n📁 Recommendations saved to: {output_file}")
    
    return all_recommendations

def explain_scoring_methodology():
    """Explain the transparent scoring methodology"""
    print("\n📚 SCORING METHODOLOGY - TRANSPARENT AND TRACEABLE")
    print("=" * 80)
    
    print("🔍 STAGE 1: CASH FLOW PRE-SCREENING (10 YEARS)")
    print("   Purpose: Filter out companies with weak cash generation")
    print("   Criteria:")
    print("   • Operating Cash Flow Consistency: 5+ positive years out of 10")
    print("   • Cash Flow Quality: OCF to Net Income ratio > 80%")
    print("   • Growth Trend: Positive cash flow growth trend")
    print("   • Predictability: R-squared > 0.5 for trend analysis")
    print("   Score Range: 0-100 (Pass threshold: 40+)")
    
    print("\n📈 STAGE 2: CONSISTENCY ANALYSIS")
    print("   Purpose: Evaluate historical performance stability")
    print("   Components:")
    print("   • Growth Consistency (50% weight): Revenue/profit growth patterns")
    print("   • Quarterly Consistency (20% weight): Quarterly performance stability")
    print("   • Profitability Consistency (20% weight): Margin stability")
    print("   • Sector Benchmarking (10% weight): Relative to sector peers")
    print("   Score Range: 0-100")
    
    print("\n🎯 STAGE 3: QUALITATIVE ANALYSIS")
    print("   Purpose: Assess non-financial factors")
    print("   Components:")
    print("   • Management Quality (25% weight): Track record, execution")
    print("   • Business Model (30% weight): Competitive moat, scalability")
    print("   • Corporate Governance (20% weight): Board independence, transparency")
    print("   • Industry Dynamics (15% weight): Sector growth, competition")
    print("   • ESG Factors (10% weight): Environmental, social, governance")
    print("   Score Range: 0-100")
    
    print("\n🏆 FINAL RECOMMENDATION LOGIC")
    print("   Combined Score = Consistency (60%) + Qualitative (40%)")
    print("   • STRONG BUY: Combined Score ≥ 70 (High conviction)")
    print("   • BUY: Combined Score 60-70 (Solid opportunity)")
    print("   • HOLD: Combined Score 50-60 (Moderate opportunity)")
    print("   • AVOID: Combined Score < 50 (Weak fundamentals)")
    
    print("\n💡 WHY THIS METHODOLOGY:")
    print("   ✅ Cash flow first: Only companies that generate real cash")
    print("   ✅ Historical consistency: Predictable performance matters")
    print("   ✅ Qualitative factors: Beyond numbers analysis")
    print("   ✅ Sector awareness: Industry-specific considerations")
    print("   ✅ Transparent scoring: Every score component is traceable")

def show_sector_analysis(results: Dict[str, Any]):
    """Show sector-wise analysis"""
    print("\n🏭 SECTOR-WISE ANALYSIS")
    print("=" * 80)
    
    sector_results = results.get('stage_3_sector_analysis', {})
    sector_discovery = sector_results.get('sector_discovery', {})
    sectors_found = sector_discovery.get('sectors_found', {})
    
    if not sectors_found:
        print("❌ No sector analysis data available")
        return
    
    # Get recommendations by sector
    recommendations = results.get('stage_4_investment_recommendations', {})
    sector_leaders = recommendations.get('sector_leaders', {})
    
    print(f"📊 Total Sectors Analyzed: {len(sectors_found)}")
    
    # Sort sectors by company count
    sorted_sectors = sorted(sectors_found.items(), key=lambda x: len(x[1]), reverse=True)
    
    print(f"\n🏆 SECTOR BREAKDOWN:")
    print("-" * 80)
    print(f"{'Rank':<4} {'Sector':<20} {'Companies':<10} {'Top Performers'}")
    print("-" * 80)
    
    for i, (sector, companies) in enumerate(sorted_sectors[:15], 1):  # Top 15 sectors
        sector_display = sector.replace('_', ' ').title()
        company_count = len(companies)
        
        # Get top performers in this sector
        leaders = sector_leaders.get(sector, [])
        top_performers = ', '.join([leader['ticker'] for leader in leaders[:3]])
        
        print(f"{i:<4} {sector_display:<20} {company_count:<10} {top_performers}")

def show_detailed_company_analysis(results: Dict[str, Any], ticker: str):
    """Show detailed analysis for a specific company"""
    print(f"\n🔍 DETAILED ANALYSIS FOR {ticker.upper()}")
    print("=" * 80)
    
    # Cash flow analysis
    cashflow_results = results.get('stage_1_cashflow_prescreen', {}).get('results', {})
    if ticker in cashflow_results:
        cf_result = cashflow_results[ticker]
        print(f"💰 CASH FLOW ANALYSIS:")
        print(f"   Score: {cf_result.get('cash_flow_score', 0):.1f}")
        print(f"   Passed: {'✅ YES' if cf_result.get('passed_prescreen', False) else '❌ NO'}")
        
        analysis = cf_result.get('analysis', {})
        ocf = analysis.get('operating_cash_flow', {})
        if ocf:
            print(f"   OCF Positive Years: {ocf.get('positive_percentage', 0):.1f}%")
            print(f"   OCF Growth Trend: {ocf.get('trend_slope', 0):.1f}")
            print(f"   Latest OCF: ₹{ocf.get('latest_ocf', 0):,.0f} Cr")
    
    # Comprehensive analysis
    comprehensive_results = results.get('stage_2_comprehensive_analysis', {}).get('results', {})
    if ticker in comprehensive_results:
        comp_result = comprehensive_results[ticker]
        print(f"\n📈 COMPREHENSIVE ANALYSIS:")
        print(f"   Sector: {comp_result.get('sector', 'Unknown')}")
        print(f"   Consistency Score: {comp_result.get('consistency_score', 0):.1f}")
        print(f"   Qualitative Score: {comp_result.get('qualitative_score', 0):.1f}")
        print(f"   Overall Score: {comp_result.get('overall_investment_score', 0):.1f}")
        print(f"   Recommendation: {comp_result.get('recommendation', 'Unknown')}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Analysis Results Viewer")
    parser.add_argument('command', nargs='?', 
                       choices=['passed', 'recommendations', 'methodology', 'sectors', 'company'],
                       default='passed',
                       help='What to display')
    parser.add_argument('--ticker', type=str, help='Ticker for detailed company analysis')
    
    args = parser.parse_args()
    
    # Load results
    results = load_latest_results()
    if not results:
        return
    
    print(f"📊 Analysis Date: {results.get('analysis_timestamp', 'Unknown')}")
    print(f"📈 Total Companies: {results.get('total_companies', 0):,}")
    
    if args.command == 'passed':
        show_cashflow_passed_companies(results)
        
    elif args.command == 'recommendations':
        show_investment_recommendations(results)
        
    elif args.command == 'methodology':
        explain_scoring_methodology()
        
    elif args.command == 'sectors':
        show_sector_analysis(results)
        
    elif args.command == 'company':
        if args.ticker:
            show_detailed_company_analysis(results, args.ticker)
        else:
            print("❌ Please provide --ticker for company analysis")
    
    print(f"\n💡 Usage Examples:")
    print(f"   python view_analysis_results.py passed          # Show 1245 companies that passed")
    print(f"   python view_analysis_results.py recommendations # Show investment recommendations")
    print(f"   python view_analysis_results.py methodology     # Explain scoring methodology")
    print(f"   python view_analysis_results.py sectors         # Show sector analysis")
    print(f"   python view_analysis_results.py company --ticker TCS  # Detailed company analysis")

if __name__ == "__main__":
    main()
