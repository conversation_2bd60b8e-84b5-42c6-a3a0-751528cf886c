#!/usr/bin/env python3
"""
🔧 MODULAR ANALYSIS MANAGER

This script manages modular analysis results:
- Result_1: Cash Flow Analysis
- Result_2: DCF Analysis  
- Result_12: DCF on Cash Flow Qualified
- Result_21: Cash Flow on DCF Results
- And more combinations...
"""

import os
import json
import glob
from datetime import datetime
from pathlib import Path

class ModularAnalysisManager:
    """
    Manager for modular analysis results and combinations
    """
    
    def __init__(self):
        self.results_dir = Path('output/modular_results')
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        print("🔧 MODULAR ANALYSIS MANAGER")
        print("=" * 80)
        print(f"📁 Results Directory: {self.results_dir}")
        print("=" * 80)
    
    def list_available_results(self):
        """List all available analysis results"""
        results = {}
        
        # Scan for result files
        for file_path in self.results_dir.glob('Result_*.json'):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                metadata = data.get('analysis_metadata', {})
                result_id = metadata.get('result_id', file_path.stem)
                
                results[result_id] = {
                    'file_path': str(file_path),
                    'timestamp': metadata.get('timestamp', 'Unknown'),
                    'analysis_type': metadata.get('analysis_type', 'Unknown'),
                    'description': metadata.get('description', 'No description'),
                    'total_companies': metadata.get('total_companies', 0),
                    'file_size': file_path.stat().st_size
                }
            except Exception as e:
                print(f"❌ Error reading {file_path}: {e}")
        
        return results
    
    def load_result(self, result_id):
        """Load a specific result by ID"""
        results = self.list_available_results()
        
        if result_id not in results:
            print(f"❌ Result {result_id} not found")
            return None
        
        file_path = results[result_id]['file_path']
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error loading {result_id}: {e}")
            return None
    
    def get_qualified_tickers(self, result_id, criteria=None):
        """Get qualified tickers from a result"""
        data = self.load_result(result_id)
        if not data:
            return []
        
        company_results = data.get('company_results', {})
        qualified_tickers = []
        
        for ticker, result in company_results.items():
            if criteria:
                # Apply custom criteria
                if criteria(ticker, result):
                    qualified_tickers.append(ticker)
            else:
                # Default qualification
                if result.get('qualified', False) or result.get('buffett_approved', False):
                    qualified_tickers.append(ticker)
        
        return qualified_tickers
    
    def create_combination_analysis(self, base_result_id, filter_result_id, combination_type="intersection"):
        """Create combination analysis (e.g., Result_12, Result_21)"""
        
        print(f"🔧 Creating combination: {base_result_id} + {filter_result_id}")
        
        # Load both results
        base_data = self.load_result(base_result_id)
        filter_data = self.load_result(filter_result_id)
        
        if not base_data or not filter_data:
            print("❌ Cannot load required results")
            return None
        
        # Get qualified tickers from filter result
        filter_tickers = set(self.get_qualified_tickers(filter_result_id))
        
        # Apply filter to base result
        base_company_results = base_data.get('company_results', {})
        filtered_results = {}
        
        for ticker, result in base_company_results.items():
            if combination_type == "intersection":
                # Only include if ticker is in both results
                if ticker in filter_tickers:
                    filtered_results[ticker] = result
            elif combination_type == "union":
                # Include all from base, mark if also in filter
                result['also_in_filter'] = ticker in filter_tickers
                filtered_results[ticker] = result
        
        # Create combination result
        combination_id = f"Result_{base_result_id.split('_')[1]}{filter_result_id.split('_')[1]}"
        
        combination_result = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'result_id': combination_id,
                'analysis_type': 'combination_analysis',
                'description': f"Combination of {base_result_id} and {filter_result_id}",
                'base_result': base_result_id,
                'filter_result': filter_result_id,
                'combination_type': combination_type,
                'total_companies': len(filtered_results)
            },
            'company_results': filtered_results,
            'summary_statistics': {
                'total_companies': len(filtered_results),
                'base_companies': len(base_company_results),
                'filter_companies': len(filter_tickers),
                'combination_rate': (len(filtered_results) / len(base_company_results)) * 100 if base_company_results else 0
            }
        }
        
        # Save combination result
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'{combination_id}_Combination_{timestamp}.json'
        filepath = self.results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(combination_result, f, indent=2)
        
        print(f"✅ Combination saved as {combination_id}: {filepath}")
        return combination_result
    
    def create_filtered_analysis(self, result_id, filter_criteria, new_result_id):
        """Create filtered analysis based on custom criteria"""
        
        print(f"🔍 Creating filtered analysis: {result_id} → {new_result_id}")
        
        # Load base result
        base_data = self.load_result(result_id)
        if not base_data:
            return None
        
        # Apply filter
        base_company_results = base_data.get('company_results', {})
        filtered_results = {}
        
        for ticker, result in base_company_results.items():
            if filter_criteria(ticker, result):
                filtered_results[ticker] = result
        
        # Create filtered result
        filtered_result = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'result_id': new_result_id,
                'analysis_type': 'filtered_analysis',
                'description': f"Filtered analysis from {result_id}",
                'base_result': result_id,
                'total_companies': len(filtered_results)
            },
            'company_results': filtered_results,
            'summary_statistics': {
                'total_companies': len(filtered_results),
                'base_companies': len(base_company_results),
                'filter_rate': (len(filtered_results) / len(base_company_results)) * 100 if base_company_results else 0
            }
        }
        
        # Save filtered result
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'{new_result_id}_Filtered_{timestamp}.json'
        filepath = self.results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(filtered_result, f, indent=2)
        
        print(f"✅ Filtered analysis saved as {new_result_id}: {filepath}")
        return filtered_result
    
    def print_results_summary(self):
        """Print summary of all available results"""
        results = self.list_available_results()
        
        print(f"\n📊 AVAILABLE ANALYSIS RESULTS")
        print("=" * 80)
        
        if not results:
            print("❌ No analysis results found")
            print("   Run analysis first:")
            print("   - python run_cashflow_analysis.py  (creates Result_1)")
            print("   - python run_direct_dcf_analysis.py  (creates Result_2)")
            return
        
        for result_id, info in sorted(results.items()):
            print(f"\n🎯 {result_id}:")
            print(f"   Type: {info['analysis_type']}")
            print(f"   Description: {info['description']}")
            print(f"   Companies: {info['total_companies']:,}")
            print(f"   Created: {info['timestamp'][:19]}")
            print(f"   File: {Path(info['file_path']).name}")
        
        print(f"\n💡 COMBINATION EXAMPLES:")
        print(f"   Result_12: DCF analysis on cash flow qualified companies")
        print(f"   Result_21: Cash flow analysis on DCF results")
        print(f"   Custom filters: Undervalued only, Overvalued only, etc.")
        
        print("=" * 80)

def main():
    """Main function for interactive use"""
    manager = ModularAnalysisManager()
    
    # Show available results
    manager.print_results_summary()
    
    # Example combinations
    results = manager.list_available_results()
    
    if 'Result_1' in results and 'Result_2' in results:
        print(f"\n🔧 CREATING EXAMPLE COMBINATIONS...")
        
        # Create Result_12: DCF on Cash Flow qualified
        result_12 = manager.create_combination_analysis('Result_2', 'Result_1')
        
        # Create Result_21: Cash Flow on DCF qualified  
        result_21 = manager.create_combination_analysis('Result_1', 'Result_2')
        
        print(f"\n✅ Example combinations created!")
    
    else:
        print(f"\n💡 TO CREATE COMBINATIONS:")
        print(f"   1. Run: python run_cashflow_analysis.py  (creates Result_1)")
        print(f"   2. Run: python run_direct_dcf_analysis.py  (creates Result_2)")
        print(f"   3. Run: python modular_analysis_manager.py  (creates combinations)")

if __name__ == "__main__":
    main()
