#!/usr/bin/env python3
"""
🎯 DIRECT DCF ANALYSIS - Skip Cash Flow Pre-screening

This script runs Warren Buffett style DCF analysis directly on ALL companies
without cash flow pre-screening, allowing you to:

1. Test DCF calculations on all 4903 companies
2. Compare results with cash flow pre-screening
3. Verify DCF accuracy and pricing
4. Save results for dashboard viewing

Usage:
    python run_direct_dcf_analysis.py                    # All companies
    python run_direct_dcf_analysis.py --sample 100       # Test with 100 companies
    python run_direct_dcf_analysis.py --tickers TCS,RELIANCE,HDFCBANK  # Specific tickers
"""

import os
import sys
import json
import time
import argparse
import psutil
from datetime import datetime
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.data_loader import ScreenerDataLoader
from warren_buffett_dcf_analysis import WarrenBuffettDCFAnalysis

class DirectDCFAnalysis:
    """
    Direct DCF Analysis without cash flow pre-screening
    """

    def __init__(self, batch_size=20, max_workers=4):
        # Auto-adjust for system capabilities
        memory_gb = psutil.virtual_memory().total / (1024**3)
        cpu_count = psutil.cpu_count()

        if memory_gb < 16:
            batch_size = min(batch_size, 15)
            print(f"🔧 Adjusted batch size to {batch_size} for {memory_gb:.1f}GB RAM")

        if cpu_count < 8:
            max_workers = min(max_workers, cpu_count)
            print(f"🔧 Adjusted max workers to {max_workers} for {cpu_count} CPU cores")

        self.batch_size = batch_size
        self.max_workers = max_workers
        self.start_time = datetime.now()

        # Create output directories
        self.output_dir = Path('output/direct_dcf_analysis')
        self.modular_dir = Path('output/modular_results')
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.modular_dir.mkdir(parents=True, exist_ok=True)

        # Initialize components
        self.data_loader = ScreenerDataLoader('../screener_data_collector/data')
        self.warren_buffett_analyzer = WarrenBuffettDCFAnalysis()

        print("🎯 DIRECT DCF ANALYSIS - NO CASH FLOW PRE-SCREENING")
        print("=" * 80)
        print(f"📊 Batch Size: {self.batch_size}")
        print(f"🔧 Max Workers: {self.max_workers}")
        print(f"📁 Output Directory: {self.output_dir}")
        print("=" * 80)

    def run_dcf_on_companies(self, tickers, description="companies"):
        """Run DCF analysis on specified companies"""
        print(f"\n🎯 RUNNING DCF ANALYSIS ON {len(tickers)} {description.upper()}")
        print("-" * 60)

        results = {
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'result_id': 'Result_2',
                'total_companies': len(tickers),
                'analysis_type': 'direct_dcf',
                'description': f'DCF Analysis - {description}'
            },
            'company_results': {},
            'summary_statistics': {
                'total_analyzed': 0,
                'successful_analyses': 0,
                'failed_analyses': 0,
                'buffett_approved': 0,
                'undervalued_companies': 0,
                'overvalued_companies': 0,
                'fairly_valued_companies': 0
            },
            'valuation_distribution': {
                'severely_undervalued': 0,  # >50% undervalued
                'undervalued': 0,           # 20-50% undervalued
                'fairly_valued': 0,         # -20% to +20%
                'overvalued': 0,            # 20-50% overvalued
                'severely_overvalued': 0    # >50% overvalued
            },
            'dcf_accuracy_check': {
                'companies_with_valid_dcf': 0,
                'companies_with_zero_values': 0,
                'average_enterprise_value': 0,
                'average_per_share_value': 0,
                'price_range_analysis': {}
            }
        }

        successful_analyses = 0
        failed_analyses = 0
        buffett_approved = 0

        enterprise_values = []
        per_share_values = []
        margin_of_safety_values = []

        for i, ticker in enumerate(tickers, 1):
            try:
                print(f"📊 Analyzing {ticker} ({i}/{len(tickers)})...")

                # Load company data
                company_data = self.data_loader.load_company_data(ticker)

                if not company_data:
                    print(f"   ❌ No data available for {ticker}")
                    failed_analyses += 1
                    continue

                # Run Warren Buffett DCF analysis
                dcf_result = self.warren_buffett_analyzer.analyze_company_buffett_style(ticker, company_data)

                if not dcf_result:
                    print(f"   ❌ DCF analysis failed for {ticker}")
                    failed_analyses += 1
                    continue

                # Extract key metrics
                intrinsic_val = dcf_result.get('intrinsic_valuation', {})
                dcf_details = intrinsic_val.get('dcf_details', {})
                margin_analysis = dcf_result.get('margin_of_safety_analysis', {})
                buffett_score = dcf_result.get('buffett_score', {})
                recommendation = dcf_result.get('recommendation', {})

                # Get key values
                enterprise_value = dcf_details.get('enterprise_value', 0)
                equity_value = dcf_details.get('equity_value', 0)

                # Calculate proper per-share value
                overview = company_data.get('overview', {})
                market_cap = float(str(overview.get('market_cap', '0')).replace(',', '')) if overview.get('market_cap') else 0
                current_price = float(str(overview.get('current_price', '0')).replace(',', '')) if overview.get('current_price') else 0

                per_share_value = 0
                margin_of_safety = 0

                if market_cap > 0 and current_price > 0 and enterprise_value > 0:
                    shares_outstanding = (market_cap * 10_000_000) / current_price
                    per_share_value = (enterprise_value * 10_000_000) / shares_outstanding
                    margin_of_safety = ((per_share_value - current_price) / current_price) * 100

                    # Sanity check for extreme values
                    if abs(margin_of_safety) > 1000:  # More than 1000% is likely a calculation error
                        print(f"   ⚠️  Extreme margin of safety detected for {ticker}: {margin_of_safety:.1f}%")
                        print(f"       Market Cap: ₹{market_cap:,.0f} Cr, Price: ₹{current_price:.2f}")
                        print(f"       Enterprise Value: ₹{enterprise_value:,.0f} Cr")
                        print(f"       Calculated Shares: {shares_outstanding:,.0f}")
                        # Cap extreme values
                        margin_of_safety = max(-95, min(500, margin_of_safety))

                # Store detailed results
                results['company_results'][ticker] = {
                    'enterprise_value': enterprise_value,
                    'equity_value': equity_value,
                    'per_share_value': per_share_value,
                    'current_price': current_price,
                    'margin_of_safety': margin_of_safety,
                    'buffett_score': buffett_score.get('buffett_score', 0),
                    'buffett_grade': buffett_score.get('buffett_grade', 'N/A'),
                    'buffett_approved': recommendation.get('buffett_approved', False),
                    'recommendation': recommendation.get('recommendation', 'UNKNOWN'),
                    'dcf_details': dcf_details,
                    'full_analysis': dcf_result
                }

                # Update statistics
                successful_analyses += 1

                if recommendation.get('buffett_approved', False):
                    buffett_approved += 1

                # Track values for accuracy check
                if enterprise_value > 0:
                    enterprise_values.append(enterprise_value)
                if per_share_value > 0:
                    per_share_values.append(per_share_value)
                if margin_of_safety != 0:
                    margin_of_safety_values.append(margin_of_safety)

                # Categorize valuation
                if margin_of_safety > 50:
                    results['valuation_distribution']['severely_undervalued'] += 1
                elif margin_of_safety > 20:
                    results['valuation_distribution']['undervalued'] += 1
                elif margin_of_safety > -20:
                    results['valuation_distribution']['fairly_valued'] += 1
                elif margin_of_safety > -50:
                    results['valuation_distribution']['overvalued'] += 1
                else:
                    results['valuation_distribution']['severely_overvalued'] += 1

                # Show progress
                if per_share_value > 0:
                    status = "UNDERVALUED" if margin_of_safety > 0 else "OVERVALUED"
                    print(f"   ✅ Enterprise Value: ₹{enterprise_value:,.0f} Cr, Per Share: ₹{per_share_value:.2f}, Status: {status} ({margin_of_safety:.1f}%)")
                else:
                    print(f"   ⚠️  Enterprise Value: ₹{enterprise_value:,.0f} Cr, Per Share calculation failed")

                # Memory optimization every 20 companies
                if i % 20 == 0:
                    import gc
                    gc.collect()
                    memory_percent = psutil.virtual_memory().percent
                    print(f"   🔧 Memory usage: {memory_percent:.1f}%")

            except Exception as e:
                print(f"   ❌ Error analyzing {ticker}: {e}")
                failed_analyses += 1

        # Update final statistics
        results['summary_statistics'].update({
            'total_analyzed': len(tickers),
            'successful_analyses': successful_analyses,
            'failed_analyses': failed_analyses,
            'buffett_approved': buffett_approved,
            'success_rate': (successful_analyses / len(tickers)) * 100 if len(tickers) > 0 else 0,
            'buffett_approval_rate': (buffett_approved / successful_analyses) * 100 if successful_analyses > 0 else 0
        })

        # DCF accuracy check
        if enterprise_values:
            results['dcf_accuracy_check'].update({
                'companies_with_valid_dcf': len(enterprise_values),
                'companies_with_zero_values': successful_analyses - len(enterprise_values),
                'average_enterprise_value': sum(enterprise_values) / len(enterprise_values),
                'min_enterprise_value': min(enterprise_values),
                'max_enterprise_value': max(enterprise_values)
            })

        if per_share_values:
            results['dcf_accuracy_check'].update({
                'average_per_share_value': sum(per_share_values) / len(per_share_values),
                'min_per_share_value': min(per_share_values),
                'max_per_share_value': max(per_share_values)
            })

        if margin_of_safety_values:
            undervalued = len([m for m in margin_of_safety_values if m > 0])
            overvalued = len([m for m in margin_of_safety_values if m < 0])

            results['summary_statistics'].update({
                'undervalued_companies': undervalued,
                'overvalued_companies': overvalued,
                'fairly_valued_companies': len(margin_of_safety_values) - undervalued - overvalued
            })

            results['dcf_accuracy_check']['price_range_analysis'] = {
                'average_margin_of_safety': sum(margin_of_safety_values) / len(margin_of_safety_values),
                'undervalued_percentage': (undervalued / len(margin_of_safety_values)) * 100,
                'overvalued_percentage': (overvalued / len(margin_of_safety_values)) * 100
            }

        return results

    def save_results(self, results, filename_suffix=""):
        """Save analysis results with JSON serialization fix"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'direct_dcf_analysis_{filename_suffix}_{timestamp}.json' if filename_suffix else f'direct_dcf_analysis_{timestamp}.json'

        filepath = self.output_dir / filename

        # Convert numpy types to native Python types for JSON serialization
        def convert_numpy_types(obj):
            """Convert numpy types to native Python types"""
            import numpy as np
            if isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            elif isinstance(obj, (np.integer, np.int64, np.int32)):
                return int(obj)
            elif isinstance(obj, (np.floating, np.float64, np.float32)):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj

        try:
            # Convert results to JSON-serializable format
            serializable_results = convert_numpy_types(results)

            with open(filepath, 'w') as f:
                json.dump(serializable_results, f, indent=2)

            print(f"\n💾 Results saved to: {filepath}")
            return filepath

        except Exception as e:
            print(f"❌ Error saving results: {e}")
            # Try to save without problematic data
            try:
                safe_results = {
                    'analysis_metadata': results.get('analysis_metadata', {}),
                    'summary_statistics': results.get('summary_statistics', {}),
                    'valuation_distribution': results.get('valuation_distribution', {}),
                    'dcf_accuracy_check': results.get('dcf_accuracy_check', {}),
                    'error': f'Full results could not be saved: {str(e)}'
                }

                with open(filepath, 'w') as f:
                    json.dump(safe_results, f, indent=2)

                print(f"💾 Partial results saved to: {filepath}")
                return filepath

            except Exception as e2:
                print(f"❌ Failed to save even partial results: {e2}")
                return None

    def save_modular_result(self, results, filename_suffix=""):
        """Save results as modular Result_2"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'Result_2_DCF_{filename_suffix}_{timestamp}.json' if filename_suffix else f'Result_2_DCF_{timestamp}.json'

        filepath = self.modular_dir / filename

        # Convert numpy types for JSON serialization
        def convert_numpy_types(obj):
            import numpy as np
            if isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            elif isinstance(obj, (np.integer, np.int64, np.int32)):
                return int(obj)
            elif isinstance(obj, (np.floating, np.float64, np.float32)):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj

        try:
            serializable_results = convert_numpy_types(results)

            with open(filepath, 'w') as f:
                json.dump(serializable_results, f, indent=2)

            print(f"💾 Modular result saved as Result_2: {filepath}")
            return filepath

        except Exception as e:
            print(f"❌ Error saving modular result: {e}")
            return None

    def print_summary(self, results):
        """Print analysis summary"""
        stats = results['summary_statistics']
        accuracy = results['dcf_accuracy_check']
        valuation = results['valuation_distribution']

        print(f"\n" + "=" * 80)
        print("📊 DIRECT DCF ANALYSIS SUMMARY")
        print("=" * 80)

        print(f"📈 ANALYSIS STATISTICS:")
        print(f"   Total Companies: {stats['total_analyzed']:,}")
        print(f"   Successful Analyses: {stats['successful_analyses']:,}")
        print(f"   Failed Analyses: {stats['failed_analyses']:,}")
        print(f"   Success Rate: {stats['success_rate']:.1f}%")

        print(f"\n⭐ BUFFETT ANALYSIS:")
        print(f"   Buffett Approved: {stats['buffett_approved']:,}")
        print(f"   Approval Rate: {stats['buffett_approval_rate']:.1f}%")

        print(f"\n💰 VALUATION DISTRIBUTION:")
        total_valued = sum(valuation.values())
        if total_valued > 0:
            print(f"   Severely Undervalued (>50%): {valuation['severely_undervalued']} ({valuation['severely_undervalued']/total_valued*100:.1f}%)")
            print(f"   Undervalued (20-50%): {valuation['undervalued']} ({valuation['undervalued']/total_valued*100:.1f}%)")
            print(f"   Fairly Valued (-20% to +20%): {valuation['fairly_valued']} ({valuation['fairly_valued']/total_valued*100:.1f}%)")
            print(f"   Overvalued (20-50%): {valuation['overvalued']} ({valuation['overvalued']/total_valued*100:.1f}%)")
            print(f"   Severely Overvalued (>50%): {valuation['severely_overvalued']} ({valuation['severely_overvalued']/total_valued*100:.1f}%)")

        print(f"\n🔍 DCF ACCURACY CHECK:")
        print(f"   Companies with Valid DCF: {accuracy['companies_with_valid_dcf']:,}")
        print(f"   Companies with Zero Values: {accuracy['companies_with_zero_values']:,}")

        if accuracy.get('average_enterprise_value', 0) > 0:
            print(f"   Average Enterprise Value: ₹{accuracy['average_enterprise_value']:,.0f} Cr")
            print(f"   Enterprise Value Range: ₹{accuracy.get('min_enterprise_value', 0):,.0f} - ₹{accuracy.get('max_enterprise_value', 0):,.0f} Cr")

        if accuracy.get('average_per_share_value', 0) > 0:
            print(f"   Average Per Share Value: ₹{accuracy['average_per_share_value']:,.2f}")
            print(f"   Per Share Range: ₹{accuracy.get('min_per_share_value', 0):,.2f} - ₹{accuracy.get('max_per_share_value', 0):,.2f}")

        price_analysis = accuracy.get('price_range_analysis', {})
        if price_analysis:
            print(f"   Average Margin of Safety: {price_analysis['average_margin_of_safety']:.1f}%")
            print(f"   Undervalued Companies: {price_analysis['undervalued_percentage']:.1f}%")
            print(f"   Overvalued Companies: {price_analysis['overvalued_percentage']:.1f}%")

        runtime = (datetime.now() - self.start_time).total_seconds() / 60
        print(f"\n⏱️  Total Runtime: {runtime:.1f} minutes")
        print("=" * 80)

def main():
    """Main function with command line argument parsing"""
    parser = argparse.ArgumentParser(description='Run direct DCF analysis without cash flow pre-screening')
    parser.add_argument('--sample', type=int, help='Number of companies to sample for testing')
    parser.add_argument('--tickers', type=str, help='Comma-separated list of specific tickers to analyze')
    parser.add_argument('--batch_size', type=int, default=20, help='Batch size for processing')
    parser.add_argument('--max_workers', type=int, default=4, help='Maximum worker processes')

    args = parser.parse_args()

    # Initialize analyzer
    analyzer = DirectDCFAnalysis(
        batch_size=args.batch_size,
        max_workers=args.max_workers
    )

    # Determine which companies to analyze
    if args.tickers:
        # Specific tickers
        tickers = [t.strip().upper() for t in args.tickers.split(',')]
        description = f"specific_tickers"
        print(f"🎯 Analyzing specific tickers: {', '.join(tickers)}")

    elif args.sample:
        # Sample of companies
        all_tickers = analyzer.data_loader.get_all_tickers()
        tickers = all_tickers[:args.sample]
        description = f"sample_{args.sample}"
        print(f"🎯 Analyzing sample of {args.sample} companies")

    else:
        # All companies
        tickers = analyzer.data_loader.get_all_tickers()
        description = "all_companies"
        print(f"🎯 Analyzing ALL {len(tickers)} companies")

    # Run analysis
    results = analyzer.run_dcf_on_companies(tickers, description)

    # Save results
    filepath = analyzer.save_results(results, description)

    # Also save as modular result (Result_2)
    modular_filepath = analyzer.save_modular_result(results, description)

    # Print summary
    analyzer.print_summary(results)

    print(f"\n🎯 ANALYSIS COMPLETE!")
    print(f"📁 Results saved to: {filepath}")
    if modular_filepath:
        print(f"🔧 Modular result saved as Result_2: {modular_filepath}")
    print(f"📊 View results in dashboard: streamlit run dashboard.py")

if __name__ == "__main__":
    main()
