#!/usr/bin/env python3
"""
Example script to analyze TCS using the fundamental analysis tool
"""

import os
import sys
import json
import matplotlib.pyplot as plt

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the FundamentalAnalyzer
from analysis import FundamentalAnalyzer

def main():
    """
    Main function to analyze TCS
    """
    # Create the analyzer
    analyzer = FundamentalAnalyzer()
    
    # Analyze TCS
    print("Analyzing TCS...")
    results = analyzer.analyze_company('TCS', ['financial_ratios', 'valuation', 'checklist'])
    
    # Check for errors
    if 'error' in results:
        print(f"Error: {results['error']}")
        return
    
    # Print financial ratios
    print("\nFinancial Ratios:")
    
    if 'ratios' in results:
        # Print profitability ratios
        if 'profitability' in results['ratios']:
            print("\n  Profitability Ratios:")
            for key, value in results['ratios']['profitability'].items():
                print(f"    {key}: {value:.2f}%")
        
        # Print leverage ratios
        if 'leverage' in results['ratios']:
            print("\n  Leverage Ratios:")
            for key, value in results['ratios']['leverage'].items():
                print(f"    {key}: {value:.2f}")
        
        # Print valuation ratios
        if 'valuation' in results['ratios']:
            print("\n  Valuation Ratios:")
            for key, value in results['ratios']['valuation'].items():
                print(f"    {key}: {value:.2f}")
    
    # Print growth rates
    if 'growth_rates' in results:
        print("\nGrowth Rates:")
        for key, value in results['growth_rates'].items():
            print(f"  {key}: {value:.2f}%")
    
    # Print DCF analysis results
    if 'dcf_analysis' in results:
        print("\nDCF Analysis:")
        print(f"  Intrinsic Value per Share: {results['dcf_analysis'].get('intrinsic_value_per_share', 'N/A')}")
        print(f"  Margin of Safety: {results['dcf_analysis'].get('margin_of_safety', 'N/A')}%")
    
    # Print checklist results
    if 'checklist' in results:
        print("\nChecklist Evaluation:")
        print(f"  Score: {results['checklist']['score']:.1f}/{results['checklist']['max_score']:.1f} ({results['checklist']['percentage']:.1f}%)")
    
    # Generate a report
    print("\nGenerating report...")
    report = analyzer.generate_report(results, 'full', 'TCS_report.md')
    print(f"Report saved to {os.path.join(analyzer.output_dir, 'TCS_report.md')}")
    
    # Create visualizations
    if 'growth_rates' in results:
        print("\nCreating visualizations...")
        
        # Create a bar chart of growth rates
        growth_rates = results['growth_rates']
        plt.figure(figsize=(10, 6))
        plt.bar(growth_rates.keys(), growth_rates.values())
        plt.title('TCS Growth Rates')
        plt.ylabel('CAGR (%)')
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # Save the chart
        output_path = os.path.join(analyzer.output_dir, 'TCS_growth_rates.png')
        plt.savefig(output_path)
        print(f"Growth rates chart saved to {output_path}")
    
    # Save the results to a JSON file
    output_path = os.path.join(analyzer.output_dir, 'TCS_analysis.json')
    with open(output_path, 'w') as f:
        # Create a copy of the results without the company_data key (to save space)
        results_copy = {k: v for k, v in results.items() if k != 'company_data'}
        json.dump(results_copy, f, indent=2)
    
    print(f"Analysis results saved to {output_path}")

if __name__ == "__main__":
    main()
