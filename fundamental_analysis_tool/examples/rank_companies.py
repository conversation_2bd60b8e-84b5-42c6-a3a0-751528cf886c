#!/usr/bin/env python3
"""
Example script to rank multiple companies using the fundamental analysis tool
"""

import os
import sys
import json
import matplotlib.pyplot as plt
import pandas as pd

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the FundamentalAnalyzer
from analysis import FundamentalAnalyzer

def main():
    """
    Main function to rank multiple companies
    """
    # Create the analyzer
    analyzer = FundamentalAnalyzer()
    
    # Define the tickers to analyze
    tickers = ['TCS', 'INFY', 'HDFCBANK', 'RELIANCE', 'SBIN']
    
    print(f"Analyzing {len(tickers)} companies: {', '.join(tickers)}")
    
    # Analyze the companies
    results = analyzer.analyze_multiple_companies(tickers, ['financial_ratios', 'valuation'])
    
    # Rank the companies
    print("\nRanking companies...")
    ranking_results = analyzer.rank_companies(tickers)
    
    # Print the rankings
    print("\nOverall Rankings:")
    for i, (ticker, score) in enumerate(ranking_results['rankings']['total'], start=1):
        print(f"{i}. {ticker}: {score:.4f}")
    
    # Print category rankings
    for category, rankings in ranking_results['rankings']['categories'].items():
        print(f"\n{category.capitalize()} Rankings:")
        for i, (ticker, score) in enumerate(rankings, start=1):
            print(f"{i}. {ticker}: {score:.4f}")
    
    # Create a visualization of the rankings
    print("\nCreating visualizations...")
    
    # Create a bar chart of overall rankings
    plt.figure(figsize=(10, 6))
    
    # Extract tickers and scores
    ranked_tickers = [ticker for ticker, _ in ranking_results['rankings']['total']]
    scores = [score for _, score in ranking_results['rankings']['total']]
    
    # Create the bar chart
    plt.bar(ranked_tickers, scores)
    plt.title('Company Rankings')
    plt.ylabel('Score')
    plt.ylim(0, 1)
    plt.tight_layout()
    
    # Save the chart
    output_path = os.path.join(analyzer.output_dir, 'company_rankings.png')
    plt.savefig(output_path)
    print(f"Rankings chart saved to {output_path}")
    
    # Create a heatmap of key metrics
    plt.figure(figsize=(12, 8))
    
    # Extract key metrics for each company
    metrics = ['roe', 'debt_to_equity', 'pat_margin', 'revenue_cagr']
    metrics_data = {}
    
    for ticker in ranked_tickers:
        metrics_data[ticker] = {}
        
        # Extract profitability ratios
        if 'ratios' in results[ticker] and 'profitability' in results[ticker]['ratios']:
            for metric in ['roe', 'pat_margin']:
                if metric in results[ticker]['ratios']['profitability']:
                    metrics_data[ticker][metric] = results[ticker]['ratios']['profitability'][metric]
        
        # Extract leverage ratios
        if 'ratios' in results[ticker] and 'leverage' in results[ticker]['ratios']:
            if 'debt_to_equity' in results[ticker]['ratios']['leverage']:
                metrics_data[ticker]['debt_to_equity'] = results[ticker]['ratios']['leverage']['debt_to_equity']
        
        # Extract growth rates
        if 'growth_rates' in results[ticker]:
            if 'revenue_cagr' in results[ticker]['growth_rates']:
                metrics_data[ticker]['revenue_cagr'] = results[ticker]['growth_rates']['revenue_cagr']
    
    # Convert to DataFrame
    df = pd.DataFrame(metrics_data).T
    
    # Create the heatmap
    plt.figure(figsize=(10, 6))
    ax = plt.subplot(111)
    
    # Use the analyzer's visualizer to create the heatmap
    analyzer.visualizer.plot_ratio_heatmap(df, 'Key Metrics Comparison', 'metrics_heatmap.png')
    
    print(f"Metrics heatmap saved to {os.path.join(analyzer.output_dir, 'metrics_heatmap.png')}")
    
    # Save the ranking results to a JSON file
    output_path = os.path.join(analyzer.output_dir, 'ranking_results.json')
    with open(output_path, 'w') as f:
        json.dump(ranking_results, f, indent=2)
    
    print(f"Ranking results saved to {output_path}")
    
    # Generate reports for the top 3 companies
    print("\nGenerating reports for top companies...")
    
    for i, (ticker, _) in enumerate(ranking_results['rankings']['total'][:3], start=1):
        report = analyzer.generate_report(results[ticker], 'full', f"{ticker}_report.md")
        print(f"Report for {ticker} saved to {os.path.join(analyzer.output_dir, f'{ticker}_report.md')}")

if __name__ == "__main__":
    main()
