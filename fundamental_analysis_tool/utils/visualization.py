#!/usr/bin/env python3
"""
Visualization Module

This module provides functions to create visualizations for financial data analysis.
It includes advanced visualization capabilities for fundamental analysis screening.
"""

import os
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
import seaborn as sns
from typing import Dict, List, Optional, Union, Any, Tuple
from matplotlib.colors import LinearSegmentedColormap

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('visualization')

class FinancialVisualizer:
    """
    Class to create visualizations for financial data
    """

    def __init__(self, output_dir: str = 'output'):
        """
        Initialize the visualizer

        Parameters:
        -----------
        output_dir : str
            Directory to save visualizations
        """
        self.output_dir = output_dir

        # Create the output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Set the default style for plots
        sns.set_style("whitegrid")
        plt.rcParams['figure.figsize'] = (12, 8)
        plt.rcParams['font.size'] = 12

    def plot_financial_trend(self,
                            data: Dict[str, List[float]],
                            title: str,
                            ylabel: str,
                            filename: Optional[str] = None) -> plt.Figure:
        """
        Plot a financial trend over time

        Parameters:
        -----------
        data : Dict[str, List[float]]
            Dictionary mapping years to values
        title : str
            Title of the plot
        ylabel : str
            Label for the y-axis
        filename : str, optional
            Filename to save the plot

        Returns:
        --------
        Matplotlib figure object
        """
        fig, ax = plt.subplots()

        # Sort the data by year
        years = sorted(data.keys())
        values = [data[year] for year in years]

        # Plot the data
        ax.plot(years, values, marker='o', linestyle='-', linewidth=2)

        # Add labels and title
        ax.set_xlabel('Year')
        ax.set_ylabel(ylabel)
        ax.set_title(title)

        # Add grid
        ax.grid(True, linestyle='--', alpha=0.7)

        # Add data labels
        for i, value in enumerate(values):
            ax.annotate(f"{value:.2f}", (years[i], values[i]),
                       textcoords="offset points",
                       xytext=(0, 10),
                       ha='center')

        # Save the plot if a filename is provided
        if filename:
            save_path = os.path.join(self.output_dir, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved plot to {save_path}")

        return fig

    def plot_financial_comparison(self,
                                data: Dict[str, Dict[str, float]],
                                metric: str,
                                title: str,
                                filename: Optional[str] = None) -> plt.Figure:
        """
        Plot a comparison of a financial metric across companies

        Parameters:
        -----------
        data : Dict[str, Dict[str, float]]
            Dictionary mapping company names to dictionaries of metrics
        metric : str
            The metric to compare
        title : str
            Title of the plot
        filename : str, optional
            Filename to save the plot

        Returns:
        --------
        Matplotlib figure object
        """
        # Extract the metric values for each company
        companies = []
        values = []

        for company, metrics in data.items():
            if metric in metrics:
                companies.append(company)
                values.append(metrics[metric])

        # Sort by value
        sorted_indices = np.argsort(values)[::-1]  # Descending order
        companies = [companies[i] for i in sorted_indices]
        values = [values[i] for i in sorted_indices]

        # Create the plot
        fig, ax = plt.subplots()

        # Plot the data
        bars = ax.barh(companies, values)

        # Add labels and title
        ax.set_xlabel(metric)
        ax.set_title(title)

        # Add data labels
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax.text(width + 0.1, bar.get_y() + bar.get_height()/2,
                   f"{values[i]:.2f}",
                   va='center')

        # Save the plot if a filename is provided
        if filename:
            save_path = os.path.join(self.output_dir, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved plot to {save_path}")

        return fig

    def plot_screening_results(self,
                              screening_results: Dict[str, Any],
                              top_n: int = 20,
                              filename: Optional[str] = None) -> plt.Figure:
        """
        Plot a summary of screening results

        Parameters:
        -----------
        screening_results : Dict[str, Any]
            Results from the screener
        top_n : int
            Number of top companies to show
        filename : str, optional
            Filename to save the plot

        Returns:
        --------
        Matplotlib figure object
        """
        # Extract data
        summary = screening_results.get('summary', {})
        passed_companies = screening_results.get('passed_companies', {})
        results = screening_results.get('results', {})

        # Create figure with subplots
        fig = plt.figure(figsize=(15, 12))
        gs = gridspec.GridSpec(2, 2, figure=fig)

        # 1. Funnel chart showing how many companies passed each tier
        ax1 = fig.add_subplot(gs[0, 0])
        self._plot_screening_funnel(ax1, summary)

        # 2. Top companies by score
        ax2 = fig.add_subplot(gs[0, 1])
        self._plot_top_companies(ax2, results, top_n)

        # 3. Metric distribution for passed companies
        ax3 = fig.add_subplot(gs[1, :])
        self._plot_metric_distribution(ax3, results, passed_companies.get('all', []))

        # Adjust layout
        plt.tight_layout()

        # Save the plot if a filename is provided
        if filename:
            save_path = os.path.join(self.output_dir, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved plot to {save_path}")

        return fig

    def _plot_screening_funnel(self, ax: plt.Axes, summary: Dict[str, int]) -> None:
        """
        Plot a funnel chart showing how many companies passed each tier

        Parameters:
        -----------
        ax : plt.Axes
            Matplotlib axes to plot on
        summary : Dict[str, int]
            Summary of screening results
        """
        # Extract data
        stages = ['Pre-filtered', 'Tier 1', 'Tier 2', 'Tier 3', 'Tier 4', 'Tier 5', 'All Tiers']
        values = [
            summary.get('pre_filtered', 0),
            summary.get('tier1_pass', 0),
            summary.get('tier2_pass', 0),
            summary.get('tier3_pass', 0),
            summary.get('tier4_pass', 0),
            summary.get('tier5_pass', 0),
            summary.get('all_pass', 0)
        ]

        # Calculate percentages
        total = summary.get('total', 0)
        percentages = [100 * v / total if total > 0 else 0 for v in values]

        # Create colors
        colors = sns.color_palette("viridis", len(stages))

        # Plot bars
        bars = ax.barh(stages, percentages, color=colors)

        # Add labels
        for i, (bar, value) in enumerate(zip(bars, values)):
            width = bar.get_width()
            ax.text(width + 1, bar.get_y() + bar.get_height()/2,
                   f"{value} ({percentages[i]:.1f}%)",
                   va='center')

        # Set labels and title
        ax.set_xlabel('Percentage of Total Companies')
        ax.set_title('Screening Funnel')

        # Set x-axis limit
        ax.set_xlim(0, 100)

    def _plot_top_companies(self, ax: plt.Axes, results: Dict[str, Dict[str, Any]], top_n: int) -> None:
        """
        Plot top companies by score

        Parameters:
        -----------
        ax : plt.Axes
            Matplotlib axes to plot on
        results : Dict[str, Dict[str, Any]]
            Screening results for each company
        top_n : int
            Number of top companies to show
        """
        # Calculate a simple score for each company
        scores = {}
        for ticker, result in results.items():
            # Skip companies with errors
            if 'error' in result:
                continue

            # Calculate score based on how many tiers were passed
            score = sum([
                result.get('tier1_pass', False),
                result.get('tier2_pass', False),
                result.get('tier3_pass', False),
                result.get('tier4_pass', False)
            ])

            scores[ticker] = score

        # Sort companies by score
        sorted_companies = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:top_n]

        # Extract data for plotting
        tickers = [item[0] for item in sorted_companies]
        scores = [item[1] for item in sorted_companies]

        # Plot bars
        bars = ax.barh(tickers, scores, color=sns.color_palette("viridis", len(tickers)))

        # Add labels
        for bar in bars:
            width = bar.get_width()
            ax.text(width + 0.1, bar.get_y() + bar.get_height()/2,
                   f"{width:.0f}",
                   va='center')

        # Set labels and title
        ax.set_xlabel('Score (Tiers Passed)')
        ax.set_title(f'Top {len(tickers)} Companies by Score')

        # Set x-axis limit
        ax.set_xlim(0, 4.5)

    def _plot_metric_distribution(self, ax: plt.Axes, results: Dict[str, Dict[str, Any]],
                                 passed_tickers: List[str]) -> None:
        """
        Plot metric distribution for passed companies

        Parameters:
        -----------
        ax : plt.Axes
            Matplotlib axes to plot on
        results : Dict[str, Dict[str, Any]]
            Screening results for each company
        passed_tickers : List[str]
            List of tickers that passed all tiers
        """
        # Extract metrics for passed companies
        metrics_data = {}

        for ticker in passed_tickers:
            if ticker in results and 'metrics' in results[ticker]:
                metrics = results[ticker]['metrics']
                for metric, value in metrics.items():
                    if value is not None:
                        if metric not in metrics_data:
                            metrics_data[metric] = []
                        metrics_data[metric].append(value)

        # Select key metrics to display
        key_metrics = [
            'roe', 'roce', 'debt_to_equity', 'interest_coverage',
            'revenue_cagr', 'profit_cagr', 'pe_ratio', 'pb_ratio'
        ]

        # Filter to only include metrics with data
        key_metrics = [m for m in key_metrics if m in metrics_data and len(metrics_data[m]) > 0]

        if not key_metrics:
            ax.text(0.5, 0.5, "No metric data available for passed companies",
                   ha='center', va='center', fontsize=14)
            return

        # Create a grid of boxplots
        num_metrics = len(key_metrics)
        num_cols = min(4, num_metrics)
        num_rows = (num_metrics + num_cols - 1) // num_cols

        # Clear the main axis and create a grid
        ax.set_visible(False)
        gs = gridspec.GridSpecFromSubplotSpec(num_rows, num_cols, subplot_spec=ax.get_subplotspec())

        # Create a boxplot for each metric
        for i, metric in enumerate(key_metrics):
            row = i // num_cols
            col = i % num_cols

            # Create subplot
            sub_ax = fig.add_subplot(gs[row, col])

            # Create boxplot
            sns.boxplot(y=metrics_data[metric], ax=sub_ax, color='skyblue')

            # Add individual points
            sns.stripplot(y=metrics_data[metric], ax=sub_ax, color='navy', alpha=0.5)

            # Set title and labels
            sub_ax.set_title(metric.replace('_', ' ').title())
            sub_ax.set_xlabel('')

            # Add median line and value
            median = np.median(metrics_data[metric])
            sub_ax.axhline(median, color='red', linestyle='--', alpha=0.7)
            sub_ax.text(0.95, median, f"{median:.2f}",
                       ha='right', va='bottom', color='red')

        # Add overall title
        fig.suptitle(f"Metric Distribution for {len(passed_tickers)} Passed Companies",
                    fontsize=16, y=0.98)

    def plot_company_comparison(self,
                               companies_data: Dict[str, Dict[str, Any]],
                               metrics: List[str],
                               title: str = "Company Comparison",
                               filename: Optional[str] = None) -> plt.Figure:
        """
        Create a radar chart comparing multiple companies across metrics

        Parameters:
        -----------
        companies_data : Dict[str, Dict[str, Any]]
            Dictionary mapping company tickers to their data
        metrics : List[str]
            List of metrics to compare
        title : str
            Title of the plot
        filename : str, optional
            Filename to save the plot

        Returns:
        --------
        Matplotlib figure object
        """
        # Number of companies and metrics
        num_companies = len(companies_data)
        num_metrics = len(metrics)

        if num_companies == 0 or num_metrics == 0:
            fig, ax = plt.subplots()
            ax.text(0.5, 0.5, "No data available for comparison",
                   ha='center', va='center', fontsize=14)
            return fig

        # Create figure
        fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(polar=True))

        # Set the angles for each metric
        angles = np.linspace(0, 2*np.pi, num_metrics, endpoint=False).tolist()
        angles += angles[:1]  # Close the loop

        # Set the labels for each metric
        labels = [metric.replace('_', ' ').title() for metric in metrics]
        labels += labels[:1]  # Close the loop

        # Plot each company
        colors = sns.color_palette("husl", num_companies)

        # Extract and normalize data
        all_values = {}
        for metric in metrics:
            all_values[metric] = []
            for company_data in companies_data.values():
                if 'metrics' in company_data and metric in company_data['metrics']:
                    value = company_data['metrics'][metric]
                    if value is not None:
                        all_values[metric].append(value)

        # Plot each company
        for i, (ticker, company_data) in enumerate(companies_data.items()):
            values = []
            for metric in metrics:
                if 'metrics' in company_data and metric in company_data['metrics']:
                    value = company_data['metrics'][metric]
                    if value is not None:
                        # Normalize the value between 0 and 1
                        metric_values = all_values[metric]
                        if metric_values:
                            min_val = min(metric_values)
                            max_val = max(metric_values)
                            if max_val > min_val:
                                normalized = (value - min_val) / (max_val - min_val)
                            else:
                                normalized = 0.5
                        else:
                            normalized = 0.5
                    else:
                        normalized = 0
                else:
                    normalized = 0
                values.append(normalized)

            # Close the loop
            values += values[:1]

            # Plot the company
            ax.plot(angles, values, linewidth=2, linestyle='solid', label=ticker, color=colors[i])
            ax.fill(angles, values, alpha=0.1, color=colors[i])

        # Set the labels
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(labels[:-1])

        # Remove radial labels
        ax.set_yticklabels([])

        # Add legend
        ax.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))

        # Add title
        plt.title(title, size=15, y=1.1)

        # Save the plot if a filename is provided
        if filename:
            save_path = os.path.join(self.output_dir, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved plot to {save_path}")

        return fig

    def plot_metric_heatmap(self,
                           screening_results: Dict[str, Any],
                           top_n: int = 20,
                           metrics: Optional[List[str]] = None,
                           filename: Optional[str] = None) -> plt.Figure:
        """
        Create a heatmap of metrics for top companies

        Parameters:
        -----------
        screening_results : Dict[str, Any]
            Results from the screener
        top_n : int
            Number of top companies to show
        metrics : List[str], optional
            List of metrics to include (default: key metrics)
        filename : str, optional
            Filename to save the plot

        Returns:
        --------
        Matplotlib figure object
        """
        # Extract results
        results = screening_results.get('results', {})
        passed_companies = screening_results.get('passed_companies', {}).get('all', [])

        if not passed_companies:
            fig, ax = plt.subplots()
            ax.text(0.5, 0.5, "No companies passed all tiers",
                   ha='center', va='center', fontsize=14)
            return fig

        # Limit to top N companies
        companies = passed_companies[:min(top_n, len(passed_companies))]

        # Default metrics if none provided
        if metrics is None:
            metrics = [
                'roe', 'roce', 'debt_to_equity', 'interest_coverage',
                'revenue_cagr', 'profit_cagr', 'pe_ratio', 'pb_ratio',
                'operating_cash_flow', 'free_cash_flow', 'ocf_to_net_income'
            ]

        # Create a DataFrame for the heatmap
        data = []
        for ticker in companies:
            if ticker in results and 'metrics' in results[ticker]:
                row = {'ticker': ticker}
                for metric in metrics:
                    row[metric] = results[ticker]['metrics'].get(metric)
                data.append(row)

        if not data:
            fig, ax = plt.subplots()
            ax.text(0.5, 0.5, "No metric data available for passed companies",
                   ha='center', va='center', fontsize=14)
            return fig

        df = pd.DataFrame(data)
        df.set_index('ticker', inplace=True)

        # Create figure
        plt.figure(figsize=(14, len(companies) * 0.5 + 2))

        # Create custom colormap (green for good, red for bad)
        cmap = sns.diverging_palette(10, 133, as_cmap=True)

        # Create heatmap
        ax = sns.heatmap(df, annot=True, fmt=".2f", cmap=cmap,
                        linewidths=.5, cbar_kws={"shrink": .8})

        # Set title
        plt.title(f"Metric Heatmap for Top {len(companies)} Companies", fontsize=16)

        # Rotate x-axis labels
        plt.xticks(rotation=45, ha='right')

        # Adjust layout
        plt.tight_layout()

        # Save the plot if a filename is provided
        if filename:
            save_path = os.path.join(self.output_dir, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved plot to {save_path}")

        return plt.gcf()