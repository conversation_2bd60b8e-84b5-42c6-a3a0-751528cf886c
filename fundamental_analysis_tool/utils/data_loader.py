#!/usr/bin/env python3
"""
Data Loader Module

This module provides functions to load financial data from the screener_data_collector
and prepare it for fundamental analysis.
"""

import os
import json
import logging
import pandas as pd
from typing import Dict, List, Optional, Union, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('data_loader')

class ScreenerDataLoader:
    """
    Class to load and process data from screener_data_collector
    """

    def __init__(self, data_dir: str = '../screener_data_collector/data'):
        """
        Initialize the data loader with the path to the screener data

        Parameters:
        -----------
        data_dir : str
            Path to the screener_data_collector data directory
        """
        self.data_dir = data_dir
        self.processed_dir = os.path.join(data_dir, 'processed')
        self.csv_dir = os.path.join(data_dir, 'csv')

        # Verify that the data directory exists
        if not os.path.exists(self.data_dir):
            logger.error(f"Data directory not found: {self.data_dir}")
            raise FileNotFoundError(f"Data directory not found: {self.data_dir}")

        # Load the ticker list
        self.ticker_list = self._load_ticker_list()
        logger.info(f"Loaded {len(self.ticker_list)} tickers")

    def _load_ticker_list(self) -> List[str]:
        """
        Load the list of all tickers from the ticker list file

        Returns:
        --------
        List of ticker symbols
        """
        ticker_file = os.path.join(self.csv_dir, 'tickers.txt')
        if not os.path.exists(ticker_file):
            logger.error(f"Ticker list file not found: {ticker_file}")
            raise FileNotFoundError(f"Ticker list file not found: {ticker_file}")

        with open(ticker_file, 'r') as f:
            tickers = [line.strip() for line in f if line.strip()]

        return tickers

    def get_all_tickers(self) -> List[str]:
        """
        Get the list of all available tickers

        Returns:
        --------
        List of ticker symbols
        """
        return self.ticker_list

    def get_data_coverage(self) -> Dict[str, Dict[str, Any]]:
        """
        Get data coverage information for all tickers

        Returns:
        --------
        Dictionary mapping ticker to coverage information
        """
        coverage = {}

        for ticker in self.ticker_list:
            # Check if full data file exists
            full_data_file = os.path.join(self.processed_dir, f"{ticker}_full.json")
            has_full_data = os.path.exists(full_data_file)

            # Check individual data files
            data_files = [
                f"{ticker}_balance_sheet.json",
                f"{ticker}_profit_loss.json",
                f"{ticker}_cash_flow.json",
                f"{ticker}_quarters.json",
                f"{ticker}_ratios.json",
                f"{ticker}_overview.json"
            ]

            available_files = []
            for file_name in data_files:
                file_path = os.path.join(self.processed_dir, file_name)
                if os.path.exists(file_path):
                    available_files.append(file_name.replace(f"{ticker}_", "").replace(".json", ""))

            # Determine if ticker has sufficient data for analysis
            # Need at least cash flow and profit_loss data
            has_sufficient_data = (
                has_full_data or
                ('cash_flow' in available_files and 'profit_loss' in available_files)
            )

            coverage[ticker] = {
                'has_full_data': has_full_data,
                'available_sections': available_files,
                'has_sufficient_data': has_sufficient_data,
                'data_file_count': len(available_files)
            }

        return coverage

    def load_company_data(self, ticker: str) -> Dict[str, Any]:
        """
        Load all available data for a specific company

        Parameters:
        -----------
        ticker : str
            Ticker symbol of the company

        Returns:
        --------
        Dictionary containing all available data for the company
        """
        # Check if the ticker is valid
        if ticker not in self.ticker_list:
            logger.warning(f"Ticker not found in the list: {ticker}")

        # Path to the full company data file
        full_data_file = os.path.join(self.processed_dir, f"{ticker}_full.json")

        # If the full data file doesn't exist, try to load individual data files
        if not os.path.exists(full_data_file):
            logger.warning(f"Full data file not found for {ticker}, trying to load individual files")
            return self._load_company_data_from_individual_files(ticker)

        # Load the full data file
        try:
            with open(full_data_file, 'r') as f:
                data = json.load(f)
            logger.info(f"Loaded full data for {ticker}")

            # Process the data to make it more usable for analysis
            processed_data = self._process_company_data(data, ticker)

            return processed_data
        except Exception as e:
            logger.error(f"Error loading data for {ticker}: {str(e)}")
            return {}

    def _load_company_data_from_individual_files(self, ticker: str) -> Dict[str, Any]:
        """
        Load company data from individual data files

        Parameters:
        -----------
        ticker : str
            Ticker symbol of the company

        Returns:
        --------
        Dictionary containing all available data for the company
        """
        data = {}

        # List of possible data files
        data_files = [
            f"{ticker}_balance_sheet.json",
            f"{ticker}_profit_loss.json",
            f"{ticker}_cash_flow.json",
            f"{ticker}_quarters.json",
            f"{ticker}_ratios.json",
            f"{ticker}_overview.json"
        ]

        # Load each data file if it exists
        for file_name in data_files:
            file_path = os.path.join(self.processed_dir, file_name)
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r') as f:
                        file_data = json.load(f)

                    # Extract the section name from the file name
                    section = file_name.replace(f"{ticker}_", "").replace(".json", "")
                    data[section] = file_data
                    logger.info(f"Loaded {section} data for {ticker}")
                except Exception as e:
                    logger.error(f"Error loading {file_name} for {ticker}: {str(e)}")

        return data

    def load_multiple_companies(self, tickers: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Load data for multiple companies

        Parameters:
        -----------
        tickers : List[str]
            List of ticker symbols

        Returns:
        --------
        Dictionary mapping ticker symbols to company data
        """
        data = {}
        for ticker in tickers:
            data[ticker] = self.load_company_data(ticker)

        return data

    def load_all_companies_summary(self) -> pd.DataFrame:
        """
        Load the summary data for all companies

        Returns:
        --------
        DataFrame containing summary data for all companies
        """
        summary_file = os.path.join(self.csv_dir, 'all_companies.csv')
        if not os.path.exists(summary_file):
            logger.error(f"Summary file not found: {summary_file}")
            raise FileNotFoundError(f"Summary file not found: {summary_file}")

        try:
            df = pd.read_csv(summary_file)
            logger.info(f"Loaded summary data for {len(df)} companies")
            return df
        except Exception as e:
            logger.error(f"Error loading summary data: {str(e)}")
            return pd.DataFrame()

    def load_master_database(self) -> pd.DataFrame:
        """
        Load the master database containing comprehensive data for all companies

        Returns:
        --------
        DataFrame containing comprehensive data for all companies
        """
        master_file = os.path.join(self.csv_dir, 'master_database.csv')
        if not os.path.exists(master_file):
            logger.error(f"Master database file not found: {master_file}")
            raise FileNotFoundError(f"Master database file not found: {master_file}")

        try:
            df = pd.read_csv(master_file)
            logger.info(f"Loaded master database with {len(df)} companies")
            return df
        except Exception as e:
            logger.error(f"Error loading master database: {str(e)}")
            return pd.DataFrame()

    def _process_company_data(self, data: Dict[str, Any], ticker: str) -> Dict[str, Any]:
        """
        Process company data to make it more usable for analysis

        Parameters:
        -----------
        data : Dict[str, Any]
            Raw company data from screener.in
        ticker : str
            Ticker symbol of the company

        Returns:
        --------
        Processed company data
        """
        processed_data = data.copy()

        try:
            # Process profit and loss data
            if 'profit_loss' in processed_data:
                pl_data = processed_data['profit_loss']

                # Get the years
                years = []
                for key in pl_data.keys():
                    if key not in ['units', 'notes'] and isinstance(pl_data[key], dict):
                        years = list(pl_data[key].keys())
                        break

                if years:
                    # Create a new structure with years as the top level
                    new_pl_data = {'units': pl_data.get('units', {})}

                    for year in years:
                        new_pl_data[year] = {}

                        for metric, values in pl_data.items():
                            if metric not in ['units', 'notes'] and isinstance(values, dict) and year in values:
                                # Map screener.in metrics to our standard metrics
                                mapped_metric = self._map_metric(metric)
                                new_pl_data[year][mapped_metric] = values[year]

                    processed_data['profit_loss'] = new_pl_data

            # Process balance sheet data
            if 'balance_sheet' in processed_data:
                bs_data = processed_data['balance_sheet']

                # Get the years
                years = []
                for key in bs_data.keys():
                    if key not in ['units', 'notes'] and isinstance(bs_data[key], dict):
                        years = list(bs_data[key].keys())
                        break

                if years:
                    # Create a new structure with years as the top level
                    new_bs_data = {'units': bs_data.get('units', {})}

                    for year in years:
                        new_bs_data[year] = {}

                        for metric, values in bs_data.items():
                            if metric not in ['units', 'notes'] and isinstance(values, dict) and year in values:
                                # Map screener.in metrics to our standard metrics
                                mapped_metric = self._map_metric(metric)
                                new_bs_data[year][mapped_metric] = values[year]

                    processed_data['balance_sheet'] = new_bs_data

            # Process cash flow data
            if 'cash_flow' in processed_data:
                cf_data = processed_data['cash_flow']

                # Get the years
                years = []
                for key in cf_data.keys():
                    if key not in ['units', 'notes'] and isinstance(cf_data[key], dict):
                        years = list(cf_data[key].keys())
                        break

                if years:
                    # Create a new structure with years as the top level
                    new_cf_data = {'units': cf_data.get('units', {})}

                    for year in years:
                        new_cf_data[year] = {}

                        for metric, values in cf_data.items():
                            if metric not in ['units', 'notes'] and isinstance(values, dict) and year in values:
                                # Map screener.in metrics to our standard metrics
                                mapped_metric = self._map_metric(metric)
                                new_cf_data[year][mapped_metric] = values[year]

                    processed_data['cash_flow'] = new_cf_data

            # Add data from all_companies.csv
            all_companies_file = os.path.join(self.csv_dir, 'all_companies.csv')
            if os.path.exists(all_companies_file):
                try:
                    df = pd.read_csv(all_companies_file)
                    if ticker in df['ticker'].values:
                        row = df[df['ticker'] == ticker].iloc[0]

                        # Add or update overview data
                        if 'overview' not in processed_data:
                            processed_data['overview'] = {}

                        overview = processed_data['overview']

                        # Add current price
                        if 'cmp' in row:
                            overview['current_price'] = row['cmp']

                        # Add P/E ratio
                        if 'pe' in row:
                            overview['stock_p/e'] = row['pe']

                        # Add market cap
                        if 'market_cap_cr' in row:
                            overview['market_cap'] = row['market_cap_cr']

                        # Add dividend yield
                        if 'div_yield_percent' in row:
                            overview['dividend_yield'] = row['div_yield_percent']

                        # Add ROCE
                        if 'roce_percent' in row:
                            if 'ratios' not in processed_data:
                                processed_data['ratios'] = {'ratios': {}}
                            processed_data['ratios']['ratios']['roce_%'] = row['roce_percent']

                        # Add quarterly data
                        if 'net_profit_qtr_cr' in row and 'sales_qtr_cr' in row:
                            if 'quarters' not in processed_data:
                                processed_data['quarters'] = {}

                            latest_quarter = f"Q{(pd.Timestamp.now().month - 1) // 3 + 1} {pd.Timestamp.now().year}"
                            processed_data['quarters'][latest_quarter] = {
                                'net_profit': row['net_profit_qtr_cr'],
                                'sales': row['sales_qtr_cr'],
                                'profit_growth': row.get('qtr_profit_var_percent', None),
                                'sales_growth': row.get('qtr_sales_var_percent', None)
                            }
                except Exception as e:
                    logger.error(f"Error adding data from all_companies.csv for {ticker}: {str(e)}")

        except Exception as e:
            logger.error(f"Error processing data for {ticker}: {str(e)}")

        return processed_data

    def _map_metric(self, metric: str) -> str:
        """
        Map screener.in metrics to our standard metrics

        Parameters:
        -----------
        metric : str
            Metric name from screener.in

        Returns:
        --------
        Mapped metric name
        """
        # Remove non-breaking spaces and other special characters
        metric = metric.replace('\xa0', ' ').replace('+', '').strip()

        # Map common metrics
        mapping = {
            'sales': 'revenue',
            'net profit': 'pat',
            'eps in rs': 'eps',
            'operating profit': 'ebitda',
            'profit before tax': 'pbt',
            'cash from operating activity': 'operating_cash_flow',
            'cash from investing activity': 'investing_cash_flow',
            'cash from financing activity': 'financing_cash_flow',
            'net cash flow': 'net_cash_flow',
            'total share capital': 'share_capital',
            'total reserves': 'reserves',
            'total debt': 'total_debt',
            'total assets': 'total_assets',
            'total liabilities': 'total_liabilities',
            'investments': 'investments',
            'net current assets': 'net_current_assets',
            'net fixed assets': 'fixed_assets',
            'opm %': 'operating_profit_margin',
            'tax %': 'tax_rate',
            'dividend payout %': 'dividend_payout'
        }

        return mapping.get(metric.lower(), metric.lower().replace(' ', '_'))

# Example usage
if __name__ == "__main__":
    loader = ScreenerDataLoader()
    tickers = loader.get_all_tickers()
    print(f"Found {len(tickers)} tickers")

    # Load data for a specific company
    if tickers:
        sample_ticker = tickers[0]
        data = loader.load_company_data(sample_ticker)
        print(f"Loaded data for {sample_ticker}: {list(data.keys())}")

    # Load summary data
    summary = loader.load_all_companies_summary()
    print(f"Summary data shape: {summary.shape}")
