#!/usr/bin/env python3
"""
Dashboard Module

This module provides an interactive dashboard for the fundamental analysis tool.
It uses <PERSON><PERSON><PERSON> and <PERSON> to create interactive visualizations and a web interface.
"""

import os
import logging
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Optional, Union, Any, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('dashboard')

class FundamentalDashboard:
    """
    Class to create interactive dashboards for fundamental analysis
    """

    def __init__(self, output_dir: str = 'output'):
        """
        Initialize the dashboard

        Parameters:
        -----------
        output_dir : str
            Directory to save dashboard outputs
        """
        self.output_dir = output_dir

        # Create the output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

    def create_screening_dashboard(self,
                                  screening_results: Dict[str, Any],
                                  filename: Optional[str] = None) -> Dict[str, Any]:
        """
        Create an interactive dashboard for screening results

        Parameters:
        -----------
        screening_results : Dict[str, Any]
            Results from the screener
        filename : str, optional
            Filename to save the dashboard HTML

        Returns:
        --------
        Dictionary containing Plotly figures
        """
        # Extract data
        summary = screening_results.get('summary', {})
        passed_companies = screening_results.get('passed_companies', {})
        results = screening_results.get('results', {})

        # Create figures
        figures = {}

        # 1. Funnel chart
        figures['funnel'] = self._create_screening_funnel(summary)

        # 2. Top companies
        figures['top_companies'] = self._create_top_companies_chart(results)

        # 3. Metric distributions
        figures['metric_distributions'] = self._create_metric_distributions(results, passed_companies.get('all', []))

        # 4. Metric correlations
        figures['metric_correlations'] = self._create_metric_correlations(results, passed_companies.get('all', []))

        # 5. Sector breakdown
        figures['sector_breakdown'] = self._create_sector_breakdown(results, passed_companies)

        # Save the dashboard if a filename is provided
        if filename:
            self._save_dashboard_html(figures, filename)

        return figures

    def _create_screening_funnel(self, summary: Dict[str, int]) -> go.Figure:
        """
        Create a funnel chart showing how many companies passed each tier

        Parameters:
        -----------
        summary : Dict[str, int]
            Summary of screening results

        Returns:
        --------
        Plotly figure
        """
        # Extract data
        stages = ['Pre-filtered', 'Tier 1', 'Tier 2', 'Tier 3', 'Tier 4', 'Tier 5', 'All Tiers']
        values = [
            summary.get('pre_filtered', 0),
            summary.get('tier1_pass', 0),
            summary.get('tier2_pass', 0),
            summary.get('tier3_pass', 0),
            summary.get('tier4_pass', 0),
            summary.get('tier5_pass', 0),
            summary.get('all_pass', 0)
        ]

        # Calculate percentages
        total = summary.get('total', 0)
        percentages = [100 * v / total if total > 0 else 0 for v in values]

        # Create text labels
        text = [f"{v} ({p:.1f}%)" for v, p in zip(values, percentages)]

        # Create figure
        fig = go.Figure()

        # Add funnel
        fig.add_trace(go.Funnel(
            y=stages,
            x=values,
            textposition="inside",
            textinfo="text",
            text=text,
            marker={"color": px.colors.sequential.Viridis},
            connector={"line": {"color": "royalblue", "dash": "dot", "width": 3}}
        ))

        # Update layout
        fig.update_layout(
            title="Screening Funnel",
            font=dict(size=14),
            height=500
        )

        return fig

    def _create_top_companies_chart(self,
                                   results: Dict[str, Dict[str, Any]],
                                   top_n: int = 20) -> go.Figure:
        """
        Create a bar chart of top companies by score

        Parameters:
        -----------
        results : Dict[str, Dict[str, Any]]
            Screening results for each company
        top_n : int
            Number of top companies to show

        Returns:
        --------
        Plotly figure
        """
        # Calculate a simple score for each company
        scores = {}
        for ticker, result in results.items():
            # Skip companies with errors
            if 'error' in result:
                continue

            # Calculate score based on how many tiers were passed
            score = sum([
                result.get('tier1_pass', False),
                result.get('tier2_pass', False),
                result.get('tier3_pass', False),
                result.get('tier4_pass', False)
            ])

            scores[ticker] = score

        # Sort companies by score
        sorted_companies = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:top_n]

        # Extract data for plotting
        tickers = [item[0] for item in sorted_companies]
        scores = [item[1] for item in sorted_companies]

        # Create figure
        fig = go.Figure()

        # Add bar chart
        fig.add_trace(go.Bar(
            x=scores,
            y=tickers,
            orientation='h',
            marker=dict(
                color=scores,
                colorscale='Viridis',
                colorbar=dict(title="Score")
            ),
            text=scores,
            textposition='auto'
        ))

        # Update layout
        fig.update_layout(
            title=f"Top {len(tickers)} Companies by Score",
            xaxis_title="Score (Tiers Passed)",
            yaxis_title="Company",
            height=600,
            margin=dict(l=150)  # Add margin for company names
        )

        return fig

    def _create_metric_distributions(self,
                                    results: Dict[str, Dict[str, Any]],
                                    passed_tickers: List[str]) -> go.Figure:
        """
        Create box plots showing the distribution of key metrics

        Parameters:
        -----------
        results : Dict[str, Dict[str, Any]]
            Screening results for each company
        passed_tickers : List[str]
            List of tickers that passed all tiers

        Returns:
        --------
        Plotly figure
        """
        # Extract metrics for passed companies
        metrics_data = {}

        for ticker in passed_tickers:
            if ticker in results and 'metrics' in results[ticker]:
                metrics = results[ticker]['metrics']
                for metric, value in metrics.items():
                    if value is not None:
                        if metric not in metrics_data:
                            metrics_data[metric] = []
                        metrics_data[metric].append(value)

        # Select key metrics to display
        key_metrics = [
            'roe', 'roce', 'debt_to_equity', 'interest_coverage',
            'revenue_cagr', 'profit_cagr', 'pe_ratio', 'pb_ratio'
        ]

        # Filter to only include metrics with data
        key_metrics = [m for m in key_metrics if m in metrics_data and len(metrics_data[m]) > 0]

        if not key_metrics:
            # Create empty figure with message
            fig = go.Figure()
            fig.add_annotation(
                text="No metric data available for passed companies",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            return fig

        # Create subplots
        num_metrics = len(key_metrics)
        num_cols = min(4, num_metrics)
        num_rows = (num_metrics + num_cols - 1) // num_cols

        fig = make_subplots(
            rows=num_rows,
            cols=num_cols,
            subplot_titles=[m.replace('_', ' ').title() for m in key_metrics]
        )

        # Add box plots for each metric
        for i, metric in enumerate(key_metrics):
            row = i // num_cols + 1
            col = i % num_cols + 1

            # Create box plot
            fig.add_trace(
                go.Box(
                    y=metrics_data[metric],
                    name=metric.replace('_', ' ').title(),
                    boxpoints='all',
                    jitter=0.3,
                    pointpos=-1.8,
                    marker=dict(color='blue', opacity=0.6),
                    line=dict(color='darkblue')
                ),
                row=row, col=col
            )

        # Update layout
        fig.update_layout(
            title=f"Metric Distributions for {len(passed_tickers)} Passed Companies",
            showlegend=False,
            height=300 * num_rows
        )

        return fig

    def _create_sector_breakdown(self,
                               results: Dict[str, Dict[str, Any]],
                               passed_companies: Dict[str, List[str]]) -> go.Figure:
        """
        Create a sector breakdown of passed companies

        Parameters:
        -----------
        results : Dict[str, Dict[str, Any]]
            Screening results for each company
        passed_companies : Dict[str, List[str]]
            Dictionary mapping tier names to lists of tickers that passed

        Returns:
        --------
        Plotly figure
        """
        # Create a DataFrame to store sector data
        data = []

        # Process each tier
        for tier, tickers in passed_companies.items():
            if tier == 'all':
                continue

            # Count companies by sector
            sector_counts = {}

            for ticker in tickers:
                if ticker in results and 'company_data' in results[ticker]:
                    company_data = results[ticker]['company_data']
                    sector = company_data.get('overview', {}).get('sector', 'Unknown')

                    if sector not in sector_counts:
                        sector_counts[sector] = 0
                    sector_counts[sector] += 1

            # Add to data
            for sector, count in sector_counts.items():
                data.append({
                    'Tier': tier,
                    'Sector': sector,
                    'Count': count
                })

        if not data:
            # Create empty figure with message
            fig = go.Figure()
            fig.add_annotation(
                text="No sector data available",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            return fig

        # Create DataFrame
        df = pd.DataFrame(data)

        # Create figure
        fig = px.bar(
            df,
            x='Sector',
            y='Count',
            color='Tier',
            barmode='group',
            title='Sector Breakdown by Tier',
            labels={'Count': 'Number of Companies', 'Sector': 'Sector'},
            height=500
        )

        # Update layout
        fig.update_layout(
            xaxis={'categoryorder': 'total descending'}
        )

        return fig

    def create_company_dashboard(self,
                               company_data: Dict[str, Any],
                               ticker: str,
                               filename: Optional[str] = None) -> Dict[str, Any]:
        """
        Create an interactive dashboard for a single company

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data
        ticker : str
            Ticker symbol
        filename : str, optional
            Filename to save the dashboard HTML

        Returns:
        --------
        Dictionary containing Plotly figures
        """
        # Create figures
        figures = {}

        # 1. Financial trends
        figures['financial_trends'] = self._create_financial_trends(company_data, ticker)

        # 2. Ratio analysis
        figures['ratio_analysis'] = self._create_ratio_analysis(company_data, ticker)

        # 3. Cash flow analysis
        figures['cash_flow_analysis'] = self._create_cash_flow_analysis(company_data, ticker)

        # 4. Valuation metrics
        figures['valuation_metrics'] = self._create_valuation_metrics(company_data, ticker)

        # Save the dashboard if a filename is provided
        if filename:
            self._save_dashboard_html(figures, filename)

        return figures

    def _create_financial_trends(self, company_data: Dict[str, Any], ticker: str) -> go.Figure:
        """
        Create a chart showing financial trends over time

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data
        ticker : str
            Ticker symbol

        Returns:
        --------
        Plotly figure
        """
        # Extract profit and loss data
        pl_data = company_data.get('profit_loss', {})

        # Remove units key if present
        if 'units' in pl_data:
            units = pl_data.pop('units')
        else:
            units = {}

        # Get years
        years = sorted([year for year in pl_data.keys() if year != 'units'])

        if not years:
            # Create empty figure with message
            fig = go.Figure()
            fig.add_annotation(
                text="No financial data available",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            return fig

        # Extract key metrics
        metrics = ['revenue', 'ebitda', 'pat']

        # Create figure with secondary y-axis
        fig = make_subplots(specs=[[{"secondary_y": True}]])

        # Add traces for each metric
        for i, metric in enumerate(metrics):
            values = []
            for year in years:
                if metric in pl_data[year]:
                    value = pl_data[year][metric]
                    if isinstance(value, str):
                        try:
                            value = float(value.replace(',', ''))
                        except ValueError:
                            value = None
                    values.append(value)
                else:
                    values.append(None)

            # Skip if no values
            if not values or all(v is None for v in values):
                continue

            # Determine if this should be on primary or secondary axis
            secondary_y = (metric == 'pat')

            # Add trace
            fig.add_trace(
                go.Scatter(
                    x=years,
                    y=values,
                    mode='lines+markers',
                    name=metric.upper(),
                    line=dict(width=3)
                ),
                secondary_y=secondary_y
            )

        # Update layout
        fig.update_layout(
            title=f"{ticker} Financial Trends",
            xaxis_title="Year",
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            ),
            height=500
        )

        # Update y-axis labels
        fig.update_yaxes(title_text="Revenue & EBITDA", secondary_y=False)
        fig.update_yaxes(title_text="PAT", secondary_y=True)

        return fig

    def _create_ratio_analysis(self, company_data: Dict[str, Any], ticker: str) -> go.Figure:
        """
        Create a chart showing key financial ratios

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data
        ticker : str
            Ticker symbol

        Returns:
        --------
        Plotly figure
        """
        # Extract ratios
        ratios = company_data.get('ratios', {})

        # Get profitability ratios
        profitability = ratios.get('profitability', {})
        leverage = ratios.get('leverage', {})
        valuation = ratios.get('valuation', {})

        if not profitability and not leverage and not valuation:
            # Create empty figure with message
            fig = go.Figure()
            fig.add_annotation(
                text="No ratio data available",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            return fig

        # Create subplots
        fig = make_subplots(
            rows=3,
            cols=1,
            subplot_titles=["Profitability Ratios", "Leverage Ratios", "Valuation Ratios"],
            vertical_spacing=0.1
        )

        # Add profitability ratios
        if profitability:
            ratio_names = []
            ratio_values = []

            for name, value in profitability.items():
                ratio_names.append(name.replace('_', ' ').title())
                ratio_values.append(value)

            fig.add_trace(
                go.Bar(
                    x=ratio_names,
                    y=ratio_values,
                    marker_color='rgb(55, 83, 109)',
                    text=ratio_values,
                    texttemplate='%{text:.2f}%',
                    textposition='auto'
                ),
                row=1, col=1
            )

        # Add leverage ratios
        if leverage:
            ratio_names = []
            ratio_values = []

            for name, value in leverage.items():
                ratio_names.append(name.replace('_', ' ').title())
                ratio_values.append(value)

            fig.add_trace(
                go.Bar(
                    x=ratio_names,
                    y=ratio_values,
                    marker_color='rgb(26, 118, 255)',
                    text=ratio_values,
                    texttemplate='%{text:.2f}',
                    textposition='auto'
                ),
                row=2, col=1
            )

        # Add valuation ratios
        if valuation:
            ratio_names = []
            ratio_values = []

            for name, value in valuation.items():
                ratio_names.append(name.replace('_', ' ').title())
                ratio_values.append(value)

            fig.add_trace(
                go.Bar(
                    x=ratio_names,
                    y=ratio_values,
                    marker_color='rgb(33, 75, 99)',
                    text=ratio_values,
                    texttemplate='%{text:.2f}',
                    textposition='auto'
                ),
                row=3, col=1
            )

        # Update layout
        fig.update_layout(
            title=f"{ticker} Financial Ratios",
            showlegend=False,
            height=800
        )

        return fig

    def _create_cash_flow_analysis(self, company_data: Dict[str, Any], ticker: str) -> go.Figure:
        """
        Create a chart showing cash flow analysis

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data
        ticker : str
            Ticker symbol

        Returns:
        --------
        Plotly figure
        """
        # Extract cash flow data
        cf_data = company_data.get('cash_flow', {})

        # Remove units key if present
        if 'units' in cf_data:
            units = cf_data.pop('units')
        else:
            units = {}

        # Get years
        years = sorted([year for year in cf_data.keys() if year != 'units' and year != 'notes'])

        if not years:
            # Create empty figure with message
            fig = go.Figure()
            fig.add_annotation(
                text="No cash flow data available",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            return fig

        # Extract key metrics
        metrics = ['operating_cash_flow', 'investing_cash_flow', 'financing_cash_flow', 'net_cash_flow']

        # Create figure
        fig = go.Figure()

        # Add traces for each metric
        for metric in metrics:
            values = []
            for year in years:
                if metric in cf_data[year]:
                    value = cf_data[year][metric]
                    if isinstance(value, str):
                        try:
                            value = float(value.replace(',', ''))
                        except ValueError:
                            value = None
                    values.append(value)
                else:
                    values.append(None)

            # Skip if no values
            if not values or all(v is None for v in values):
                continue

            # Add trace
            fig.add_trace(
                go.Bar(
                    x=years,
                    y=values,
                    name=metric.replace('_', ' ').title()
                )
            )

        # Update layout
        fig.update_layout(
            title=f"{ticker} Cash Flow Analysis",
            xaxis_title="Year",
            yaxis_title="Amount",
            barmode='group',
            height=500
        )

        return fig

    def _create_valuation_metrics(self, company_data: Dict[str, Any], ticker: str) -> go.Figure:
        """
        Create a chart showing valuation metrics

        Parameters:
        -----------
        company_data : Dict[str, Any]
            Company data
        ticker : str
            Ticker symbol

        Returns:
        --------
        Plotly figure
        """
        # Extract DCF analysis data
        dcf_analysis = company_data.get('dcf_analysis', {})
        intrinsic_value_band = company_data.get('intrinsic_value_band', {})

        if not dcf_analysis and not intrinsic_value_band:
            # Create empty figure with message
            fig = go.Figure()
            fig.add_annotation(
                text="No valuation data available",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            return fig

        # Create figure
        fig = make_subplots(
            rows=2,
            cols=1,
            subplot_titles=["DCF Valuation", "Intrinsic Value Band"],
            vertical_spacing=0.2
        )

        # Add DCF valuation metrics
        if dcf_analysis:
            metrics = ['base_fcf', 'enterprise_value', 'equity_value', 'intrinsic_value_per_share']
            values = [dcf_analysis.get(m) for m in metrics]
            labels = [m.replace('_', ' ').title() for m in metrics]

            fig.add_trace(
                go.Bar(
                    x=labels,
                    y=values,
                    marker_color='rgb(55, 83, 109)',
                    text=values,
                    texttemplate='%{text:.2f}',
                    textposition='auto'
                ),
                row=1, col=1
            )

        # Add intrinsic value band
        if intrinsic_value_band:
            metrics = ['lower_value', 'base_value', 'upper_value', 'current_price']
            values = [intrinsic_value_band.get(m) for m in metrics]
            labels = [m.replace('_', ' ').title() for m in metrics]

            # Create color map
            colors = ['green', 'green', 'green', 'red' if values[3] > values[2] else 'green']

            fig.add_trace(
                go.Bar(
                    x=labels,
                    y=values,
                    marker_color=colors,
                    text=values,
                    texttemplate='%{text:.2f}',
                    textposition='auto'
                ),
                row=2, col=1
            )

        # Update layout
        fig.update_layout(
            title=f"{ticker} Valuation Analysis",
            showlegend=False,
            height=700
        )

        return fig

    def _save_dashboard_html(self, figures: Dict[str, go.Figure], filename: str) -> None:
        """
        Save dashboard figures to an HTML file

        Parameters:
        -----------
        figures : Dict[str, go.Figure]
            Dictionary of Plotly figures
        filename : str
            Filename to save the dashboard HTML
        """
        try:
            # Import plotly.offline here to avoid importing it if not needed
            import plotly.offline as pyo

            # Create HTML content
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Fundamental Analysis Dashboard</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                    }
                    .dashboard-container {
                        display: flex;
                        flex-direction: column;
                        gap: 20px;
                    }
                    .figure-container {
                        border: 1px solid #ddd;
                        border-radius: 5px;
                        padding: 10px;
                        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    }
                    h1 {
                        color: #333;
                    }
                </style>
            </head>
            <body>
                <h1>Fundamental Analysis Dashboard</h1>
                <div class="dashboard-container">
            """

            # Add each figure
            for name, fig in figures.items():
                div = pyo.plot(fig, include_plotlyjs=False, output_type='div')
                html_content += f'<div class="figure-container">{div}</div>\n'

            # Close HTML
            html_content += """
                </div>
                <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            </body>
            </html>
            """

            # Save to file
            save_path = os.path.join(self.output_dir, filename)
            with open(save_path, 'w') as f:
                f.write(html_content)

            logger.info(f"Saved dashboard to {save_path}")

        except Exception as e:
            logger.error(f"Error saving dashboard HTML: {str(e)}")

    def _create_metric_correlations(self,
                                   results: Dict[str, Dict[str, Any]],
                                   passed_tickers: List[str]) -> go.Figure:
        """
        Create a correlation matrix for key metrics

        Parameters:
        -----------
        results : Dict[str, Dict[str, Any]]
            Screening results for each company
        passed_tickers : List[str]
            List of tickers that passed all tiers

        Returns:
        --------
        Plotly figure
        """
        # Extract metrics for passed companies
        data = []

        for ticker in passed_tickers:
            if ticker in results and 'metrics' in results[ticker]:
                row = {'ticker': ticker}
                row.update(results[ticker]['metrics'])
                data.append(row)

        if not data:
            # Create empty figure with message
            fig = go.Figure()
            fig.add_annotation(
                text="No metric data available for passed companies",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            return fig

        # Create DataFrame
        df = pd.DataFrame(data)
        df.set_index('ticker', inplace=True)

        # Select key metrics
        key_metrics = [
            'roe', 'roce', 'debt_to_equity', 'interest_coverage',
            'revenue_cagr', 'profit_cagr', 'pe_ratio', 'pb_ratio'
        ]

        # Filter to only include metrics with data
        key_metrics = [m for m in key_metrics if m in df.columns]

        # Calculate correlation matrix
        corr_matrix = df[key_metrics].corr()

        # Create heatmap
        fig = go.Figure(data=go.Heatmap(
            z=corr_matrix.values,
            x=corr_matrix.columns,
            y=corr_matrix.index,
            colorscale='RdBu_r',
            zmin=-1, zmax=1,
            text=np.round(corr_matrix.values, 2),
            texttemplate="%{text:.2f}",
            colorbar=dict(title="Correlation")
        ))

        # Update layout
        fig.update_layout(
            title="Correlation Matrix of Key Metrics",
            height=600,
            width=700
        )

        return fig
