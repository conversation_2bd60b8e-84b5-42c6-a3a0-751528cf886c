#!/bin/bash

# Run Fundamental Analysis Tool
# This script provides a convenient way to run the fundamental analysis tool

# Function to display usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Run the fundamental analysis tool"
    echo ""
    echo "Options:"
    echo "  --ticker TICKER       Analyze a specific ticker"
    echo "  --tickers TICKERS     Analyze multiple tickers (comma-separated)"
    echo "  --analysis TYPE       Type of analysis to perform (financial_ratios, valuation, checklist, full)"
    echo "  --report TYPE         Generate a report (full, summary, valuation, ratios, checklist)"
    echo "  --output FILE         Output file for the report"
    echo "  --filter FILE         Filter file (JSON or YAML)"
    echo "  --rank                Rank companies"
    echo "  --example EXAMPLE     Run an example (analyze_tcs, rank_companies)"
    echo "  --help                Display this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --ticker RELIANCE"
    echo "  $0 --tickers RELIANCE,TCS,HDFCBANK --rank"
    echo "  $0 --filter filters/buffett_filter.json --rank"
    echo "  $0 --example analyze_tcs"
}

# Check if no arguments were provided
if [ $# -eq 0 ]; then
    usage
    exit 0
fi

# Parse command line arguments
EXAMPLE=""

while [[ $# -gt 0 ]]; do
    case "$1" in
        --example)
            EXAMPLE="$2"
            shift 2
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            # Pass all other arguments to the Python script
            break
            ;;
    esac
done

# Activate conda environment if available
if command -v conda &> /dev/null; then
    eval "$(conda shell.bash hook)"
    if conda env list | grep -q "edit"; then
        echo "Activating conda environment 'edit'..."
        conda activate edit
    fi
fi

# Run an example if specified
if [ -n "$EXAMPLE" ]; then
    case "$EXAMPLE" in
        analyze_tcs)
            echo "Running TCS analysis example..."
            python examples/analyze_tcs.py
            ;;
        rank_companies)
            echo "Running company ranking example..."
            python examples/rank_companies.py
            ;;
        *)
            echo "Unknown example: $EXAMPLE"
            echo "Available examples: analyze_tcs, rank_companies"
            exit 1
            ;;
    esac
else
    # Run the analysis script with the provided arguments
    python analysis.py "$@"
fi
