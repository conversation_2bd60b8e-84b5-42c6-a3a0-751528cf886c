#!/usr/bin/env python3
"""
Test Enhanced Analysis System

This script tests the complete enhanced analysis system including:
1. Cash flow pre-screening
2. Enhanced sector analysis and discovery
3. Qualitative analysis framework
4. Integration of all components
"""

import os
import sys
import json
import logging

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules
from utils.data_loader import ScreenerDataLoader
from models.cashflow_prescreener import CashFlowPreScreener
from models.sector_analyzer import SectorAnalyzer
from models.qualitative_analyzer import QualitativeAnalyzer
from models.consistency_analyzer import ConsistencyAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_enhanced_system')

def test_cash_flow_prescreening():
    """Test the cash flow pre-screening system"""
    print("=" * 60)
    print("TESTING CASH FLOW PRE-SCREENING")
    print("=" * 60)
    
    # Initialize components
    data_loader = ScreenerDataLoader('../screener_data_collector/data')
    prescreener = CashFlowPreScreener()
    
    # Test with sample companies
    test_tickers = ['TCS', 'HDFCBANK', 'RELIANCE', 'BAJFINANCE', 'ICICIBANK']
    
    results = {}
    for ticker in test_tickers:
        print(f"\nTesting {ticker}...")
        
        company_data = data_loader.load_company_data(ticker)
        if company_data:
            prescreen_result = prescreener.prescreen_company(company_data)
            results[ticker] = prescreen_result
            
            passed = prescreen_result['passed_prescreen']
            score = prescreen_result['cash_flow_score']
            
            print(f"  Cash Flow Score: {score:.1f}")
            print(f"  Passed Pre-screen: {'✓' if passed else '✗'}")
            
            if passed:
                print(f"  Reasons: {', '.join(prescreen_result['reasons_passed'])}")
            else:
                print(f"  Failed: {', '.join(prescreen_result['reasons_failed'])}")
        else:
            print(f"  No data available for {ticker}")
    
    # Summary
    passed_count = sum(1 for r in results.values() if r['passed_prescreen'])
    print(f"\nPre-screening Summary:")
    print(f"  Companies tested: {len(results)}")
    print(f"  Passed pre-screening: {passed_count}")
    print(f"  Pass rate: {(passed_count/len(results)*100):.1f}%")
    
    return results

def test_sector_discovery():
    """Test the enhanced sector discovery system"""
    print("\n" + "=" * 60)
    print("TESTING SECTOR DISCOVERY")
    print("=" * 60)
    
    # Initialize components
    data_loader = ScreenerDataLoader('../screener_data_collector/data')
    sector_analyzer = SectorAnalyzer()
    
    # Load data for a sample of companies
    all_tickers = data_loader.get_all_tickers()
    sample_tickers = all_tickers[:50]  # Test with first 50 companies
    
    print(f"Testing sector discovery with {len(sample_tickers)} companies...")
    
    # Load company data
    companies_data = {}
    for ticker in sample_tickers:
        company_data = data_loader.load_company_data(ticker)
        if company_data:
            companies_data[ticker] = company_data
    
    print(f"Loaded data for {len(companies_data)} companies")
    
    # Discover sectors
    sector_discovery = sector_analyzer.discover_all_sectors_and_players(companies_data)
    
    # Print results
    sectors_found = sector_discovery['sectors_found']
    print(f"\nSectors discovered: {len(sectors_found)}")
    
    for sector, companies in sectors_found.items():
        if len(companies) >= 3:  # Only show sectors with multiple companies
            print(f"  {sector}: {len(companies)} companies")
            print(f"    Companies: {', '.join(companies[:5])}{'...' if len(companies) > 5 else ''}")
    
    # Print insights
    insights = sector_discovery.get('insights', [])
    print(f"\nSector Insights:")
    for insight in insights:
        print(f"  • {insight}")
    
    # Export sector mapping
    output_file = sector_analyzer.export_sector_mapping(sector_discovery)
    print(f"\n✓ Sector mapping exported to {output_file}")
    
    return sector_discovery

def test_qualitative_analysis():
    """Test the qualitative analysis framework"""
    print("\n" + "=" * 60)
    print("TESTING QUALITATIVE ANALYSIS")
    print("=" * 60)
    
    # Initialize components
    data_loader = ScreenerDataLoader('../screener_data_collector/data')
    qualitative_analyzer = QualitativeAnalyzer()
    sector_analyzer = SectorAnalyzer()
    
    # Test with sample companies
    test_tickers = ['TCS', 'HDFCBANK', 'RELIANCE', 'BAJFINANCE']
    
    results = {}
    for ticker in test_tickers:
        print(f"\nTesting qualitative analysis for {ticker}...")
        
        company_data = data_loader.load_company_data(ticker)
        if company_data:
            # Classify sector
            sector = sector_analyzer.classify_sector(company_data)
            
            # Perform qualitative analysis
            qualitative_result = qualitative_analyzer.analyze_company_qualitative_factors(
                company_data, sector=sector
            )
            results[ticker] = qualitative_result
            
            score = qualitative_result['qualitative_score']
            print(f"  Sector: {sector}")
            print(f"  Qualitative Score: {score:.1f}")
            
            # Print component scores
            component_scores = qualitative_result['component_scores']
            print(f"  Component Scores:")
            for component, data in component_scores.items():
                comp_score = data.get('score', 0)
                print(f"    {component.replace('_', ' ').title()}: {comp_score:.1f}")
            
            # Print SWOT summary
            strengths = qualitative_result.get('strengths', [])
            weaknesses = qualitative_result.get('weaknesses', [])
            if strengths:
                print(f"  Strengths: {', '.join(strengths[:2])}")
            if weaknesses:
                print(f"  Weaknesses: {', '.join(weaknesses[:2])}")
            
            # Print investment thesis
            thesis = qualitative_result.get('investment_thesis', '')
            if thesis:
                print(f"  Investment Thesis: {thesis[:100]}...")
        else:
            print(f"  No data available for {ticker}")
    
    return results

def test_integrated_analysis():
    """Test the integrated analysis system"""
    print("\n" + "=" * 60)
    print("TESTING INTEGRATED ANALYSIS")
    print("=" * 60)
    
    # Initialize all components
    data_loader = ScreenerDataLoader('../screener_data_collector/data')
    prescreener = CashFlowPreScreener()
    sector_analyzer = SectorAnalyzer()
    qualitative_analyzer = QualitativeAnalyzer()
    consistency_analyzer = ConsistencyAnalyzer()
    
    # Test with sample companies
    test_tickers = ['TCS', 'HDFCBANK', 'RELIANCE', 'BAJFINANCE', 'ICICIBANK']
    
    integrated_results = {}
    
    for ticker in test_tickers:
        print(f"\nIntegrated analysis for {ticker}...")
        
        company_data = data_loader.load_company_data(ticker)
        if not company_data:
            print(f"  No data available for {ticker}")
            continue
        
        result = {
            'ticker': ticker,
            'analysis_stages': {}
        }
        
        # Stage 1: Cash Flow Pre-screening
        prescreen_result = prescreener.prescreen_company(company_data)
        result['analysis_stages']['cash_flow_prescreen'] = prescreen_result
        
        cash_flow_passed = prescreen_result['passed_prescreen']
        cash_flow_score = prescreen_result['cash_flow_score']
        
        print(f"  Stage 1 - Cash Flow Pre-screen: {'PASS' if cash_flow_passed else 'FAIL'} (Score: {cash_flow_score:.1f})")
        
        if cash_flow_passed:
            # Stage 2: Sector Classification
            sector = sector_analyzer.classify_sector(company_data)
            result['sector'] = sector
            print(f"  Stage 2 - Sector Classification: {sector}")
            
            # Stage 3: Consistency Analysis
            consistency_result = consistency_analyzer.analyze_company_consistency(company_data)
            result['analysis_stages']['consistency'] = consistency_result
            
            consistency_score = consistency_result.get('overall_consistency_score', 0)
            print(f"  Stage 3 - Consistency Analysis: {consistency_score:.1f}")
            
            # Stage 4: Qualitative Analysis
            qualitative_result = qualitative_analyzer.analyze_company_qualitative_factors(
                company_data, sector=sector
            )
            result['analysis_stages']['qualitative'] = qualitative_result
            
            qualitative_score = qualitative_result['qualitative_score']
            print(f"  Stage 4 - Qualitative Analysis: {qualitative_score:.1f}")
            
            # Stage 5: Overall Assessment
            overall_score = (cash_flow_score * 0.3 + 
                           consistency_score * 0.4 + 
                           qualitative_score * 0.3)
            result['overall_score'] = overall_score
            result['recommendation'] = 'BUY' if overall_score > 70 else 'HOLD' if overall_score > 50 else 'AVOID'
            
            print(f"  Overall Score: {overall_score:.1f}")
            print(f"  Recommendation: {result['recommendation']}")
        else:
            result['overall_score'] = 0
            result['recommendation'] = 'REJECT'
            print(f"  Result: REJECTED at pre-screening stage")
        
        integrated_results[ticker] = result
    
    # Summary
    print(f"\n" + "=" * 60)
    print("INTEGRATED ANALYSIS SUMMARY")
    print("=" * 60)
    
    passed_prescreen = sum(1 for r in integrated_results.values() 
                          if r['analysis_stages'].get('cash_flow_prescreen', {}).get('passed_prescreen', False))
    
    print(f"Companies tested: {len(integrated_results)}")
    print(f"Passed cash flow pre-screening: {passed_prescreen}")
    
    # Show top performers
    qualified_companies = [(ticker, result) for ticker, result in integrated_results.items() 
                          if result.get('overall_score', 0) > 0]
    qualified_companies.sort(key=lambda x: x[1]['overall_score'], reverse=True)
    
    print(f"\nTop Performers:")
    for i, (ticker, result) in enumerate(qualified_companies[:3], 1):
        score = result['overall_score']
        recommendation = result['recommendation']
        sector = result.get('sector', 'Unknown')
        print(f"  {i}. {ticker} ({sector}): {score:.1f} - {recommendation}")
    
    return integrated_results

def main():
    """Main function to run all tests"""
    print("ENHANCED ANALYSIS SYSTEM TEST SUITE")
    print("=" * 80)
    
    try:
        # Test 1: Cash Flow Pre-screening
        cashflow_results = test_cash_flow_prescreening()
        
        # Test 2: Sector Discovery
        sector_results = test_sector_discovery()
        
        # Test 3: Qualitative Analysis
        qualitative_results = test_qualitative_analysis()
        
        # Test 4: Integrated Analysis
        integrated_results = test_integrated_analysis()
        
        # Save all results
        os.makedirs('output', exist_ok=True)
        
        all_results = {
            'cashflow_prescreening': cashflow_results,
            'sector_discovery': sector_results,
            'qualitative_analysis': qualitative_results,
            'integrated_analysis': integrated_results
        }
        
        with open('output/enhanced_system_test_results.json', 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        print(f"\n" + "=" * 80)
        print("✅ ALL TESTS COMPLETED SUCCESSFULLY!")
        print("✅ Enhanced analysis system is ready for production")
        print("✅ Results saved to output/enhanced_system_test_results.json")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        logger.error(f"Test suite failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🚀 Ready to run enhanced analysis on all 4903 companies!")
        print(f"\nNext steps:")
        print(f"1. Run cash flow pre-screening on all companies")
        print(f"2. Perform comprehensive analysis on companies that pass")
        print(f"3. Generate sector-wise investment recommendations")
    else:
        print(f"\n⚠️ Please fix the issues before proceeding to full analysis")
