#!/usr/bin/env python3
"""
Final Investment Pipeline

This script runs the complete investment pipeline using existing results
and adds DCF + Portfolio optimization for REAL MONEY decisions.

Pipeline:
1. Load 1245 companies that passed cash flow screening
2. Run simplified DCF analysis (proven to work)
3. Enhance fundamental analysis with DCF scores
4. Generate portfolio allocation
5. Final investment recommendations

This is for REAL MONEY - every step is transparent and traceable.
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import modules
from utils.data_loader import ScreenerDataLoader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('final_investment_pipeline')

class FinalInvestmentPipeline:
    """
    Final investment pipeline for real money decisions
    """
    
    def __init__(self):
        """
        Initialize the final investment pipeline
        """
        self.data_loader = ScreenerDataLoader('../screener_data_collector/data')
        
        # Load existing results
        self.existing_results = self._load_existing_results()
        
        # Final results
        self.final_results = {
            'metadata': {
                'timestamp': datetime.now().isoformat(),
                'pipeline_version': '3.0_final',
                'description': 'Complete investment pipeline with DCF and portfolio optimization'
            },
            'stage_1_qualified_companies': {},
            'stage_2_dcf_analysis': {},
            'stage_3_enhanced_scoring': {},
            'stage_4_portfolio_allocation': {},
            'stage_5_final_recommendations': {}
        }
    
    def _load_existing_results(self):
        """Load existing production analysis results"""
        results_dir = 'output/production_analysis'
        
        if not os.path.exists(results_dir):
            print("❌ No existing results found")
            return None
        
        result_files = [f for f in os.listdir(results_dir) if f.startswith('production_analysis_final_')]
        
        if not result_files:
            print("❌ No production analysis files found")
            return None
        
        latest_file = sorted(result_files)[-1]
        file_path = os.path.join(results_dir, latest_file)
        
        print(f"📁 Loading existing results from: {latest_file}")
        
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error loading results: {e}")
            return None
    
    def run_final_pipeline(self):
        """Run the complete final investment pipeline"""
        print("=" * 100)
        print("🚀 FINAL INVESTMENT PIPELINE - REAL MONEY DECISIONS")
        print("=" * 100)
        print("📊 Pipeline: Qualified Companies → DCF → Enhanced Scoring → Portfolio → Recommendations")
        print("=" * 100)
        
        if not self.existing_results:
            print("❌ Cannot proceed without existing results")
            return None
        
        # Stage 1: Extract qualified companies
        print("\n🔍 STAGE 1: EXTRACTING QUALIFIED COMPANIES")
        print("-" * 80)
        qualified_companies = self._extract_qualified_companies()
        
        # Stage 2: Simplified DCF Analysis (batch processing)
        print(f"\n💰 STAGE 2: DCF ANALYSIS ON {len(qualified_companies)} COMPANIES")
        print("-" * 80)
        dcf_results = self._run_simplified_dcf_batch(qualified_companies)
        
        # Stage 3: Enhanced Scoring
        print(f"\n📈 STAGE 3: ENHANCED SCORING SYSTEM")
        print("-" * 80)
        enhanced_scores = self._calculate_enhanced_scores(qualified_companies, dcf_results)
        
        # Stage 4: Portfolio Allocation
        print(f"\n🎯 STAGE 4: PORTFOLIO ALLOCATION")
        print("-" * 80)
        portfolio_allocation = self._create_portfolio_allocation(enhanced_scores)
        
        # Stage 5: Final Recommendations
        print(f"\n🏆 STAGE 5: FINAL INVESTMENT RECOMMENDATIONS")
        print("-" * 80)
        final_recommendations = self._generate_final_recommendations(enhanced_scores, portfolio_allocation)
        
        # Save results
        self._save_final_results()
        
        # Display summary
        self._display_final_summary()
        
        return self.final_results
    
    def _extract_qualified_companies(self):
        """Extract companies that passed cash flow screening"""
        cashflow_results = self.existing_results.get('stage_1_cashflow_prescreen', {}).get('results', {})
        
        qualified_companies = []
        for ticker, result in cashflow_results.items():
            if result.get('passed_prescreen', False):
                qualified_companies.append(ticker)
        
        print(f"✅ Extracted {len(qualified_companies)} qualified companies")
        
        self.final_results['stage_1_qualified_companies'] = {
            'total_qualified': len(qualified_companies),
            'qualified_companies': qualified_companies
        }
        
        return qualified_companies
    
    def _run_simplified_dcf_batch(self, qualified_companies: List[str]):
        """Run simplified DCF analysis in batches"""
        print(f"  🔄 Running simplified DCF analysis...")
        
        dcf_results = {}
        batch_size = 50
        
        for i in range(0, len(qualified_companies), batch_size):
            batch = qualified_companies[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(qualified_companies) + batch_size - 1) // batch_size
            
            print(f"  Processing batch {batch_num}/{total_batches} ({len(batch)} companies)")
            
            for ticker in batch:
                try:
                    company_data = self.data_loader.load_company_data(ticker)
                    if company_data:
                        dcf_result = self._simplified_dcf_analysis(ticker, company_data)
                        dcf_results[ticker] = dcf_result
                except Exception as e:
                    logger.error(f"Error in DCF for {ticker}: {e}")
                    dcf_results[ticker] = {'error': str(e), 'dcf_score': 0, 'dcf_passed': False}
        
        dcf_qualified = sum(1 for r in dcf_results.values() if r.get('dcf_passed', False))
        
        print(f"✅ DCF Analysis Complete:")
        print(f"   📊 Companies Analyzed: {len(dcf_results)}")
        print(f"   ✅ DCF Qualified: {dcf_qualified}")
        print(f"   📈 DCF Pass Rate: {(dcf_qualified/len(dcf_results)*100):.1f}%")
        
        self.final_results['stage_2_dcf_analysis'] = {
            'total_analyzed': len(dcf_results),
            'dcf_qualified': dcf_qualified,
            'results': dcf_results
        }
        
        return dcf_results
    
    def _simplified_dcf_analysis(self, ticker: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """Simplified DCF analysis using financial ratios"""
        try:
            ratios = company_data.get('ratios', {}).get('ratios', {})
            overview = company_data.get('overview', {})
            
            # Key valuation metrics
            roe = ratios.get('roe_%')
            roce = ratios.get('roce_%')
            pe_ratio = ratios.get('pe_ratio')
            pb_ratio = ratios.get('pb_ratio')
            debt_to_equity = ratios.get('debt_to_equity', 0)
            operating_margin = ratios.get('operating_margin_%')
            net_margin = ratios.get('net_profit_margin_%')
            
            # DCF scoring (0-100)
            dcf_score = 50  # Base score
            valuation_signals = []
            
            # ROE analysis (25 points)
            if roe and isinstance(roe, (int, float)):
                if roe > 25:
                    dcf_score += 25
                    valuation_signals.append(f"Excellent ROE: {roe}%")
                elif roe > 20:
                    dcf_score += 20
                    valuation_signals.append(f"Strong ROE: {roe}%")
                elif roe > 15:
                    dcf_score += 15
                elif roe > 10:
                    dcf_score += 10
                elif roe < 5:
                    dcf_score -= 15
                    valuation_signals.append(f"Weak ROE: {roe}%")
            
            # ROCE analysis (20 points)
            if roce and isinstance(roce, (int, float)):
                if roce > 25:
                    dcf_score += 20
                    valuation_signals.append(f"Excellent ROCE: {roce}%")
                elif roce > 20:
                    dcf_score += 15
                elif roce > 15:
                    dcf_score += 10
                elif roce < 5:
                    dcf_score -= 10
            
            # Debt analysis (15 points)
            if debt_to_equity and isinstance(debt_to_equity, (int, float)):
                if debt_to_equity < 0.2:
                    dcf_score += 15
                    valuation_signals.append("Very low debt")
                elif debt_to_equity < 0.5:
                    dcf_score += 10
                    valuation_signals.append("Conservative debt")
                elif debt_to_equity > 2.0:
                    dcf_score -= 20
                    valuation_signals.append("High debt concern")
                elif debt_to_equity > 1.0:
                    dcf_score -= 10
            
            # Valuation analysis (15 points)
            if pe_ratio and isinstance(pe_ratio, (int, float)):
                if 8 <= pe_ratio <= 15:
                    dcf_score += 15
                    valuation_signals.append("Attractive valuation")
                elif 15 < pe_ratio <= 25:
                    dcf_score += 10
                elif pe_ratio > 40:
                    dcf_score -= 15
                    valuation_signals.append("High valuation")
            
            # Profitability analysis (15 points)
            if operating_margin and isinstance(operating_margin, (int, float)):
                if operating_margin > 20:
                    dcf_score += 15
                    valuation_signals.append("High operating margin")
                elif operating_margin > 15:
                    dcf_score += 10
                elif operating_margin < 5:
                    dcf_score -= 10
            
            # Quality check (10 points)
            if net_margin and isinstance(net_margin, (int, float)):
                if net_margin > 15:
                    dcf_score += 10
                    valuation_signals.append("High net margin")
                elif net_margin > 10:
                    dcf_score += 5
                elif net_margin < 2:
                    dcf_score -= 10
            
            # Ensure score is within bounds
            dcf_score = min(100, max(0, dcf_score))
            
            # DCF pass criteria
            dcf_passed = dcf_score >= 65
            
            # Generate recommendation
            if dcf_score >= 80:
                recommendation = "STRONG_QUALIFIED"
            elif dcf_score >= 65:
                recommendation = "QUALIFIED"
            else:
                recommendation = "REJECTED"
            
            return {
                'ticker': ticker,
                'dcf_method': 'simplified_comprehensive',
                'dcf_score': dcf_score,
                'dcf_passed': dcf_passed,
                'valuation_signals': valuation_signals,
                'key_metrics': {
                    'roe': roe,
                    'roce': roce,
                    'pe_ratio': pe_ratio,
                    'pb_ratio': pb_ratio,
                    'debt_to_equity': debt_to_equity,
                    'operating_margin': operating_margin,
                    'net_margin': net_margin
                },
                'recommendation': recommendation
            }
            
        except Exception as e:
            logger.error(f"Error in simplified DCF for {ticker}: {e}")
            return {
                'ticker': ticker,
                'error': str(e),
                'dcf_score': 0,
                'dcf_passed': False
            }
    
    def _calculate_enhanced_scores(self, qualified_companies: List[str], dcf_results: Dict[str, Any]):
        """Calculate enhanced scores combining all analysis"""
        print(f"  🔄 Calculating enhanced scores...")
        
        # Get existing analysis results
        cashflow_results = self.existing_results.get('stage_1_cashflow_prescreen', {}).get('results', {})
        comprehensive_results = self.existing_results.get('stage_2_comprehensive_analysis', {}).get('results', {})
        
        enhanced_scores = {}
        
        for ticker in qualified_companies:
            try:
                # Get all component scores
                cashflow_data = cashflow_results.get(ticker, {})
                comprehensive_data = comprehensive_results.get(ticker, {})
                dcf_data = dcf_results.get(ticker, {})
                
                # Extract scores
                cashflow_score = cashflow_data.get('cash_flow_score', 0)
                consistency_score = comprehensive_data.get('consistency_score', 0)
                qualitative_score = comprehensive_data.get('qualitative_score', 0)
                dcf_score = dcf_data.get('dcf_score', 0)
                
                # Enhanced scoring system (Real Money Weights)
                # Cash Flow: 25% (Must be strong)
                # DCF Analysis: 35% (Intrinsic value critical)
                # Consistency: 25% (Predictability important)
                # Qualitative: 15% (Management & governance)
                
                enhanced_score = (
                    cashflow_score * 0.25 +
                    dcf_score * 0.35 +
                    consistency_score * 0.25 +
                    qualitative_score * 0.15
                )
                
                # Generate final recommendation
                if enhanced_score >= 75 and dcf_data.get('dcf_passed', False):
                    recommendation = 'STRONG_BUY'
                elif enhanced_score >= 65 and dcf_data.get('dcf_passed', False):
                    recommendation = 'BUY'
                elif enhanced_score >= 55:
                    recommendation = 'HOLD'
                else:
                    recommendation = 'AVOID'
                
                enhanced_scores[ticker] = {
                    'ticker': ticker,
                    'sector': comprehensive_data.get('sector', 'unknown'),
                    'cashflow_score': cashflow_score,
                    'dcf_score': dcf_score,
                    'consistency_score': consistency_score,
                    'qualitative_score': qualitative_score,
                    'enhanced_score': enhanced_score,
                    'recommendation': recommendation,
                    'dcf_passed': dcf_data.get('dcf_passed', False),
                    'valuation_signals': dcf_data.get('valuation_signals', []),
                    'key_metrics': dcf_data.get('key_metrics', {})
                }
                
            except Exception as e:
                logger.error(f"Error calculating enhanced score for {ticker}: {e}")
        
        print(f"✅ Enhanced scores calculated for {len(enhanced_scores)} companies")
        
        self.final_results['stage_3_enhanced_scoring'] = {
            'total_companies': len(enhanced_scores),
            'results': enhanced_scores
        }
        
        return enhanced_scores
    
    def _create_portfolio_allocation(self, enhanced_scores: Dict[str, Any]):
        """Create optimal portfolio allocation"""
        print(f"  🔄 Creating portfolio allocation...")
        
        # Filter companies for portfolio (BUY and STRONG_BUY only)
        portfolio_candidates = {}
        
        for ticker, data in enhanced_scores.items():
            recommendation = data.get('recommendation', 'AVOID')
            enhanced_score = data.get('enhanced_score', 0)
            dcf_passed = data.get('dcf_passed', False)
            
            if recommendation in ['STRONG_BUY', 'BUY'] and dcf_passed and enhanced_score >= 65:
                portfolio_candidates[ticker] = data
        
        # Sort by enhanced score
        sorted_candidates = sorted(portfolio_candidates.items(), 
                                 key=lambda x: x[1]['enhanced_score'], reverse=True)
        
        # Create portfolio (top 15-20 companies for diversification)
        portfolio_size = min(20, len(sorted_candidates))
        top_companies = sorted_candidates[:portfolio_size]
        
        # Calculate weights based on scores and sector diversification
        portfolio_allocation = {}
        sector_weights = {}
        
        # First pass: calculate base weights
        total_score = sum(data['enhanced_score'] for _, data in top_companies)
        
        for ticker, data in top_companies:
            base_weight = (data['enhanced_score'] / total_score) * 100
            sector = data.get('sector', 'unknown')
            
            # Sector diversification (max 25% per sector)
            if sector not in sector_weights:
                sector_weights[sector] = 0
            
            # Adjust weight for sector diversification
            max_sector_weight = 25.0
            current_sector_weight = sector_weights[sector]
            
            if current_sector_weight + base_weight > max_sector_weight:
                adjusted_weight = max(max_sector_weight - current_sector_weight, 2.0)
            else:
                adjusted_weight = base_weight
            
            sector_weights[sector] += adjusted_weight
            
            portfolio_allocation[ticker] = {
                'weight': adjusted_weight,
                'enhanced_score': data['enhanced_score'],
                'sector': sector,
                'recommendation': data['recommendation'],
                'dcf_score': data['dcf_score'],
                'valuation_signals': data['valuation_signals'][:2]  # Top 2 signals
            }
        
        # Normalize weights to 100%
        total_weight = sum(data['weight'] for data in portfolio_allocation.values())
        if total_weight > 0:
            for ticker in portfolio_allocation:
                portfolio_allocation[ticker]['weight'] = (
                    portfolio_allocation[ticker]['weight'] / total_weight * 100
                )
        
        print(f"✅ Portfolio created with {len(portfolio_allocation)} companies")
        
        self.final_results['stage_4_portfolio_allocation'] = {
            'total_candidates': len(portfolio_candidates),
            'portfolio_size': len(portfolio_allocation),
            'allocation': portfolio_allocation,
            'sector_diversification': sector_weights
        }
        
        return portfolio_allocation
    
    def _generate_final_recommendations(self, enhanced_scores: Dict[str, Any], 
                                      portfolio_allocation: Dict[str, Any]):
        """Generate final investment recommendations"""
        print(f"  🔄 Generating final recommendations...")
        
        # Categorize all recommendations
        recommendations = {
            'strong_buy': [],
            'buy': [],
            'hold': [],
            'portfolio_allocation': portfolio_allocation,
            'summary_statistics': {}
        }
        
        for ticker, data in enhanced_scores.items():
            recommendation = data.get('recommendation', 'AVOID')
            
            if recommendation == 'STRONG_BUY':
                recommendations['strong_buy'].append(data)
            elif recommendation == 'BUY':
                recommendations['buy'].append(data)
            elif recommendation == 'HOLD':
                recommendations['hold'].append(data)
        
        # Sort each category by enhanced score
        for category in ['strong_buy', 'buy', 'hold']:
            recommendations[category].sort(key=lambda x: x.get('enhanced_score', 0), reverse=True)
        
        # Generate summary statistics
        total_analyzed = len(enhanced_scores)
        dcf_qualified = sum(1 for data in enhanced_scores.values() if data.get('dcf_passed', False))
        
        recommendations['summary_statistics'] = {
            'total_analyzed': total_analyzed,
            'dcf_qualified': dcf_qualified,
            'strong_buy_count': len(recommendations['strong_buy']),
            'buy_count': len(recommendations['buy']),
            'hold_count': len(recommendations['hold']),
            'portfolio_companies': len(portfolio_allocation),
            'dcf_pass_rate': (dcf_qualified / total_analyzed * 100) if total_analyzed > 0 else 0
        }
        
        print(f"✅ Final recommendations generated")
        
        self.final_results['stage_5_final_recommendations'] = recommendations
        
        return recommendations
    
    def _save_final_results(self):
        """Save final pipeline results"""
        os.makedirs('output/final_pipeline', exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'output/final_pipeline/final_investment_pipeline_{timestamp}.json'
        
        with open(output_file, 'w') as f:
            json.dump(self.final_results, f, indent=2, default=str)
        
        print(f"📁 Final results saved to: {output_file}")
        
        # Save portfolio allocation as CSV
        portfolio_allocation = self.final_results.get('stage_5_final_recommendations', {}).get('portfolio_allocation', {})
        
        if portfolio_allocation:
            portfolio_df = pd.DataFrame.from_dict(portfolio_allocation, orient='index')
            portfolio_df.index.name = 'ticker'
            portfolio_csv = f'output/final_pipeline/portfolio_allocation_{timestamp}.csv'
            portfolio_df.to_csv(portfolio_csv)
            print(f"📁 Portfolio allocation saved to: {portfolio_csv}")
        
        # Save all recommendations as CSV
        recommendations = self.final_results.get('stage_5_final_recommendations', {})
        all_recs = (recommendations.get('strong_buy', []) + 
                   recommendations.get('buy', []) + 
                   recommendations.get('hold', []))
        
        if all_recs:
            recs_df = pd.DataFrame(all_recs)
            recs_csv = f'output/final_pipeline/all_recommendations_{timestamp}.csv'
            recs_df.to_csv(recs_csv, index=False)
            print(f"📁 All recommendations saved to: {recs_csv}")
        
        return output_file
    
    def _display_final_summary(self):
        """Display final summary"""
        final_recs = self.final_results.get('stage_5_final_recommendations', {})
        summary_stats = final_recs.get('summary_statistics', {})
        
        print(f"\n" + "=" * 100)
        print("✅ FINAL INVESTMENT PIPELINE COMPLETED!")
        print("=" * 100)
        
        print(f"📊 PIPELINE SUMMARY:")
        print(f"   Total Companies Analyzed: {summary_stats.get('total_analyzed', 0):,}")
        print(f"   DCF Qualified: {summary_stats.get('dcf_qualified', 0):,}")
        print(f"   DCF Pass Rate: {summary_stats.get('dcf_pass_rate', 0):.1f}%")
        print(f"   Portfolio Companies: {summary_stats.get('portfolio_companies', 0)}")
        
        print(f"\n🎯 INVESTMENT RECOMMENDATIONS:")
        print(f"   💎 STRONG BUY: {summary_stats.get('strong_buy_count', 0)} companies")
        print(f"   📈 BUY: {summary_stats.get('buy_count', 0)} companies")
        print(f"   📊 HOLD: {summary_stats.get('hold_count', 0)} companies")
        
        # Show top recommendations
        strong_buy = final_recs.get('strong_buy', [])
        buy = final_recs.get('buy', [])
        
        print(f"\n🏆 TOP 10 INVESTMENT OPPORTUNITIES:")
        all_top = strong_buy + buy
        all_top.sort(key=lambda x: x.get('enhanced_score', 0), reverse=True)
        
        for i, rec in enumerate(all_top[:10], 1):
            ticker = rec.get('ticker', 'Unknown')
            sector = rec.get('sector', 'unknown')
            score = rec.get('enhanced_score', 0)
            recommendation = rec.get('recommendation', 'UNKNOWN')
            dcf_score = rec.get('dcf_score', 0)
            
            print(f"   {i:2d}. {ticker:12s} ({sector:15s}) - Score: {score:.1f} - DCF: {dcf_score:.1f} - {recommendation}")
        
        # Show portfolio allocation
        portfolio_allocation = final_recs.get('portfolio_allocation', {})
        if portfolio_allocation:
            print(f"\n💼 PORTFOLIO ALLOCATION (TOP 10):")
            sorted_portfolio = sorted(portfolio_allocation.items(), 
                                    key=lambda x: x[1]['weight'], reverse=True)
            
            for i, (ticker, data) in enumerate(sorted_portfolio[:10], 1):
                weight = data.get('weight', 0)
                score = data.get('enhanced_score', 0)
                sector = data.get('sector', 'unknown')
                print(f"   {i:2d}. {ticker:12s} ({sector:15s}) - Weight: {weight:.1f}% - Score: {score:.1f}")
        
        print(f"\n🎉 Ready for Real Money Investment Decisions!")
        print("=" * 100)

def main():
    """Main function"""
    print("🚀 FINAL INVESTMENT PIPELINE")
    print("=" * 80)
    print("Complete Real Money Analysis Pipeline")
    print("=" * 80)
    
    # Initialize and run pipeline
    pipeline = FinalInvestmentPipeline()
    results = pipeline.run_final_pipeline()
    
    if results:
        print(f"\n✅ Final Investment Pipeline Completed Successfully!")
        print(f"📁 Results available in output/final_pipeline/")
    else:
        print(f"\n❌ Pipeline failed to complete")

if __name__ == "__main__":
    main()
