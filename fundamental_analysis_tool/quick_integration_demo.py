#!/usr/bin/env python3
"""
Quick Integration Demo - Working Example
Demonstrates integration between fundamental analysis and portfolio optimization

This script shows a working example of:
1. Getting qualified tickers from fundamental analysis
2. Collecting price data for those tickers
3. Running basic portfolio optimization
4. Displaying results
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, '..'))

# Import fundamental analysis system
from professional_modular_system import ProfessionalModularSystem

# Import portfolio optimization system
try:
    from Portfolio_optimization.optimal_portfolio_builder import OptimalPortfolioBuilder
    PORTFOLIO_AVAILABLE = True
    print("✅ Portfolio optimization system available")
except ImportError as e:
    print(f"❌ Portfolio optimization not available: {e}")
    PORTFOLIO_AVAILABLE = False


def get_sample_qualified_tickers(system, sample_size=20):
    """
    Get a sample of qualified tickers for demonstration
    
    Since we don't have analysis results yet, we'll use a sample from ALL_TICKERS
    representing high-quality Indian companies
    """
    print("🔍 Getting sample qualified tickers...")
    
    # Get all available tickers
    all_tickers = system.get_ticker_set_tickers('ALL_TICKERS')
    print(f"📊 Total available tickers: {len(all_tickers)}")
    
    # Use a curated sample of high-quality Indian companies
    # These represent companies that would typically pass fundamental analysis
    high_quality_sample = [
        'TCS', 'RELIANCE', 'HDFCBANK', 'INFY', 'ICICIBANK',
        'SBIN', 'BAJFINANCE', 'HINDUNILVR', 'ITC', 'KOTAKBANK',
        'HCLTECH', 'ASIANPAINT', 'AXISBANK', 'MARUTI', 'SUNPHARMA',
        'TITAN', 'WIPRO', 'NESTLEIND', 'ULTRACEMCO', 'POWERGRID'
    ]
    
    # Filter to only include tickers that exist in our database
    qualified_sample = [ticker for ticker in high_quality_sample if ticker in all_tickers]
    
    # Limit to sample size
    qualified_sample = qualified_sample[:sample_size]
    
    print(f"✅ Selected {len(qualified_sample)} qualified tickers for demo:")
    print(f"📋 Sample tickers: {qualified_sample}")
    
    return qualified_sample


def collect_price_data_for_sample(qualified_tickers, period='3y'):
    """
    Collect price data for the qualified ticker sample
    """
    if not PORTFOLIO_AVAILABLE:
        print("❌ Cannot collect price data - portfolio system not available")
        return None
    
    print(f"\n💰 Collecting {period} price data for {len(qualified_tickers)} companies...")
    
    try:
        # Initialize portfolio builder
        builder = OptimalPortfolioBuilder(
            data_dir='demo_portfolio_data',
            results_dir='demo_portfolio_results'
        )
        
        # Collect stock data
        print("📊 Starting data collection...")
        builder.collect_stock_data(max_stocks=len(qualified_tickers), period=period)
        
        # Get returns data for our specific tickers
        print("📈 Extracting returns data...")
        returns_data = builder.get_returns_data(qualified_tickers)
        
        if returns_data is not None and len(returns_data.columns) > 0:
            print(f"✅ Successfully collected data for {len(returns_data.columns)} companies")
            print(f"📊 Data shape: {returns_data.shape}")
            print(f"📅 Date range: {returns_data.index.min()} to {returns_data.index.max()}")
            
            # Show sample of available tickers with data
            available_tickers = list(returns_data.columns)
            print(f"📋 Tickers with data: {available_tickers[:10]}{'...' if len(available_tickers) > 10 else ''}")
            
            return {
                'returns_data': returns_data,
                'available_tickers': available_tickers,
                'builder': builder
            }
        else:
            print("❌ No returns data collected")
            return None
            
    except Exception as e:
        print(f"❌ Error collecting price data: {e}")
        return None


def run_basic_portfolio_optimization(price_data_result):
    """
    Run basic portfolio optimization on the collected data
    """
    if not price_data_result:
        print("❌ No price data available for optimization")
        return None
    
    print("\n🎯 Running basic portfolio optimization...")
    
    returns_data = price_data_result['returns_data']
    available_tickers = price_data_result['available_tickers']
    builder = price_data_result['builder']
    
    try:
        # Set the selected stocks and returns data
        builder.selected_stocks = available_tickers
        builder.selected_returns = returns_data
        
        # Run traditional optimization methods
        print("🔧 Running traditional optimization methods...")
        traditional_results = builder.run_traditional_optimization()
        
        if traditional_results:
            print("✅ Traditional optimization completed successfully!")
            
            # Display results summary
            print("\n📊 PORTFOLIO OPTIMIZATION RESULTS:")
            print("=" * 50)
            
            for method, result in traditional_results.items():
                if isinstance(result, dict) and 'Return' in result:
                    print(f"\n🎯 {method.upper()}:")
                    print(f"  Expected Return: {result['Return']:.2%}")
                    print(f"  Volatility: {result['Volatility']:.2%}")
                    print(f"  Sharpe Ratio: {result['Sharpe']:.3f}")
            
            return traditional_results
        else:
            print("❌ Traditional optimization failed")
            return None
            
    except Exception as e:
        print(f"❌ Error in portfolio optimization: {e}")
        return None


def display_portfolio_weights(builder):
    """
    Display the portfolio weights from optimization
    """
    try:
        print("\n💼 PORTFOLIO WEIGHTS:")
        print("=" * 30)
        
        # Try to get weights from the builder's optimization results
        if hasattr(builder, 'optimization_results') and builder.optimization_results:
            for method, weights in builder.optimization_results.items():
                if isinstance(weights, dict):
                    print(f"\n🎯 {method.upper()} Portfolio:")
                    sorted_weights = sorted(weights.items(), key=lambda x: x[1], reverse=True)
                    for ticker, weight in sorted_weights[:10]:  # Top 10 holdings
                        print(f"  {ticker}: {weight:.1%}")
        else:
            print("📊 Portfolio weights not available in current format")
            
    except Exception as e:
        print(f"❌ Error displaying portfolio weights: {e}")


def main():
    """
    Main demonstration function
    """
    print("🚀 QUICK INTEGRATION DEMO")
    print("=" * 50)
    print("Demonstrating Fundamental Analysis + Portfolio Optimization Integration")
    
    # Step 1: Initialize fundamental analysis system
    print("\n📊 Step 1: Initialize Fundamental Analysis System")
    system = ProfessionalModularSystem()
    
    # Step 2: Get qualified tickers (sample for demo)
    print("\n🎯 Step 2: Get Qualified Tickers")
    qualified_tickers = get_sample_qualified_tickers(system, sample_size=15)
    
    if not qualified_tickers:
        print("❌ No qualified tickers available")
        return
    
    # Step 3: Collect price data
    print("\n💰 Step 3: Collect Price Data")
    price_data_result = collect_price_data_for_sample(qualified_tickers, period='3y')
    
    if not price_data_result:
        print("❌ Price data collection failed")
        return
    
    # Step 4: Run portfolio optimization
    print("\n🎯 Step 4: Portfolio Optimization")
    optimization_results = run_basic_portfolio_optimization(price_data_result)
    
    if optimization_results:
        # Step 5: Display portfolio weights
        print("\n💼 Step 5: Portfolio Weights")
        display_portfolio_weights(price_data_result['builder'])
        
        # Step 6: Summary and next steps
        print("\n🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("✅ Successfully demonstrated:")
        print("  - Fundamental analysis ticker extraction")
        print("  - Price data collection for qualified companies")
        print("  - Portfolio optimization with multiple methods")
        print("  - Results analysis and display")
        
        print("\n🚀 Next Steps:")
        print("  1. Run actual fundamental analysis to get qualified companies")
        print("  2. Collect price data for all qualified tickers")
        print("  3. Implement advanced optimization methods")
        print("  4. Add dashboard integration")
        print("  5. Create investment recommendations")
        
        # Save demo results
        demo_results = {
            'timestamp': datetime.now().isoformat(),
            'qualified_tickers': qualified_tickers,
            'available_tickers': price_data_result['available_tickers'],
            'optimization_methods': list(optimization_results.keys()) if optimization_results else [],
            'status': 'success'
        }
        
        results_file = Path('demo_integration_results.json')
        with open(results_file, 'w') as f:
            json.dump(demo_results, f, indent=2)
        
        print(f"\n📁 Demo results saved to: {results_file}")
        
    else:
        print("\n❌ Demo failed at portfolio optimization stage")
        print("🔧 Check Portfolio_optimization system installation and configuration")


if __name__ == "__main__":
    main()
