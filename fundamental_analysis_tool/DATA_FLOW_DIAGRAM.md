# 📊 **COMPREHENSIVE DATA FLOW DIAGRAM**

## 🎯 **OVERVIEW**

This document provides detailed data flow diagrams showing how data moves through the financial analysis system, from collection to final presentation.

---

## 🔄 **MASTER DATA FLOW ARCHITECTURE**

### **📊 Complete System Data Flow**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   External      │    │   Data          │    │   Analysis      │
│   Data Sources  │───▶│   Collection    │───▶│   Processing    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ • screener.in   │    │ • Raw Data      │    │ • Fundamental   │
│ • NSE/BSE       │    │ • Company Info  │    │ • DCF Analysis  │
│ • Manual Input  │    │ • Financial     │    │ • Cash Flow     │
│                 │    │   Statements    │    │ • Portfolio     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                    ┌─────────────────┐    ┌─────────────────┐
                    │   Unified       │    │   Results       │
                    │   Data Access   │    │   Storage &     │
                    │   (data_loader) │    │   Presentation  │
                    └─────────────────┘    └─────────────────┘
```

---

## 📥 **DATA COLLECTION FLOW**

### **🔍 Primary Data Collection (screener.in)**
```
screener.in API
    │
    ▼
screener_data_collector/collect_data.py
    │
    ├── Company List Collection
    │   ├── All 4,950+ companies
    │   ├── Basic financial metrics
    │   └── Market data
    │
    ├── Individual Company Data
    │   ├── Detailed financials
    │   ├── Historical data (10 years)
    │   ├── Quarterly results
    │   └── Ratios & metrics
    │
    ▼
screener_data_collector/data/
    ├── summary_data.csv (All companies overview)
    ├── individual_companies/
    │   ├── TICKER1.json
    │   ├── TICKER2.json
    │   └── ... (4,950+ files)
    └── metadata/
        ├── collection_timestamp.json
        └── data_quality_report.json
```

### **🏢 Secondary Data Collection (NSE/BSE)**
```
NSE/BSE APIs
    │
    ▼
exchange_data_collector/
    │
    ├── Company Details
    │   ├── Corporate information
    │   ├── Management details
    │   └── Business descriptions
    │
    ├── Annual Reports
    │   ├── PDF downloads
    │   ├── Text extraction
    │   └── Key metrics parsing
    │
    ▼
exchange_data_collector/
    ├── company_data/
    │   ├── TICKER1_details.json
    │   └── TICKER2_details.json
    └── annual_reports/
        ├── TICKER1_2023.pdf
        └── TICKER2_2023.pdf
```

---

## 🔧 **DATA PROCESSING FLOW**

### **📊 Unified Data Access Layer**
```
Raw Data Sources
    │
    ├── screener_data_collector/data/
    ├── exchange_data_collector/company_data/
    └── Manual inputs (CUSTOM_LIST)
    │
    ▼
utils/data_loader.py (ScreenerDataLoader)
    │
    ├── get_all_tickers() → List[str]
    │   └── Returns: ['TCS', 'RELIANCE', 'SBIN', ...]
    │
    ├── load_company_data(ticker) → Dict
    │   └── Returns: {
    │       'Name': 'Tata Consultancy Services',
    │       'Market Cap': 12500000,
    │       'PE': 25.5,
    │       'ROE %': 42.1,
    │       ... (200+ financial metrics)
    │   }
    │
    ├── load_all_companies_summary() → DataFrame
    │   └── Returns: Pandas DataFrame with all companies
    │
    └── get_company_data(ticker) → Dict
        └── Enhanced company data with error handling
```

### **🎯 Ticker Set Generation Flow**
```
professional_modular_system.py::get_ticker_set_tickers()
    │
    ├── ALL_TICKERS
    │   └── data_loader.get_all_tickers() → 4,903 companies
    │
    ├── CASHFLOW_QUALIFIED
    │   └── _get_cashflow_qualified_tickers() → 1,245 companies
    │       ├── Load latest cashflow analysis results
    │       ├── Extract qualified companies
    │       └── Return ticker list
    │
    ├── DCF_FILTERED (Multiple categories)
    │   └── _get_dcf_filtered_tickers(filter_function) → Variable count
    │       ├── Load latest DCF analysis results
    │       ├── Apply margin of safety filter
    │       └── Return filtered tickers
    │
    └── CROSS_ANALYSIS
        └── _get_cross_analysis_tickers(analysis_type, base_set) → Variable count
            ├── Find cross-analysis result files
            ├── Extract qualified companies
            └── Return intersection tickers
```

---

## 🔬 **ANALYSIS PROCESSING FLOW**

### **🏗️ Fundamental Analysis Data Flow**
```
Input: List[str] tickers
    │
    ▼
models/screener.py::screen_companies()
    │
    ├── Pre-filtering Phase
    │   ├── Load summary data for all companies
    │   ├── Apply basic filters (market cap, data availability)
    │   └── Reduce ticker list (e.g., 4,903 → 2,500)
    │
    ├── Parallel Processing Phase
    │   ├── ThreadPoolExecutor (4 workers)
    │   ├── For each ticker:
    │   │   ├── data_loader.load_company_data(ticker)
    │   │   ├── _extract_metrics() → standardized metrics dict
    │   │   ├── _check_tier_1() → financial health
    │   │   ├── _check_tier_2() → growth metrics
    │   │   ├── _check_tier_3() → valuation ratios
    │   │   ├── _check_tier_4() → cash flow quality
    │   │   ├── _check_tier_5() → historical consistency
    │   │   └── consistency_analyzer.analyze_company_consistency()
    │   └── Collect results from all workers
    │
    ├── Enhancement Phase
    │   ├── qualitative_analyzer.analyze_company()
    │   ├── sector_analyzer.analyze_company_sector()
    │   └── Calculate overall fundamental score
    │
    ▼
Output: {
    'metadata': {...},
    'screening_results': {...},
    'company_results': {
        'TCS': {
            'tier1_pass': True,
            'tier2_pass': True,
            'tier3_pass': False,
            'tier4_pass': True,
            'tier5_pass': True,
            'fundamental_score': 85.2,
            'qualified': True
        },
        ...
    },
    'summary': {
        'total_analyzed': 2500,
        'qualified_companies': [...],
        'qualification_rate': 12.5
    }
}
```

### **💰 Cash Flow Analysis Data Flow**
```
Input: List[str] tickers
    │
    ▼
run_cashflow_analysis.py
    │
    ├── For each ticker:
    │   ├── data_loader.get_company_data(ticker)
    │   ├── Extract cash flow metrics:
    │   │   ├── Operating Cash Flow (10 years)
    │   │   ├── Free Cash Flow (10 years)
    │   │   ├── Cash Flow Growth rates
    │   │   └── Cash Flow Quality ratios
    │   ├── Calculate cash flow score (0-100)
    │   ├── Apply qualification criteria
    │   └── Determine pass/fail status
    │
    ▼
Output: {
    'metadata': {...},
    'company_results': {
        'TCS': {
            'qualified': True,
            'cash_flow_score': 87.5,
            'reasons_passed': ['Consistent OCF', 'Strong FCF'],
            'ocf_10_year': [1000, 1200, 1400, ...],
            'fcf_10_year': [800, 950, 1100, ...]
        },
        ...
    },
    'summary_statistics': {
        'total_companies': 4903,
        'qualified_companies': 1245,
        'qualification_rate': 25.4
    }
}
```

### **📈 DCF Analysis Data Flow**
```
Input: List[str] tickers
    │
    ▼
run_direct_dcf_analysis.py
    │
    ├── For each ticker:
    │   ├── data_loader.get_company_data(ticker)
    │   ├── Calculate Free Cash Flow:
    │   │   ├── Operating Cash Flow
    │   │   ├── - Capital Expenditures
    │   │   └── = Free Cash Flow
    │   ├── Estimate Growth Rate:
    │   │   ├── Historical FCF growth
    │   │   ├── Revenue growth analysis
    │   │   └── Conservative estimates
    │   ├── Calculate WACC:
    │   │   ├── Cost of Equity (CAPM)
    │   │   ├── Cost of Debt
    │   │   └── Weighted average
    │   ├── Project Future Cash Flows (10 years)
    │   ├── Calculate Terminal Value
    │   ├── Discount to Present Value
    │   ├── Calculate Per-Share Value
    │   └── Calculate Margin of Safety
    │
    ▼
Output: {
    'metadata': {...},
    'company_results': {
        'TCS': {
            'enterprise_value': 1250000,
            'per_share_value': 3850.25,
            'current_price': 3200.00,
            'margin_of_safety': 20.3,
            'buffett_approved': False,
            'buffett_score': 65.2
        },
        ...
    },
    'summary_statistics': {
        'total_analyzed': 4903,
        'successful_analyses': 4756,
        'buffett_approved': 0
    },
    'valuation_distribution': {
        'severely_undervalued': 2699,
        'undervalued': 211,
        'fairly_valued': 414,
        'overvalued': 432,
        'severely_overvalued': 1147
    }
}
```

---

## 🔄 **CROSS-ANALYSIS DATA FLOW**

### **🎯 Cross-Analysis Execution Flow**
```
Base Analysis Results
    │
    ├── CASHFLOW Analysis Results
    │   └── Extract qualified tickers → List[str]
    │
    ├── DCF Analysis Results  
    │   └── Extract category tickers → List[str]
    │
    └── FUNDAMENTAL Analysis Results
        └── Extract qualified tickers → List[str]
    │
    ▼
Cross-Analysis Ticker Sets
    │
    ├── CASHFLOW_ON_DCF_SEVERELY_UNDERVALUED
    │   ├── Load DCF results
    │   ├── Filter for severely undervalued (margin > 50%)
    │   ├── Run CASHFLOW analysis on filtered tickers
    │   └── Result: 342 companies
    │
    ├── DCF_ON_CASHFLOW_QUALIFIED
    │   ├── Load CASHFLOW results
    │   ├── Extract qualified companies
    │   ├── Run DCF analysis on qualified tickers
    │   └── Result: 1,245 companies
    │
    └── FUNDAMENTAL_ON_CROSS_RESULTS
        ├── Load any cross-analysis results
        ├── Extract final ticker list
        ├── Run FUNDAMENTAL analysis
        └── Result: Variable count
```

---

## 📊 **RESULTS STORAGE & RETRIEVAL FLOW**

### **💾 Professional Result Storage**
```
Analysis Results
    │
    ▼
professional_modular_system.py::_save_professional_result()
    │
    ├── Generate Analysis ID
    │   └── Format: {ANALYSIS_TYPE}_{TICKER_SET}_{HASH}
    │
    ├── Create Professional Metadata
    │   ├── analysis_id
    │   ├── analysis_type
    │   ├── ticker_set
    │   ├── total_companies
    │   ├── sample_size
    │   ├── timestamp
    │   └── description
    │
    ├── Standardize Result Format
    │   ├── metadata: {...}
    │   ├── company_results: {...}
    │   ├── summary_statistics: {...}
    │   └── analysis_specific_data: {...}
    │
    ▼
output/modular_analysis/
    ├── FUNDAMENTAL_ALL_TICKERS_96fa78c6_20250525_010644.json
    ├── CASHFLOW_DCF_SEVERELY_UNDERVALUED_8a7b9c2d_20250525_011234.json
    └── DCF_CASHFLOW_QUALIFIED_5f3e8d1a_20250525_012345.json
```

### **🔍 Result Retrieval & Display Flow**
```
Dashboard Request
    │
    ▼
professional_modular_system.py::list_available_analyses()
    │
    ├── Scan output/modular_analysis/ directory
    ├── Parse filenames for metadata
    ├── Load JSON files for summary info
    ├── Sort by timestamp (newest first)
    └── Return analysis list
    │
    ▼
dashboard.py::display_saved_results()
    │
    ├── Display analysis cards with:
    │   ├── Analysis ID
    │   ├── Analysis Type
    │   ├── Ticker Set
    │   ├── Company Count
    │   ├── Timestamp
    │   └── Action buttons
    │
    ├── On "View Results" click:
    │   ├── Load full JSON file
    │   ├── Parse analysis type
    │   ├── Extract relevant data
    │   ├── Apply investment implications
    │   ├── Create interactive tables
    │   ├── Add filtering options
    │   └── Display comprehensive results
    │
    └── On "Download" click:
        └── Serve JSON file for download
```

---

## 🎯 **DATA TRANSFORMATION POINTS**

### **📊 Key Data Transformations**
```
1. Raw Financial Data → Standardized Metrics
   ├── Unit normalization (Cr, Rs, %)
   ├── Missing value handling
   ├── Data type conversion
   └── Validation checks

2. Company Analysis → Investment Implications
   ├── Tier performance → Investment type
   ├── Score calculation → Risk assessment
   ├── Multiple criteria → Single recommendation
   └── Technical analysis → Plain English

3. Individual Results → Summary Statistics
   ├── Company-level data → Aggregate metrics
   ├── Pass/fail counts → Qualification rates
   ├── Score distributions → Performance insights
   └── Trend analysis → System health metrics

4. Analysis Results → Cross-Analysis Input
   ├── Result files → Ticker lists
   ├── Qualification criteria → Filtered sets
   ├── Multiple analyses → Combined insights
   └── Sequential processing → Compound filtering
```

**🎯 This comprehensive data flow documentation provides a complete understanding of how data moves through the system, enabling effective debugging, optimization, and enhancement of the financial analysis platform.**
